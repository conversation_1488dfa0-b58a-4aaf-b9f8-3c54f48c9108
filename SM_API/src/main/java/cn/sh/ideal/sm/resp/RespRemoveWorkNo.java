package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespRemoveWorkNo extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = -986703050873990778L;
	/**
	 * 
	 */
	private Map<String,Object> data;
	public RespRemoveWorkNo(String resultCode, String resultMsg,  Map<String,Object> data) {
		super(resultCode, resultMsg);
	}
	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}
	

}
