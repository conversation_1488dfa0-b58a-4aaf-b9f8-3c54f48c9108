package cn.sh.ideal.sm.resp;

import java.util.HashMap;
import java.util.List;

public class RespPeakReport extends BaseRespone{
	/**
	 * 
	 */
	private static final long serialVersionUID = 5736000708977990968L;
	/**
	 * 
	 */
	private List<HashMap<String, Object>> data;
	public  RespPeakReport(String resultCode, String resultMsg, List<HashMap<String, Object>> list) {
		super(resultCode, resultMsg);
		this.data = list;
	}
	public List<HashMap<String, Object>> getData() {
		return data;
	}
	public void setData(List<HashMap<String, Object>> data) {
		this.data = data;
	}
	
}
