package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespUpdateSession extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Map<String, Object> data;
	public RespUpdateSession(String resultCode, String resultMsg, Map<String, Object> object) {
		super(resultCode, resultMsg);
		this.data = object;
	}
	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}
	
	
}
