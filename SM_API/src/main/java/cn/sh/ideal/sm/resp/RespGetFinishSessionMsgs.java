package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespGetFinishSessionMsgs extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Map<String, Object> data;
	
	public RespGetFinishSessionMsgs(String resultCode, String resultMsg, Map<String, Object> sessionMap) {

		super(resultCode, resultMsg);
		this.data = sessionMap;
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}
	
}
