package cn.sh.ideal.sm.resp;

import java.io.Serializable;

public class BaseRespone implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1742723626382766331L;
	/**
	 * 
	 */
	private String resultCode;
	private String resultMsg;
	
	protected  BaseRespone(String resultCode,String resultMsg){
		 this.resultCode = resultCode;
		 this.resultMsg = resultMsg;
	}
	
	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	
}
