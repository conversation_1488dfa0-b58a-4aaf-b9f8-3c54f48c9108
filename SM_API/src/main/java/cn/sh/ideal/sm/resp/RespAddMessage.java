package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespAddMessage extends BaseRespone{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4454059629919304371L;
	/**
	 * 
	 */
	private Map<String,Object> data;

	public RespAddMessage(String resultCode, String resultMsg, Map<String,Object> data) {
		super(resultCode, resultMsg);
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}

	
}
