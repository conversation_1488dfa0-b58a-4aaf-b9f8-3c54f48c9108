package cn.sh.ideal.sm.resp;

import java.io.Serializable;
import java.util.Date;


public class AgentStatistics implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -513067311587204395L;
	private String tenantCode;
	private String workNo;
	private String sessionId;
	private Integer agentResponse;
	private Integer serviceTime;
	private Integer satisfied;
	private String startTime;
	private String endTime;
	private Date beginTime;
	private Date minTime;
	
	public Date getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}
	public Date getMinTime() {
		return minTime;
	}
	public void setMinTime(Date minTime) {
		this.minTime = minTime;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public Integer getAgentResponse() {
		return agentResponse;
	}
	public void setAgentResponse(Integer agentResponse) {
		this.agentResponse = agentResponse;
	}
	public Integer getServiceTime() {
		return serviceTime;
	}
	public void setServiceTime(Integer serviceTime) {
		this.serviceTime = serviceTime;
	}
	public Integer getSatisfied() {
		return satisfied;
	}
	public void setSatisfied(Integer satisfied) {
		this.satisfied = satisfied;
	}

	
	

}
