/**
 * Project Name:SM Maven Webapp
 * File Name:SessionSortItem.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月30日下午4:10:26
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.resp;

import java.io.Serializable;

/**
 * ClassName:SessionSortItem <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2014年12月30日 下午4:10:26 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionSortItem implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String skillQueue;
    private String queueName;
    private String channelCode;
    private String channelName;
    private long sortCount;
    public String getSkillQueue() {
        return skillQueue;
    }
    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }
    public String getQueueName() {
        return queueName;
    }
    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }
    public String getChannelCode() {
        return channelCode;
    }
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
    public String getChannelName() {
        return channelName;
    }
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
    public long getSortCount() {
        return sortCount;
    }
    public void setSortCount(long sortCount) {
        this.sortCount = sortCount;
    }
    

}
