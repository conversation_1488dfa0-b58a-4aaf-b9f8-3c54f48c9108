package cn.sh.ideal.sm.resp;

public class RespRemoveSessionCache extends BaseRespone{
	/**
	 * 
	 */
	private static final long serialVersionUID = -662493502096785753L;
	/**
	 * 
	 */
	private String data;
	public RespRemoveSessionCache(String resultCode, String resultMsg, String sessionId) {
		super(resultCode, resultMsg);
		this.data = sessionId;
	}
	
	public String getData() {
		return data;
	}
	public void setData(String data) {
		this.data = data;
	}
	
}
