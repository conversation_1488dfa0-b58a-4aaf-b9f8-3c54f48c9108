package cn.sh.ideal.sm.resp;

import java.util.List;

public class RespAgentCountReport extends BaseRespone{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private List<Object> data;
	public RespAgentCountReport(String resultCode, String resultMsg, List<Object> list) {
		super(resultCode, resultMsg);
		this.data = list;
	}
	public List<Object> getData() {
		return data;
	}
	public void setData(List<Object> data) {
		this.data = data;
	}
	
}
