package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespUpdateSessionUserActive extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5692601939855791200L;
	/**
	 * 
	 */
	private Map<String, Object> data;
	
	public RespUpdateSessionUserActive(String resultCode, String resultMsg, Map<String, Object> data) {
		super(resultCode, resultMsg);
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}

	
}
