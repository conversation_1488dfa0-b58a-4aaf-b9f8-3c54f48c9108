package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespAddWorkNo extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5401020668563765135L;
	/**
	 * 
	 */
	private Map<String,Object> data;
	public RespAddWorkNo(String resultCode, String resultMsg, Map<String,Object> data) {
		super(resultCode, resultMsg);
	}
	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}

	
}
