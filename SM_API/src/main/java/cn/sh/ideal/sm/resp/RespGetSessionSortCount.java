package cn.sh.ideal.sm.resp;

public class RespGetSessionSortCount extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private FlagModel data;
	
	public RespGetSessionSortCount(String resultCode, String resultMsg, FlagModel flag) {
		super(resultCode, resultMsg);
		this.data = flag;
	}

	public FlagModel getData() {
		return data;
	}

	public void setData(FlagModel data) {
		this.data = data;
	}


	
}
