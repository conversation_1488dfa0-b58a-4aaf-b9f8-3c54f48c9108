package cn.sh.ideal.sm.resp;

import java.util.List;

import cn.sh.ideal.model.SessionInfo;

public class RespCreateInitiativeSession extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8094018786324389399L;
	/**
	 * 
	 */
	private List<SessionInfo> data;
	private List<String> failAccounts;
	public  RespCreateInitiativeSession(String resultCode, String resultMsg, List<SessionInfo> sessionInfo,List<String> failAccounts) {
		super(resultCode, resultMsg);
		this.data = sessionInfo;
		this.failAccounts =failAccounts;
	}
	public List<SessionInfo> getData() {
		return data;
	}
	public void setData(List<SessionInfo> data) {
		this.data = data;
	}
	public List<String> getFailAccounts() {
		return failAccounts;
	}
	public void setFailAccounts(List<String> failAccounts) {
		this.failAccounts = failAccounts;
	}

	
}
