package cn.sh.ideal.sm.service;

import cn.sh.ideal.sm.req.ReportData;
import cn.sh.ideal.sm.req.SatisfyData;
import cn.sh.ideal.sm.req.TenantData;
import cn.sh.ideal.sm.resp.AgentStatistics;
import cn.sh.ideal.sm.resp.RespAgentCountReport;
import cn.sh.ideal.sm.resp.RespAgentHisDataReport;
import cn.sh.ideal.sm.resp.RespAgentStatistics;
import cn.sh.ideal.sm.resp.RespPeakReport;
import cn.sh.ideal.sm.resp.RespPutSatisfied;
import cn.sh.ideal.sm.resp.RespQueryChannelServiceCount;
import cn.sh.ideal.sm.resp.RespQuerySkillWaitAvgTime;

public interface ReportService {
	/**
	 * 服务峰谷分析报表
	 * @param reportData
	 * @return
	 */
	public RespPeakReport peakReport(ReportData data); 
	/**
	 * 服务峰谷分析报表所需坐席数
	 * @param reportData
	 * @return
	 */
	public RespAgentCountReport agentCountReport(ReportData data);
	/**
	 * 提交会话满意度
	 * @param reportData
	 * @return
	 */
	public RespPutSatisfied putSatisfied(SatisfyData data);
	/**
	 * 各技能组平均等待时常
	 * @param reportData
	 * @return
	 */
	public RespQuerySkillWaitAvgTime querySkillWaitAvgTime(TenantData data);
	/**
	 * 各渠道服务数
	 * @param tenant 渠道账号
	 * @return
	 */
	public RespQueryChannelServiceCount queryChannelServiceCount(String tenant);
	/**
	 * 坐席历史交互数据
	 * @param reportData
	 * @return
	 */
	public RespAgentHisDataReport agentHisDataReport(TenantData data);
	/**
	 * 坐席个人绩效统计
	 * @param reportData
	 * @return
	 */
	public RespAgentStatistics agentStatistics(AgentStatistics data);
}
