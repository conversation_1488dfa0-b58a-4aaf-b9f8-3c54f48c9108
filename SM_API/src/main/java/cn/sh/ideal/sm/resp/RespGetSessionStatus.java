package cn.sh.ideal.sm.resp;

public class RespGetSessionStatus extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = -4850177083727345048L;
	/**
	 * 
	 */
	private SessionStatusModel data;
	
	public RespGetSessionStatus(String resultCode, String resultMsg, SessionStatusModel sessionStatusModel) {
		super(resultCode, resultMsg);
		this.data = sessionStatusModel;
	}

	public SessionStatusModel getData() {
		return data;
	}

	public void setData(SessionStatusModel data) {
		this.data = data;
	}
	
	
}
