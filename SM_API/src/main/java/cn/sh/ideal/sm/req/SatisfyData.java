/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionData.java
 * Package Name:cn.sh.ideal.mir.session.data
 * Date:2014年12月4日下午5:10:58
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.req;


import java.io.Serializable;

/**
 * ClassName:SatisfyData <br/>
 * Function: 满意度需要的字段信息 <br/>
 * Date:     2014年12月4日 下午5:10:58 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SatisfyData implements Serializable{
    private static final long serialVersionUID = 1L;
    
    /*会话ID*/
    private String sessionId;
    
    /*工号*/
    private String workNo;
    
    /*满意度*/
    private Integer satified;
    /*创建时间*/
    private long createTime;
    
    /*请求时间*/
    private long requestTime;
    
    /*建议*/
    private String suggest;

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public Integer getSatified() {
		return satified;
	}

	public void setSatified(Integer satified) {
		this.satified = satified;
	}

	public long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}

	public long getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(long requestTime) {
		this.requestTime = requestTime;
	}

	public String getSuggest() {
		return suggest;
	}

	public void setSuggest(String suggest) {
		this.suggest = suggest;
	}

	@Override
	public String toString() {
		return "SatisfyData [sessionId=" + sessionId + ", workNo=" + workNo
				+ ", satified=" + satified + ", createTime=" + createTime
				+ ", requestTime=" + requestTime + ", suggest=" + suggest + "]";
	}
}
