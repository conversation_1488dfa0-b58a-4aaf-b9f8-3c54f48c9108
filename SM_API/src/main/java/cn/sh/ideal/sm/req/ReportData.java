/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionData.java
 * Package Name:cn.sh.ideal.mir.session.data
 * Date:2014年12月4日下午5:10:58
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.req;


import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * ClassName:ReportData <br/>
 * Function: 查询report需要的字段信息 <br/>
 * Date:     2014年12月4日 下午5:10:58 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class ReportData implements Serializable{
    private static final long serialVersionUID = 1L;
    
    /*技能组标识*/
    private String skillTypeFlag;
    
    /*技能组标识*/
    private String skillQueueFlag;
    
    /*业务标识*/
    private String businessTypeFlag;
    /*渠道标识*/
    private String channelFlag;
    
    /*空参结果集*/
    private List<Object> emptyList;
    
    /*状态*/
    private String status;
    
    private String[] statuses;
    
    /*权值*/
    private Integer scale;
    
    /*租户*/
    private String[] tenantCodes;
    
    /*租户*/
    private String[] tenantCode;
    
    /*工号*/
    private String workNo;
    
    /*技能组*/
    private String skillQueue;
    
    private String[] skillQueues;
    
    /*技能组分类*/
    private String skillType;
    
    private String[] skillTypes;

    /*开始时间*/
    private String startTime;
    
    /*结束时间*/
    private String endTime;
	public String getSkillTypeFlag() {
		return skillTypeFlag;
	}

	public void setSkillTypeFlag(String skillTypeFlag) {
		this.skillTypeFlag = skillTypeFlag;
	}

	public String getSkillQueueFlag() {
		return skillQueueFlag;
	}

	public void setSkillQueueFlag(String skillQueueFlag) {
		this.skillQueueFlag = skillQueueFlag;
	}

	public String getBusinessTypeFlag() {
		return businessTypeFlag;
	}

	public void setBusinessTypeFlag(String businessTypeFlag) {
		this.businessTypeFlag = businessTypeFlag;
	}

	public String getChannelFlag() {
		return channelFlag;
	}

	public void setChannelFlag(String channelFlag) {
		this.channelFlag = channelFlag;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Integer getScale() {
		return scale;
	}

	public void setScale(Integer scale) {
		this.scale = scale;
	}

	public String[] getTenantCodes() {
		return tenantCodes;
	}

	public void setTenantCodes(String[] tenantCodes) {
		this.tenantCodes = tenantCodes;
	}

	public String[] getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String[] tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getSkillType() {
		return skillType;
	}

	public void setSkillType(String skillType) {
		this.skillType = skillType;
	}

	public String[] getStatuses() {
		return statuses;
	}

	public void setStatuses(String[] statuses) {
		this.statuses = statuses;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String[] getSkillQueues() {
		return skillQueues;
	}

	public void setSkillQueues(String[] skillQueues) {
		this.skillQueues = skillQueues;
	}

	public String[] getSkillTypes() {
		return skillTypes;
	}

	public void setSkillTypes(String[] skillTypes) {
		this.skillTypes = skillTypes;
	}

	public List<Object> getEmptyList() {
		return emptyList;
	}

	public void setEmptyList(List<Object> emptyList) {
		this.emptyList = emptyList;
	}

	@Override
	public String toString() {
		return "ReportData [skillTypeFlag=" + skillTypeFlag
				+ ", skillQueueFlag=" + skillQueueFlag + ", businessTypeFlag="
				+ businessTypeFlag + ", channelFlag=" + channelFlag
				+ ", emptyList=" + emptyList + ", status=" + status
				+ ", statuses=" + Arrays.toString(statuses) + ", scale="
				+ scale + ", tenantCodes=" + Arrays.toString(tenantCodes)
				+ ", tenantCode=" + Arrays.toString(tenantCode) + ", workNo="
				+ workNo + ", skillQueue=" + skillQueue + ", skillQueues="
				+ Arrays.toString(skillQueues) + ", skillType=" + skillType
				+ ", skillTypes=" + Arrays.toString(skillTypes)
				+ ", startTime=" + startTime + ", endTime=" + endTime + "]";
	}
}
