/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionData.java
 * Package Name:cn.sh.ideal.mir.session.data
 * Date:2014年12月4日下午5:10:58
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.req;


import java.io.Serializable;

/**
 * ClassName:SkillWaitAvgTimeData <br/>
 * Function: 查询report需要的字段信息 <br/>
 * Date:     2014年12月4日 下午5:10:58 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class TenantData implements Serializable{
    private static final long serialVersionUID = 1L;
    
    /*租户*/
    private String tenantCode;
    
    /*工号*/
    private String workNo;
    
    /*开始时间*/
    private String startTime;
    
    /*结束时间*/
    private String endTime;

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
    
}
