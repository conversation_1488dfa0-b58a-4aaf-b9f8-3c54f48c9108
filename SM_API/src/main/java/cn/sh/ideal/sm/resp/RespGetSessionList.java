package cn.sh.ideal.sm.resp;

import java.util.List;

import cn.sh.ideal.model.SessionInfo;

public class RespGetSessionList extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5242239584076036454L;
	/**
	 * 
	 */
	private List<SessionInfo> data;
	public  RespGetSessionList(String resultCode, String resultMsg, List<SessionInfo> sessionList) {
		super(resultCode, resultMsg);
		this.data  = sessionList;
	}
	public List<SessionInfo> getData() {
		return data;
	}
	public void setData(List<SessionInfo> data) {
		this.data = data;
	}

	
}
