package cn.sh.ideal.sm.service;

import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.sm.resp.RespAddMessage;
import cn.sh.ideal.sm.resp.RespAddWorkNo;
import cn.sh.ideal.sm.resp.RespCreateInitiativeSession;
import cn.sh.ideal.sm.resp.RespCreateSession;
import cn.sh.ideal.sm.resp.RespGetFinishSessionMsgs;
import cn.sh.ideal.sm.resp.RespGetPendingTask;
import cn.sh.ideal.sm.resp.RespGetSession;
import cn.sh.ideal.sm.resp.RespGetSessionActiveFlag;
import cn.sh.ideal.sm.resp.RespGetSessionCount;
import cn.sh.ideal.sm.resp.RespGetSessionList;
import cn.sh.ideal.sm.resp.RespGetSessionSortCount;
import cn.sh.ideal.sm.resp.RespGetSessionStatus;
import cn.sh.ideal.sm.resp.RespQuitSessionSort;
import cn.sh.ideal.sm.resp.RespRemoveSessionCache;
import cn.sh.ideal.sm.resp.RespRemoveWorkNo;
import cn.sh.ideal.sm.resp.RespUpdateSession;
import cn.sh.ideal.sm.resp.RespUpdateSessionAgActive;
import cn.sh.ideal.sm.resp.RespUpdateSessionUserActive;

public interface SessionService {
	/**
	 * 创建会话
	 * @param sessionData
	 * @return
	 */
	public RespCreateSession createSession(SessionData sessionData); 
	/**
	 * 创建主动会话
	 * @param sessionData
	 * @return
	 */
	public RespCreateInitiativeSession createInitiativeSession(SessionData sessionData);
	/**
	 * 获取会话内容
	 */
	public RespGetSession getSession(SessionData sessionData);
	
	/**
	 * 获取会话可发送状态（网关点对点消息接口）
	 */
	public RespGetSessionActiveFlag  getSessionActiveFlag(SessionData sessionData);
	
	/**
	 * 分状态获取会话列表
	 */
	public RespGetSessionList getSessionList(SessionData sessionData);
	
	/**
	 * 获取会话各个状态的数量
	 */
	public RespGetSessionCount getSessionCount(SessionData sessionData);
	
	/**
	 * 获取会话排队接口
	 */
	public RespGetSessionSortCount getSessionSortCount(SessionData sessionData);
	
	/**
	 * 获取会话状态接口
	 */
	public RespGetSessionStatus  getSessionStatus(SessionData sessionData);
	
	/**
	 * 更新坐席最后一次会话活动时间
	 */
	public RespUpdateSessionAgActive updateSessionAgActive(SessionData sessionData);
	
	/**
	 * 更新用户最后一次会话活动时间
	 */
	public RespUpdateSessionUserActive updateSessionUserActive(SessionData sessionData);
	
	/**
	 * 向会话添加消息
	 */
	public RespAddMessage addMessage(SessionData sessionData);
	
	/**
	 * 向会话添加坐席工号
	 */
	public RespAddWorkNo addWorkNo(SessionData sessionData);
	
	/**
	 * 移除坐席
	 */
	public RespRemoveWorkNo removeWorkNo(SessionData sessionData);
	
	/**
	 * 更新会话
	 */
	public RespUpdateSession updateSession(SessionData sessionData);
	
	/**
	 * 根据sessionId 移出会话
	 */
	public RespRemoveSessionCache removeSessionCache(SessionData sessionData);
	
	/**
	 * 获取坐席待处理任务(人工状态会话)数
	 */
	public RespGetPendingTask getPendingTask(SessionData sessionData);
	
	/**
	 * 获取未完成会话消息
	 */
	public RespGetFinishSessionMsgs getUnfinishSessionMsgs(SessionData sessionData);
	
	/**
	 * 退出排队
	 * @param sessionData
	 * @return
	 */
	public RespQuitSessionSort quitSessionSort(SessionData sessionData);
	
	/**
	 * SM系统初始化
	 * @throws Exception
	 */
	public BaseResponse smInit() throws Exception;
	
	/**
	 * SM系统初始化
	 * @throws Exception
	 */
	public BaseResponse reloadSessionTip() throws Exception;
}
