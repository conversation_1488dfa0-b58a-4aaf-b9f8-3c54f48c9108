package cn.sh.ideal.sm.resp;

public class RespGetSessionActiveFlag extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer data;
	public RespGetSessionActiveFlag(String resultCode, String resultMsg,Integer integer) {
		super(resultCode, resultMsg);
		this.data = integer;
	}
	public Integer getData() {
		return data;
	}
	public void setData(Integer data) {
		this.data = data;
	}
	
	

}
