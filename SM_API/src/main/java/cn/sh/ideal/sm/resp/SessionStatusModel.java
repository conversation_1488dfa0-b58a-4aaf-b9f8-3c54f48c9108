package cn.sh.ideal.sm.resp;

import java.io.Serializable;

public class SessionStatusModel implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String sessionId;
	private String status;
	private String statusSymbol;
	
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getStatusSymbol() {
		return statusSymbol;
	}
	public void setStatusSymbol(String statusSymbol) {
		this.statusSymbol = statusSymbol;
	}
	
}
