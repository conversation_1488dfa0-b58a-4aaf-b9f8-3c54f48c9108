package cn.sh.ideal.sm.resp;

import cn.sh.ideal.model.SessionInfo;

public class RespCreateSession extends BaseRespone{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private SessionInfo data;
	public RespCreateSession(String resultCode, String resultMsg, SessionInfo sessionInfo) {
		super(resultCode, resultMsg);
		this.data = sessionInfo;
	}
	public SessionInfo getData() {
		return data;
	}
	public void setData(SessionInfo data) {
		this.data = data;
	}
	
}
