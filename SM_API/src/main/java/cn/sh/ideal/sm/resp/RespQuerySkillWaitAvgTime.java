package cn.sh.ideal.sm.resp;

import java.util.HashMap;
import java.util.List;

public class RespQuerySkillWaitAvgTime extends BaseRespone{
	/**
	 * 
	 */
	private static final long serialVersionUID = 2388056050675394251L;
	/**
	 * 
	 */
	private List<HashMap<String, Object>> data;
	public  RespQuerySkillWaitAvgTime(String resultCode, String resultMsg, List<HashMap<String, Object>> list) {
		super(resultCode, resultMsg);
		this.data = list;
	}
	public List<HashMap<String, Object>> getData() {
		return data;
	}
	public void setData(List<HashMap<String, Object>> data) {
		this.data = data;
	}
	
}
