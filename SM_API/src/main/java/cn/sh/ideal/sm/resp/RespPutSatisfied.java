package cn.sh.ideal.sm.resp;

public class RespPutSatisfied extends BaseRespone{
	/**
	 * 
	 */
	private static final long serialVersionUID = 5969440630336501482L;
	/**
	 * 
	 */
	private Object data;
	public RespPutSatisfied(String resultCode, String resultMsg, Object object) {
		super(resultCode, resultMsg);
		this.data = object;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
}
