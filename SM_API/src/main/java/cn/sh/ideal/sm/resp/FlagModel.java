package cn.sh.ideal.sm.resp;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class FlagModel implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer flag;
	private Integer count;
	private Integer time;
	private List<Map<String, Object>> skillQueueSorts;
	private List<SessionSortItem> items;
	
	public Integer getFlag() {
		return flag;
	}
	public void setFlag(Integer flag) {
		this.flag = flag;
	}
	public Integer getCount() {
		return count;
	}
	public void setCount(Integer count) {
		this.count = count;
	}
	public Integer getTime() {
		return time;
	}
	public void setTime(Integer time) {
		this.time = time;
	}
	public List<Map<String, Object>> getSkillQueueSorts() {
		return skillQueueSorts;
	}
	public void setSkillQueueSorts(List<Map<String, Object>> skillQueueSorts) {
		this.skillQueueSorts = skillQueueSorts;
	}
	public List<SessionSortItem> getItems() {
		return items;
	}
	public void setItems(List<SessionSortItem> items) {
		this.items = items;
	}
	
	
}
