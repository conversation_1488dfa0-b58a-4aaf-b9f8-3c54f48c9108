package cn.sh.ideal.sm.resp;

import java.util.Map;

public class RespUpdateSessionAgActive extends BaseRespone{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4922681164630938640L;
	/**
	 * 
	 */
	private Map<String, Object> data;
	
	public RespUpdateSessionAgActive(String resultCode, String resultMsg, Map<String, Object> data) {
		super(resultCode, resultMsg);	
		this.data = data;
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}

	
}
