/**
 * 
 */
package cn.sh.ideal.mgw.base;

import java.util.HashMap;
import java.util.Map;


public enum WeiboInf {
	WB_UPDATESTATUS("updateStatus","1"),//发布微博接口
	WB_CREATRCOMMENT("createComment","2"),//微博评论接口
	WB_REPLAYCOMMENT("replyComment","3"),//回复评论接口
	WB_DESTROY("destroy","4"),//删除微博接口
	WB_GETSTATUSCOUNT("getStatusesCount","5"),//微博评论数，转发数统计接口
	WB_GETUSERCOUNT("getUserCount","6"),//批量获取用户的粉丝数、关注数、微博数 
	WB_GETEMOTIONS("getEmotions","7"),//获取微博官方表情的详细信息
	
	WB_GETMENTIONS("getMentions","8"),//@我的微博
	WB_GETCOMMENTMENTIONS("getCommentMentions","9"),//@我的评论
	WB_GETUSERTIMELINEBYUID("getUserTimelineByUid","10"),//获取用户发布的微博列表
	WB_GET_COMMENT_LIST("getCommentList","11"),//根据微博id获取评论列表
	WB_GETREPLYCOMMENT("getReplyComment","12"),//回复
	WB_GETREPLYMSG("getReplyMsg","13"),//私信
	WB_CREATEMENU("createMenu","14"),//创建菜单
	WB_SHOWMENU("showMenu","15"),//查询菜单
	WB_DELMENU("delMenu","16"),//删除菜单
	WB_SENDALL("sendall","17"),//消息群发
	WB_USERINFO("userInfo","18"),//消息群发
	WB_GETCOMMENTSBYUSER("getCommentMentionsByUser","19"),//获取用户收到的评论列表
	WB_GETSTATUSREPORTTIMELINE("getStatusReportTimeLine","21");//获取微博的转发列表
	
	private String name;
	
	private String code;
	
	
	private WeiboInf(String name, String code) {
		this.name = name;
		this.code = code;
	}
	private static Map<String, WeiboInf> weiBoInfMap;
	static {
		weiBoInfMap = new HashMap<String, WeiboInf>();
		for(WeiboInf weiboInf : WeiboInf.values())
			weiBoInfMap.put(weiboInf.getCode(), weiboInf);
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	public static WeiboInf codeOf(String code) {
		return weiBoInfMap.get(code);
	}
}

