package cn.sh.ideal.mgw.base;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2014/08/21
 */
public enum Platform {
	SMS("SMS", "1001"),
	EMAIL("EMAIL", "1002"),
	WX("WX", "1003"), 
	YX("YX", "1004"),
	HAIRSMS("HAIRSMS", "2001"),
	NTALK("NTALK", "2002"), 
	SHWX("SHWX", "2003"), 
	SHAPP("SHAPP", "2005"),
	SINAMB("SINAMB", "1005"),
	ALIPAY("ALIPAY", "1010"),
	QY("QY", "1013"),
	WEBCHAT("WEBCHAT", "1012"),
	CSSMS("CSSMS", "1016"),
	TOOLBAR("TOOLBAR", "1009"),
	QQ("QQ","1019"),
	SINAWEIBO("SINAWEIBO","1020");
	
	private String name;
	
	private String code;
	
	private static Map<String, Platform> platformMap;
	static {
		platformMap = new HashMap<String, Platform>();
		for(Platform platform : Platform.values())
			platformMap.put(platform.getCode(), platform);
	}
	
	private Platform(String name, String code) {
		this.name = name;
		this.code = code;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	public static Platform codeOf(String code) {
		return platformMap.get(code);
	}
}
