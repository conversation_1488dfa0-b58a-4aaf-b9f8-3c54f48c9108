/**
 * 
 */
package cn.sh.ideal.mgw.model.req;


/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.req
 * @typeName WeiboRequestDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月4日 下午11:02:03
 * @version 
 */
public class <PERSON>boRequestDTO extends BaseRequestDTO{
	
	private static final long serialVersionUID = 4054490345795201387L;

	private String signature;

	private String timestamp;

	private String nonce;

	private String echostr;
	
	private String tenantCode;
	
	private String imitation;
	
	private String xmlContent;

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getNonce() {
		return nonce;
	}

	public void setNonce(String nonce) {
		this.nonce = nonce;
	}

	public String getEchostr() {
		return echostr;
	}

	public void setEchostr(String echostr) {
		this.echostr = echostr;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getImitation() {
		return imitation;
	}

	public void setImitation(String imitation) {
		this.imitation = imitation;
	}

	public String getXmlContent() {
		return xmlContent;
	}

	public void setXmlContent(String xmlContent) {
		this.xmlContent = xmlContent;
	}
	
	
	

}
