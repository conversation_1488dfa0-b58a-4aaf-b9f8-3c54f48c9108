package cn.sh.ideal.mgw.model.req;

import java.io.Serializable;

public class WeiboInfoRequestDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5756543825898787409L;

	//消息类型 文本:text 位置:position 语音:voice 图片:image
	private String type;
	
	//消息的接收者
	private String receiver_id;
	
	//消息的发送者
	private String sender_id;	
	
	//消息创建时间
	private String created_at;
	
	//私信内容
	private String text;	
	
	private String token;	
		
	private WeiboInfoData2 data;
	
	public WeiboInfoData2 getData() {
		return data;
	}

	public void setData(WeiboInfoData2 data) {
		this.data = data;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getReceiver_id() {
		return receiver_id;
	}

	public void setReceiver_id(String receiver_id) {
		this.receiver_id = receiver_id;
	}

	public String getSender_id() {
		return sender_id;
	}

	public void setSender_id(String sender_id) {
		this.sender_id = sender_id;
	}

	public String getCreated_at() {
		return created_at;
	}

	public void setCreated_at(String created_at) {
		this.created_at = created_at;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	
}
