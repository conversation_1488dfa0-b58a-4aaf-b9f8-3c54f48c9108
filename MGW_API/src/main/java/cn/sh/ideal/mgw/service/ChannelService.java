/**
 * 
 */
package cn.sh.ideal.mgw.service;

import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.response.BaseResponseDTO;
import cn.sh.ideal.mgw.model.response.ChannelResponseDTO;
import cn.sh.ideal.model.ChannelConfig;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server
 * @typeName ChannelService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月30日 下午5:17:22
 * @version 
 */
public interface ChannelService {
	/**
	 * @return 
	 * @Description 获取渠道信息
	 */
	public ChannelResponseDTO getChannelInfoByTenantCode(ChannelConfig channelConfig);
	
	/**
	 * @return 
	 * @Description	验证敏感词服务
	 */
	public ChannelResponseDTO isSensitive(SensitiveLog sensitiveLog);
	
	/**
	 * @return 
	 * @Description	同步渠道信息服务
	 */
	public BaseResponseDTO syncChannelConfig();
	
	
}
