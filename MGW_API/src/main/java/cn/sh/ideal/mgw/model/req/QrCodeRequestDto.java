/**
 * 
 */
package cn.sh.ideal.mgw.model.req;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.req
 * @typeName QrCodeRequestDto
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月31日 下午2:08:17
 * @version 
 */
public class QrCodeRequestDto extends BaseRequestDTO{
	
	private static final long serialVersionUID = -4919008716143772442L;
	
	private String channelCode;
	private String platformId;
	private String openid;
	private String actionName;
	private String userId;
	private String clientId;
	private String usageType;
	private String fromChannel;
	public String getChannelCode() {
		return channelCode;
	}
	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
	public String getPlatformId() {
		return platformId;
	}
	public void setPlatformId(String platformId) {
		this.platformId = platformId;
	}
	public String getOpenid() {
		return openid;
	}
	public void setOpenid(String openid) {
		this.openid = openid;
	}
	public String getActionName() {
		return actionName;
	}
	public void setActionName(String actionName) {
		this.actionName = actionName;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getUsageType() {
		return usageType;
	}
	public void setUsageType(String usageType) {
		this.usageType = usageType;
	}
	public String getFromChannel() {
		return fromChannel;
	}
	public void setFromChannel(String fromChannel) {
		this.fromChannel = fromChannel;
	}
	@Override
	public String toString() {
		return "QrCodeRequestDto [channelCode=" + channelCode + ", platformId="
				+ platformId + ", openid=" + openid + ", actionName="
				+ actionName + ", userId=" + userId + ", clientId=" + clientId
				+ ", usageType=" + usageType + ", fromChannel=" + fromChannel
				+ "]";
	}
	
	
}
