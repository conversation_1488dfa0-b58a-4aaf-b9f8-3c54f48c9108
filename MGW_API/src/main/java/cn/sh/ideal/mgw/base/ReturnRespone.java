package cn.sh.ideal.mgw.base;

public class ReturnRespone {

	private String touser;
	
	private String msgtype;
	
	private String funcflag;
	
	private ReturnType text;

	public String getTouser() {
		return touser;
	}

	public void setTouser(String touser) {
		this.touser = touser;
	}

	public String getMsgtype() {
		return msgtype;
	}

	public void setMsgtype(String msgtype) {
		this.msgtype = msgtype;
	}

	public String getFuncflag() {
		return funcflag;
	}

	public void setFuncflag(String funcflag) {
		this.funcflag = funcflag;
	}

	public ReturnType getText() {
		return text;
	}

	public void setText(ReturnType text) {
		this.text = text;
	}
}
