/**
 * 
 */
package cn.sh.ideal.mgw.service.channel;

import cn.sh.ideal.mgw.model.req.OpenWeixinRequestDTO;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName OpenweixinService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月29日 上午10:52:12
 * @version 
 */
public interface OpenweixinService {
	
	/**
	 * @return 
	 * @Description	
	 */
	public String launch(OpenWeixinRequestDTO openWeixinRequestDTO);
	
	
	/**
	 * @return 
	 * @Description	获取第三方平台token
	 */
	public void receive(OpenWeixinRequestDTO openWeixinRequestDTO);
	
	/**
	 * @return 
	 * @Description	
	 */
	public String messageReceive(OpenWeixinRequestDTO openWeixinRequestDTO);
	
	
}
