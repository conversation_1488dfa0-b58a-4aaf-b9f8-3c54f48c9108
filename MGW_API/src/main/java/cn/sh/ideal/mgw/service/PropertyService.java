/**
 * 
 */
package cn.sh.ideal.mgw.service;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.service
 * @typeName PropertyService
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月14日 上午11:25:44
 * @version 
 */
public interface PropertyService {
	/**
	 * @Description filePath example : conf/openweixin.properties
	 * 				fileName example : xx.xx
	 */
	public String getProperties(String filePath , String fileName);
}
