package cn.sh.ideal.mgw.service;

import java.util.List;

import cn.sh.ideal.model.WeiboComentsSum;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName MessageService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月28日 下午4:45:56
 * @version 
 */
public interface WeiboCommentsSumService {

	/**
	 * @return 
	 * @Description 点对点发送服务
	 */
	public List<WeiboComentsSum> weiboComentSumSend(WeiboComentsSum message);
	
}
