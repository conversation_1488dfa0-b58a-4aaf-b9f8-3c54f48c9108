package cn.sh.ideal.mgw.base;

public class Constants {
	
	/*************************  平台   ***************************/
	
	public static final String PLATFORM_WECHAT = "WX";
	
	public static final String PLATFORM_YICHAT = "YX";
	
	public static final String PLATFORM_WEIBO = "WEIBO";
	
	public static final String PLATFORM_ALIPAY = "ALIPAY";
	
	public static final String PLATFORM_QY = "QY";
	public static final String PLATFORM_SINAMB = "SINAMB";
	
	

	/*************************  接口入参出参名   ***************************/
	
	public static final String PARAM_PID = "pid";
	
	public static final String PARAM_APPID = "appId";
	
	public static final String PARAM_TENANT_CODE = "tenantCode";
	
	public static final String PARAM_WORK_NO = "workNo";
	
	public static final String PARAM_MESSAGE_ID = "messageId";
	
	public static final String PARAM_SESSION_ID = "sessionId";
	
	public static final String PARAM_TOOLBAR_ID = "toolbarId";
	
	public static final String PARAM_MSG = "msg";
	
	public static final String PARAM_RESULT_CODE = "resultCode";
	
	public static final String PARAM_RESULT_MSG = "resultMsg";
	
	public static final String PARAM_MSG_ID = "msgId";
	
	public static final String PARAM_SECRET = "secret";
	
	public static final String PARAM_MENU = "menu";
	
	public static final String PARAM_NAME = "name";
	
	public static final String PARAM_KEYWORD = "keyword";
	
	public static final String PARAM_FLOW = "flow";
	
	public static final String PARAM_MSG_TYPE = "msgType";
	
	public static final String PARAM_INTERFACE_TYPE = "interfaceType";
	
	public static final String PARAM_INTERFACE_URL = "interfaceUrl";
	
	public static final String PARAM_INTERFACE_METHOD = "interfaceMethod";
	
	public static final String PARAM_CONTENT = "content";
	
	public static final String PARAM_MENUS = "menus";
	
	public static final String PARAM_CHANNEL = "channel";
	
	public static final String PARAM_CHANNEL_TYPE = "channelType";
	
	public static final String PARAM_NICKNAME= "nickname";
	
	public static final String PARAM_MENU_ID = "menuId";
	
	public static final String PARAM_APP_SECRET = "appSecret";
	
	public static final String PARAM_ACCOUNT = "account";
	
	public static final String PARAM_PASSWORD = "password";
	
	public static final String PARAM_POP_SERVER = "popServer";
	
	public static final String PARAM_POP_PORT = "popPort";
	
	public static final String PARAM_IMAP_SERVER = "imapServer";
	
	public static final String PARAM_IMAP_PORT = "imapPort";
	
	public static final String PARAM_SMTP_SERVER = "smtpServer";
	
	public static final String PARAM_SMTP_PORT = "smtpPort";
	
	public static final String PARAM_PRIVATE_KEY = "privateKey";
	
	public static final String PARAM_PUBLIC_KEY = "publicKey";
	
	public static final String PARAM_OAUTH_URL = "oauthUrl";
	
	public static final String PARAM_OWNER = "owner";
	
	public static final String PARAM_APPKEY = "appkey";
	
	public static final String PARAM_TYPE = "type";
	
	public static final String PARAM_VERIFICATION = "verification";
	
	public static final String PARAM_PLATFORM_ID = "platformId";
	
	public static final String PARAM_OPENID = "openId";
	
	public static final String PARAM_MAXID = "maxid";
	
	public static final String PARAM_PARTICIPANT = "participant";
	
	public static final String PARAM_FROMUSER = "fromuser";
	
	public static final String PARAM_TOUSER = "touser";
	
	public static final String PARAM_SUBJECT = "subject";
	
	public static final String PARAM_ATTACHMENT = "attachment";
	
	public static final String PARAM_COUNT = "count";
	
	public static final String PARAM_SESSIONS = "sessions";
	
	public static final String ACTION_NAME = "actionName";
	
	public static final String PARAM_AGENT_ID = "agentId";
	
	public static final String CHANNEL_CODE="channelCode";
	
	
	public final static String SUCCESS_CODE = "0";
	
	public final static String ERROR_CODE = "-1";
	
	//redis key
	public static final String KEY_CHANNEL_INFO = "channelinfo_";
	
	//redis QQ key
	public static final String KEY_QQ_INFO = "qqinfo_list";
	
	public static final String KEY_OWN_CHANNEL_INFO = "own_channelinfo_";
	
	public static final String KEY_ADAPTER_MAP="adapter_maps";
	
	public static final String MGW_ADDRESS="MGW_ADDRESS";
	public static final String AG_ADDRESS="AG_ADDRESS";
	public static final String IMR_ADDRESS="IMR_ADDRESS";
	public static final String SM_ADDRESS="SM_ADDRESS";
	public static final String MIR_ADDRESS="MIR_ADDRESS";
	public static final String CMS_ADDRESS="CMS_ADDRESS";
	
	/** 启用自助渠道**/
	public static final String SELF_ENABLED ="1";
	/** 停用自助渠道**/
	public static final String SELF_DISABLE ="0";
	
	/**
	 * 电信短信返回状态
	 */
	public static final String Delivered_To_Network="DeliveredToNetwork";//短消息已成功递交。 
	public static final String Delivery_Uncertain="DeliveryUncertain";//递交状态未知";//例如，因为短消息被发送到另外一个网络。 
	public static final String Delivery_Impossible="DeliveryImpossible";//无法成功发送；短消息在超时前无法被递交。 
	public static final String Message_Waiting="MessageWaiting";//消息仍在排队等待递交。这是一个临时状态，悬置着，等待转换为前述的状态之一。 
	public static final String Delivered_To_Terminal="DeliveredToTerminal";//短消息已发给终端。 
	public static final String Delivery_Notification_NotSupported="DeliveryNotificationNotSupported";//不支持短消息提交通知。 
	
	/**
	 * 微博接口返回值
	 */
	public static final String WB_MSG_EXTCOUNT="最多不超过100个;";
	/**
	 * 微博接口调用返回值
	 */
	public static final String MSG_PARAM_CREATEED_AT ="created_at";
	
	/**
	 * 微博请求参数列表
	 */
	public static final String WB_PARAM_WBREQTYPE="wbreqtype";
	public static final String WB_PARAM_ACCESS_TOKEN = "access_token";
	public static final String WB_PARAM_COMMENT = "comment";
	public static final String WB_PARAM_ID = "id";
	public static final String WB_PARAM_CID = "cid";
	public static final String WB_PARAM_COMMENT_ORI = "comment_ori";
	public static final String WB_PARAM_IDS="ids";
	public static final String WB_PARAM_WITHOUTMENTION = "without_mention";
	public static final String WB_PARAM_STATUS="status";
	public static final String WB_PARAM_ANNOTATIONS = "annotations";
	public static final String WB_PARAM_LAT="lat";
	public static final String WB_PARAM_LONGS="longs";
	public static final String WB_PARAM_PIC="pic";
	public static final String WB_PARAM_UIDS="uids";
	public static final String WB_PARAM_TYPE="type";
	public static final String WB_PARAM_LANGUAGE="language";
	public static final String WB_RESULT="result";
	public static final String WB_MSG_ID="msg_id";
	public static final String WB_MSG_INFO="msg_info";
	public static final String WB_MSG_PARAM="param";
	public static final String WB_RES="res";
	public static final String MSG_PARAM_OPERTYPE ="operType";
	public static final String WB_PARAM_ISTIME="isTime";
	public static final String WB_PARAM_DATE = "date";
	
	
	public static final String WB_PARAM_VISIBLE="visible";
	public static final String WB_PARAM_LISTID="list_id";
	
	/**
	 * 
	 * 微博接口定义
	 */
	public static final String WB_GETMENTIONS="8";//@我的微博
	public static final String WB_GETCOMMENTMENTIONS="9";//@我的评论
	public static final String WB_GET_USER_TIMELINET="10";//获取用户发布的微博列表
	public static final String WB_GET_COMMENT_LIST="11";//根据微博id获取评论列表
	public static final String WB_GET_REPLYCOMMENT ="12";//回复
	public static final String WB_GET_COMMENT_LIST_BYUSER="19";//获取某个用户收到的评论列表
	public static final String WB_GET_USER_TIMELINESIDS ="20";//获取用户发布的微博ids
	public static final String WB_GET_USER_TIMELINESSTATUS ="21";//获取指定微博的转发微博列表
	
	
	
	/**
	 * 
	 * 微博推送参数
	 */
	
	public static final String WB_WID="wid";//微博ID
	public static final String WB_CONENT="content";//内容（微博内容或评论内容）
	public static final String WB_CREATEDATE="create_date";//创建时间
	public static final String WB_SENDDATE="send_date";//推送时间
	public static final String WB_PIC="pic";//图片
	public static final String WB_COMID="gateway_id";//微博评论ID
	public static final String WB_USERID="user_id";//用户ID
	public static final String WB_NIKENAME="user_nickname";//用户昵称
	public static final String WB_TYPE="weibo_type";//微博类型（1:@我的微博）2：评论 3:回复 4：私信

	
	public static final String IMG_HTTP_URL="IMG_HTTP_URL";
	public static final String FTP_PATH="FTP_PATH";
	public static final String SYS_PARAM_TRANSCODING_ADDRESS="TRANSCODING_ADDRESS";
	
	
	public static final String CUSTOMER_IDENTIFICATION ="CUSTOMER_IDENTIFICATION";
	
	public static final String WB_PARAM_RIP= "rip";
	
	//发布微博
	public static final String WB_RES_WEIBOID="res_weiboid";

	
	//评论微博
		public static final String WB_RES_COMID="res_comid";
		
		//回复评论
		public static final String WB_RES_REPLAYID="res_replyid";
	
		//批量获取用户的粉丝数、关注数、微博数
		public static final String WB_RES_USERCOUNT="res_usercount";
		
		//微博评论数，转发数统计接口
		public static final String WB_RES_STATUSCOUNT="res_statuscount";
		
		//public static final String WB_RESULT="res";
		public static final String WB_RES_ERROR="调用微博API失败";
}
