package cn.sh.ideal.mgw.model.req;

import java.io.Serializable;

/**
 * 微博授权MODEL
 * <AUTHOR>
 *
 */
public class WeiboAuthorize implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 8428640653743147706L;
	//租户号
	private String tenantCode;
	//微博申请应用的id
	private String client_id;
	//微博回调code
	private String code;
	//微博回调state
	private String state;
	
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getClient_id() {
		return client_id;
	}
	public void setClient_id(String client_id) {
		this.client_id = client_id;
	}
	
	public String toString(){
		return "tenantCode：" +  tenantCode + ",client_id: " + client_id;
		
	}
}
