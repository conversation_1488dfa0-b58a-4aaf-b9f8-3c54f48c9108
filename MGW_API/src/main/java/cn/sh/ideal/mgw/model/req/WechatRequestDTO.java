/**
 * 
 */
package cn.sh.ideal.mgw.model.req;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.req
 * @typeName WechatRequestDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月31日 下午5:25:44
 * @version 
 */
public class WechatRequestDTO extends BaseRequestDTO{
	
	private static final long serialVersionUID = -5539105135107750135L;
	
	private String signature;
	private String timestamp;
	private String nonce;
	private String imitation;
	private String token;
	private String tenantCode;
	private String appid;
	private String secret;
	private String xmlContent;
	
	public String getXmlContent() {
		return xmlContent;
	}
	public void setXmlContent(String xmlContent) {
		this.xmlContent = xmlContent;
	}
	public String getSignature() {
		return signature;
	}
	public void setSignature(String signature) {
		this.signature = signature;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	public String getNonce() {
		return nonce;
	}
	public void setNonce(String nonce) {
		this.nonce = nonce;
	}
	public String getImitation() {
		return imitation;
	}
	public void setImitation(String imitation) {
		this.imitation = imitation;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getSecret() {
		return secret;
	}
	public void setSecret(String secret) {
		this.secret = secret;
	}
	public WechatRequestDTO(String signature, String timestamp, String nonce,
			String imitation, String token, String tenantCode, String appid,
			String secret, String xmlContent) {
		super();
		this.signature = signature;
		this.timestamp = timestamp;
		this.nonce = nonce;
		this.imitation = imitation;
		this.token = token;
		this.tenantCode = tenantCode;
		this.appid = appid;
		this.secret = secret;
		this.xmlContent = xmlContent;
	}
	public WechatRequestDTO() {
		super();
	}
	
	
}
