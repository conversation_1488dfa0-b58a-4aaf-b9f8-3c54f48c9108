package cn.sh.ideal.mgw.model.req;

import java.io.Serializable;
import java.util.Date;

import cn.sh.ideal.model.MessageInfo;


public class SensitiveLog implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.ID
     *
     * @mbggenerated
     */
    private int id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.CHANNEL_CODE
     *
     * @mbggenerated
     */
    private String channelCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.MESSAGE_BUSINESS
     *
     * @mbggenerated
     */
    private String messageBusiness;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.TENANT_ID
     *
     * @mbggenerated
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.SEND_ACCOUT
     *
     * @mbggenerated
     */
    private String sendAccout;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.ACCEPTED_ACCOUNT
     *
     * @mbggenerated
     */
    private String acceptedAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.CREATE_TIME
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.SESSION_ID
     *
     * @mbggenerated
     */
    private String sessionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.STATUS
     *
     * @mbggenerated
     */
    private String status;
    
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.CONTENT
     *
     * @mbggenerated
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.SENSITIVE_ID
     *
     * @mbggenerated
     */
    private String sensitiveId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SENSITIVE_LOG.SENSITIVE_WORD
     *
     * @mbggenerated
     */
    private String sensitiveWord;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table MGW_SENSITIVE_LOG
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;
    
    
    
    
    

    public SensitiveLog() {
		super();
		// TODO Auto-generated constructor stub
	}
    
    

	public SensitiveLog(String channelCode,  String tenantCode, String sendAccout, String acceptedAccount,    String content) {
		super();
		this.channelCode = channelCode;
		this.tenantCode = tenantCode;
		this.sendAccout = sendAccout;
		this.acceptedAccount = acceptedAccount;
		this.content = content;
	}
	public SensitiveLog(MessageInfo message) {
		super();
		this.channelCode = message.getChannelCode();
		this.tenantCode = message.getTenantCode();
		this.sendAccout = message.getSendAccount();
		this.acceptedAccount = message.getAcceptedAccount();
		this.content = message.getContent();
	}



	/**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.ID
     *
     * @return the value of MGW_SENSITIVE_LOG.ID
     *
     * @mbggenerated
     */
    public int getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.ID
     *
     * @param id the value for MGW_SENSITIVE_LOG.ID
     *
     * @mbggenerated
     */
    public void setId(int id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.MESSAGE_CHANNEL
     *
     * @return the value of MGW_SENSITIVE_LOG.MESSAGE_CHANNEL
     *
     * @mbggenerated
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.MESSAGE_CHANNEL
     *
     * @param messageChannel the value for MGW_SENSITIVE_LOG.MESSAGE_CHANNEL
     *
     * @mbggenerated
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.MESSAGE_BUSINESS
     *
     * @return the value of MGW_SENSITIVE_LOG.MESSAGE_BUSINESS
     *
     * @mbggenerated
     */
    public String getMessageBusiness() {
        return messageBusiness;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.MESSAGE_BUSINESS
     *
     * @param messageBusiness the value for MGW_SENSITIVE_LOG.MESSAGE_BUSINESS
     *
     * @mbggenerated
     */
    public void setMessageBusiness(String messageBusiness) {
        this.messageBusiness = messageBusiness == null ? null : messageBusiness.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.TENANT_ID
     *
     * @return the value of MGW_SENSITIVE_LOG.TENANT_ID
     *
     * @mbggenerated
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.TENANT_ID
     *
     * @param tenantId the value for MGW_SENSITIVE_LOG.TENANT_ID
     *
     * @mbggenerated
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.SEND_ACCOUT
     *
     * @return the value of MGW_SENSITIVE_LOG.SEND_ACCOUT
     *
     * @mbggenerated
     */
    public String getSendAccout() {
        return sendAccout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.SEND_ACCOUT
     *
     * @param sendAccout the value for MGW_SENSITIVE_LOG.SEND_ACCOUT
     *
     * @mbggenerated
     */
    public void setSendAccout(String sendAccout) {
        this.sendAccout = sendAccout == null ? null : sendAccout.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.ACCEPTED_ACCOUNT
     *
     * @return the value of MGW_SENSITIVE_LOG.ACCEPTED_ACCOUNT
     *
     * @mbggenerated
     */
    public String getAcceptedAccount() {
        return acceptedAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.ACCEPTED_ACCOUNT
     *
     * @param acceptedAccount the value for MGW_SENSITIVE_LOG.ACCEPTED_ACCOUNT
     *
     * @mbggenerated
     */
    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount == null ? null : acceptedAccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.CREATE_TIME
     *
     * @return the value of MGW_SENSITIVE_LOG.CREATE_TIME
     *
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.CREATE_TIME
     *
     * @param createTime the value for MGW_SENSITIVE_LOG.CREATE_TIME
     *
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.SESSION_ID
     *
     * @return the value of MGW_SENSITIVE_LOG.SESSION_ID
     *
     * @mbggenerated
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.SESSION_ID
     *
     * @param sessionId the value for MGW_SENSITIVE_LOG.SESSION_ID
     *
     * @mbggenerated
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.STATUS
     *
     * @return the value of MGW_SENSITIVE_LOG.STATUS
     *
     * @mbggenerated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.STATUS
     *
     * @param status the value for MGW_SENSITIVE_LOG.STATUS
     *
     * @mbggenerated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.CONTENT
     *
     * @return the value of MGW_SENSITIVE_LOG.CONTENT
     *
     * @mbggenerated
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.CONTENT
     *
     * @param content the value for MGW_SENSITIVE_LOG.CONTENT
     *
     * @mbggenerated
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.SENSITIVE_ID
     *
     * @return the value of MGW_SENSITIVE_LOG.SENSITIVE_ID
     *
     * @mbggenerated
     */
    public String getSensitiveId() {
        return sensitiveId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.SENSITIVE_ID
     *
     * @param sensitiveId the value for MGW_SENSITIVE_LOG.SENSITIVE_ID
     *
     * @mbggenerated
     */
    public void setSensitiveId(String sensitiveId) {
        this.sensitiveId = sensitiveId == null ? null : sensitiveId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SENSITIVE_LOG.SENSITIVE_WORD
     *
     * @return the value of MGW_SENSITIVE_LOG.SENSITIVE_WORD
     *
     * @mbggenerated
     */
    public String getSensitiveWord() {
        return sensitiveWord;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SENSITIVE_LOG.SENSITIVE_WORD
     *
     * @param sensitiveWord the value for MGW_SENSITIVE_LOG.SENSITIVE_WORD
     *
     * @mbggenerated
     */
    public void setSensitiveWord(String sensitiveWord) {
        this.sensitiveWord = sensitiveWord == null ? null : sensitiveWord.trim();
    }
}