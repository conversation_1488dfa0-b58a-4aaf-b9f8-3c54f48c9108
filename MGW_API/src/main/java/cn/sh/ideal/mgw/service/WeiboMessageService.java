/**
 * 
 */
package cn.sh.ideal.mgw.service;


import java.io.UnsupportedEncodingException;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import cn.sh.ideal.mgw.model.req.WeiboChannelRequest;
import cn.sh.ideal.mgw.model.response.WeiboChannelResponse;
import cn.sh.ideal.mgw.model.response.WeiboSendResponse;
import cn.sh.ideal.model.MessageInfoSend;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName MessageService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月28日 下午4:45:56
 * @version 
 */
public interface WeiboMessageService {

	/** 
	 * @Description 点对点发送服务
	 */
	public WeiboSendResponse weibonewSend(MessageInfoSend message);
	
	
	/**
	 * 历史微博查询
	 * @param pageNum
	 * @param pageSize
	 * @param messageInfoSend
	 * @return
	 */
	public PageInfo historyInfo(int pageNum, int pageSize,MessageInfoSend messageInfoSend);

	/**
	 * 全网查询
	 * @param pageNum
	 * @param pageSize
	 * @param messageInfoSend
     * @return
     */
	public JSONObject searchLimited(int pageNum, int pageSize, MessageInfoSend messageInfoSend) throws UnsupportedEncodingException;
	/**
	 * 微博渠道查询接口
	 */
	
	public WeiboChannelResponse queryWeiboChannelConfig(WeiboChannelRequest wcr);
	
	/**
	 * 微博转发功能
	 * @param messageInfoSend
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public  JSONObject transpondWeibo(MessageInfoSend messageInfoSend) throws UnsupportedEncodingException;
}
