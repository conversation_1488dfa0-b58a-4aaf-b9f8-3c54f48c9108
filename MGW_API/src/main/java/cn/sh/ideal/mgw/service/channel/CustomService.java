/**
 *
 */
package cn.sh.ideal.mgw.service.channel;

import cn.sh.ideal.mgw.model.response.CustomResponseDTO;
import cn.sh.ideal.model.MessageInfo;


/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName CustomerServer
 * <AUTHOR>
 * @Description:  自有渠道
 * @date 2016年3月28日 下午1:54:22
 * @version
 */
public interface CustomService {

	/**
	 * @return
	 * @Description
	 */
	public CustomResponseDTO receiveCustomMessage(MessageInfo messageInfo);
}
