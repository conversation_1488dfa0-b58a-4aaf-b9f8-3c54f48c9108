package cn.sh.ideal.mgw.model.req;

import java.io.Serializable;

public class WeiboInfoData2 implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -4868205869067973580L;

	//经度
	private String longitude;
	
	//纬度
	private String latitude;
	
	//语音文件ID，发送者通过此ID读取语音
	private String vfid;	
	
	//语音文件ID，接收者通过此ID读取语音
	private String tovfid;
	
	//菜单点击事件类型
	private String subtype;
	
	//被点击的自定义菜单的key值
	private String key;

	public String getSubtype() {
		return subtype;
	}

	public void setSubtype(String subtype) {
		this.subtype = subtype;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getVfid() {
		return vfid;
	}

	public void setVfid(String vfid) {
		this.vfid = vfid;
	}

	public String getTovfid() {
		return tovfid;
	}

	public void setTovfid(String tovfid) {
		this.tovfid = tovfid;
	}
	
}
