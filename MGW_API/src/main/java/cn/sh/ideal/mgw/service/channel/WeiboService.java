/**
 * 
 */
package cn.sh.ideal.mgw.service.channel;

import cn.sh.ideal.mgw.model.req.WeiboInfoRequestDTO;
import cn.sh.ideal.mgw.model.response.WeiboResponseDTO;
import cn.sh.ideal.model.MessageInfo;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server.channel
 * @typeName WeiboService
 * <AUTHOR>
 * @Description:  微博接入
 * @date 2016年3月29日 下午5:15:54
 * @version 
 */
public interface WeiboService {
	
//	public String get(WeiboRequestDTO weiboRequestDTO);
	
//	public String post(WeiboRequestDTO weiboRequestDTO);
	
	public WeiboResponseDTO receiveWeiboMessage(WeiboInfoRequestDTO weiboInfoRequestDTO);
	
}