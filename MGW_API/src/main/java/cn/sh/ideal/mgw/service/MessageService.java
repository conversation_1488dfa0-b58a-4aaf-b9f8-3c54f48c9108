/**
 * 
 */
package cn.sh.ideal.mgw.service;

import cn.sh.ideal.mgw.model.response.BaseResponseDTO;
import cn.sh.ideal.model.MessageInfo;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName MessageService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月28日 下午4:45:56
 * @version 
 */
public interface MessageService {

	/**
	 * @return 
	 * @Description 点对点发送服务
	 */
	public BaseResponseDTO newSend(MessageInfo messageInfo);
	
	
	
}
