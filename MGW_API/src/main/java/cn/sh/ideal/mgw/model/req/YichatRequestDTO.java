/**
 * 
 */
package cn.sh.ideal.mgw.model.req;


/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.req
 * @typeName YichatRequestDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月4日 下午11:09:19
 * @version 
 */
public class YichatRequestDTO extends BaseRequestDTO{
	
	private static final long serialVersionUID = 8212770260973106510L;
	
	private String signature;
	private String timestamp;
	private String nonce;
	private String imitation;
	private String tenantChannelId;
	private String xmlContent;
	private String echostr;
	public String getSignature() {
		return signature;
	}
	public void setSignature(String signature) {
		this.signature = signature;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	public String getNonce() {
		return nonce;
	}
	public void setNonce(String nonce) {
		this.nonce = nonce;
	}
	public String getImitation() {
		return imitation;
	}
	public void setImitation(String imitation) {
		this.imitation = imitation;
	}
	public String getTenantChannelId() {
		return tenantChannelId;
	}
	public void setTenantChannelId(String tenantChannelId) {
		this.tenantChannelId = tenantChannelId;
	}
	public String getXmlContent() {
		return xmlContent;
	}
	public void setXmlContent(String xmlContent) {
		this.xmlContent = xmlContent;
	}
	public String getEchostr() {
		return echostr;
	}
	public void setEchostr(String echostr) {
		this.echostr = echostr;
	}
	
	
}
