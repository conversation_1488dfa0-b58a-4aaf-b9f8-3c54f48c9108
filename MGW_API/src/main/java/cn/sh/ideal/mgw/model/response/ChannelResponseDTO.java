/**
 * 
 */
package cn.sh.ideal.mgw.model.response;

import java.util.List;
import java.util.Map;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.response
 * @typeName ChannelResponseDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月5日 上午11:30:30
 * @version 
 */
public class ChannelResponseDTO extends BaseResponseDTO{

	private static final long serialVersionUID = -1790673675235225179L;
	
	private List<Map<String, String>> data;
	
	private Boolean isSensitive; 

	public List<Map<String, String>> getData() {
		return data;
	}

	public void setData(List<Map<String, String>> data) {
		this.data = data;
	}

	public ChannelResponseDTO(String resultCode, Object resultMsg,
			List<Map<String, String>> data) {
		super(resultCode, resultMsg);
		this.data = data;
	}

	public ChannelResponseDTO(String resultCode, Object resultMsg) {
		super(resultCode, resultMsg);
	}

	public Boolean getIsSensitive() {
		return isSensitive;
	}

	public void setIsSensitive(Boolean isSensitive) {
		this.isSensitive = isSensitive;
	}
	
	
	
}
