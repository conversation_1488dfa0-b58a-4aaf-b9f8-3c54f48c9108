/**
 * 
 */
package cn.sh.ideal.mgw.model.response;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.response
 * @typeName QrCodeResponseDto
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月31日 下午2:10:46
 * @version 
 */
public class QrCodeResponseDTO extends BaseResponseDTO{
	
	private String qrcodeImgUrl;

	public String getQrcodeImgUrl() {
		return qrcodeImgUrl;
	}

	public void setQrcodeImgUrl(String qrcodeImgUrl) {
		this.qrcodeImgUrl = qrcodeImgUrl;
	}

	public QrCodeResponseDTO(String resultCode, Object resultMsg,
			String qrcodeImgUrl) {
		super(resultCode, resultMsg);
		this.qrcodeImgUrl = qrcodeImgUrl;
	}

	public QrCodeResponseDTO() {
		super();
	}
	
	

}
