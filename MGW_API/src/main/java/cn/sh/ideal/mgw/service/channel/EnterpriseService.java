/**
 * 
 */
package cn.sh.ideal.mgw.service.channel;

import cn.sh.ideal.mgw.model.req.EnterpriseRequestDTO;
import cn.sh.ideal.mgw.model.response.EnterpriseResponseDTO;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName EnterpriseService
 * <AUTHOR>
 * @Description: 企业号相关服务
 * @date 2016年3月29日 上午10:50:10
 * @version 
 */
public interface EnterpriseService {
	
	/**
	 * @return 
	 * @Description	 企业号接入GET方式(校验URL时调用)
	 */
	public EnterpriseResponseDTO get(EnterpriseRequestDTO enterpriseRequestDTO);
	
	/**
	 * @return 
	 * @Description	企业号接入POST方式
	 */
	public EnterpriseResponseDTO post(EnterpriseRequestDTO enterpriseRequestDTO);
	
}
