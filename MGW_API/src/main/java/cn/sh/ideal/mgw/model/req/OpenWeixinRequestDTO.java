/**
 * 
 */
package cn.sh.ideal.mgw.model.req;

import java.io.Serializable;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.req
 * @typeName OpenweixinrequestDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月4日 下午10:52:25
 * @version 
 */
public class OpenWeixinRequestDTO extends BaseRequestDTO{

	private static final long serialVersionUID = -3288054298158344185L;
	
	private String authCode;
	private String tenantCode;
	private String timestamp;
	private String msgSignature;
	private String nonce;
	private String xmlContent;
	
	
	public String getAuthCode() {
		return authCode;
	}
	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	public String getMsgSignature() {
		return msgSignature;
	}
	public void setMsgSignature(String msgSignature) {
		this.msgSignature = msgSignature;
	}
	public String getNonce() {
		return nonce;
	}
	public void setNonce(String nonce) {
		this.nonce = nonce;
	}
	public String getXmlContent() {
		return xmlContent;
	}
	public void setXmlContent(String xmlContent) {
		this.xmlContent = xmlContent;
	}
	@Override
	public String toString() {
		return "OpenWeixinRequestDTO [authCode=" + authCode + ", tenantCode="
				+ tenantCode + ", timestamp=" + timestamp + ", msgSignature="
				+ msgSignature + ", nonce=" + nonce + ", xmlContent="
				+ xmlContent + "]";
	}
	
	
	
}
