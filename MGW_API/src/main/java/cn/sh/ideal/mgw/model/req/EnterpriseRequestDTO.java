/**
 * 
 */
package cn.sh.ideal.mgw.model.req;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.req
 * @typeName EnterpriseRequestDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月4日 下午10:19:06
 * @version 
 */
public class EnterpriseRequestDTO extends BaseRequestDTO{
	
	private static final long serialVersionUID = 6723787129025491930L;
	
	private String timestamp;
	private String msgSignature;
	private String nonce;
	private String echostr;
	private String corpId;
	private String xmlContent;
	
	
	
	public String getXmlContent() {
		return xmlContent;
	}
	public void setXmlContent(String xmlContent) {
		this.xmlContent = xmlContent;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	public String getMsgSignature() {
		return msgSignature;
	}
	public void setMsgSignature(String msgSignature) {
		this.msgSignature = msgSignature;
	}
	public String getNonce() {
		return nonce;
	}
	public void setNonce(String nonce) {
		this.nonce = nonce;
	}
	public String getEchostr() {
		return echostr;
	}
	public void setEchostr(String echostr) {
		this.echostr = echostr;
	}
	public String getCorpId() {
		return corpId;
	}
	public void setCorpId(String corpId) {
		this.corpId = corpId;
	}
	
	
}
