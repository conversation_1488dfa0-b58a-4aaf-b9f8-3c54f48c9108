/**
 * 
 */
package cn.sh.ideal.mgw.service.channel;

import cn.sh.ideal.mgw.model.req.AlipayRequestDTO;
import cn.sh.ideal.mgw.model.response.AlipayResponseDTO;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.server
 * @typeName ChannelService
 * <AUTHOR>
 * @Description:  支付宝渠道服务类
 * @date 2016年3月29日 上午10:16:27
 * @version 
 */
public interface AlipayService {
	
	/**
	 * @Description	
	 */
	public AlipayResponseDTO post(AlipayRequestDTO alipayRequestDTO);
	
}
