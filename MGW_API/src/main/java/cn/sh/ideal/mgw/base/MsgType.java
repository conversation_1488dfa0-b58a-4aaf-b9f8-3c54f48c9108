package cn.sh.ideal.mgw.base;

public enum MsgType {
	TEXT("text", 1),
	VOICE("voice", 2),
	IMAGE("image", 3),
	MPNEWS("mpnews", 3),
	NEWS("news", 3),
	MPVIDEO("mpvideo", 4),
	VIDEO("video", 4),
	MUSIC("music", 5),
	ARTICLES("articles", 6),
	POSITION("position",7),
	TEMPLATE("template", 8);
	private String name;
	
	private int index;
	
	private MsgType(String name, int index) {
		this.name = name;
		this.index = index;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @return the index
	 */
	public int getIndex() {
		return index;
	}
	
}
