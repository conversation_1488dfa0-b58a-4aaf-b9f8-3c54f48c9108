/**
 * 
 */
package cn.sh.ideal.mgw.service;

import cn.sh.ideal.mgw.model.req.QrCodeRequestDto;
import cn.sh.ideal.mgw.model.response.QrCodeResponseDTO;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server
 * @typeName FilesService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月30日 下午5:21:42
 * @version 
 */
public interface FilesService {

	/**
	 * @Description	
	 */
//	public String getQrcodeImage(Platform platform, String ticket);
	
	
	public QrCodeResponseDTO getQrCode(QrCodeRequestDto qrCodeRequestDto);
}
