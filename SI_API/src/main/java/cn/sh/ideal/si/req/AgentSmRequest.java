package cn.sh.ideal.si.req;

import java.util.Date;

import cn.sh.ideal.model.BaseRequest;

/**
 * dubbo agentSM服务（向坐席推送私聊消息）请求参数
 * <AUTHOR>
 * @Date 2016/03/25
 * @Version v1.0
 */


public class AgentSmRequest extends BaseRequest {

	private static final long serialVersionUID = 1L;

	private int id;
	
	private String uuid;

	private String tenantCode;

	/**
	 * 可以是工号 名称 登录名
	 */
	private String name;	

	/**
	 * 发送消息的工号
	 */
	private String workNo;	
	
	/**
	 * 接收消息的工号,这里只能是同一租户下的工号
	 */
	private String accept;	

	private Date createTime;

	private String type;	

	private String content;	

	private Object agent;
	
	public String cids(){
		return new StringBuffer(this.accept).append("@@").append(this.tenantCode).toString();
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getAccept() {
		return accept;
	}

	public void setAccept(String accept) {
		this.accept = accept;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Object getAgent() {
		return agent;
	}

	public void setAgent(Object agent) {
		this.agent = agent;
	}

}
