package cn.sh.ideal.si.req;

import java.util.List;

import cn.sh.ideal.model.BaseRequest;
import cn.sh.ideal.model.MessageInfo;

/**
 * dubbo pushMessageInfos服务（向坐席推送会话消息）请求参数
 * <AUTHOR>
 * @Date 2016/03/25
 * @Version v1.0
 */


public class PushMessageInfoRequest extends BaseRequest {

	private static final long serialVersionUID = 1L;
	
	private List<MessageInfo> messageInfos;

	public List<MessageInfo> getMessageInfos() {
		return messageInfos;
	}

	public void setMessageInfos(List<MessageInfo> messageInfos) {
		this.messageInfos = messageInfos;
	}
	
}
