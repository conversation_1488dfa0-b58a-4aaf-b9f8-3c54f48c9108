package cn.sh.ideal.si.service;

import java.util.List;

import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.si.req.AgentSmRequest;
import cn.sh.ideal.si.req.ChangeStatusRequest;
import cn.sh.ideal.si.req.PushMessageInfoRequest;
import cn.sh.ideal.si.req.ScanLoginRequest;
import cn.sh.ideal.si.req.StipRequest;

/**
 * dubbo 消息推送服务
 * <AUTHOR>
 * @Date 2016/03/25
 * @Version v1.0
 */

public interface MessageService {
	
	/**
	 * 普通消息推送
	 * @param request 兼容旧数组格式
	 * @return
	 */
	public BaseResponse pushMessageInfos(List<MessageInfo> request) ;

	/**
	 * 普通消息推送
	 * @param request
	 * @return
	 */
	public BaseResponse pushMessageInfosNew(PushMessageInfoRequest request) ;
	
	/**
	 * 坐席私聊消息推送
	 * @param request
	 * @return
	 */
	public BaseResponse agentSM(AgentSmRequest request) ;
	
	/**
	 * 通知坐席事件
	 * @param request
	 * @return
	 */
	public BaseResponse stip(StipRequest request) ;
	
	/**
	 * 坐席状态改变事件通知
	 * @param request
	 * @return
	 */
	public BaseResponse changeStatus(ChangeStatusRequest request) ;
	
	/**
	 * 坐席扫描登录通知
	 * @param request
	 * @return
	 */
	public BaseResponse scanLogin(ScanLoginRequest request) ;

}
