package cn.sh.ideal.si.req;

import cn.sh.ideal.model.BaseRequest;
import cn.sh.ideal.model.SessionInfo;

/**
 * dubbo skip服务（向坐席推送消息提醒）请求参数
 * <AUTHOR>
 * @Date 2016/03/25
 * @Version v1.0
 */


public class StipRequest extends BaseRequest {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 坐席工号
	 */
	private String workNo;		
	
	/**
	 * 提示类型(可以自定义)
	 * 已经定义好的提示类型：
	 * TIME_OUT,//超时
	 * TIME_OUT_RELAY,//超时转发
	 * TIME_OUT_CLOSE,//超时关闭
	 * CUSTOMER_QUIT,//客户关闭
	 * AGENT_TIMEOUT,//坐席超时
	 */
	private String type;		
	
	/**
	 * 租户CODE
	 */
	private String tenantCode;	
	
	/**
	 * 会话ID
	 */
	private String sessionId;	
	
	/**
	 * 提示内容
	 */
	private String content;		
	
	/**
	 * 可选描述
	 */
	private String descr;	
	
	private String lastActiveUser;
	private String sendType;
	/**
	 * 扩展参数
	 */
	private Object extData;
	
	
	private String timeOutAction;
	/**
	 * 会话
	 */
	private SessionInfo session;
	private String channelCode;

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	public Object getExtData() {
		return extData;
	}

	public void setExtData(Object extData) {
		this.extData = extData;
	}

	public SessionInfo getSession() {
		return session;
	}

	public void setSession(SessionInfo session) {
		this.session = session;
	}
	
	public String getLastActiveUser() {
		return lastActiveUser;
	}

	public void setLastActiveUser(String lastActiveUser) {
		this.lastActiveUser = lastActiveUser;
	}
	
	
	
	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}
	
	

	public String getTimeOutAction() {
		return timeOutAction;
	}

	public void setTimeOutAction(String timeOutAction) {
		this.timeOutAction = timeOutAction;
	}

	public String toString(){
		return "[channelCode:"+this.channelCode+",content:"+this.content+",descr:"+this.descr+",lastActiveUser:"+this.lastActiveUser+",sessionId:"+this.sessionId+",tenantCode:"+this.tenantCode+",workNo:"+this.workNo+"]";
	}

}
