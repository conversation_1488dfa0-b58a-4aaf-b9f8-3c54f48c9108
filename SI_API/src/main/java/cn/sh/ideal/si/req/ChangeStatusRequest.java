package cn.sh.ideal.si.req;

import cn.sh.ideal.model.BaseRequest;

/**
 * dubbo changeStatus服务（向坐席推送消息提醒）请求参数
 * <AUTHOR>
 * @Date 2016/03/25
 * @Version v1.0
 */

public class ChangeStatusRequest extends BaseRequest {

	private static final long serialVersionUID = 1L;

	/**
	 * 坐席工号
	 */
	private String workNo ;
	
	/**
	 * 租户CODE
	 */
	private String tenantCode ;
	
	/**
	 * 状态 ：1(示闲) 2(示忙) 4(签入) 8(签出) 16(退出)
	 */
	private String status ;

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
}
