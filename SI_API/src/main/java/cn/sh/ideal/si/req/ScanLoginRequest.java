package cn.sh.ideal.si.req;

import cn.sh.ideal.model.BaseRequest;

/**
 * dubbo scanLogin服务（扫描二维码切换用户登录）请求参数
 * <AUTHOR>
 * @Date 2016/03/25
 * @Version v1.0
 */


public class ScanLoginRequest extends BaseRequest {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 坐席工号（type=1时必填）
	 */
	private String workNo ;	
	
	/**
	 * 需要切到的坐席端的Websocket通道ID
	 */
	private String websocketSessionIdNew ;
	
	/**
	 * 被切换的坐席端Websocket通道ID
	 */
	private String websocketSessionIdOld ;	
	
	/**
	 * 客户端类型：
	 * 0-普通（默认）
	 * 10-移动客户端(iPhone)
	 * 10-移动客户端(iPad)
	 * 20-移动客户端(Android Mobile)
	 * 21-移动客户端(Android Pad)
	 */
	private String clientType ;
	
	/**
	 * 需要切到的坐席端的Websocket通道IP(ServiceInvoker为集群模式时需要)
	 */
	private String websocketIPNew ;	
	
	/**
	 * 是否需要验证码验证（1：需要验证码，0：不需要验证码，默认为0）
	 */
	private String type ;
	
	/**
	 * 验证码
	 */
	private String verifyCode ;				
	
	/**
	 * 租户编号（type=1时必填）
	 */
	private String tenantCode ;

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getWebsocketSessionIdNew() {
		return websocketSessionIdNew;
	}

	public void setWebsocketSessionIdNew(String websocketSessionIdNew) {
		this.websocketSessionIdNew = websocketSessionIdNew;
	}

	public String getWebsocketSessionIdOld() {
		return websocketSessionIdOld;
	}

	public void setWebsocketSessionIdOld(String websocketSessionIdOld) {
		this.websocketSessionIdOld = websocketSessionIdOld;
	}

	public String getClientType() {
		return clientType;
	}

	public void setClientType(String clientType) {
		this.clientType = clientType;
	}

	public String getWebsocketIPNew() {
		return websocketIPNew;
	}

	public void setWebsocketIPNew(String websocketIPNew) {
		this.websocketIPNew = websocketIPNew;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getVerifyCode() {
		return verifyCode;
	}

	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}				
}
