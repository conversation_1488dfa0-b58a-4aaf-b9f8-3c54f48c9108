package cn.sh.ideal.imr.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * 请求封装体
 * <AUTHOR>
 * @since 2014/9/29
 */
public class PlatformMessage implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String id;
	@XStreamAlias("MsgId")
	private String msgId;								//消息ID
	@XStreamAlias("ToUserName")
	private String toUser;								//平台OPENID
	@XStreamAlias("FromUserName")
	private String openId;								//用户OPENID
	@XStreamAlias("MsgType")
	private String msgType;							//消息类型,event,text,link,location...
	@XStreamAlias("Content")
	private String content;								//用户输入内容		
	@XStreamAlias("CreateTime")
	private long createTime;
	@XStreamAlias("EventKey")
	private String eventId;
	@XStreamAlias("Event")
	private String eventType;
	
	@XStreamAlias("PicUrl")
	private String picUrl;			//图片地址
	@XStreamAlias("Location_X")
	private String x;
	@XStreamAlias("Location_Y")
	private String y;
	@XStreamAlias("Label")
	private String label;
	@XStreamAlias("UserInfo")
	private String userInfo;
	@XStreamAlias("Source")
	private String source;			// 1=坐席发起
	@XStreamAlias("LimitOprTime")
	private String limitOprTime;	// 限制操作时间
	@XStreamAlias("LimitErrorCount")
	private String limitErrorCount;	// 限制操作次数
	
	private String msg;
	
	private String customerId;
	
	private String callbackUrl;
	
	private Map<String, String[]> parameters;	//扩展入参，通过GET方式传入
	
    private Date receiveTime;
    
    private String followData;
	
	
	public static enum MsgType {
		text, image, voice, video, location, link, event, position
	}
	
	
	
	
	public String getFollowData() {
		return followData;
	}
	public void setFollowData(String followData) {
		this.followData = followData;
	}
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
	/**
	 * @return the toUser
	 */
	public String getToUser() {
		return toUser;
	}
	/**
	 * @param toUser the toUser to set
	 */
	public void setToUser(String toUser) {
		this.toUser = toUser;
	}
	/**
	 * @return the openId
	 */
	public String getOpenId() {
		return openId;
	}
	/**
	 * @param openId the openId to set
	 */
	public void setOpenId(String openId) {
		this.openId = openId;
	}
	/**
	 * @return the content
	 */
	public String getContent() {
		return content;
	}
	/**
	 * @param content the content to set
	 */
	public void setContent(String content) {
		this.content = content;
	}
	/**
	 * @return the msgId
	 */
	public String getMsgId() {
		return msgId;
	}
	/**
	 * @param msgId the msgId to set
	 */
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}
	/**
	 * @return the eventId
	 */
	public String getEventId() {
		return eventId;
	}
	/**
	 * @param eventId the eventId to set
	 */
	public void setEventId(String eventId) {
		this.eventId = eventId;
	}
	/**
	 * @return the x
	 */
	public String getX() {
		return x;
	}
	/**
	 * @param x the x to set
	 */
	public void setX(String x) {
		this.x = x;
	}
	/**
	 * @return the y
	 */
	public String getY() {
		return y;
	}
	/**
	 * @param y the y to set
	 */
	public void setY(String y) {
		this.y = y;
	}
	/**
	 * @return the label
	 */
	public String getLabel() {
		return label;
	}
	/**
	 * @param label the label to set
	 */
	public void setLabel(String label) {
		this.label = label;
	}
	/**
	 * @return the eventType
	 */
	public String getEventType() {
		return eventType;
	}
	/**
	 * @param eventType the eventType to set
	 */
	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
	/**
	 * @return the userInfo
	 */
	public String getUserInfo() {
		return userInfo;
	}
	/**
	 * @param userInfo the userInfo to set
	 */
	public void setUserInfo(String userInfo) {
		this.userInfo = userInfo;
	}
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	/**
	 * @return the limitOprTime
	 */
	public String getLimitOprTime() {
		return limitOprTime;
	}
	/**
	 * @param limitOprTime the limitOprTime to set
	 */
	public void setLimitOprTime(String limitOprTime) {
		this.limitOprTime = limitOprTime;
	}
	/**
	 * @return the limitErrorCount
	 */
	public String getLimitErrorCount() {
		return limitErrorCount;
	}
	/**
	 * @param limitErrorCount the limitErrorCount to set
	 */
	public void setLimitErrorCount(String limitErrorCount) {
		this.limitErrorCount = limitErrorCount;
	}
	/**
	 * @return the parameters
	 */
	public Map<String, String[]> getParameters() {
		return parameters;
	}
	/**
	 * @param parameters the parameters to set
	 */
	public void setParameters(Map<String, String[]> parameters) {
		this.parameters = parameters;
	}
	
	/**
	 * @return the msgType
	 */
	public String getMsgType() {
		return msgType;
	}
	
	/**
	 * @param msgType the msgType to set
	 */
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	/**
	 * @return the createTime
	 */
	public long getCreateTime() {
		return createTime;
	}
	/**
	 * @param createTime the createTime to set
	 */
	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}
	
	/**
	 * @return the picUrl
	 */
	public String getPicUrl() {
		return picUrl;
	}
	/**
	 * @param picUrl the picUrl to set
	 */
	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}
	
	
	public String getCustomerId() {
		return customerId;
	}
	
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	
	
	/**
	 * @return the msg
	 */
	public String getMsg() {
		return msg;
	}
	/**
	 * @param msg the msg to set
	 */
	public void setMsg(String msg) {
		this.msg = msg;
	}
	
	/**
	 * @return the callbackUrl
	 */
	public String getCallbackUrl() {
		return callbackUrl;
	}
	/**
	 * @param callbackUrl the callbackUrl to set
	 */
	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}
	
	/**
	 * @return the receiveTime
	 */
	public Date getReceiveTime() {
		return receiveTime;
	}
	/**
	 * @param receiveTime the receiveTime to set
	 */
	public void setReceiveTime(Date receiveTime) {
		this.receiveTime = receiveTime;
	}
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "PlatformMessage [msgId=" + msgId + ", toUser=" + toUser
				+ ", openId=" + openId + ", msgType=" + msgType + ", content="
				+ content + ", createTime=" + createTime + ", eventId="
				+ eventId + ", eventType=" + eventType + ", picUrl=" + picUrl
				+ ", x=" + x + ", y=" + y + ", label=" + label + ", userInfo="
				+ userInfo + ", source=" + source + ", limitOprTime="
				+ limitOprTime + ", limitErrorCount=" + limitErrorCount
				+ ", customerId=" + customerId + ", parameters=" + parameters
				+ "]";
	}
	
	
	
}
