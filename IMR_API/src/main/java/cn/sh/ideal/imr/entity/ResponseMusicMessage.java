package cn.sh.ideal.imr.entity;


/**
 * 音乐消息
 * 
 */
public class ResponseMusicMessage extends ResponseMessage {
	private static final long serialVersionUID = 1L;
	
	// 音乐
	private Music Music;

	public Music getMusic() {
		return Music;
	}

	public void setMusic(Music music) {
		Music = music;
	}

	@Override
	public String toString() {
		return "MusicMessage [Music=" + Music + ", toString()="
				+ super.toString() + "]";
	}

	
	
}