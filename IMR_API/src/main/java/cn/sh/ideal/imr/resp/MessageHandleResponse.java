/**
 * 
 */
package cn.sh.ideal.imr.resp;

import java.io.Serializable;

import cn.sh.ideal.imr.entity.ResponseMessage;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class MessageHandleResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public MessageHandleResponse() {
		super();
	}

	public MessageHandleResponse(ResponseMessage responseMessage) {
		super();
		this.responseMessage = responseMessage;
	}

	private ResponseMessage responseMessage;

	public ResponseMessage getResponseMessage() {
		return responseMessage;
	}

	public void setResponseMessage(ResponseMessage responseMessage) {
		this.responseMessage = responseMessage;
	}
	
	
	
}
