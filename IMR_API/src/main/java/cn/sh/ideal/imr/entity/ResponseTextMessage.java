package cn.sh.ideal.imr.entity;

import cn.sh.ideal.imr.base.Constants;

/**
 * 文本消息
 * 
 */
public class ResponseTextMessage extends ResponseMessage {
	private static final long serialVersionUID = 1L;
	
	// 回复的消息内容
	private String Content;
	private String sendType;

	public String getContent() {
		return Content;
	}

	public void setContent(String content) {
		Content = content;
	}
	

	public ResponseTextMessage() {
	}
	

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}

	public ResponseTextMessage(String content) {
		super();
		super.setMsgType(Constants.RESP_MESSAGE_TYPE_TEXT);
		Content = content;
	}
	
	public ResponseTextMessage(String content,String sendType) {
		super();
		super.setMsgType(Constants.RESP_MESSAGE_TYPE_TEXT);
		Content = content;
		this.sendType=sendType;
	}

	@Override
	public String toString() {
		return "TextMessage [Content=" + Content + ",sendType=" + sendType + ", toString()="
				+ super.toString() + "]";
	}
	
	
	
}