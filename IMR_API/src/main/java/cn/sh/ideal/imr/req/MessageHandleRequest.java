package cn.sh.ideal.imr.req;

import java.io.Serializable;

import cn.sh.ideal.imr.entity.PlatformMessage;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class MessageHandleRequest implements Serializable{
	
	/**
	 * version
	 */
	private static final long serialVersionUID = 1L;
	
	private PlatformMessage platformMessage;
	
	public MessageHandleRequest () {};
	
	public MessageHandleRequest(PlatformMessage message) {
		this.platformMessage = message;
	}

	public PlatformMessage getPlatformMessage() {
		return platformMessage;
	}

	public void setPlatformMessage(PlatformMessage platformMessage) {
		this.platformMessage = platformMessage;
	}
	
	
}
