<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>multi_media</groupId>
		<artifactId>multi_media</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<artifactId>MGW</artifactId>
	<name>MGW</name>
	<description>MGW的服务提供类</description>

	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<env>dev</env>
				<warName>MGW</warName>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env>test</env>
				<warName>MGW</warName>
			</properties>
		</profile>
		<profile>
			<id>demo</id>
			<properties>
				<env>demo</env>
				<warName>MGW</warName>
			</properties>
		</profile>
		<profile>
			<id>test_mysql</id>
			<properties>
				<env>test_mysql</env>
				<warName>MGW_MYSQL</warName>
			</properties>
		</profile>
		<profile>
			<id>mysql</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<db>mysql</db>
			</properties>
		</profile>
		<profile>
			<id>oracle</id>
			<properties>
				<db>oracle</db>
			</properties>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>MIR_API</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>IMR_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>MGW_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>CM</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>${javax-servlet.version}</version>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>SM_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>Transcoding_API</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>
		<dependency>
			<groupId>oracle.jdbc</groupId>
			<artifactId>ojdbc</artifactId>
		</dependency>
		<dependency>
		  <groupId>com.alibaba</groupId>
		  <artifactId>fastjson</artifactId>
		  <version>1.2.28</version>
		</dependency>
		<dependency>
		  <groupId>org.nutz</groupId>
		  <artifactId>nutz</artifactId>
		  <version>1.b.50</version>
		</dependency>
		<dependency>
		  <groupId>org.apache.ibatis</groupId>
		  <artifactId>ibatis-core</artifactId>
		  <version>3.0</version>
		</dependency>
		
		<dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-lang3</artifactId>
		  <version>3.3.2</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.5.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.1</version>
		</dependency>
		<dependency>
			<groupId>axis</groupId>
			<artifactId>axis-saaj</artifactId>
			<version>1.4</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>axis</groupId>
			<artifactId>axis-wsdl4j</artifactId>
			<version>1.5.1</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>axis</groupId>
			<artifactId>axis-jaxrpc</artifactId>
			<version>1.4</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.htmlparser</groupId>
			<artifactId>htmlparser</artifactId>
			<version>2.1</version>
		</dependency>
		<dependency>
			<groupId>xom</groupId>
			<artifactId>xom</artifactId>
			<version>1.2.5</version>
		</dependency>
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.7</version>
		</dependency>
		<dependency>
			<groupId>com.enterprisedt</groupId>
			<artifactId>edtFTPj</artifactId>
			<version>1.5.3</version>
		</dependency>
		<!--alipay -->
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>alipay-sdk</artifactId>
			<version>1.4</version>
		</dependency>
		<!-- 修改过的微博4j -->
		<dependency>
			<groupId>com.weibo4j</groupId>
			<artifactId>weibo4j-oauth2</artifactId>
			<version>*******</version>
		</dependency>
		<dependency>
			<groupId>org.apache.directory.studio</groupId>
			<artifactId>org.dom4j.dom4j</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.11</version>
		</dependency>
		<!-- 阿里巴巴数据源 包 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
			<version>3.6.3</version>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>Transcoding_API</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
	</dependencies>
 
	<build>
		<finalName>MGW</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<filters>
			<filter>src/main/resources/conf/app.properties</filter>
			<filter>src/main/resources/conf/${env}/jdbc-${db}-${env}.properties</filter>
		</filters>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<transformers>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>cn.sh.ideal.mgw.boot.Bootstrap</mainClass>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.handlers</resource>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.schemas</resource>
								</transformer>
							</transformers>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- <plugin> <artifactId>maven-antrun-plugin</artifactId> <executions> 
				<execution> <id>copy-jar</id> <phase>package</phase> <configuration> <tasks> 
				<delete dir="D:/alljar/MGW.jar" /> <copy todir="D:/alljar/"> <fileset dir="target/"> 
				<include name="MGW.jar" /> </fileset> </copy> </tasks> </configuration> <goals> 
				<goal>run</goal> </goals> </execution> </executions> </plugin> -->
		</plugins>
	</build>



</project>