package cn.sh.ideal.mgw.dao;

import java.io.Serializable;
import java.util.List;

import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.model.MessageInfoSend;



public interface MessageInfoSendDao extends GenericDao<MessageInfoSend, Serializable> {
	public List<WeiboMsgInfo> historyInfo(MessageInfoSend messageInfoSend);
	
	public List<WeiboMsgInfo> historyInfoquery(MessageInfoSend messageInfoSend);
}