package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.util.Date;

public class Sensitive implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.auto_ID
     *
     * @mbggenerated
     */
    private Integer autoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_WORD
     *
     * @mbggenerated
     */
    private String sensitiveWord;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_CREATE_ID
     *
     * @mbggenerated
     */
    private String sensitiveCreateId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_CREATE_DATE
     *
     * @mbggenerated
     */
    private Date sensitiveCreateDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_UPDATE_ID
     *
     * @mbggenerated
     */
    private String sensitiveUpdateId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_UPDATE_DATE
     *
     * @mbggenerated
     */
    private Date sensitiveUpdateDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_DELETE_ID
     *
     * @mbggenerated
     */
    private String sensitiveDeleteId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_DELETE_DATE
     *
     * @mbggenerated
     */
    private Date sensitiveDeleteDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.STATUS
     *
     * @mbggenerated
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.CHANNEL_ID
     *
     * @mbggenerated
     */
    private String channelId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.SENSITIVE_TYPE
     *
     * @mbggenerated
     */
    private String sensitiveType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_SENSITIVE.TENANT_CODE
     *
     * @mbggenerated
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table CMS_SENSITIVE
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;
    
    private String matchingType;
    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.auto_ID
     *
     * @return the value of CMS_SENSITIVE.auto_ID
     *
     * @mbggenerated
     */
    
    
    public Integer getAutoId() {
        return autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.auto_ID
     *
     * @param autoId the value for CMS_SENSITIVE.auto_ID
     *
     * @mbggenerated
     */
    public void setAutoId(Integer autoId) {
        this.autoId = autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_WORD
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_WORD
     *
     * @mbggenerated
     */
    public String getSensitiveWord() {
        return sensitiveWord;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_WORD
     *
     * @param sensitiveWord the value for CMS_SENSITIVE.SENSITIVE_WORD
     *
     * @mbggenerated
     */
    public void setSensitiveWord(String sensitiveWord) {
        this.sensitiveWord = sensitiveWord == null ? null : sensitiveWord.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_CREATE_ID
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_CREATE_ID
     *
     * @mbggenerated
     */
    public String getSensitiveCreateId() {
        return sensitiveCreateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_CREATE_ID
     *
     * @param sensitiveCreateId the value for CMS_SENSITIVE.SENSITIVE_CREATE_ID
     *
     * @mbggenerated
     */
    public void setSensitiveCreateId(String sensitiveCreateId) {
        this.sensitiveCreateId = sensitiveCreateId == null ? null : sensitiveCreateId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_CREATE_DATE
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_CREATE_DATE
     *
     * @mbggenerated
     */
    public Date getSensitiveCreateDate() {
        return sensitiveCreateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_CREATE_DATE
     *
     * @param sensitiveCreateDate the value for CMS_SENSITIVE.SENSITIVE_CREATE_DATE
     *
     * @mbggenerated
     */
    public void setSensitiveCreateDate(Date sensitiveCreateDate) {
        this.sensitiveCreateDate = sensitiveCreateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_UPDATE_ID
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_UPDATE_ID
     *
     * @mbggenerated
     */
    public String getSensitiveUpdateId() {
        return sensitiveUpdateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_UPDATE_ID
     *
     * @param sensitiveUpdateId the value for CMS_SENSITIVE.SENSITIVE_UPDATE_ID
     *
     * @mbggenerated
     */
    public void setSensitiveUpdateId(String sensitiveUpdateId) {
        this.sensitiveUpdateId = sensitiveUpdateId == null ? null : sensitiveUpdateId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_UPDATE_DATE
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_UPDATE_DATE
     *
     * @mbggenerated
     */
    public Date getSensitiveUpdateDate() {
        return sensitiveUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_UPDATE_DATE
     *
     * @param sensitiveUpdateDate the value for CMS_SENSITIVE.SENSITIVE_UPDATE_DATE
     *
     * @mbggenerated
     */
    public void setSensitiveUpdateDate(Date sensitiveUpdateDate) {
        this.sensitiveUpdateDate = sensitiveUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_DELETE_ID
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_DELETE_ID
     *
     * @mbggenerated
     */
    public String getSensitiveDeleteId() {
        return sensitiveDeleteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_DELETE_ID
     *
     * @param sensitiveDeleteId the value for CMS_SENSITIVE.SENSITIVE_DELETE_ID
     *
     * @mbggenerated
     */
    public void setSensitiveDeleteId(String sensitiveDeleteId) {
        this.sensitiveDeleteId = sensitiveDeleteId == null ? null : sensitiveDeleteId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_DELETE_DATE
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_DELETE_DATE
     *
     * @mbggenerated
     */
    public Date getSensitiveDeleteDate() {
        return sensitiveDeleteDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_DELETE_DATE
     *
     * @param sensitiveDeleteDate the value for CMS_SENSITIVE.SENSITIVE_DELETE_DATE
     *
     * @mbggenerated
     */
    public void setSensitiveDeleteDate(Date sensitiveDeleteDate) {
        this.sensitiveDeleteDate = sensitiveDeleteDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.STATUS
     *
     * @return the value of CMS_SENSITIVE.STATUS
     *
     * @mbggenerated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.STATUS
     *
     * @param status the value for CMS_SENSITIVE.STATUS
     *
     * @mbggenerated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.CHANNEL_ID
     *
     * @return the value of CMS_SENSITIVE.CHANNEL_ID
     *
     * @mbggenerated
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.CHANNEL_ID
     *
     * @param channelId the value for CMS_SENSITIVE.CHANNEL_ID
     *
     * @mbggenerated
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.SENSITIVE_TYPE
     *
     * @return the value of CMS_SENSITIVE.SENSITIVE_TYPE
     *
     * @mbggenerated
     */
    public String getSensitiveType() {
        return sensitiveType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.SENSITIVE_TYPE
     *
     * @param sensitiveType the value for CMS_SENSITIVE.SENSITIVE_TYPE
     *
     * @mbggenerated
     */
    public void setSensitiveType(String sensitiveType) {
        this.sensitiveType = sensitiveType == null ? null : sensitiveType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_SENSITIVE.TENANT_CODE
     *
     * @return the value of CMS_SENSITIVE.TENANT_CODE
     *
     * @mbggenerated
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_SENSITIVE.TENANT_CODE
     *
     * @param tenantCode the value for CMS_SENSITIVE.TENANT_CODE
     *
     * @mbggenerated
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }
    
	public String getMatchingType() {
		return matchingType;
	}

	public void setMatchingType(String matchingType) {
		this.matchingType = matchingType;
	}
}