package cn.sh.ideal.mgw.model.return0x;

import java.util.List;


public class Return0xResponse {

	private List<Return0xField> protocolfields;
	
	private List<Return0xModel> reqitem;
	
	private List<Return0xProperty> sessionpropertys;
	
	private String msg;


	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public List<Return0xProperty> getSessionpropertys() {
		return sessionpropertys;
	}

	public void setSessionpropertys(List<Return0xProperty> sessionpropertys) {
		this.sessionpropertys = sessionpropertys;
	}

	public List<Return0xModel> getReqitem() {
		return reqitem;
	}

	public void setReqitem(List<Return0xModel> reqitem) {
		this.reqitem = reqitem;
	}

	public List<Return0xField> getProtocolfields() {
		return protocolfields;
	}

	public void setProtocolfields(List<Return0xField> protocolfields) {
		this.protocolfields = protocolfields;
	}
}
