package cn.sh.ideal.mgw.adapter;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.util.SpringContextUtil;

/**
 * 消息转换工具类
 * 
 * <AUTHOR>
 * 2015-3-6
 *
 */
public class MsgConvertUtil {
	private static MsgAdapterFactory adapterFactory = null;
	/**
	 * 将多媒体消息转化成Message格式
	 * 
	 * @param channelCode
	 * @param data
	 * @return
	 */
	public static MessageInfo media2Message(String channelCode,Object data){
		if (adapterFactory==null) {
			adapterFactory=(MsgAdapterFactory) SpringContextUtil.getBean("simpleMsgAdapterFactory");
		}
		MessageInfo message = null;
		try {
			message = adapterFactory.create(channelCode).media2Message(channelCode, data);
		} catch (Exception e) {
			System.out.println(e);
		}
		
		message.setSource("1");
		return message;
	}
	
	/**
	 * 将Message转换成多媒体格式
	 * 
	 * @param message
	 * @return
	 */
	public static Object message2Media(MessageInfo message){
		if (adapterFactory==null) {
			adapterFactory=(MsgAdapterFactory) SpringContextUtil.getBean("simpleMsgAdapterFactory");
		}
		return adapterFactory.create(message.getChannelCode()).message2Media(message);
	}
	
	/**
	 * 判断字符串是否是jsonObject
	 * 
	 * @param jsonString
	 * @return
	 */
	public static Object getJsonObj(String jsonString) {
		if (jsonString.startsWith("{")) {
			JSONObject jsonObject = JSONObject.parseObject(jsonString);
			return jsonObject;
		} else {
			return jsonString;
		}

	}
}
