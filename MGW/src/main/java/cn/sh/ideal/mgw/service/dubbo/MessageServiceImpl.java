package cn.sh.ideal.mgw.service.dubbo;

import static cn.sh.ideal.mgw.base.Constants.PARAM_APPID;
import static cn.sh.ideal.mgw.base.Constants.PARAM_APP_SECRET;
import static cn.sh.ideal.mgw.base.Constants.PARAM_PRIVATE_KEY;

import java.io.Serializable;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.MsgConvertUtil;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.init.InitService;
import cn.sh.ideal.mgw.mediasend.emailsend.EmailSender;
import cn.sh.ideal.mgw.mediasend.model.MediaSendModel;
import cn.sh.ideal.mgw.model.QQBean;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.response.BaseResponseDTO;
import cn.sh.ideal.mgw.service.MessageService;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.QqService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.utils.TokenUtil;
import cn.sh.ideal.mgw.utils.sender.SenderFactory;
import cn.sh.ideal.mgw.utils.sender.ToolBarSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.util.EmojiConverter;

import com.alibaba.fastjson.JSONObject;


/**
 * @project MGW
 * @Package cn.sh.ideal.mgw
 * @typeName MessageService
 * <AUTHOR> Zhou
 * @Description:  
 * @date 2016年3月29日 下午10:14:38
 * @version 
 */
@Component("dubboMessageService")
public class MessageServiceImpl implements MessageService {

	private static final Logger log = LoggerFactory.getLogger(MessageServiceImpl.class);

	@Resource(name = "sensitiveService")
	private SensitiveService sensitiveService;
	
	@Autowired
	private LocalSessionService sessionService;
	
	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private SysInitService sysInitServcie;
	
	@Autowired
	private InitService initService;
	
	@Autowired
	private EmailSender emailSender;
	
	@Autowired
	private QqService qqService;
	
	@Autowired
	private MessageInfoDao messageDao;
	
	@Override
	public BaseResponseDTO newSend(MessageInfo message) {
		
		try {
			
			String jsonString = JSONObject.toJSONString(message);
			
			log.info("Request MessageService.nsend[" + jsonString + "]");
			
			if (StringUtils.isEmpty(jsonString)) {
				throw new RuntimeException("request body is null!");
			}
			
			BaseResponseDTO response = new BaseResponseDTO(BaseResponseDTO.SUCCESS_CODE,BaseResponseDTO.SUCCESS_MSG);
			
			SensitiveLog sensitiveLog = new SensitiveLog(message);
			log.info("message:"+message.getContent());
			log.info("is contains sensitive:"+sensitiveService.filter(sensitiveLog));
			if (sensitiveService.filter(sensitiveLog)) {
				log.info("is contains sensitive ======================");
				response.setResultCode("-999");
				response.setResultMsg("包含敏感词");
				return response;
//				throw new RuntimeException("该消息包含敏感词消息被过滤!");
			}
			
			// 消息格式转换.调用转换器
			Object mObject = MsgConvertUtil.message2Media(message);
						
			if (!StringUtils.isEmpty(message.getSessionId())) {
				RequestSessionbean requestSessionbean = new RequestSessionbean(message);
				requestSessionbean.setSessionId(message.getSessionId());
				sessionService.updateSessionAgActive(requestSessionbean);
			}
			//查询渠道ID是否是自有渠道消息
			ChannelConfig channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_OWN_CHANNEL_INFO + message.getChannelCode());
			
			if (channelConfig != null) {
				channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + message.getSendAccount());
				if(channelConfig==null){
					initService.init();
					channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + message.getSendAccount());
				}
				JSONObject configObj = JSONObject.parseObject(channelConfig.getAccountConfig());
				JSONObject customSend = null;
				/*
				 * 获取到自有渠道发送的url. 多个url用";"分割解析出url遍历调用.如果只有一个就.
				 * channelType:1用的是老的message格式.其他类型用的是现在渠道使用的messageInfo格式
				 *
				 */
				if (configObj.containsKey("url")) {
					String url = configObj.getString("url");
					if (url.indexOf(";") > 0) {
						String[] urls = url.split(";");
						for (int i = 0; i < urls.length; i++) { 
							if ("1".equals(channelConfig.getChannelType())) {
								customSend = new ToolBarSender().customSend(JSONObject.toJSONString(mObject), urls[i]);
							} else {
								customSend = new ToolBarSender().customSend(jsonString, urls[i]);
							}
						}
					} else {
						if ("1".equals(channelConfig.getChannelType())) {
							customSend = new ToolBarSender().customSend(JSONObject.toJSONString(mObject), url);
						} else {
							customSend = new ToolBarSender().customSend(jsonString, url);
						}
					}
					log.info("request" + customSend.toJSONString());
				}

			} else {

				Platform platform = Platform.codeOf(message.getChannelCode());

				switch (platform) {
				case ALIPAY:
				case WX:
				case YX:
				case QY: {
					String appid = null;
					String secret = null;
					String token = null;

					JSONObject msgObj = (JSONObject) mObject;

					channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + message.getSendAccount());

					if (channelConfig == null)
						
						throw new RuntimeException("appid and secret is null or fromuser not found");

					JSONObject configObj = JSONObject.parseObject(channelConfig.getAccountConfig());

					if (platform == Platform.ALIPAY) {
						token = configObj.getString(PARAM_PRIVATE_KEY);
						if (StringUtils.isEmpty(token))
							throw new RuntimeException("private key is null");
					} else {
						appid = configObj.getString(PARAM_APPID);
						secret = configObj.getString(PARAM_APP_SECRET);

						if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(secret))
							throw new RuntimeException("appid and secret is null or fromuser not found");

						String agentId = configObj.getString("agentId");
						if (!StringUtils.isEmpty(agentId))
							msgObj.put("agentid", agentId);
					}

					// wechat and yichat
					if (StringUtils.isEmpty(token))
						token = new TokenUtil(platform.getName(), appid, secret, redisDao).getToken();

					JSONObject jsonObject = SenderFactory.getSender(platform.getName()).customSend(JSONObject.toJSONString(mObject), token);
					if (jsonObject.containsKey("errcode") && !"0".equals(jsonObject.getString("errcode"))) {
						throw new RuntimeException("调用微信接口出错:" + jsonObject.getString("errcode"));
					}
					break;
				}
				case SMS:{
					MediaSendModel media = (MediaSendModel) mObject;
					if(StringUtils.isEmpty(media.getReceiver()) || StringUtils.isEmpty(media.getSender())){
						throw new RuntimeException("发送者或是接受者为空！");
					}
					channelConfig = sysInitServcie.getChannelInfo(media.getSender());
					if(channelConfig == null){
						throw new RuntimeException("channelConfig not found!");
					}
					
					JSONObject configObj = JSONObject.parseObject(channelConfig.getAccountConfig());
					
					String smsLkey = configObj.getString("key");
					JSONObject jsonObject = SenderFactory.getSender(platform.getName()).customSend(JSONObject.toJSONString(mObject), smsLkey);
					
					if (jsonObject.containsKey("resultCode") && !"0".equals(jsonObject.getString("resultCode"))) {
						throw new RuntimeException("调用联合维拓短信网关出错:"+jsonObject.getString("resultMsg"));
					}
					break;

				}
				case EMAIL:
				case CSSMS: {
					MediaSendModel media = (MediaSendModel) mObject;
					if (platform == Platform.EMAIL) {
						if (!StringUtils.isEmpty(media.getAttachPath())) {
							String[] attachs = media.getAttachPath().split(",");
							String attach;
							String[] fileNames = new String[attachs.length];
							for (int i = 0; i < attachs.length; i++) {
								attach = attachs[i];
								if (StringUtils.isEmpty(attach))
									continue;
								media.setAttachPath(attach.substring(0, attach.lastIndexOf("/") + 1));
								fileNames[i] = attach.substring(attach.lastIndexOf("/") + 1);
							}
							media.setAttchFiles(fileNames);
						}
						if (!emailSender.send(media))
							throw new RuntimeException("email send failed");
					} else if (platform == Platform.SMS) {

					} 

					break;
				}
				case QQ: {
					String qqKey = message.getAcceptedAccount() + "#" + message.getSendAccount();
					QQBean qqBean = (QQBean) redisDao.readValue(qqKey);
					log.info("systemId[" + qqBean.getSystemId() + "]," + "staffId[" + qqBean.getStaffId() + "]," + "acceptedAccount[" + message.getAcceptedAccount() + "]," + "sendAccount["
							+ message.getSendAccount() + "],");
					log.info("############" + message.getContent());
					qqService.qq0x04(qqBean.getSystemId(), qqBean.getStaffId(), message.getSendAccount(), message.getAcceptedAccount(), message.getContent());
					break;
				}
				case SINAWEIBO:
					String accessToken = (String) redisDao.readValue("sinaweibo_accesstoken_" + message.getSendAccount());
//					accessToken = "2.00WZaRiF3fxaXC36af8d9c18TBHYpB";
					log.info("############################");
					log.info("singwb_accessToken=" + accessToken);
					log.info("############################");
				JSONObject obj = SenderFactory.getSender(platform.getName()).customSend(JSONObject.toJSONString(mObject), accessToken);

					String msgInfo = (String) obj.get(Constants.WB_MSG_INFO);
					Object res = obj.get(Constants.WB_RESULT);
					// 请求参数格式有误
					if (null != msgInfo && !"".equals(msgInfo)) {
						response = new BaseResponseDTO(BaseResponseDTO.ERROR_CODE,msgInfo);
					} else {
						if (null != res && !"".equals(res)) {
							response = new BaseResponseDTO(BaseResponseDTO.SUCCESS_CODE,res);
						} else {
							response = new BaseResponseDTO(BaseResponseDTO.ERROR_CODE,"微博接口调用失败");
						}
					}

					log.info("Response MessageController.weiBoSend[" + JSONObject.toJSONString(response) + "]");
										
					break;
				default:
					throw new IllegalArgumentException("pid not match");
				}
			}
			
			if ("text".equals(message.getMsgType())) {

				message.setContent(EmojiConverter.encode(message.getContent()));
			}
			messageDao.insert(message);

			log.info("Response MessageController.nsend[" + response.toString() + "]");
			
			return response;
			
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			return new BaseResponseDTO(BaseResponseDTO.ERROR_CODE,e.getMessage() + "\"}");
		}
		
	}

	

}
