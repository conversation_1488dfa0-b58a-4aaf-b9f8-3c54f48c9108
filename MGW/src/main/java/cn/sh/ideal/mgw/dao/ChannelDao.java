package cn.sh.ideal.mgw.dao;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.mgw.model.Summary;
import cn.sh.ideal.model.ChannelConfig;


public interface ChannelDao {
	
	public ChannelConfig getChannelConfigByAppidAndSecret(Map<String, String> map);

	public void insertConfig(ChannelConfig channelConfig);

	public void disableByTenantId(String tenantId);

	public boolean existsFollowUser(Map<String, String> participant);
	
	public boolean existsBindingUser(Map<String, String> participant);

	public ChannelConfig getChannelConfigByTenantId(String tenantId);

	public ChannelConfig getChannelConfigById(String channelId);

	public void updateChannelConfig(ChannelConfig channelConfig);

	public String getAccountByAppidAndSecret(Map<String, String> condition);

	public List<ChannelConfig> getChannelConfigsByTenantId(String tenantId);

	public boolean existsAccount(ChannelConfig channelConfig);

	public List<Summary> querySummarys(String maxid);
	
	public String getAccountConfigByChannelCode(String code);
}