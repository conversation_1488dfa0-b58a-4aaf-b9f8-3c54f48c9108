package cn.sh.ideal.mgw.utils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.FetchProfile;
import javax.mail.Flags;
import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Part;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Store;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import javax.mail.search.FlagTerm;
import javax.mail.search.SearchTerm;

import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.util.MailSSLSocketFactory;

public class EmailConnecter {

    /***
     * 获取发送邮件所需的session
     * 
     * @param smtpServer
     * @param port
     * @param isSSL
     * @param username
     * @param password
     * @return
     * @throws GeneralSecurityException
     */
    public static Session getSMTPSession(String smtpServer, String port,
            boolean isSSL,boolean isTLS, final String username, final String password)
            throws GeneralSecurityException {

        Properties props = System.getProperties();
        // 如果是SSL方式则需添加下列参数，声明使用SSL方式建立连接
        if (isSSL) {
            MailSSLSocketFactory sf = new MailSSLSocketFactory();

            sf.setTrustAllHosts(true);
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.ssl.socketFactory", sf);
            props.setProperty("mail.smtp.socketFactory.port", port);
        }
        
        if(isTLS){
        	props.put("mail.smtp.starttls.enable", "true");
//        	props.put("mail.smtp.socketFactory.fallback", "true");
        }
        props.setProperty("mail.smtp.host", smtpServer);
        props.setProperty("mail.smtp.port", port);
        props.put("mail.smtp.auth", "true");

        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });
        return session;
    }

    /***
     * 获取IMAPStore，使用完毕后需要关闭store
     * 
     * @param imapServer
     * @param port
     * @param isSSL
     * @param username
     * @param password
     * @return
     * @throws MessagingException
     * @throws GeneralSecurityException
     */
    public static Store getIMAPStore(String imapServer, String port,
            boolean isSSL,boolean isTLS, String username, String password)
            throws MessagingException, GeneralSecurityException {

        Properties props = System.getProperties();
        // 如果是SSL方式则需添加下列参数，声明使用SSL方式建立连接
        if (isSSL) {
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            props.put("mail.imap.ssl.enable", "true");
            props.put("mail.imap.ssl.socketFactory", sf);

        }
        
        // 如果是TLS方式则需添加下列参数
        if(isTLS){
        	props.put("mail.imap.starttls.enable", "true");
//        	props.put("mail.imap.ssl.checkserveridentity", "true");
        }
        props.setProperty("mail.store.protocol", "imap");
        props.setProperty("mail.imap.port", port); // 端口
        props.setProperty("mail.imap.host", imapServer);
        props.setProperty("mail.imap.connectiontimeout", "1200000");
        props.setProperty("mail.imap.timeout", "1200000");
        props.setProperty("mail.imap.auth.login.disable", "false");
        Session session = Session.getInstance(props);

        Store store = session.getStore("imap");
        store.connect(imapServer, username, password);
        return store;
    }

    /***
     * 获取POPStore，使用完毕后需要关闭store (未测试)
     * 
     * @param popServer
     * @param port
     * @param isSSL
     * @param username
     * @param password
     * @return
     * @throws GeneralSecurityException
     * @throws MessagingException
     */
    public static Store getPOPStore(String popServer, String port,
            boolean isSSL,boolean isTLS, String username, String password)
            throws GeneralSecurityException, MessagingException {

        Properties props = System.getProperties();
        // 如果是SSL方式则需添加下列参数，声明使用SSL方式建立连接
        if (isSSL) {
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            props.put("mail.pop3.ssl.enable", "true");
            props.put("mail.pop3.ssl.socketFactory", sf);
        }
        // 如果是TLS方式则需添加下列参数
        if(isTLS){
        	props.put("mail.smtp.starttls.enable", "true");
        }
        props.setProperty("mail.store.protocol", "pop3");
        props.setProperty("mail.imap.port", port); // 端口
        props.setProperty("mail.imap.host", popServer);
        props.setProperty("mail.imap.connectiontimeout", "1200000");
        props.setProperty("mail.imap.timeout", "1200000");
        props.setProperty("mail.imap.auth.login.disable", "false");
        Session session = Session.getInstance(props);
        ;

        Store store = session.getStore("pop3");
        store.connect(popServer, username, password);
        return store;
    }

    public static void main(String[] args) throws GeneralSecurityException,
            AddressException, MessagingException {
        // String username = "<EMAIL>";
        // Session session = EmailConnecter.getSMTPSession("smtp.exmail.qq.com",
        // "465", true, username, "sanya904123908");
        // String username = "<EMAIL>";
        // Session session = EmailConnecter.getSMTPSession("smtp.126.com",
        // "25", false, username, "utfdsrgtdgumaxne");
        // Message msg = new MimeMessage(session);
        //
        // // -- Set the FROM and TO fields --
        // msg.setFrom(new InternetAddress(username));
        // msg.setRecipients(Message.RecipientType.TO,
        // InternetAddress.parse("<EMAIL>", false));
        // msg.setSubject("Hello");
        // msg.setText("How are you");
        // msg.setSentDate(new Date());
        // Transport.send(msg);
        //
        // System.out.println("Message sent.");
    	//(String imapServer, String port,
                //boolean isSSL,boolean isTLS, String username, String password)
    	Store store = getIMAPStore("imap.partner.outlook.cn","993",true,true,"<EMAIL>","Xot87758");
//    	Store store = getIMAPStore("imap.163.com","993",true,true,"logcy","logcy1979");
    	IMAPFolder folder = (IMAPFolder) store.getFolder("INBOX");
		folder.open(Folder.READ_WRITE); // 打开收件箱

		// 设置搜索条件
		SearchTerm serSearchTerm = new FlagTerm(new Flags(Flags.Flag.SEEN), false);

		Message[] messages = folder.search(serSearchTerm);
		// 流量控制
		int mailNum = 0;

		int maxNum = 10;// 本次可接收量

		if (maxNum >= messages.length) {
			mailNum = messages.length;
		} else {
			mailNum = maxNum;
		}

		// 获取当前时间(精确到天)
		String currentReceiveDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

		if (messages != null) {
			for (int j = 0; j < mailNum; j++) {
				messages[j].setFlag(Flags.Flag.SEEN, true);
			}
		}
//    	Session session = getSMTPSession("smtp.partner.outlook.cn", "587",
//                false,true, "<EMAIL>", "Xot87758");
//    	
//    	MimeMessage message = new MimeMessage(session); 
//        // 设置发件人 
//        message.setFrom(new InternetAddress("<EMAIL>"));
//        // 设置邮件主题 
//        message.setSubject("使用javamail发送简单文本邮件"); 
//        // 设置收件人 
//        message.setRecipient(RecipientType.TO, new InternetAddress("<EMAIL>")); 
//        // 设置发送时间 
//        message.setSentDate(new Date()); 
//        // 设置纯文本内容为邮件正文 
//        message.setText("使用POP3协议发送文本邮件测试!!!"); 
//        // 保存并生成最终的邮件内容 
//        message.saveChanges(); 
// 
//        // 获得Transport实例对象 
//        Transport transport = session.getTransport(); 
//        // 打开连接 
//        transport.connect("<EMAIL>", "Xot87758"); 
//        // 将message对象传递给transport对象，将邮件发送出去 
//        transport.sendMessage(message, message.getAllRecipients());
//        transport.close();
        // 关闭连接 

    	
        // 以读写模式打开收件箱 
         
        // 获得收件箱的邮件列表 
         
        // 打印不同状态的邮件数量 
//        doDelete(doReceive());
    	
//    	Store store = getPOPStore("pop.partner.outlook.cn","995",
//                false,false, "<EMAIL>", "Xot87758");
//    	Folder folder = store.getFolder("INBOX");
//        folder.open(Folder.READ_ONLY);
//
//        int size = folder.getMessageCount();
//        Message message = folder.getMessage(size);
//
//        String from = message.getFrom()[0].toString();
//        String subject = message.getSubject();
//
//        System.out.println("From: " + from);
//        System.out.println("Subject: " + subject);
//        folder.close(false);
//        store.close();
    }
    
   

    public static String[] doReceive() throws GeneralSecurityException,
            MessagingException {
        // Store store = EmailConnecter.getIMAPStore("imap.exmail.qq.com",
        // "993",
        // true, "<EMAIL>", "sanya904123908");
        Store store = EmailConnecter.getPOPStore("pop.exmail.qq.com", "995",
                false,true, "<EMAIL>", "sanya904123908");
        // Store store = EmailConnecter.getIMAPStore("imap.126.com", "143",
        // false, "<EMAIL>", "utfdsrgtdgumaxne");
        Folder inbox = null;
        String[] messageIDs = null;
        try {
            inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_WRITE);
            FetchProfile profile = new FetchProfile();
            profile.add(FetchProfile.Item.ENVELOPE);

            // 设置搜索条件
            // SearchTerm serSearchTerm = new FlagTerm(new
            // Flags(Flags.Flag.SEEN),
            // false);
            //
            // Message[] messages = inbox.search(serSearchTerm);
            Message[] messages = inbox.getMessages();
            inbox.fetch(messages, profile);
            System.out.println("收件箱的邮件数：" + messages.length);
            messageIDs = new String[messages.length];
            for (int i = 0; i < messages.length; i++) {
                // 邮件发送者
                String from;
                try {
                    // boolean flag = messages[i].getFlags().contains(
                    // Flags.Flag.SEEN);
                    // System.out.println(flag);
                    // if (!flag) {
                    // messages[i].setFlag(Flags.Flag.SEEN, true);
                    // messages[i].saveChanges();
                    MimeMessage mm = (MimeMessage) messages[i];

                    System.out.println("messageId:" + mm.getMessageID());
                    from = decodeText(mm.getFrom()[0].toString());

                    InternetAddress ia = new InternetAddress(from);
                    System.out.println("FROM:" + ia.getPersonal()
                            + ia.getAddress());
                    // 邮件标题
                    System.out.println("TITLE:" + mm.getSubject());
                    // 邮件大小
                    System.out.println("SIZE:" + mm.getSize());
                    // 邮件发送时间
                    System.out.println("DATE:" + mm.getSentDate());

                    if (mm.isMimeType("multipart/*")) {
                        Multipart mp = (Multipart) mm.getContent();
                        for (int j = 0; j < mp.getCount(); j++) {

                            BodyPart mpart = mp.getBodyPart(j);
                            getBody(mpart);

                        }
                    }
                    messageIDs[i] = mm.getMessageID();
                    // }
                    // messages[i].setFlag(Flags.Flag.DELETED, true);
                    // messages[i].saveChanges();
                } catch (Exception e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inbox != null)
                    inbox.close(false);
            } catch (Exception e) {
            }
            try {
                if (store != null)
                    store.close();
            } catch (Exception e) {
            }
        }
        return messageIDs;
    }

    public static void doDelete(Store store, String[] messageIDs)
            throws MessagingException, GeneralSecurityException {
        if (messageIDs != null) {
            System.out.println("开始删除邮件");
            /*
             * Store store = EmailConnecter.getIMAPStore("imap.exmail.qq.com",
             * "993", true, "<EMAIL>", "sanya904123908");
             */
            Folder inbox = null;
            try {
                inbox = store.getFolder("INBOX");
                inbox.open(Folder.READ_WRITE);
                FetchProfile profile = new FetchProfile();
                profile.add(FetchProfile.Item.ENVELOPE);
                Message[] messages = inbox.getMessages();
                inbox.fetch(messages, profile);
                for (int i = 0; i < messages.length; i++) {
                    MimeMessage mm = (MimeMessage) messages[i];
                    for (int j = 0; j < messageIDs.length; j++) {
                        if (messageIDs[j].equals(mm.getMessageID())) {
                            mm.setFlag(Flags.Flag.DELETED, true);
                            break;
                        }
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (inbox != null)
                        inbox.close(false);
                } catch (Exception e) {
                }
                try {
                    if (store != null)
                        store.close();
                } catch (Exception e) {
                }
            }
        }
    }

    public static void doDelete(String[] messageIDs) throws MessagingException,
            GeneralSecurityException {
        if (messageIDs != null) {
            System.out.println("开始删除邮件");
            Store store = EmailConnecter.getIMAPStore("imap.exmail.qq.com",
                    "993", false,true, "<EMAIL>", "sanya904123908");
            Folder inbox = null;
            try {
                inbox = store.getFolder("INBOX");
                inbox.open(Folder.READ_WRITE);
                FetchProfile profile = new FetchProfile();
                profile.add(FetchProfile.Item.ENVELOPE);
                Message[] messages = inbox.getMessages();
                inbox.fetch(messages, profile);
                for (int i = 0; i < messages.length; i++) {
                    MimeMessage mm = (MimeMessage) messages[i];
                    for (int j = 0; j < messageIDs.length; j++) {
                        if (messageIDs[j].equals(mm.getMessageID())) {
                            mm.setFlag(Flags.Flag.DELETED, true);
                            break;
                        }
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (inbox != null)
                        inbox.close(false);
                } catch (Exception e) {
                }
                try {
                    if (store != null)
                        store.close();
                } catch (Exception e) {
                }
            }
        }
    }

    public static void getBody(BodyPart mp) throws MessagingException,
            IOException {

        if (mp.isMimeType("multipart/*")) {
            Multipart tmp = (Multipart) mp.getContent();
            for (int j = 0; j < tmp.getCount(); j++) {
                BodyPart mpart = tmp.getBodyPart(j);
                getBody(mpart);
            }
        } else {
            String disposition = mp.getDisposition();

            if ((disposition != null)
                    && ((disposition.equals(Part.ATTACHMENT)) || (disposition
                            .equals(Part.INLINE)))) {
                String fileName = mp.getFileName();
                System.out.println("附件：" + decodeText(fileName));
            }
        }

    }

    public static String decodeText(String text)
            throws UnsupportedEncodingException {
        if (text == null)
            return null;
        if (text.startsWith("=?GB") || text.startsWith("=?gb"))
            text = MimeUtility.decodeText(text);
        else
            text = new String(text.getBytes("ISO8859_1"));
        return text;
    }
}
