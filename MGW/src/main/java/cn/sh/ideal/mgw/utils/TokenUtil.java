package cn.sh.ideal.mgw.utils;

/**
 * token工具类，用于封装token存取刷新等操作，适用于微信以及易信
 * 
 * <AUTHOR>
 * @modify yan
 * 
 * */
import java.io.Serializable;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.util.NetUtil;
import cn.sh.ideal.util.RedisLock;

import com.alibaba.fastjson.JSONObject;

public class TokenUtil {
	private RedisDao<String, Serializable> redisDao;
	private String appid;
	private String secret;
	private String url;
	private String platform;
	
	/**
	 * 构造方法，根据传入的平台类型构造实例，默认使用微信配置文件
	 * 
	 * @param platform
	 *            平台类型
	 */
	public TokenUtil(String platform, String appid, String secret, RedisDao<String, Serializable> redisDao) {
		this.appid = appid;
		this.secret = StringUtils.defaultIfEmpty(secret, "");
		this.redisDao = redisDao;
		this.url = String.format(PropertiesUtil.getProperty(PropertiesUtil.CONFIG, platform + "." + "TOKEN_URL"), appid, secret);
		this.platform = platform;

	}

	/**
	 * 提供token获取方法
	 * 
	 * @return token
	 * */
	public String getToken() throws Exception {

		String accessToken = (String) redisDao.readValue(appid + "_" + secret + "_access_token");

		if (accessToken == null) {
			try {
				// 如果token过期或者不存在则请求新的token
				if (this.redisDao.saveNxValue(appid + "_" + secret + "_access_token", RedisLock.LOCKED, RedisLock.EXPIRE, TimeUnit.SECONDS)) {
					accessToken = requestToken();
				} else {
					// 加锁失败则说明其他线程正在更新token则等待其他线程完成操作
					for (int i = 0; i < 20; i++) {
						accessToken = (String) redisDao.readValue(appid + "_" + secret + "_access_token");
						if (StringUtils.isEmpty(accessToken)) {
							Thread.sleep(500);
						} else {
							return accessToken;
						}
					}
				}
			} finally {
				
			}
		}
		return accessToken;

	}

	/**
	 * 提供token和过期时间
	 * 
	 * @return token
	 * */
	public JSONObject getAccessToken() throws Exception {
		String accessToken = getToken();
		Date tokenExpires = (Date) redisDao.readValue(appid + "_" + secret + "_token_expires");
		JSONObject json = new JSONObject(); 
		json.put("accessToken", accessToken);   
		json.put("tokenExpires", tokenExpires);

		return json;

	}

	/**
	 * 获取token
	 * */
	public String requestToken() throws Exception {
		
		//获取第三方平台访问令牌
		
		if("OPENWEIXIN".equals(platform)) {
			String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, "OPENWEIXIN.TOKEN_URL");
			String tikect = (String) redisDao.readValue("OPEN_WEIXIN_TIKECT_" + appid);

			if (StringUtils.isEmpty(tikect)) {
				throw new Exception("第三方平台tikect未获取.");
			}

			JSONObject requestObj = new JSONObject();

			requestObj.put("component_appid", appid);
			requestObj.put("component_appsecret", secret);
			requestObj.put("component_verify_ticket", tikect);

			JSONObject responseObj = JSONObject.parseObject(NetUtil.send(url,
					NetUtil.POST, requestObj.toJSONString()));

			if (!responseObj.containsKey("component_access_token")) {
				throw new Exception(responseObj.toJSONString());
			}

			String accessToken = responseObj.getString("component_access_token");
			int expiresIn = responseObj.getInteger("expires_in");

			redisDao.saveValue(appid + "_" + secret + "_access_token", accessToken, expiresIn, TimeUnit.SECONDS);

			return accessToken;
		}
		
		
		//微信公众号第三方平台托管获取token
		if(StringUtils.isEmpty(secret)) {
			
			synchronized (TokenUtil.class) {
				
				
				String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, "OPENWEIXIN.AUTHORIZER_TOKEN_URL");
				String componentAppid = PropertiesUtil.getProperty(PropertiesUtil.OPENWEIXIN, "openweixin.appId");
				String componentAppSecret = PropertiesUtil.getProperty(PropertiesUtil.OPENWEIXIN, "openweixin.appSecret");
				String refreshToken = (String)redisDao.readValue(appid + "__refresh_token");
				
				TokenUtil tokenUtil = new TokenUtil("OPENWEIXIN", componentAppid, componentAppSecret, this.redisDao);
				String componentAccessToken = tokenUtil.getToken();
				
				if(StringUtils.isEmpty(refreshToken)) 
					throw new Exception("refresh token is null.");
				
				JSONObject requestObj = new JSONObject();
				requestObj.put("component_appid", componentAppid);
				requestObj.put("authorizer_appid", appid);
				requestObj.put("authorizer_refresh_token", refreshToken);
				
				JSONObject responseObj = JSONObject.parseObject(NetUtil.send(url + componentAccessToken, NetUtil.POST, requestObj.toJSONString()));
				if(!responseObj.containsKey("authorizer_access_token")) {
					throw new Exception(responseObj.toJSONString());
				}
				
				String accessToken = responseObj.getString("authorizer_access_token");
				refreshToken = responseObj.getString("authorizer_refresh_token");
				int expiresIn = responseObj.getInteger("expires_in");
				
				redisDao.saveValue(appid + "__access_token", accessToken, expiresIn - 10, TimeUnit.SECONDS);
				redisDao.saveValue(appid + "__refresh_token", refreshToken);
				
				return accessToken;
				
			} 
			
		} else {

			String result = NetUtil.send(url, "GET", "");
	
			JSONObject jsonObj = JSONObject.parseObject(result);
			String access_token = jsonObj.getString("access_token");
	
			if (access_token != null && !"".equals(access_token)) {
	
				int expires_in = jsonObj.getIntValue("expires_in");
	
				// 失效时间提前60秒,防止过期现象
				expires_in -= 60;
	
				Date expiresDate = new Date(System.currentTimeMillis() + (expires_in * 1000));
	
				redisDao.saveValue(appid + "_" + secret + "_access_token", access_token, expires_in, TimeUnit.SECONDS);
				redisDao.saveValue(appid + "_" + secret + "_token_expires", expiresDate, expires_in, TimeUnit.SECONDS);
	
				return access_token;
			}
	
			return "";
		}
	}

	public static void main(String[] args) throws Exception {
		// System.out.println(new TokenUtil("YX",
		// "6f592af64f4f409e908774e34d02910f",
		// "a147a8a757244c089dcdbfb2103964cb", null).requestToken());
		// System.out.println(new TokenUtil("YX",
		// "39017ca714db47f9a460daddc6dc17ac",
		// "306cae383e3348219c5376c6d503a332", null).requestToken());
		System.out.println(new TokenUtil("WX", "wx602f0ea7533f5d55", "02305694d37437637168fb241ff2cf06", null).requestToken());
	}
}
