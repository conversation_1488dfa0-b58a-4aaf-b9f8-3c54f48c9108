//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.sh.ideal.mgw.service.dubbo.channel;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.req.MessageHandleRequest;
import cn.sh.ideal.imr.resp.MessageHandleResponse;
import cn.sh.ideal.imr.service.IMRService;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.response.CustomResponseDTO;
import cn.sh.ideal.mgw.service.channel.CustomService;
import cn.sh.ideal.mgw.service.local.BlackService;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.XmlObject;
import cn.sh.ideal.mgw.utils.sender.ToolBarSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.util.EmojiConverter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("customService")
public class CustomServiceImpl implements CustomService {
	private static final Logger log = Logger.getLogger(CustomServiceImpl.class);
	@Resource(
			name = "blackService"
	)
	private BlackService blackService;
	@Resource(
			name = "sensitiveService"
	)
	private SensitiveService sensitiveService;
	@Autowired
	private SysInitService initService;
	@Autowired
	private ChannelConfigDao channelConfigDao;
	@Autowired
	private MessageInfoDao messageInfoDao;
	@Autowired
	private LocalSessionService sessionService;
	String[] selfServiceStatus = new String[]{"1", "2","3","6"};
	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;
	@Autowired
	private IMRService imrService;
	@Value("#{config['outAgent']}")
	private String outAgent;
	@Value("#{config['media.imrUrl']}")
	private String imrUrl;
	@Value("#{config['media.imrUrl']}")
	private String mirUrl;
	@Value("#{config['EC.coding.url']}")
	private String codingUrl;

	public CustomServiceImpl() {
	}

	public CustomResponseDTO receiveCustomMessage(MessageInfo requestMessage) {
		CustomResponseDTO result = new CustomResponseDTO();

		try {
			log.info("推送自有渠道信息:" + JSONObject.toJSONString(requestMessage));
			SensitiveLog sensitiveLog = new SensitiveLog(requestMessage);
			if (this.sensitiveService.filter(sensitiveLog)) {
				result.setResultCode("-1");
				result.setResultMsg("消息已经被过滤");
				log.info("消息已经被过滤" + JSON.toJSONString(sensitiveLog));
			} else if (this.blackService.blackService(requestMessage.getChannelCode(), requestMessage.getSendAccount(), requestMessage.getTenantCode())) {
				result.setResultCode("-1");
				result.setResultMsg("消息已经被过滤");
				log.info("在线客服消息已经黑名单被过滤,发送账号:" + requestMessage.getSendAccount());
			} else {
				ChannelConfig channelConfig = new ChannelConfig();
				channelConfig.setTenantCode(requestMessage.getTenantCode());
				channelConfig.setChannelEnable("1");
				channelConfig.setChannelCode(requestMessage.getChannelCode());
				channelConfig.setChannelAccount(requestMessage.getAcceptedAccount());
				List<ChannelConfig> channelInfoList = this.channelConfigDao.query(channelConfig);
				if (channelInfoList == null || channelInfoList.size() <= 0) {
					result.setResultCode("-1");
					result.setResultMsg("渠道配置不存在");
					return result;
				}

				channelConfig = (ChannelConfig)channelInfoList.get(0);
				RequestSessionbean requestSessionbean = new RequestSessionbean(requestMessage);
				if (StringUtils.isEmpty(requestMessage.getSkillType())) {
					requestSessionbean.setSkillType(channelConfig.getRealTime());
				}

				SessionInfo sessioninfo = this.sessionService.getSession(requestSessionbean);
				String channelCode = requestMessage.getChannelCode();
				if (!"1".equals(channelConfig.getSelfType()) || !ArrayUtils.contains(this.selfServiceStatus, sessioninfo.getStatus()) && !this.outAgent.equals(requestMessage.getContent())) {
					try {
						requestMessage.setSessionId(sessioninfo.getSessionId());
						requestMessage.setSkillType(sessioninfo.getSkillType());
						requestMessage.setSource("1");
						requestMessage.setMessageSource("1");
						if ("text".equals(requestMessage.getMsgType())) {
							requestMessage.setContent(EmojiConverter.encode(requestMessage.getContent()));
						}

						if (StringUtils.isEmpty(requestMessage.getSessionId())) {
							requestMessage.setSessionId(sessioninfo.getSessionId());
						}

						this.messageInfoDao.insert(requestMessage);
						if (StringUtils.isNotEmpty(sessioninfo.getCustomerId())) {
							requestMessage.setCustomerId(sessioninfo.getCustomerId());
						}

						this.redisMessage.listrPush("WAITING_REQUEST_MESSAGE", requestMessage);
						log.info("messageInfo存入redis , " + JSON.toJSONString(requestMessage));
						if ("4".equals(requestMessage.getSkillType())) {
							JSONObject param = new JSONObject();
							param.put("sessionId", requestMessage.getSessionId());
							NetUtil.send(this.codingUrl, "POST", param.toJSONString());
						}
					} catch (Exception var24) {
						log.error("自定义渠道接收消息异常", var24);
					}
				} else {
					Object contentObj = getJsonObj(requestMessage.getContent());
					PlatformMessage platformMessage = null;
					if (contentObj instanceof JSONObject) {
						platformMessage = (PlatformMessage)JSONObject.toJavaObject((JSONObject)contentObj, PlatformMessage.class);
					} else {
						platformMessage = new PlatformMessage();
						platformMessage.setContent((String)contentObj);
					}

					platformMessage.setFollowData(requestMessage.getFollowData());
					platformMessage.setMsgType(requestMessage.getMsgType());
					platformMessage.setToUser(requestMessage.getAcceptedAccount());
					platformMessage.setOpenId(requestMessage.getSendAccount());
					MessageHandleResponse messageHandle = this.imrService.messageHandle(new MessageHandleRequest(platformMessage));
					if (messageHandle != null && messageHandle.getResponseMessage() != null) {
						JSONObject requestObj = new JSONObject();
						requestObj.put("userId", requestMessage.getUserId());
						requestObj.put("acceptedAccount", requestMessage.getSendAccount());
						requestObj.put("sendAccount", requestMessage.getAcceptedAccount());
						requestObj.put("tenantCode", requestMessage.getTenantCode());
						requestObj.put("workNo", "");
						requestObj.put("messageId", "");
						requestObj.put("sessionId", sessioninfo.getSessionId());
						requestObj.put("channelCode", channelCode);
						requestObj.put("businessType", requestMessage.getBusinessType());
						requestObj.put("appId", "");
						requestObj.put("account", new JSONArray());
						String msgType = messageHandle.getResponseMessage().getMsgType().toLowerCase();
						requestObj.put("msgType", msgType.toLowerCase());
						JSONObject msgObj = new JSONObject();
						String xmlString = messageHandle.getResponseMessage().toXMLString();
						XmlObject xmlObject = XmlObject.parse(xmlString);
						byte var18 = -1;
						switch(msgType.hashCode()) {
							case 3377875:
								if (msgType.equals("news")) {
									var18 = 1;
								}
								break;
							case 3556653:
								if (msgType.equals("text")) {
									var18 = 0;
								}
						}

						switch(var18) {
							case 0:
								requestObj.put("content", xmlObject.getChildValue("Content"));
								break;
							case 1:
								msgObj.put("articlesCount", xmlObject.getChildValue("ArticlesCount"));
								List<XmlObject> articles = xmlObject.getUniqueSubXmlObject("Articles").getSubXmlObjects("item");
								JSONArray itemAry = new JSONArray();
								Iterator i$ = articles.iterator();

								while(i$.hasNext()) {
									XmlObject item = (XmlObject)i$.next();
									JSONObject itemObj = new JSONObject();
									itemObj.put("title", item.getChildValue("Title"));
									itemObj.put("description", item.getChildValue("Description"));
									itemObj.put("picurl", item.getChildValue("PicUrl"));
									itemObj.put("url", item.getChildValue("Url"));
									itemAry.add(itemObj);
								}

								msgObj.put("articles", itemAry);
								requestObj.put("content", msgObj);
						}

						String url = JSONObject.parseObject(channelConfig.getAccountConfig()).getString("url");
						if (url.indexOf(";") > 0) {
							String[] urls = url.split(";");

							for(int i = 0; i < urls.length; ++i) {
								(new ToolBarSender()).customSend(requestObj.toJSONString(), urls[i]);
							}
						} else {
							(new ToolBarSender()).customSend(requestObj.toJSONString(), url);
						}
					}
				}

				result.setResultCode("0");
				result.setResultMsg("消息已存入任务池");
				result.setSessionId(sessioninfo.getSessionId());
			}

			return result;
		} catch (Exception var25) {
			var25.printStackTrace();
			result.setResultCode("-1");
			result.setResultMsg("消息接收失败");
			return result;
		}
	}

	public static Object getJsonObj(String jsonString) {
		if (jsonString.startsWith("{")) {
			JSONObject jsonObject = JSONObject.parseObject(jsonString);
			return jsonObject;
		} else {
			return jsonString;
		}
	}
}
