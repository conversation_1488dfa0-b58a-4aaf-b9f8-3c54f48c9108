package cn.sh.ideal.mgw.service.dubbo;

import static cn.sh.ideal.mgw.base.Constants.PARAM_RESULT_MSG;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import weibo4j.http.ImageItem;
import weibo4j.model.Comment;
import weibo4j.model.Status;
import weibo4j.model.WeiboException;
import weibo4j.org.json.JSONArray;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.base.WeiboInf;


import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.dao.MessageInfoSendDao;
import cn.sh.ideal.mgw.excutor.WeiboReceiveExcutor;
import cn.sh.ideal.mgw.model.StatusCount;
import cn.sh.ideal.mgw.model.UserCounts;
import cn.sh.ideal.mgw.model.response.BaseResponseDTO;
import cn.sh.ideal.mgw.service.WeiboHandMessageService;
import cn.sh.ideal.mgw.service.WeiboMessageService;
import cn.sh.ideal.mgw.thread.WeiboReceiveThread;
import cn.sh.ideal.mgw.utils.sender.WeiboSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.MessageInfoSend;
import cn.sh.ideal.util.BaseController;
import cn.sh.ideal.util.PropertiesUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

@Component("weiboHandMessageService")
public class WeiboHandServiceImpl extends BaseController implements WeiboHandMessageService ,ApplicationContextAware{

	private static final Logger log = Logger.getLogger(WeiboMessageServiceImpl.class);
	
	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	private ApplicationContext context;
	@Override
	public void setApplicationContext(ApplicationContext arg0)
			throws BeansException {
		this.context=arg0;
	}
	@Override
	public MessageInfoSend weiboHandSend(MessageInfoSend message) {
		// TODO Auto-generated method stub
		//这边做处理，手动调用定时任务评论
		System.out.println("**************进入微博收取手动1:*************");
		log.info("**************进入微博收取手动2:*************");
		ThreadGroup tg = null;
		try {
			Thread.sleep(6000);
			tg = new ThreadGroup("weibo");
		} catch (InterruptedException e1) {
			e1.printStackTrace();
		}
			try {
				ChannelConfig channelConfig = new ChannelConfig();
				channelConfig.setChannelCode("1005");
				channelConfig.setChannelEnable("1");
				List<ChannelConfig> mailList = channelConfigDao.query(channelConfig);
				Thread threads[] = new Thread[tg.activeCount()];
				tg.enumerate(threads);
				for (int i = 0; i < mailList.size(); i++) {
					ChannelConfig info = mailList.get(i);
					JSONObject infoJson = JSON.parseObject(info.getAccountConfig());
					infoJson.put("tenant", info.getTenantCode());
					infoJson.put("uid", info.getChannelAccount());
					boolean f = false;
					String name = info.getChannelAccount();
					for (int j = 0; threads != null
							&& j < threads.length; j++) {
						Thread thread = threads[j];
						if (thread.getName().equals(name)) {
							f = true;
							break;
						}
					}
					if (!f) {
						WeiboReceiveThread rt = new WeiboReceiveThread(infoJson,context);
						Thread th = new Thread(tg, rt, name);
						th.start();
					}
				}
				// Thread.sleep(60000);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
			try {
				//Thread.sleep(60000);
				Thread.sleep(6000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		return null;
	}
}
