package cn.sh.ideal.mgw.service.local;

import java.util.List;

import cn.sh.ideal.mgw.model.QQJoinBean;
import cn.sh.ideal.mgw.model.return0x.Return0xModel;
import cn.sh.ideal.mgw.model.return0x.Return0xResp;

public interface QqService {

	public Return0xModel qq0x01(String systemId, String loginUsername, String md5pwd, String loginDate);
	
	public Return0xModel qq0x01c(String systemId, String loginUsername, String logoutDate);
	
	public Return0xResp qq0x32(String loginUserId);
	
	public List<String> qq0x33(String loginUserId, String openId, String hostImNumber, String sequence, String size);
	
	public Return0xModel qq0x04(String systemId, String loginUserId, String fromUserName, String toUserName, String content);
	
	public Return0xModel qq0x17(String systemId, String loginUserId, String openId, String hostImNumber);
	/**
	 * 关闭坐席会话
	 * @param qqJoinList
	 */
	public void closeQQSession(List<QQJoinBean> qqJoinList);
}
