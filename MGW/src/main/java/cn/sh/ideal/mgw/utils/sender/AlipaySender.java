package cn.sh.ideal.mgw.utils.sender;

import java.util.List;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.base.MsgType;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.XmlObject;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayMobilePublicMessageCustomSendRequest;
import com.alipay.api.request.AlipayMobilePublicMessageSingleSendRequest;
import com.alipay.api.request.AlipayMobilePublicMessageTotalSendRequest;
import com.alipay.api.response.AlipayMobilePublicMessageCustomSendResponse;
import com.alipay.api.response.AlipayMobilePublicMessageSingleSendResponse;
import com.alipay.api.response.AlipayMobilePublicMessageTotalSendResponse;

public class AlipaySender implements Sender {

	private static final Logger log = Logger.getLogger(AlipaySender.class);

	public JSONObject customSend(String json, String privateKey) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(json);
		String appid = jsonObject.getString("fromuser");
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype")
				.toUpperCase());
		
		JSONObject requestObj = new JSONObject();
		requestObj.put("toUserId", jsonObject.getString("touser"));
		requestObj.put("createTime", System.currentTimeMillis());
		
		switch (type) {
		case NEWS:
			requestObj.put("msgType", "image-text");
			JSONArray articleArrays = new JSONArray();
			JSONArray articles = jsonObject.getJSONArray("articles");
			JSONObject article, articleObj;
			for (int i = 0; i < articles.size(); i++) {
				article = articles.getJSONObject(i);
				articleObj = new JSONObject();
				
				String title = article.getString("title");
				String desc = article.getString("description");
				String imageUrl = article.getString("picurl");
				String jumpUrl = article.getString("url");
				
				articleObj.put("actionName", "立即查看");
				articleObj.put("desc", desc);
				articleObj.put("imageUrl", imageUrl);
				articleObj.put("title", title);
				articleObj.put("url", jumpUrl);
				
				
				articleArrays.add(articleObj);
			}
			requestObj.put("articles", articleArrays);
			break;
		case IMAGE:
			requestObj.put("msgType", "image-text");
			articleArrays = new JSONArray();
			articleObj = new JSONObject();
			
			String imageUrl = jsonObject.getJSONObject("image").getString("media_id");
			
			articleObj.put("actionName", "");
			articleObj.put("desc", "");
			articleObj.put("imageUrl", imageUrl);
			articleObj.put("title", "");
			articleObj.put("url", "");
			
			articleArrays.add(articleObj);
			
			requestObj.put("articles", articleArrays);
			break;
		case TEXT: {
			requestObj.put("msgType", "text");
			requestObj.put("text", jsonObject.get("text"));
			break;
		}
		case TEMPLATE: {
			requestObj.put("template", jsonObject.get("template"));
			return sendTemplate(requestObj.toJSONString(), privateKey, appid);
		}

		default:
			throw new IllegalArgumentException(String.format("not supported msgtype %s", type.getName()));
		}

		String bizContent = requestObj.toJSONString();
		
		return send(bizContent, privateKey, appid);
	}
	
	private JSONObject sendTemplate(String jsonString, String privateKey, String appid) throws Exception {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG,
				"ALIPAY.GATEWAY_URL");
		
		AlipayClient client = new DefaultAlipayClient(url, appid, privateKey);
		// request
		AlipayMobilePublicMessageSingleSendRequest request = new AlipayMobilePublicMessageSingleSendRequest();
		request.setBizContent(jsonString);
		
		log.info(String.format("Request[%s] method[TemplateMessage] bizContent[%s]", url, jsonString));
		
		// response
		AlipayMobilePublicMessageSingleSendResponse response = client.execute(request);
		
		String result = response.getBody();
		
		log.info(String.format("Response[%s] method[TemplateMessage] result[%s]", url, result));
		
		
		JSONObject responseObj = JSONObject.parseObject(result).getJSONObject("alipay_mobile_public_message_single_send_response");
		
		JSONObject resultObj = new JSONObject();
		String code = responseObj.getString("code");
		resultObj.put("errcode", "200".equals(code) ? "0" : code);
		resultObj.put("errmsg", responseObj.getString("msg"));
		
		return resultObj;		
	}

	
	public String xml2json(String xml) throws Exception{
		XmlObject xmlObject = XmlObject.parse(xml);
		MsgType type = MsgType.NEWS;
		
		JSONObject requestObj = new JSONObject();
		requestObj.put("toUserId", xmlObject.getChildValue("ToUserId"));
		requestObj.put("createTime", System.currentTimeMillis());
		
		switch (type) {
		case NEWS:
			requestObj.put("msgType", "image-text");
			JSONArray articleArrays = new JSONArray();
			List<XmlObject> articles = xmlObject.getUniqueSubXmlObject("Articles").getSubXmlObjects("Item");
			JSONObject articleObj;
			XmlObject article;
			for (int i = 0; i < articles.size(); i++) {
				article = articles.get(i);
				articleObj = new JSONObject();
				
				String title = article.getChildValue("Title");
				String desc = article.getChildValue("Desc");
				String imageUrl = article.getChildValue("ImageUrl");
				String jumpUrl = article.getChildValue("Url");
				
				articleObj.put("actionName", "立即查看");
				articleObj.put("desc", desc);
				articleObj.put("imageUrl", imageUrl);
				articleObj.put("title", title);
				articleObj.put("url", jumpUrl);
				
				
				articleArrays.add(articleObj);
			}
			requestObj.put("articles", articleArrays);
			break;
		default:
			throw new IllegalArgumentException(String.format("not supported msgtype %s", type.getName()));
		}

		return requestObj.toJSONString();
	}

	public JSONObject send(String xml, String privateKey, String appid) throws Exception{
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG,
				"ALIPAY.GATEWAY_URL");
		
		AlipayClient client = new DefaultAlipayClient(url, appid, privateKey);
		// request
		AlipayMobilePublicMessageCustomSendRequest request = new AlipayMobilePublicMessageCustomSendRequest();
		request.setBizContent(xml);
		
		log.info(String.format("Request[%s] method[MessagePush] bizContent[%s]", url, xml));
		
		// response
		AlipayMobilePublicMessageCustomSendResponse response = client.execute(request);
		
		String result = response.getBody();
		
		log.info(String.format("Response[%s] method[MessagePush] result[%s]", url, result));
		
		
		JSONObject responseObj = JSONObject.parseObject(result).getJSONObject("alipay_mobile_public_message_custom_send_response");
		
		JSONObject resultObj = new JSONObject();
		String code = responseObj.getString("code");
		resultObj.put("errcode", "200".equals(code) ? "0" : code);
		resultObj.put("errmsg", responseObj.getString("msg"));
		
		return resultObj;
	}
	
	
	public JSON multiSend(String json, String token) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(json);
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype")
				.toUpperCase());

		JSONObject requestObj = new JSONObject();
		switch (type) {
		case NEWS:
			requestObj.put("msgType", "image-text");
			requestObj.put("createTime", System.currentTimeMillis());
			
			JSONArray articles = new JSONArray();
			JSONArray array = jsonObject.getJSONArray("articles");
			
			JSONObject article = null;
			for (int i = 0; i < array.size(); i++) {
				JSONObject item = array.getJSONObject(i);
				article = new JSONObject();
				article.put("actionName", "立即查看");
				article.put("desc", item.get("content"));
				article.put("imageUrl", item.get("picurl"));
				article.put("title", item.get("title"));
				article.put("url", item.get("content_source_url"));
			}
			
			requestObj.put("articles", articles);
			break;
		case TEXT: 
			requestObj.put("msgType", "text");
			requestObj.put("text", jsonObject.getJSONObject("text"));
			break;
		default:
			throw new IllegalArgumentException(String.format(
					"not supported msgtype %s", type.getName()));
		}
		
		String appid = jsonObject.getString("fromuser");
		return total(requestObj.toJSONString(), token, appid);
	}
	
	public JSONObject total(String json, String privateKey, String appid)
			throws Exception {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG,
				"ALIPAY.GATEWAY_URL");

		AlipayClient client = new DefaultAlipayClient(url, appid, privateKey);
		// request
		AlipayMobilePublicMessageTotalSendRequest request = new AlipayMobilePublicMessageTotalSendRequest();
		request.setBizContent(json);

		log.info(String.format(
				"Request[%s] method[MessagePush] bizContent[%s]", url, json));

		// response
		AlipayMobilePublicMessageTotalSendResponse response = client
				.execute(request);

		String result = response.getBody();

		log.info(String.format("Response[%s] method[MessagePush] result[%s]",
				url, result));

		JSONObject responseObj = JSONObject.parseObject(result).getJSONObject(
				"alipay_mobile_public_message_total_send_response");

		JSONObject resultObj = new JSONObject();
		String code = responseObj.getString("code");
		resultObj.put("errcode", "200".equals(code) ? "0" : code);
		resultObj.put("errmsg", responseObj.getString("msg"));

		return resultObj;
	}

	
	public static void main(String[] args) throws Exception{
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG,
				"ALIPAY.GATEWAY_URL");
		
		AlipayClient client = new DefaultAlipayClient(url, "2015011500025717", "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALFK5TqAKxDQ3aV0mj5PndH+hsDOktsooN2KYMfScU6+Bik5POJdv97LNcxsBNboXPa9a/FvzrP8DSzXOCG8v+ttsyeFrGJKk3ActPCPRq/JzLg2uQkmi/J+cbHELS5xwPiEnLAV+b0SHDzpH6C+ZQghRRoisMqYkUJRhuov/tdnAgMBAAECgYBJp77h9ftuSge7RF0MRBFh+6dKFMgoKpHiWCnq+j++oe13VOM5fA9JZQ0dt0avbeupSnq83GF4nt9uYJ9eTMdCOeFafliwV/JzJhPJ8gYa9IT8ojFUYyojdNswWoOGjCjTE07/jBd5RaUSd6yC+Lz8S227beAIYcfYeK4C8R0+QQJBAPciumGWsHPW2Gm2Ds4GaeC0btuUkouCdZdIm8woB6MfAIeh7YGJH2au0U/k8qVlWEfcvC7oGPBNBK5N8WIGg4cCQQC3ptj9WrrxnFwyFsEhVYNsvSW4GGAvgYN03iy3UriYRhOXrVKwqAow/sCslGHx7DRrbVxsYnV7xamx3fNUx8UhAkAs5odcYadvdHJfSm+IKQwlgkK97FFvvWUl9lAwGHnCvkg3XEkthr9IxaxLyrn3KOGQNN+P2iVly2h1Tr3zZGGTAkBfvq2L0Fx8EA+E0ZYYgdOFwkrC4npq6fOV3yqi3I5+XLJkNilRDhpaFsZucuFpVcKyaw5vNxmW0KM6psLwZUHhAkEA7896pUYqGSMngNSbVwiSBgW9ahvhdnsckwBQ/PFablXVkAefSIwY0fpD0EfKPgzGCz1qTw6b5+/b6mgkzDgo1A==");
		// request
		AlipayMobilePublicMessageCustomSendRequest request = new AlipayMobilePublicMessageCustomSendRequest();
		request.setBizContent("{\"articles\":[{\"actionName\":\"立即查看\",\"desc\":\"浦发银行信用卡中心提供您多渠道办理方式。\n例如：支付宝服务窗办卡、微信办卡、销售人员、网点、信用卡官网在线申请、浦发银行网银。\n\n输入序号查看相关问题  :\n89) 我要办卡\n90) 以卡办卡申请方式\n91) 附属卡申请方式\n92) 网上申请\n93) 信用卡申请失败\n\",\"imageUrl\":\"\",\"title\":\"\",\"url\":\"\"}],\"createTime\":1422343312724,\"msgType\":\"image-text\",\"toUserId\":\"yvSzBX7OK5yfFRBEoRbAMv9LFlaR3hgAJa6Z-VUdfoS21d1ystzZWfwyIkboff0Z01\"}");
		
//		log.info(String.format("Request[%s] method[MessagePush] bizContent[%s]", url, xml));
		
		// response
		AlipayMobilePublicMessageCustomSendResponse response = client.execute(request);
		
		String result = response.getBody();
		
		log.info(String.format("Response[%s] method[MessagePush] result[%s]", url, result));
		
		
		System.out.println(result);
	}
}
