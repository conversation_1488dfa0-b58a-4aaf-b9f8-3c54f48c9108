package cn.sh.ideal.mgw.service.local.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.dao.BlackLogDao;
import cn.sh.ideal.mgw.model.BlackList;
import cn.sh.ideal.mgw.model.BlackLog;
import cn.sh.ideal.mgw.service.local.BlackService;


@Service("blackService")
public class BlackServiceImpl implements BlackService {
	private final Logger log = LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	private cn.sh.ideal.mgw.dao.BlacklistDao BlacklistDao;
	@Autowired
	private BlackLogDao blackLogDao;

	@Autowired
	private RedisDao<String, BlackList> redisDao;
	
	private static final String BLACKLIST_KEY = "BLACKLIST:";

	public BlackList getBlacklist(String channelCode, String account, String tenant) {
		try {
			List<BlackList> blackLists = redisDao.listRangeAll(BLACKLIST_KEY.concat(tenant));
			if (blackLists!=null && blackLists.size()>0) {
				for (BlackList bList : blackLists) {
					if(bList.getBlackUser().equals(account) 
							&& bList.getChannelId().equals(channelCode)){
						return bList;
					}
				}
			}

		} catch (Exception e) {
			log.error("获取黑名单异常", e);
		}

		return null;
	}

	public Boolean blackService(String channelCode, String account, String tenant) {
		BlackLog blackLog = null;

		try {
			BlackList bList = getBlacklist(channelCode, account, tenant);
			if (bList != null) {
				blackLog = new BlackLog(bList, channelCode);
				blackLogDao.insert(blackLog);
				return true;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return false;

	}

	@Override
	public void init() {
		log.info("加载黑名单……");
		BlackList black = new BlackList();
		black.setStatus("1");
		
		Map<String,List<BlackList>> maps = new HashMap<String,List<BlackList>>();
		try {
			Set<String> okeys = redisDao.getKeysByPattern(BLACKLIST_KEY.concat("*"));
					
			List<BlackList> blackLists=BlacklistDao.query(black);
			if (blackLists!=null && blackLists.size()>0) {
				for (BlackList bList : blackLists) {
					if(null == bList.getTenantCode()) continue;
					List<BlackList> list = maps.get(bList.getTenantCode());
					if(null == list){
						list = new ArrayList<BlackList>();
						maps.put(bList.getTenantCode(), list);
					}
					
					list.add(bList);
				}
				
				for(String key : maps.keySet()){
					redisDao.listrPush(BLACKLIST_KEY.concat(key), maps.get(key));
					okeys.remove(BLACKLIST_KEY.concat(key));
				}
				
				//移除已删除数据
				for(String okey : okeys){
					redisDao.deleteValue(okey);
				}
			}
		} catch (Exception e) {
			log.error("初始化黑名单异常", e);
		}
	}
}
