package cn.sh.ideal.mgw.thread;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import weibo4j.Timeline;
import weibo4j.http.AccessToken;
import weibo4j.http.HttpClient;
import weibo4j.http.Response;
import weibo4j.model.Comment;
import weibo4j.model.CommentWapper;
import weibo4j.model.PostParameter;
import weibo4j.model.Status;
import weibo4j.model.StatusWapper;
import weibo4j.model.WeiboException;
import weibo4j.util.WeiboConfig;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.MsgConvertUtil;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.dao.CommonDao;
import cn.sh.ideal.mgw.dao.MediaReceiveDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.dao.MessageInfoSendDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageLogDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageTempDao;
import cn.sh.ideal.mgw.dao.WeiboCommentsDao;
import cn.sh.ideal.mgw.model.MediaModel;
import cn.sh.ideal.mgw.model.ReceiveMessageLog;
import cn.sh.ideal.mgw.model.ReceiveMessageTemp;
import cn.sh.ideal.mgw.model.UserTimelineIds;
import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.service.WeiboCommentsSumService;
import cn.sh.ideal.mgw.service.dubbo.WeiboCommentsServiceImpl;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.service.local.impl.BaseService;
import cn.sh.ideal.mgw.utils.HttpUtil;
import cn.sh.ideal.mgw.utils.IdealFtpClient;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.Util;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.MessageInfoSend;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.WeiboComentsSum;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

public class WeiboTranspondThread implements Runnable {

	public WeiboTranspondThread() {
		super();
	}

	private static final Logger log = LoggerFactory.getLogger(WeiboTranspondThread.class);
	
	private WeiboCommentsDao weiboCommentsDao;
	private MediaReceiveDao mediaReceiveDao;
	private SensitiveService sensitiveService;
	private LocalSessionService LocalsessionService;

	private ReceiveMessageLogDao receiveMessageLogDao;
	private ReceiveMessageTempDao receiveMessageTempDao;
	
	@Autowired
	private MessageInfoSendDao messageInfoSendDao;

	private  JSONObject jsonInfo;
	private CommonDao commonDao;
	private SysInitService initService;
	
	private String accessTokens;
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private WeiboCommentsSumService weiboCommentsSumService;
	
	
	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;
	
	private final String channelCode = "1005";

	private HttpClient client = new HttpClient();
	SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Autowired
	private MessageInfoDao messageInfoDao;
	private static String mirUrl = (String) PropertiesUtil.getProperty(PropertiesUtil.APP, "media.mirUrl");

	public WeiboTranspondThread(JSONObject json, ApplicationContext context) {
		this.jsonInfo = json;
		this.commonDao = context.getBean("commonDao", CommonDao.class);
		this.mediaReceiveDao = context.getBean("mediaReceiveDao", MediaReceiveDao.class);
		this.weiboCommentsDao = context.getBean("weiboCommentsDao", WeiboCommentsDao.class);
		this.messageInfoSendDao = context.getBean("messageInfoSendDao", MessageInfoSendDao.class);
		
		this.weiboCommentsSumService = context.getBean("weiboCommentsSumService", WeiboCommentsSumService.class);
		this.redisDao = context.getBean("redisDao", RedisDao.class);
		this.redisMessage = context.getBean("redisDao",RedisDao.class);
		this.receiveMessageLogDao = context.getBean("receiveMessageLogDao", ReceiveMessageLogDao.class);
		this.receiveMessageTempDao = context.getBean("receiveMessageTempDao", ReceiveMessageTempDao.class);

		this.messageInfoDao = context.getBean("messageInfoDao", MessageInfoDao.class);
		this.sensitiveService = context.getBean("sensitiveService", SensitiveService.class);
		this.LocalsessionService = context.getBean("LocalsessionService", LocalSessionService.class);
		this.initService = context.getBean("sysInitService",
				SysInitService.class);
	}

public void run() {
		
		String uid = jsonInfo.getString("uid");
		 log.info("uid=="+uid);
		//String uid = "5379187935";
		//String accessToken = "2.00jVWCsF0AoYz555936c5d19WOtaRC";//(String) redisDao.readValue("weibo_accesstoken_" + uid);
	   // String accessToken = "2.00nYS2PEJ_wqpCa35601d493eqnd2E";//(String) redisDao.readValue("weibo_accesstoken_" + uid);
	   String accessToken="";
	   try {
		   	   accessToken = (String)redisDao.readValue("weibo2_accesstoken_" +uid);
			  // log.info("accessToken=="+accessToken);
			   accessTokens = accessToken;
			try {
				//返回微博的全部转发微博列表  21
				log.info("获取转发微博列表开始");
				getReportStatusByWbid(accessToken);
				log.info("获取转发微博列表结束");
			} catch (Exception e) {
				e.printStackTrace();
				log.info("返回微博的全部转发微博列表接口失败"+e);
			}
	
			log.info("----------结束转发微博结束------------" + jsonInfo.getString("uid") + ":" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
			} catch (Exception e) {
				log.error("token值为空：uid="+uid,e);
			}
		}
	/**
	 * 返回一条微博的全部转发微博列表。
	 * 
	 * @throws WeiboException
	 * 
	 */
		public void getReportStatusByWbid(String token) throws WeiboException {
			
			// 获取微博id
			ReceiveMessageLog msgLog = new ReceiveMessageLog();
			//msgLog.setOperType(Constants.WB_GET_USER_TIMELINESIDS);
			//msgLog.setTenantId(jsonInfo.getString("tenant"));
			//msgLog.setChannelAccount(jsonInfo.getString("uid"));
			//msgLog.setTenantId("530000");
			//msgLog.setChannelAccount(uid);
			
			//到发布的微博中去找微博id
			
			List<WeiboMsgInfo> weiboMsgInfoList = null;
			MessageInfoSend messageInfoSend = new MessageInfoSend();
			messageInfoSend.setType("1");
			messageInfoSend.setMsgType("weibo");
			
			weiboMsgInfoList = messageInfoSendDao.historyInfo(messageInfoSend);
			
			log.info("weiboMsgInfoList大小为：："+weiboMsgInfoList.size());
			
			for(WeiboMsgInfo weiboMsgInfo:weiboMsgInfoList){
				
				String weiboid =weiboMsgInfo.getWeiboId();
				
				try {
					
					Date date=new Date();
					DateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String time=format.format(date);
					log.info("时间为："+time+"---微博id:"+weiboid);
					if(StringUtils.isNotBlank(weiboid)){
						StatusWapper status = getRepostTimeline(weiboid, token);
						parseWbContentZf(status, Constants.WB_GET_USER_TIMELINESSTATUS);
					}
					Thread.sleep(600000);
				} catch (Exception e) {
					// TODO: handle exception
				}
				
			}		
	}

	/**
	 * 获取指定微博的转发微博列表
	 * 
	 * @return list of the user_timeline
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/user_timeline
	 * @since JDK 1.5
	 */
	public StatusWapper getRepostTimeline(String wbid, String token) throws WeiboException {
		client.setToken(token);
		long maxId = getMaxId(Constants.WB_GET_USER_TIMELINESSTATUS,wbid);
		log.info("进入getRepostTimeline方法：wbid="+wbid);
		if (maxId == 0) {
			try {
				log.info("maxId等于0：maxId="+maxId);
				return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/repost_timeline.json?id=" + wbid));
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
		} else {
			log.info("maxId不等于0：maxId="+maxId);
			return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/repost_timeline.json?id=" + wbid+"&since_id="+maxId));
		}
	}
	public static void main(String[] args) {
		Date date=new Date();
		DateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String time=format.format(date);
		System.out.println(time);
	}
	
	/**
	 * 
	 * 判断是否是第一次信息
	 * 
	 * @return
	 */
	public long getMaxId(String operType, String msgId) {
		ReceiveMessageTemp msgTmp = new ReceiveMessageTemp();
		// 判断是否是第一次获取信息，否则获取最新的信息。
		msgTmp.setChannelAccount(jsonInfo.getString("uid"));
		msgTmp.setTenantId(jsonInfo.getString("tenant"));
		msgTmp.setOperType(operType);
		msgTmp.setChannelMessageId(msgId);
		String maxId = receiveMessageTempDao.getMessageTempId(msgTmp);
		maxId = maxId == null ? "0" : maxId;
		long maxid = Long.parseLong(maxId);
		return maxid;
	}

	/**
	 * 
	 * 判断是否是第一次信息
	 * 
	 * @return
	 */
	public long getMaxId(String operType) {
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		// 判断是否是第一次获取信息，否则获取最新的信息。
		msgLog.setChannelAccount(jsonInfo.getString("uid"));
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setOperType(operType);
		String maxId = receiveMessageLogDao.getChannelMessageId(msgLog);
		maxId = maxId == null ? "0" : maxId;
		long maxid = Long.parseLong(maxId);
		return maxid;
	}

	

	/**
	 * 转发微博解析微博内容
	 * 
	 * @param statusWapper
	 * @param reqType
	 */
	
	public void parseWbContentZf(StatusWapper statusWapper, String reqType) {
		for (Status s : statusWapper.getStatuses()) {
			String beforeWbid=s.getRetweetedStatus().getMid();
			String weiboId = s.getId().toString(); // 微博ID
			// 获取该微博的评论列表
			// this.getCommentById(weiboId,);
			MediaModel model = new MediaModel();

			String weiboText = s.getText(); // 转发的微博内容
			String nickName = s.getUser().getName();
			String senderId = s.getUser().getId().toString();
			
			//获取原始微博的图片信息
			
			String reciveName = jsonInfo.getString("uid"); // 接收人
			String sendTime = sf.format(s.getCreatedAt());
			String reciveTime = sf.format(new Date());
			String geo = s.getGeo();// 地理信息字段
			String mid = s.getMid();
			
			//获取原始微博的图片信息
			weibo4j.org.json.JSONArray picUrls = s.getRetweetedStatus().getPicUrls();
			String forwardNum =Integer.valueOf(s.getRepostsCount()).toString();// 转发数
			String commentNum = Integer.valueOf(s.getCommentsCount()).toString();
			String fansNum = Integer.valueOf(s.getUser().getFollowersCount()).toString(); // 粉丝数
			String smallPic = s.getThumbnailPic();// 缩略图，小图
			String middlePic = s.getBmiddlePic();// 中图
			String originalPic = s.getOriginalPic();// 原图
			Status status = s.getRetweetedStatus();
			//获取用户头像信息
			String headimgUrl = s.getUser().getProfileImageUrl();
			// 给模板赋值
			model.setUid(senderId);
			
			model.setYuanWeiboId(beforeWbid);
			model.setTaskId(commonDao.newMessageId());
			model.setMediaId(weiboId);
			model.setMediaContent(weiboText);
			model.setMediaSender(nickName);
			model.setMediaSendTime(sendTime);
			model.setMediaReceiver(reciveName);
			model.setMediaReceiveTime(reciveTime);
			model.setStatus(status);

			model.setGeo(geo);
			model.setMid(mid);
			model.setMediaFowardNum(forwardNum);
			model.setMediaCommentsNum(commentNum);
			model.setPicUrls(picUrls);
			model.setSmallPic(smallPic);// 微博小图设置在该字段中
			model.setMiddlePic(middlePic); // 微博中图设置在该字段中
			model.setOriginalPic(originalPic); // 微博原图设置在该字段中
			model.setWbSource(s.getSource());
			model.setVisible(s.getVisible());
			model.setChannelType("3");// 微博渠道
			model.setMediaFunsNum(fansNum);
			model.setOperType(reqType);// 
			model.setHeadimgUrl(headimgUrl);
			// 上传图片
			uploadFile(model);
			insertReceiveMsgLog(model.getTaskId(), weiboText, weiboId, reqType);
			if (Constants.WB_GET_USER_TIMELINESSTATUS.equals(reqType)) {
				//insertReceiveMsgTemp(weiboId, beforeWbid, reqType);
				insertReceiveMsgTemp(beforeWbid, weiboId, reqType);
			}
			MessageInfo mi = insertMessage(model);
			String messageString = JSON.toJSONString(mi);
			redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,mi);
			log.info("wechat messageInfo write in redis转发 : "+ messageString);
		}
	}

	/**
	 * 上传图片
	 * 
	 * @param model
	 */
	public void uploadFile(MediaModel model) {

		// 解析图片URL并上传到FTP服务器上
		weibo4j.org.json.JSONArray imageList = model.getPicUrls();
		try {

			// 获取当前时间(精确到天)

			String fileName = "";
			for (int i = 0; i < imageList.length(); i++) {
				String currentReceiveDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
				String path = "/weiboReceiveFile/" + currentReceiveDate + "/" + model.getMediaId() + "/";
				String url = imageList.get(i).toString();

				if (url.lastIndexOf("/") + 1 != 0) {
					fileName = url.substring(url.lastIndexOf("/") + 1);
				}
				InputStream in = PaserPictureUrl(url);
				if (in != null) {
					saveFile(fileName, in, path, model);
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error("附件上传FTP出错:this.getClass().getName()+uploadFile" + e.getMessage());

		}
	}

	/**
	 * 将接收到的信息插入到log表
	 * 
	 * @param msgId
	 * @param content
	 * @param chMsgId
	 */
	public void insertReceiveMsgLog(String msgId, String content, String chMsgId, String operType) {
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		msgLog.setChannelMessageId(chMsgId);
		msgLog.setMessageData(content);
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setChannelAccount(jsonInfo.getString("uid"));
		msgLog.setMessageId(msgId);
		msgLog.setOperType(operType);
		msgLog.setChannelId("1005");
		receiveMessageLogDao.insert(msgLog);
	}

	/**
	 * 将接收到的信息插入到temp表
	 * 
	 * @param msgId
	 * @param content
	 * @param chMsgId
	 */
	public void insertReceiveMsgTemp(String chMsgId, String msgTempId, String operType) {
		ReceiveMessageTemp msgLog = new ReceiveMessageTemp();
		msgLog.setChannelMessageId(chMsgId);
		msgLog.setMessageTempId(msgTempId);
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setChannelAccount(jsonInfo.getString("uid"));
		msgLog.setOperType(operType);
		//msgLog.setcha
		receiveMessageTempDao.insert(msgLog);
	}

	/**
	 * 将接收到的信息插入messageinfo表
	 * 
	 * @param model
	 * @return
	 */
	public MessageInfo insertMessage(MediaModel model) {
		MessageInfo message = null;
		// 微博信息不需要拦截等操作，直接插入到多媒体正式表
		model.setMediaIsAssign("0");
		model.setMediaIsDo("1");
		//weibo 消息type
		String weiboMessageType ="";
		// 获取租户id
		String tenant = jsonInfo.getString("tenant");
		RequestSessionbean requestSessionbean = new RequestSessionbean(model.getUid(), jsonInfo.getString("uid"), Platform.SINAMB.getCode(), tenant,model.getHeadimgUrl());
		log.info("user----------:"+jsonInfo.getString("uid"));
		log.info("jsonInfo----:"+jsonInfo);
		ChannelConfig channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + jsonInfo.getString("uid"));
		//requestSessionbean.setSkillType(channelConfig.getRealTime());
		
		// TODO 区分不同的微博消息类型 ,session save skill_type
		if(model.getOperType().equals(Constants.WB_GETMENTIONS) || model.getOperType().equals(Constants.WB_GETCOMMENTMENTIONS) || model.getOperType().equals(Constants.WB_GET_USER_TIMELINESSTATUS)){
			String wbGetAtMe = PropertiesUtil.getProperty(PropertiesUtil.WEIBO, "wbGetAtMe");
			requestSessionbean.setSkillType(wbGetAtMe);
			weiboMessageType = wbGetAtMe;
		}else if(model.getOperType().equals(Constants.WB_GET_COMMENT_LIST_BYUSER) || model.getOperType().equals(Constants.WB_GET_COMMENT_LIST)){
			String wbGetComments = PropertiesUtil.getProperty(PropertiesUtil.WEIBO, "wbGetComments");
			requestSessionbean.setSkillType(wbGetComments);
			weiboMessageType = wbGetComments;
		}else{
			requestSessionbean.setSkillType(channelConfig.getRealTime());
			weiboMessageType = channelConfig.getRealTime();
		}

		requestSessionbean.setNickname(model.getMediaSender());
		//商业类型
		if(model.getOperType()!=null && !"".equals(model.getOperType())){
			requestSessionbean.setBusinessType(model.getOperType());
		}
		model.getOperType();
		ReceiveMessageLog receivelog =new ReceiveMessageLog();
		
		receivelog.setChannelMessageId(model.getMid());
		receivelog.setChannelAccount(jsonInfo.getString("uid"));
		receivelog.setTenantId(tenant);
		receivelog.setOperType(model.getOperType());
		ReceiveMessageLog receiveMessageLog = receiveMessageLogDao.loadByChannelMessageId(receivelog);
		if (receiveMessageLog != null) {
			model.setHtmlContent(receiveMessageLog.getMessageData());
		}
		SensitiveLog sensitiveLog = new SensitiveLog(Platform.SINAMB.getCode(), tenant, model.getUid(), jsonInfo.getString("uid"), model.getMediaContent());
		if (sensitiveService.filter(sensitiveLog)) {
			log.info("评论消息已经被过滤" + JSON.toJSONString(sensitiveLog));
			message = null;
		} else {
			log.info("请求的requestSessionbean" + JSON.toJSONString(requestSessionbean));
			try {
				//增加weibo的ID区分会话类型
				requestSessionbean.setIdCard(model.getMediaId());
				//微博昵称传递
				requestSessionbean.setNickname(model.getMediaSender());
				SessionInfo sessioninfo = LocalsessionService.getSession(requestSessionbean);
				message = MsgConvertUtil.media2Message(Platform.SINAMB.getCode(), model);
				message.setSessionId(sessioninfo.getSessionId());
				message.setTenantCode(sessioninfo.getTenantCode());
				System.out.println(message.getContent());
				message.setPicUrls(model.getPicUrls().toString());
				message.setSkillType(weiboMessageType);
				
				JSONObject jsonReq = JSONObject.parseObject(message.getContent());
				String param = jsonReq.getString("param");
				JSONObject contentReq = JSONObject.parseObject(param);
				//微博id
				String weiboId = contentReq.getString("weiboid");
				
				log.info("Constants.WB_GETCOMMENTMENTIONS==="+Constants.WB_GETCOMMENTMENTIONS+"::reTtype="+model.getOperType());
				if(Constants.WB_GETCOMMENTMENTIONS.equals(model.getOperType()) && !"".equals(model.getYuanWeiboUserid())){
					jsonReq.put("YuanWeiboUserid",model.getYuanWeiboUserid());
				}
				if(Constants.WB_GETMENTIONS.equals(model.getOperType())){
					jsonReq.put("YuanWeiboUserid",model.getUid());
				}
				
				jsonReq.put("picurls",message.getPicUrls().toString());
				 message.setContent(jsonReq.toString());
				messageInfoDao.insert(message);
				if(weiboId!=null){
					WeiboComentsSum weiboComentsSum = new WeiboComentsSum();
					weiboComentsSum.setId(weiboId);
					List<WeiboComentsSum> weiboComentsList = weiboCommentsDao.query(weiboComentsSum);
					 if(weiboComentsList.size()>0){
						// mi.setComments(weiboComentsList.get(0).getComments());
						//mi.setReposts(weiboComentsList.get(0).getReposts());
						//mi.setAttitudes(weiboComentsList.get(0).getAttitudes());
						 
						 jsonReq.put("comments",weiboComentsList.get(0).getComments());
						 jsonReq.put("reposts",weiboComentsList.get(0).getReposts());
						 jsonReq.put("attitudes",weiboComentsList.get(0).getAttitudes());
					 }else{
						 jsonReq.put("comments",0);
						 jsonReq.put("reposts",0);
						 jsonReq.put("attitudes",0);
					 }
					 message.setContent(jsonReq.toString());
				}
			} catch (Exception e) {
				System.out.println("yichang:---------"+e);
			}
			
		}
		return message;
	}

	/**
	 * 给MIR发送HTTP请求
	 * 
	 * @param msgInfo
	 */
	public void sendContentToMir(MessageInfo msgInfo) {
		String messageString = JSON.toJSONString(msgInfo);
		// 去掉转码时候的转义符
		JSONObject messageJson = JSON.parseObject(messageString);
		messageJson.put("content", BaseService.getJsonObj(messageJson.getString("content")));
		// 发送json
		//FIXME NetUtil
		String result = NetUtil.send(initService.getSysParam(Constants.MIR_ADDRESS).getParamValue() + mirUrl, "POST", messageJson.toJSONString());

		log.info(result + "result");
	}

	/*// 解析weibo
	public void InsertMessage(MediaModel model, String tenant, JSONArray picUrls) {
		try {
			// mediaReceiveDao.WeiboaddInitTask(model);
			String result = "";
			// 微博信息不需要拦截等操作，直接插入到多媒体正式表
			model.setMediaIsAssign("0");
			model.setMediaIsDo("1");
			try {
				RequestSessionbean requestSessionbean = new RequestSessionbean(model.getUid(), jsonInfo.getString("uid"), channelCode, tenant);
				SensitiveLog sensitiveLog = new SensitiveLog(channelCode, tenant, model.getUid(), jsonInfo.getString("uid"), model.getMediaContent());
				if (sensitiveService.filter(sensitiveLog)) {
					log.info("微博消息已经被过滤" + JSON.toJSONString(sensitiveLog));
				} else {
					log.info("请求的requestSessionbean" + JSON.toJSONString(requestSessionbean));

					SessionInfo sessioninfo = LocalsessionService.getSession(requestSessionbean);
					// 封装消息体

					MessageInfo message = MsgConvertUtil.media2Message(channelCode, model);
					message.setSessionId(sessioninfo.getSessionId());
					message.setTenantCode(sessioninfo.getTenantCode());
					message.setAcceptedAccount(jsonInfo.getString("uid"));
					messageInfoDao.insert(message);
					String messageString = JSON.toJSONString(message);
					// 去掉转码时候的转义符
					JSONObject messageJson = JSON.parseObject(messageString);
					messageJson.put("content", BaseService.getJsonObj(messageJson.getString("content")));
					// 发送json
					result = NetUtil.send(initService.getSysParam(Constants.MIR_ADDRESS).getParamValue() + mirUrl, "POST", messageJson.toJSONString());

					log.info(result + "result");
				}

			} catch (Exception e) {
				log.error("存储数据异常!", e);
			}

		} catch (Exception e) {
			// mediaReceiveDao.addReceiveErrorLog("3",e.getMessage(),
			// model.getMediaReceiver(), model.getMediaSender(), "2",
			// model.getMediaTitle(), model.getMediaContent(),
			// model.getTaskId());
			log.error(e.getMessage(), e);
		}
	}*/

	// 解析获取到的图片URL,并以IO流形式返回
	public InputStream PaserPictureUrl(String urlPath) throws IOException {
		URL url;
		java.io.BufferedInputStream bis = null;
		try {
			if (Util.NullToString(urlPath).equals("")) {
				return null;
			} else {
				url = new URL(urlPath);
				bis = new BufferedInputStream(url.openStream());
			}

		} catch (MalformedURLException e) {
			e.printStackTrace();
		}

		return bis;
	}

	private String saveFile(String fileName, InputStream in, String pathFile, MediaModel model) {
		// 将附件上传到指定FTP目录上去
		IdealFtpClient ftpClient = null;
		String fileName2 = "";
		try {
			ftpClient = new IdealFtpClient();

			List list = commonDao.getSysParam("FTP_UPLOAD_URL");
			Map map = (Map) list.get(0);
			String code_desc = (String) map.get("CODE_DESC");

			String[] ftp = code_desc.split(":");

			ftpClient.connectServer((String) map.get("CODE_VALUE"), ftp[0], ftp[1], ftp[2]);
			// ftpClient.connectServer("127.0.0.1", "yuankang", "yuankang");
			ftpClient.changeDir(pathFile);

			if (ftpClient.isExists(fileName)) {
				fileName2 = "[1]" + fileName;
			} else {
				fileName2 = fileName;
			}

			ftpClient.uploadFile(in, fileName2);

			ftpClient.close();

			// 记录上传路径
			model.setMediaFileName(fileName2);
			model.setMediaFilePath(pathFile);
			mediaReceiveDao.addTaskFile(model);

		} catch (Exception e1) {
			log.error("附件上传FTP出错:" + e1.getMessage());
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
		return fileName2;
	}

	public AccessToken getAccessTokenByCode(String code, String client_id, String client_secret, String redirect_uri) throws WeiboException {
		HttpClient client = new HttpClient();
		return new AccessToken(client.post(WeiboConfig.getValue("accessTokenURL"), new PostParameter[] { new PostParameter("client_id", client_id), new PostParameter("client_secret", client_secret),
				new PostParameter("grant_type", "authorization_code"), new PostParameter("code", code), new PostParameter("redirect_uri", redirect_uri) }, false));
	}

	public static StatusWapper constructWapperStatus(JSONObject jsonStatus) throws WeiboException, weibo4j.org.json.JSONException {
		com.alibaba.fastjson.JSONArray statuses = null;
		try {
			if (jsonStatus.containsKey("statuses")) {
				statuses = jsonStatus.getJSONArray("statuses");
			}
			if (jsonStatus.containsKey("reposts")) {
				statuses = jsonStatus.getJSONArray("reposts");
			}
			int size = statuses.size();
			List<Status> status = new ArrayList<Status>(size);
			for (int i = 0; i < size; i++) {
				status.add(new Status(statuses.getJSONObject(i).toString()));
			}
			long previousCursor = jsonStatus.getLong("previous_cursor");
			long nextCursor = jsonStatus.getLong("next_cursor");
			long totalNumber = jsonStatus.getLong("total_number");
			String hasvisible = jsonStatus.getString("hasvisible");
			return new StatusWapper(status, previousCursor, nextCursor, totalNumber, hasvisible);
		} catch (JSONException jsone) {
			throw new WeiboException(jsone);
		}
	}

}
