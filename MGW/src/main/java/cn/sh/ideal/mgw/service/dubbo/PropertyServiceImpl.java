/**
 * 
 */
package cn.sh.ideal.mgw.service.dubbo;

import org.springframework.stereotype.Component;

import cn.sh.ideal.mgw.service.PropertyService;
import cn.sh.ideal.mgw.utils.PropertiesUtil;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.service.dubbo
 * @typeName PropertyServiceImpl
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月14日 上午11:32:55
 * @version 
 */
@Component("propertyService")
public class PropertyServiceImpl implements PropertyService {

	/* 
	 * @see cn.sh.ideal.mgw.service.PropertyService#getProperties(java.lang.String, java.lang.String)
	 */
	@Override
	public String getProperties(String filePath, String fileName) {
		
		try {
			return PropertiesUtil.getProperty(filePath, fileName);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
		
	}

}
