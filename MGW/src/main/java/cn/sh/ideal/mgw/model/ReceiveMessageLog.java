package cn.sh.ideal.mgw.model;

import java.io.Serializable;

public class ReceiveMessageLog implements Serializable{

	 private static final long serialVersionUID = 1L;
	 
	 
	 private Integer id;
	 private String messageId;
	 private String channelMessageId;
	 private String channelId;
	 private String messageData;
	 private String tenantId;
	 private String channelAccount;
	 private String operType;
	 
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getMessageId() {
		return messageId;
	}
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}
	public String getChannelMessageId() {
		return channelMessageId;
	}
	public void setChannelMessageId(String channelMessageId) {
		this.channelMessageId = channelMessageId;
	}
	public String getChannelId() {
		return channelId;
	}
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
	public String getMessageData() {
		return messageData;
	}
	public void setMessageData(String messageData) {
		this.messageData = messageData;
	}
	public String getTenantId() {
		return tenantId;
	}
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	public String getChannelAccount() {
		return channelAccount;
	}
	public void setChannelAccount(String channelAccount) {
		this.channelAccount = channelAccount;
	}
	public String getOperType() {
		return operType;
	}
	public void setOperType(String operType) {
		this.operType = operType;
	}

}
