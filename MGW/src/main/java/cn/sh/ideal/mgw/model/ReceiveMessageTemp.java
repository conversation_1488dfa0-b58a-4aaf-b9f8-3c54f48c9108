package cn.sh.ideal.mgw.model;

import java.io.Serializable;

public class ReceiveMessageTemp implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	 private Integer id;
	 private String messageTempId;
	 private String channelMessageId;
	 private String tenantId;
	 private String channelAccount;
	 private String operType;
	 private String createTime;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getMessageTempId() {
		return messageTempId;
	}
	public void setMessageTempId(String messageTempId) {
		this.messageTempId = messageTempId;
	}
	public String getChannelMessageId() {
		return channelMessageId;
	}
	public void setChannelMessageId(String channelMessageId) {
		this.channelMessageId = channelMessageId;
	}
	public String getTenantId() {
		return tenantId;
	}
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	public String getChannelAccount() {
		return channelAccount;
	}
	public void setChannelAccount(String channelAccount) {
		this.channelAccount = channelAccount;
	}
	public String getOperType() {
		return operType;
	}
	public void setOperType(String operType) {
		this.operType = operType;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	 
	 
}
