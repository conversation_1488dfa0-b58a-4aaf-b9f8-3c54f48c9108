package cn.sh.ideal.mgw.service.local.impl;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.model.QQBean;
import cn.sh.ideal.mgw.model.QQJoinBean;
import cn.sh.ideal.mgw.model.TestXmlModel;
import cn.sh.ideal.mgw.model.return0x.Return0xField;
import cn.sh.ideal.mgw.model.return0x.Return0xModel;
import cn.sh.ideal.mgw.model.return0x.Return0xProperty;
import cn.sh.ideal.mgw.model.return0x.Return0xResp;
import cn.sh.ideal.mgw.service.local.QqService;
import cn.sh.ideal.mgw.utils.CharacterUtil;
import cn.sh.ideal.mgw.utils.EncryptUtil;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.Return0xUtil;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.util.Constants;

import com.alibaba.fastjson.JSONObject;

@Service("qqService")
public class QqServiceImpl implements QqService {

	private static final Logger log = Logger.getLogger(QqServiceImpl.class);
	
	private String qqUrl = null;
	
	private String qqHostNumber = null;
	
	private String tenantCode = null;
	
	private static String closeSessionUrl = (String) PropertiesUtil.getProperty(PropertiesUtil.APP, "session.closeUrl");
	
	@Autowired
	private SysInitService initService;
	
	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;
	
	/**
	 * 初始化操作
	 */
	@PostConstruct
	public void init() {
		log.info("加载QQ调用接口配置……");
		ChannelConfig channelConfig = new ChannelConfig();
		channelConfig.setChannelEnable("1");
		channelConfig.setChannelCode(Platform.QQ.getCode());
		List<ChannelConfig> list = channelConfigDao.query(channelConfig);
		if(list != null && list.size() > 0){
			ChannelConfig channelConfigTemp = list.get(0);
			JSONObject configObj = JSONObject.parseObject(channelConfigTemp.getAccountConfig());			
			qqUrl = (String) configObj.get("url");
			qqHostNumber = channelConfigTemp.getChannelAccount();
			tenantCode = channelConfigTemp.getTenantCode();
		}
		else
			qqUrl = "http://*************:8004";
		log.info("加载QQ调用路径" + qqUrl); 
	}
	/**
	 * 登录
	 * @param systemId		接入号ID 17219
	 * @param loginUsername	登录客服用户名 1008
	 * @param pwd
	 * @param loginDate		登录日期
	 */
	public Return0xModel qq0x01(String systemId, String loginUsername, String md5pwd, String loginDate){		
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("SystemId", systemId);
		temp.put("LoginUsername", loginUsername);					
		temp.put("EncSystemId", EncryptUtil.aesEncrypt(systemId, md5pwd));
		temp.put("EncLoginUsername", EncryptUtil.aesEncrypt(loginUsername, md5pwd));
		temp.put("OpTime", loginDate);		
		//LoginFlag 0:正常登录 1:强行登陆
		temp.put("LoginFlag", "1");
		//登录时设置的状态信息 默认1
		temp.put("Status", "1");
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x01");
		txm.setFieldsValue(temp);
		//发送 POST 请求
        String result = NetUtil.send(qqUrl, NetUtil.POST, txm.returnXmlStr());
//		System.out.println(result);
        Return0xModel r0m = Return0xUtil.getReturn0xModel(result,"response");
        return r0m;
	}
	
	/**
	 * 登出
	 * @param systemId		接入号ID 17219
	 * @param loginUsername	登出客服用户名 1008
	 * @param logoutDate	登出日期
	 */
	public Return0xModel qq0x01c(String systemId, String loginUsername, String logoutDate){
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("SystemId", systemId);
		temp.put("LoginUsername", loginUsername);					
		temp.put("OpTime", logoutDate);
		
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x1c");
		txm.setFieldsValue(temp);
//		txm.setMsgValue("testtest");
		log.info("===qq0x01c request=" + txm.returnXmlStr());
		//发送 POST 请求
        String result = NetUtil.send(qqUrl, NetUtil.POST, txm.returnXmlStr());
        Return0xModel r0m = Return0xUtil.getReturn0xModel(result,"response");
        return r0m;
	}
	
	/**
	 * 消息传输协议
	 */
	public Return0xModel qq0x04(String systemId, String loginUserId, String fromUserName, String toUserName, String content){
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("SystemId", systemId);
		temp.put("LoginUserId", loginUserId);
		temp.put("SubCommand", "1");			
		temp.put("FromUserType", "1");
		//消息发送者
		temp.put("FromUserName", fromUserName);
		temp.put("ToUserType", "1");
		//消息接受者
		temp.put("ToUserName", toUserName);		
		temp.put("MsgType", "0");
		temp.put("SendType", "0");
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x04");
		txm.setFieldsValue(temp);
		try {
			log.info("===qq0x04 content=" + content + "___" + CharacterUtil.getEncoding(content));
			if("gb2312".equals(CharacterUtil.getEncoding(content))){
				String gbk = EncryptUtil.encodeStr(content, "gbk");
				log.info("===qq0x04 encodeStr gbk=" + gbk);
				txm.setMsgValue(gbk);
			}
			else{
				String gbk = null;
				String utf8 = new String(content.getBytes("UTF-8"));  
				log.info("===qq0x04 encodeStr utf8=" + utf8);
				gbk = EncryptUtil.encodeStr(content, "gbk");
				log.info("===qq0x04 encodeStr gbk=" + gbk);
				txm.setMsgValue(gbk);
			}			
			log.info("===qq0x04 request=" + txm.returnXmlStr());
			//发送 POST 请求
	        String result = NetUtil.send(qqUrl, NetUtil.POST, txm.returnXmlStr());
	        Return0xModel r0m = Return0xUtil.getReturn0xModel(result,"response");
	        return r0m;
		} catch (UnsupportedEncodingException e) {
			log.error(" qq0x04 " + e.getMessage(), e);
		} catch (Exception e) {
			log.error(" qq0x04 " + e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 结束会话协议
	 * @param systemId
	 * @param loginUserId
	 * @param openId
	 * @param hostImNumber
	 * @return
	 */
	public Return0xModel qq0x17(String systemId, String loginUserId, String openId, String hostImNumber){
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("SystemId", systemId);
		temp.put("LoginUserId", loginUserId);
		temp.put("OpenId", openId);			
		temp.put("HostImNumber", hostImNumber);
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x17");
		txm.setFieldsValue(temp);
		log.info("===qq0x17 request=" + txm.returnXmlStr());
		//发送 POST 请求
        String result = NetUtil.send(qqUrl, NetUtil.POST, txm.returnXmlStr());
        Return0xModel r0m = Return0xUtil.getReturn0xModel(result,"response");
        return r0m;
	}
	
	/**
	 * 拉取客服缓存协议	根据对外接口的返回值做逻辑处理
	 * @param loginUserId	实际对应登录协议返回中的StaffId
	 * @return
	 */
	public Return0xResp qq0x32(String loginUserId){
		List<QQJoinBean> qqJoinList = null;
		Return0xResp r0xResp = new Return0xResp();
		Return0xModel qq0x32Model = qq0x32TInterface(loginUserId);
		if("0".equals(qq0x32Model.getResult().getCode())){
        	List<Return0xModel> qq0x32List = qq0x32Model.getResponse().getReqitem();
        	if(qq0x32List != null && qq0x32List.size() > 0){
        		qqJoinList = new ArrayList<QQJoinBean>();        		
        		for(int j = 0; j < qq0x32List.size(); j++){
        			QQJoinBean qqJoinBean = new QQJoinBean();
        			Return0xModel qq0x32SingleModel = qq0x32List.get(j);
        			if("0x30".equalsIgnoreCase(qq0x32SingleModel.getCommand())){
        				r0xResp.setCode("1");
        				return r0xResp;
        			}
        			List<Return0xField> qq0x32SPList = qq0x32SingleModel.getResponse().getProtocolfields();
        			for(int h = 0; h < qq0x32SPList.size(); h++){
        				Return0xField r0xField = qq0x32SPList.get(h);
        				if("UserName".equalsIgnoreCase(r0xField.getKey())){
        					qqJoinBean.setOpenId(r0xField.getValue());
        				}
        				else if("UserType".equalsIgnoreCase(r0xField.getKey())){
        					qqJoinBean.setUserType(r0xField.getValue());
        				}
        			}
        			List<Return0xProperty> qq0x32SSList = qq0x32SingleModel.getResponse().getSessionpropertys();
        			for(int h = 0; h < qq0x32SSList.size(); h++){
        				Return0xProperty r0xProperty = qq0x32SSList.get(h);
        				if("hostImNumber".equalsIgnoreCase(r0xProperty.getKey())){
        					try {
								qqJoinBean.setHostImNumber(EncryptUtil.decodeStr(r0xProperty.getValue(),"gbk"));
							} catch (UnsupportedEncodingException e) {
								log.error(e.getMessage(), e);
							}
        				}
        				else if("imUserNick".equalsIgnoreCase(r0xProperty.getKey())){
        					try {
								qqJoinBean.setImUserNick(EncryptUtil.decodeStr(r0xProperty.getValue(),"gbk"));
							} catch (UnsupportedEncodingException e) {
								log.error(e.getMessage(), e);
							}
        				}
        			}
        			qqJoinList.add(qqJoinBean);
        			log.info(String.format("qq0x32 拉取客服缓存协议,UserName:%s,hostImNumber:%s", qqJoinBean.getOpenId(),qqJoinBean.getHostImNumber()));
        		}
        	}						        	
        }		
        else{								
			log.info(String.format("qq0x32 拉取客服缓存协议失败,LoginUserId:%s", loginUserId));
		}	
		//0 表示拉取成功
		r0xResp.setCode("0");
		r0xResp.setQqJoinList(qqJoinList);
        return r0xResp;
	}
	
	public List<String> qq0x33(String loginUserId, String openId, String hostImNumber, String sequence, String size){
		List<String> contentList = new LinkedList<String>();
		Return0xModel r0m = this.qq0x33TInterface(loginUserId, openId, hostImNumber, sequence, size);
		
		List<Return0xField> protocolfieldsList = r0m.getResponse().getProtocolfields();
		if(protocolfieldsList != null && protocolfieldsList.size() > 0){
			for(int i = 0; i < protocolfieldsList.size(); i++){
				Return0xField r0xField = protocolfieldsList.get(i);
	        	if("MaxMsgSeq".equalsIgnoreCase(r0xField.getKey())){
	        		log.info("MaxMsgSeq=" + r0xField.getValue());
	        	}        	
			}
		}
		
		List<Return0xModel> reqitemList = r0m.getResponse().getReqitem();		
		if(reqitemList != null && reqitemList.size() > 0){
			Collections.reverse(reqitemList);
			for(int i = 0; i < reqitemList.size(); i++){
				Return0xModel qq0x33SingleModel = reqitemList.get(i);
				try {
					contentList.add(EncryptUtil.decodeStr(qq0x33SingleModel.getResponse().getMsg(), "gbk"));
					log.info("qq0x33SingleModel.getResponse().getMsg _" + qq0x33SingleModel.getResponse().getMsg() + "_" + EncryptUtil.decodeStr(qq0x33SingleModel.getResponse().getMsg(), "gbk"));
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
		}
		return contentList;
	}
	
	/**
	 * 拉取客服缓存协议 对外接口
	 * @param loginUserId	实际对应登录协议返回中的StaffId
	 * @return
	 */
	private Return0xModel qq0x32TInterface(String loginUserId){
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("LoginUserId", loginUserId);
		temp.put("isWebRequest", "2");					
		temp.put("convType", "2");
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x32");
		txm.setFieldsValue(temp);
		log.info("===qq0x32 request=" + txm.returnXmlStr());
		//发送 POST 请求
		String result = NetUtil.send(qqUrl, NetUtil.POST, txm.returnXmlStr());
		log.info("===qq0x32 before transformat result=" + result);
		result = result.replaceAll("[\\x00-\\x08\\x0b-\\x0c\\x0e-\\x1f]", "");
		log.info("===qq0x32 after transformat result=" + result);
        Return0xModel r0m = Return0xUtil.getReturn0xModel(result,"response");
        return r0m;
	}
	
	/**
	 * 拉取消息
	 * @param loginUserId
	 * @param openId
	 * @param hostImNumber
	 * @param sequence	开始序号
	 * @param size		每次拉取条数
	 * @return
	 */
	private Return0xModel qq0x33TInterface(String loginUserId, String openId, String hostImNumber, String sequence, String size){
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("LoginUserId", loginUserId);
		temp.put("OpenId", openId);		
		temp.put("HostImNumber", hostImNumber);
		temp.put("cache-sequence", sequence);
		temp.put("cache-size", size);
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x33");
		txm.setFieldsValue(temp);
		log.info("===qq0x33 request=" + txm.returnXmlStr());
		//发送 POST 请求
        String result = NetUtil.send(qqUrl, NetUtil.POST, txm.returnXmlStr());
        log.info("===qq0x33 result=" + result);
        Return0xModel r0m = Return0xUtil.getReturn0xModel(result,"response");
        return r0m;
	}
	
	/**
	 * 调用坐席会话关闭接口
	 */
	private JSONObject closeSession(String sessionId,String tenantCode){
			JSONObject json = new JSONObject();
			JSONObject jsonData = new JSONObject();
			JSONObject jsonResult = new JSONObject();
		
			try {
			//***************调用SM接口
			
			json.put(Constants.PARAM_SESSION_ID, sessionId);
			json.put(Constants.PARAM_TENANT_CODE,tenantCode);
			json.put(Constants.PARAM_STATUS,"9");
			
			json.put(Constants.PARAM_DATA,jsonData);
			
			SysParam  sysParam =  initService.getSysParam("SERVICE_INVOKER_ADDRESS");
			String url = sysParam.getParamValue() + closeSessionUrl;
			log.info("QQ调用会话关闭接口 url[" + url + "]");
			String result = NetUtil.send(url, NetUtil.POST,json.toJSONString());
			log.info(Thread.currentThread().getName() +"reponse SM ["+ result + "]");
			
			jsonResult = JSONObject.parseObject(result);
			log.info("QQ调用会话关闭接口 jsonResult[" + jsonResult + "]");
		} catch (Exception e) {
			log.info("QQ调用会话关闭接口失败！sessionId["+sessionId, e);
		}			
		return jsonResult;	
	}
	
	/**
	 * 关闭坐席会话
	 * @param qqJoinList
	 * @param qqBean
	 */
	public void closeQQSession(List<QQJoinBean> qqJoinList){
		if(qqJoinList != null && qqJoinList.size() > 0){
			Set<String> keys = redisDao.getKeysByPattern("*" + qqHostNumber);
			log.info("QQ redis qqHostNumber=" + qqHostNumber + "___keys="+keys);
			for(String key : keys){
				QQBean qqBean = (QQBean) redisDao.readValue(key);
				log.info("QQ redis qqBean.getSessionId=" + qqBean.getSessionId());
				boolean flag = true;
				for(int j = 0; j < qqJoinList.size(); j++){
			        QQJoinBean qqJoinBean = qqJoinList.get(j);
			        if(key.equals(qqJoinBean.getOpenId() + "#" + qqJoinBean.getHostImNumber())){
			        	flag = false;
			        	break;
					}
				}
				if(flag){
					log.info("QQ_closeSession qqBean.getSessionId=" + qqBean.getSessionId());
					closeSession(qqBean.getSessionId(),tenantCode);
					redisDao.deleteValue(key);				
				}
			}
			
		}
	}
}
