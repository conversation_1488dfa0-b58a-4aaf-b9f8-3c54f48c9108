package cn.sh.ideal.mgw.model;

import java.util.List;

public class WeiboUserData {

	//用户是否订阅该账号，值为0时，代表此用户没有订阅该账号；
	private String subscribe;
	
	private String uid;
	
	private String nicname;
	//性别，1：男、2：女、0：未知
	private String sex;
	
	private String city;

	private String country;
	
	private String province;
	//用户当前的语言版本，zh_CN 简体，zh_TW 繁体，en英语
	private String language;
	//用户头像地址（中图），50×50像素
	private String headimgurl;
	//用户头像地址（大图），180×180像素
	private String headimgurl_large;
	//用户头像地址（高清），高清头像原图
	private String headimgurl_hd;
	//订阅时间
	private String subscribe_time;
	//该用户是否关注access_token中的uid，1：是，0：否
	private String follow;
	
	public String getSubscribe() {
		return subscribe;
	}
	public void setSubscribe(String subscribe) {
		this.subscribe = subscribe;
	}
	public String getUid() {
		return uid;
	}
	public void setUid(String uid) {
		this.uid = uid;
	}
	public String getNicname() {
		return nicname;
	}
	public void setNicname(String nicname) {
		this.nicname = nicname;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getLanguage() {
		return language;
	}
	public void setLanguage(String language) {
		this.language = language;
	}
	public String getHeadimgurl() {
		return headimgurl;
	}
	public void setHeadimgurl(String headimgurl) {
		this.headimgurl = headimgurl;
	}
	public String getHeadimgurl_large() {
		return headimgurl_large;
	}
	public void setHeadimgurl_large(String headimgurl_large) {
		this.headimgurl_large = headimgurl_large;
	}
	public String getHeadimgurl_hd() {
		return headimgurl_hd;
	}
	public void setHeadimgurl_hd(String headimgurl_hd) {
		this.headimgurl_hd = headimgurl_hd;
	}
	public String getSubscribe_time() {
		return subscribe_time;
	}
	public void setSubscribe_time(String subscribe_time) {
		this.subscribe_time = subscribe_time;
	}
	public String getFollow() {
		return follow;
	}
	public void setFollow(String follow) {
		this.follow = follow;
	}
}
