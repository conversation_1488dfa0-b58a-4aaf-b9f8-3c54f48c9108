package cn.sh.ideal.mgw.utils.sender;

import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.activation.MimetypesFileTypeMap;
import javax.imageio.ImageIO;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import weibo4j.http.HttpClient;
import weibo4j.http.ImageItem;
import weibo4j.http.Response;
import weibo4j.model.Comment;
import weibo4j.model.Emotion;
import weibo4j.model.PostParameter;
import weibo4j.model.Status;
import weibo4j.model.WeiboException;
import weibo4j.org.json.JSONArray;
import weibo4j.org.json.JSONException;
import weibo4j.util.WeiboConfig;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.MsgType;
import cn.sh.ideal.mgw.base.WeiboInf;
import cn.sh.ideal.mgw.model.StatusCount;
import cn.sh.ideal.mgw.model.UserCounts;
import cn.sh.ideal.mgw.model.WeiboMenuSendData;
import cn.sh.ideal.mgw.model.WeiboMsgSendData;
import cn.sh.ideal.mgw.model.response.WeiboResponseDTO;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class WeiboSender implements Sender{

	private static HttpClient client = new HttpClient();
	private static final Logger log = Logger.getLogger(WeiboSender.class);
	/**
	 * 微博接口入口
	 * @param reqStr
	 * @return 调用结果
	 */
	@Override
	public  JSONObject customSend(String reqStr,String token){
		JSONObject obj = JSONObject.parseObject(reqStr);
		JSONObject resultObj=new JSONObject();
		//获取微博请求接口
		String type=(String)obj.get(Constants.MSG_PARAM_OPERTYPE);
		WeiboInf reqType=WeiboInf.codeOf(type);
		Status wbStatus=null;
		switch (reqType) {		
			//私信回复
			case WB_GETREPLYMSG:
				resultObj = customSends(obj,token);
				break;
			case WB_CREATEMENU:
				resultObj = createMenu(obj,token);
				break;
			case WB_SHOWMENU:
				resultObj = showMenu(obj,token);
				break;
			case WB_DELMENU:
				resultObj = delMenu(obj,token);
				break;
			case WB_USERINFO:
				resultObj = userInfo(obj,token);
				break;
			default:
				throw new IllegalArgumentException("not supported msgtype");
		}
		return resultObj;
		
	}
	
	//发送客服消息
	public JSONObject customSends(JSONObject obj, String token) {
		JSONObject resultObj = null;
		String msgReplyType=obj.getString(Constants.PARAM_MSG_TYPE);//回复类别，text：纯文本、articles：图文、position：位置，默认为text。
		WeiboMsgSendData weiboMsgSendData = new WeiboMsgSendData();
		weiboMsgSendData.setAccess_token(token);
		weiboMsgSendData.setReceiver_id(obj.getString("acceptedAccount"));
		//取值为1时，通过本接口发送的消息会进入发送方的私信箱；取值为0时，通过本接口发送的消息不会进入发送方的私信箱
		weiboMsgSendData.setSave_sender_box("1");
		String msgType = obj.getString("msgType");
		weiboMsgSendData.setType(msgType);
		if("text".equals(msgType)){
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("text", obj.getString("content"));
			weiboMsgSendData.setData(jsonObj.toJSONString());
		}
		else if("articles".equals(msgType)){
			JSONObject jsonObj = new JSONObject();
			com.alibaba.fastjson.JSONArray articlesObj = obj.getJSONArray(msgType);
			/**
			for(int i=0;i<articlesObj.size();i++){
				JSONObject singleObj = articlesObj.getJSONObject(i);
				log.info("###################");
				log.info("display_name=" + singleObj.getString("display_name") + "__summary=" + singleObj.getString("summary"));
				log.info("###################");
			}
			*/
			jsonObj.put("articles", articlesObj);
			weiboMsgSendData.setData(jsonObj.toJSONString());
		}
		else{
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("longitude", obj.getString("longitude"));
			jsonObj.put("latitude", obj.getString("latitude"));
			weiboMsgSendData.setData(jsonObj.toJSONString());
		}
					
		try {
			resultObj = WeiboSender.msgReply(msgReplyType, weiboMsgSendData, token);
		} catch (WeiboException e) {
			e.printStackTrace();
		}
		return resultObj;
	}
	
	/**
	 * 创建菜单
	 * @param obj
	 * @param token
	 * @return
	 */
	public JSONObject createMenu(JSONObject obj, String token) {
		JSONObject resultObj = null;
		WeiboMenuSendData weiboMenuSendData = new WeiboMenuSendData();
		weiboMenuSendData.setAccess_token(token);
		obj.remove(Constants.MSG_PARAM_OPERTYPE);
//		String buttonStr = obj.getString("button");		
		log.info("#######################################");
		log.info("buttonObj=" + obj.toJSONString());
		log.info("#######################################");
		weiboMenuSendData.setMenus(obj.toJSONString());		
		
		try {
			resultObj = WeiboSender.createWBMenu(weiboMenuSendData, token);
		} catch (WeiboException e) {
			e.printStackTrace();
		}		
		return resultObj;
	}
	
	/**
	 * 查询菜单
	 * @param obj
	 * @param token
	 * @return
	 */
	public JSONObject showMenu(JSONObject obj, String token) {
		JSONObject resultObj = null;		
		try {
			resultObj = WeiboSender.showWBMenu(token);
		} catch (WeiboException e) {
			e.printStackTrace();
		}		
		return resultObj;
	}
	
	/**
	 * 删除菜单
	 * @param obj
	 * @param token
	 * @return
	 */
	public JSONObject delMenu(JSONObject obj, String token) {
		JSONObject resultObj = null;		
		try {
			resultObj = WeiboSender.delWBMenu(token);
		} catch (WeiboException e) {
			e.printStackTrace();
		}		
		return resultObj;
	}
	
	public JSONObject userInfo(JSONObject obj, String token) {
		JSONObject resultObj = null;		
		try {
			String uid = obj.getString("uid");
			resultObj = WeiboSender.userWBInfo(uid, token);
		} catch (WeiboException e) {
			e.printStackTrace();
		}		
		return resultObj;
	}
	
	//群发客服消息
	public JSONObject sendAll(JSONObject obj, String token) {
		JSONObject resultObj = null;
		String msgReplyType=obj.getString(Constants.PARAM_MSG_TYPE);//回复类别，text：纯文本、articles：图文、position：位置，默认为text。
		com.alibaba.fastjson.JSONArray toUser = obj.getJSONArray(Constants.PARAM_TOUSER);
		WeiboMsgSendData weiboMsgSendData = new WeiboMsgSendData();
		weiboMsgSendData.setAccess_token(token);		
		//取值为1时，通过本接口发送的消息会进入发送方的私信箱；取值为0时，通过本接口发送的消息不会进入发送方的私信箱
		weiboMsgSendData.setSave_sender_box("1");
		String msgType = obj.getString("msgType");
		weiboMsgSendData.setType(msgType);
		if("text".equals(msgType)){
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("text", obj.getString("content"));
			weiboMsgSendData.setData(jsonObj.toJSONString());
		}
		else if("articles".equals(msgType)){
			JSONObject jsonObj = new JSONObject();
			com.alibaba.fastjson.JSONArray articlesObj = obj.getJSONArray(msgType);
			/**
			for(int i=0;i<articlesObj.size();i++){
				JSONObject singleObj = articlesObj.getJSONObject(i);
				log.info("###################");
				log.info("display_name=" + singleObj.getString("display_name") + "__summary=" + singleObj.getString("summary"));
				log.info("###################");
			}
			*/
			jsonObj.put("articles", articlesObj);
			weiboMsgSendData.setData(jsonObj.toJSONString());
		}
		else{
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("longitude", obj.getString("longitude"));
			jsonObj.put("latitude", obj.getString("latitude"));
			weiboMsgSendData.setData(jsonObj.toJSONString());
		}
		
		for (int i = 0; i < toUser.size(); i++) {
			weiboMsgSendData.setReceiver_id(toUser.getJSONObject(i).toString());
			try {
				resultObj = WeiboSender.msgReply(msgReplyType, weiboMsgSendData, token);
			} catch (WeiboException e) {
				e.printStackTrace();
			}
		}
//		weiboMsgSendData.setReceiver_id(obj.getString("acceptedAccount"));		
		
		return resultObj;
		}
	
	public JSONObject uploadFile(JSONObject json, MsgType type, String token) {
		String wechaturl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_SINAMB + ".MEDIA_URL");

		String fileurl = "";
		String fileName = "";
		if (json.containsKey("media_id")) {
			String fUrl = json.getString("media_id");
			String regex = "/([^/]+)$";

			Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(fUrl);
			if (m.find()) {
				fileurl=fUrl;
				fileName = m.group(1);

			}

		} else {
			fileurl = json.getString("url");
			fileName = json.getString("fileName");
		}
		Map<String, String> fileMap = new HashMap<String, String>();
		fileMap.put("userfile", fileName);

		String respStr = formUpload(wechaturl + token + "&type=" + type.getName().replaceAll("mp", ""), fileMap, fileurl);

		return JSONObject.parseObject(respStr);
	}
	private String formUpload(String urlStr, Map<String, String> fileMap, String fileurl) {
		String res = "";
		HttpURLConnection conn = null;
		String BOUNDARY = "---------------------------123821742118716"; // boundary就是request头和上传文件内容的分隔符
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(30000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);

			OutputStream out = new DataOutputStream(conn.getOutputStream());

			// file
			if (fileMap != null) {
				Iterator<Entry<String, String>> iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Entry<String, String> entry = (Entry<String, String>) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();
					String contentType = new MimetypesFileTypeMap().getContentType(file);
					if (filename.toLowerCase().endsWith(".png")) {
						contentType = "image/png";
					} else if (filename.toLowerCase().endsWith(".mp3")) {
						contentType = "audio/x-mpeg";
					} else if (filename.toLowerCase().endsWith(".wma")) {
						contentType = "application/mswma";
					} else if (filename.toLowerCase().endsWith(".mp4")) {
						contentType = "video/mp4";
					} else if (contentType == null || contentType.equals("")) {
						contentType = "application/octet-stream";
					}

					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"; filename=\"" + filename + "\"\r\n");
					strBuf.append("Content-Type:" + contentType + "\r\n\r\n");

					out.write(strBuf.toString().getBytes());

					URL httpUrl = new URL(fileurl);

					InputStream in = httpUrl.openStream();
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}

			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();

			// 读取返回数据
			StringBuffer strBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				strBuf.append(line).append("\n");
			}
			res = strBuf.toString();
			reader.close();
			reader = null;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			return "{\"errcode\":\"-1\", \"errmsg\":\"" + e.getMessage() + "\"}";
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return res;
	}
	
	@Override
	public JSON multiSend(String json, String token) throws Exception {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WEIBO
				+ ".STATUSES_UPLOAD");

		JSONObject jsonObject = JSONObject.parseObject(json);
		HttpClient client = new HttpClient();
		client.setToken(token);
		JSONObject responseObj = JSONObject.parseObject(client.post(url, new PostParameter[] { 
				new PostParameter("status", jsonObject.getString("status")), 
				new PostParameter("url", "url")}, true).getResponseAsString());
		return responseObj;
	}
	

	/**
	 * 发送私信
	 * @param type
	 * @param weiboMsgSendData
	 * @param token
	 * @return
	 * @throws WeiboException
	 */
	public static JSONObject msgReply(String type, WeiboMsgSendData weiboMsgSendData, String token)
			throws WeiboException {	
		weibo4j.http.HttpClient client = new HttpClient();
		client.setToken(token);
		List<PostParameter> params = new ArrayList<PostParameter>();  
		String msgReplyUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WEIBO + ".CUSTOM_URL");
		log.info("##################################################");
		log.info("WeiboResponseDTO_msgReply_type=" + type + "_msgReplyUrl=" + msgReplyUrl);
		log.info("##################################################");
		log.info("access_token=" + weiboMsgSendData.getAccess_token() + "_type=" + weiboMsgSendData.getType() + "_data=" + weiboMsgSendData.getData());
		log.info("receiver_id=" + weiboMsgSendData.getReceiver_id() + "_save_sender_box=" + weiboMsgSendData.getSave_sender_box());
		log.info("##################################################");
		
		Response resp = client.post(msgReplyUrl, new PostParameter[] {
				new PostParameter("access_token", weiboMsgSendData.getAccess_token()),
				new PostParameter("type", weiboMsgSendData.getType()),
				new PostParameter("data", weiboMsgSendData.getData()),
				new PostParameter("receiver_id", weiboMsgSendData.getReceiver_id()),
				new PostParameter("save_sender_box", weiboMsgSendData.getSave_sender_box())
		});
		log.info("##################################################");
		log.info("WeiboResponseDTO_Response=" + resp);
		log.info("##################################################");
		JSONObject jsonObj = new JSONObject();
		try {
			jsonObj.put("result", resp.asJSONObject().get("result"));
			jsonObj.put("sender_id", resp.asJSONObject().get("sender_id"));
			jsonObj.put("receiver_id", resp.asJSONObject().get("receiver_id"));
			jsonObj.put("type", resp.asJSONObject().get("type"));
			jsonObj.put("data", resp.asJSONObject().get("data"));
		} catch (JSONException e) {
			e.printStackTrace();
		}		
		return jsonObj;
	}
	
	/**
	 * 菜单创建
	 * @param weiboMenuSendData
	 * @param token
	 * @return
	 * @throws WeiboException
	 */
	public static JSONObject createWBMenu(WeiboMenuSendData weiboMenuSendData, String token)
			throws WeiboException {	
		weibo4j.http.HttpClient client = new HttpClient();
		client.setToken(token);
		List<PostParameter> params = new ArrayList<PostParameter>();  
		String createMenuUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WEIBO + ".CREATE_MENU");
		log.info("##################################################");
		log.info("access_token=" + weiboMenuSendData.getAccess_token() + "_menus=" + weiboMenuSendData.getMenus());
		log.info("##################################################");
		
		Response resp = client.post(createMenuUrl, new PostParameter[] {
				new PostParameter("access_token", weiboMenuSendData.getAccess_token()),
				new PostParameter("menus", weiboMenuSendData.getMenus())
		});
		log.info("##################################################");
		log.info("WeiboResponseDTO_Response=" + resp);
		log.info("##################################################");
		JSONObject jsonObj = new JSONObject();
		try {
			if(resp.asJSONObject().get("result") != null){
				jsonObj.put("result", resp.asJSONObject().get("result"));
			}
			else{
				jsonObj.put("request", resp.asJSONObject().get("request"));
				jsonObj.put("error_code", resp.asJSONObject().get("error_code"));
				jsonObj.put("error", resp.asJSONObject().get("error"));
			}			
		} catch (JSONException e) {
			e.printStackTrace();
		}		
		return jsonObj;
	}
	
	/**
	 * 调用微博菜单查询接口
	 * @param token
	 * @return
	 * @throws WeiboException
	 */
	public static JSONObject showWBMenu(String token)
			throws WeiboException {	
		weibo4j.http.HttpClient client = new HttpClient();
		client.setToken(token);
		String showMenuUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WEIBO + ".SHOW_MENU");
		log.info("##################################################");
		log.info("access_token=" + token);
		log.info("##################################################");
		
		Response resp = client.get(showMenuUrl, new PostParameter[] {
				new PostParameter("access_token", token)
		});
		log.info("##################################################");
		log.info("WeiboResponseDTO_Response=" + resp);
		log.info("##################################################");
		JSONObject jsonObj = new JSONObject();
		try {
			if(resp.asJSONObject().get("menu") != null){
				jsonObj.put("result", resp.asJSONObject().get("menu"));
			}
			else{
				jsonObj.put("request", resp.asJSONObject().get("request"));
				jsonObj.put("error_code", resp.asJSONObject().get("error_code"));
				jsonObj.put("error", resp.asJSONObject().get("error"));
			}			
		} catch (JSONException e) {
			e.printStackTrace();
		}		
		return jsonObj;
	}
	
	/**
	 * 调用微博菜单删除
	 * @param token
	 * @return
	 * @throws WeiboException
	 */
	public static JSONObject delWBMenu(String token)
			throws WeiboException {	
		weibo4j.http.HttpClient client = new HttpClient();
		client.setToken(token);
		String delMenuUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WEIBO + ".DEL_MENU");
		log.info("##################################################");
		log.info("access_token=" + token);
		log.info("##################################################");
		
		Response resp = client.post(delMenuUrl, new PostParameter[] {
				new PostParameter("access_token", token)
		});
		log.info("##################################################");
		log.info("WeiboResponseDTO_Response=" + resp);
		log.info("##################################################");
		JSONObject jsonObj = new JSONObject();
		try {
			if(resp.asJSONObject().get("result") != null){
				jsonObj.put("result", resp.asJSONObject().get("result"));
			}
			else{
				jsonObj.put("request", resp.asJSONObject().get("request"));
				jsonObj.put("error_code", resp.asJSONObject().get("error_code"));
				jsonObj.put("error", resp.asJSONObject().get("error"));
			}			
		} catch (JSONException e) {
			e.printStackTrace();
		}		
		return jsonObj;
	}
	
	/**
	 * 调用微博客户查询接口
	 * @param uid
	 * @param token
	 * @return
	 * @throws WeiboException
	 */
	public static JSONObject userWBInfo(String uid,String token)
			throws WeiboException {	
		weibo4j.http.HttpClient client = new HttpClient();
		client.setToken(token);
		String userInfoUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WEIBO + ".USER_INFO");
		log.info("##################################################");
		log.info("access_token=" + token);
		log.info("##################################################");
		
		Response resp = client.post(userInfoUrl, new PostParameter[] {
				new PostParameter("access_token", token),
				new PostParameter("uid", uid)
		});
		log.info("##################################################");
		log.info("WeiboResponseDTO_Response=" + resp);
		log.info("##################################################");
		JSONObject jsonObj = new JSONObject();
		try {
			if(resp.asJSONObject().get("follow") != null && !"0".equals(resp.asJSONObject().get("follow"))){
				jsonObj.put("result", true);
				jsonObj.put("subscribe", resp.asJSONObject().get("subscribe"));
				jsonObj.put("uid", resp.asJSONObject().get("uid"));
				jsonObj.put("nicname", resp.asJSONObject().get("nicname"));
				//性别，1：男、2：女、0：未知
				jsonObj.put("sex", resp.asJSONObject().get("sex"));
				jsonObj.put("city", resp.asJSONObject().get("city"));
				jsonObj.put("country", resp.asJSONObject().get("country"));
				jsonObj.put("province", resp.asJSONObject().get("province"));
				jsonObj.put("language", resp.asJSONObject().get("language"));
				//用户头像地址（中图），50×50像素
				jsonObj.put("headimgurl", resp.asJSONObject().get("headimgurl"));
				//用户头像地址（大图），180×180像素
				jsonObj.put("headimgurl_large", resp.asJSONObject().get("headimgurl_large"));
				//用户头像地址（高清），高清头像原图
				jsonObj.put("headimgurl_hd", resp.asJSONObject().get("headimgurl_hd"));
				jsonObj.put("subscribe_time", resp.asJSONObject().get("subscribe_time"));
				jsonObj.put("follow", resp.asJSONObject().get("follow"));
			}
			else{
				jsonObj.put("request", resp.asJSONObject().get("request"));
				jsonObj.put("error_code", resp.asJSONObject().get("error_code"));
				jsonObj.put("error", resp.asJSONObject().get("error"));
			}			
		} catch (JSONException e) {
			e.printStackTrace();
		}		
		return jsonObj;
	}
	
	/**
	 * 上传图片并发布一条新微博
	 * 
	 * @param status
	 *            要发布的微博文本内容，必须做URLencode，内容不超过140个汉字
	 * @param pic
	 *            要上传的图片，仅支持JPEG、GIF、PNG格式，图片大小小于5M。
	 * @param lat
	 *            纬度，有效范围：-90.0到+90.0，+表示北纬，默认为0.0。
	 * @param long 经度，有效范围：-180.0到+180.0，+表示东经，默认为0.0。
	 * @return Status
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/upload
	 * @since JDK 1.5
	 */
	public static Status uploadStatus(String status, ImageItem item, Float lat,Float longs,String token,String listId,int visible,String annotations
			) throws Exception {
		client.setToken(token);
		return new Status(client.multPartURL(WeiboConfig.getValue("baseURL")
				+ "statuses/upload.json", new PostParameter[] {
				new PostParameter("status", java.net.URLEncoder.encode(status,"UTF-8")),
				new PostParameter("lat", lat),
				new PostParameter("long", longs),
				new PostParameter("list_id", listId),
				new PostParameter("visible", visible),
				new PostParameter("annotations", "")}, item));
	}
	
	/**
	 * 发布一条新微博
	 * 
	 * @param status
	 *            要发布的微博文本内容，必须做URLencode，内容不超过140个汉字
	 * @param lat
	 *            纬度，有效范围：-90.0到+90.0，+表示北纬，默认为0.0。
	 * @param long 经度，有效范围：-180.0到+180.0，+表示东经，默认为0.0。
	 * @param annotations
	 *            元数据，主要是为了方便第三方应用记录一些适合于自己使用的信息，每条微博可以包含一个或者多个元数据，
	 *            必须以json字串的形式提交，字串长度不超过512个字符，具体内容可以自定
	 * @return Status
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/update
	 * @since JDK 1.5
	 */
	public static Status updateStatus(String status, Float lat, Float longs,String listId,int visible,
			String annotations,String token) throws WeiboException {
		client.setToken(token);
		if(listId.equals("")){
		
			return new Status(client.post(WeiboConfig.getValue("baseURL")
					+ "statuses/update.json", new PostParameter[] {
					new PostParameter("status", status),
					new PostParameter("lat", lat),
					new PostParameter("long", longs),
					new PostParameter("annotations", ""),//先默认为空
					new PostParameter("visible", visible)}));
		}else{
			return new Status(client.post(WeiboConfig.getValue("baseURL")
					+ "statuses/update.json", new PostParameter[] {
					new PostParameter("status", status),
					new PostParameter("lat", lat),
					new PostParameter("long", longs),
					new PostParameter("annotations", ""),//先默认为空
					new PostParameter("list_id", listId),
					new PostParameter("visible", visible)}));
		}
	}
	
	public static void main(String[] args) {
		try {
			//Status ss =uploadStatus("test00",new Float(0),new Float(0),"3",0,"0","2.00jVWCsFJ_wqpC3c08ffb379T4LUFE");
			Status ss =updateStatus("nihao",new Float(0),new Float(0),"3",0,"0","2.00nYS2PEJ_wqpCa35601d493eqnd2E");
			
			System.out.println("ss========="+ss.getId());
		} catch (WeiboException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 对一条微博进行评论
	 * 
	 * @param comment
	 *            评论内容，必须做URLencode，内容不超过140个汉字
	 * @param id
	 *            需要评论的微博ID
	 * @param comment_ori
	 *            当评论转发微博时，是否评论给原微博，0：否、1：是，默认为0。
	 * @return Comment
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/comments/create
	 * @since JDK 1.5
	 */
	public static Comment createComment(String comment, String id, Integer comment_ori,String token)
			throws WeiboException {
		client.setToken(token);
		return new Comment(client.post(WeiboConfig.getValue("baseURL")
				+ "comments/create.json", new PostParameter[] {
				new PostParameter("comment", comment),
				new PostParameter("id", id),
				new PostParameter("comment_ori", comment_ori.toString()) }));
	}
	
	
	/**
	 * 回复一条评论
	 * 
	 * @param comment
	 *            评论内容，必须做URLencode，内容不超过140个汉字
	 * @param cid
	 *            需要回复的评论ID
	 * @param id
	 *            需要评论的微博ID
	 * @param without_mention
	 *            回复中是否自动加入“回复@用户名”，0：是、1：否，默认为0。
	 * @param comment_ori
	 *            当评论转发微博时，是否评论给原微博，0：否、1：是，默认为0。
	 * @return Comment
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/comments/reply
	 * @since JDK 1.5
	 */
	public static Comment replyComment(String cid, String id, String comment,
			Integer without_mention, Integer comment_ori,String token ) throws WeiboException {
		client.setToken(token);
		return new Comment(
				client.post(
						WeiboConfig.getValue("baseURL") + "comments/reply.json",
						new PostParameter[] {
								new PostParameter("comment", comment),
								new PostParameter("id", id),
								new PostParameter("cid", cid),
								new PostParameter("without_mention",
										without_mention),
								new PostParameter("comment_ori", comment_ori
										) }));
	}
	
	/**
	 * 根据微博ID删除指定微博
	 * 
	 * @param id
	 *            需要删除的微博ID
	 * @return Status
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/destroy
	 * @since JDK 1.5
	 */
	public static Status destroy(String id,String token ) throws WeiboException {
		client.setToken(token);
		return new Status(client.post(WeiboConfig.getValue("baseURL")
				+ "statuses/destroy.json",
				new PostParameter[] { new PostParameter("id", id) }));
	}
	
	/**
	 * 批量获取指定微博的转发数评论数
	 * 
	 * @param ids
	 *            需要获取数据的微博ID，多个之间用逗号分隔，最多不超过100个
	 * @return
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/emotions
	 * @since JDK 1.5
	 */
	public static JSONArray getStatusesCount(String ids,String token) throws WeiboException {
		client.setToken(token);
		return client.get(
				WeiboConfig.getValue("baseURL") + "statuses/count.json",
				new PostParameter[] { new PostParameter("ids", ids) }).asJSONArray();
	}
	
	/**
	 * 批量获取用户的粉丝数、关注数、微博数
	 * 
	 * @param uids
	 *            需要获取数据的用户UID，多个之间用逗号分隔，最多不超过100个
	 * @return jsonobject
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/users/domain_show
	 * @since JDK 1.5
	 */
	public static List<UserCounts> getUserCount(String uids,String token) throws WeiboException {
		client.setToken(token);
		return UserCounts.constructUserCount(client.get(
				WeiboConfig.getValue("baseURL") + "users/counts.json",
				new PostParameter[] { new PostParameter("uids", uids) }));
	}
}
