package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.util.Date;

public class WhiteList implements Serializable {
    

    private static final long serialVersionUID = 1L;

    /**  */
    private Integer autoId;

    /**  */
    private String whiteUser;

    /**  */
    private Date whiteBeginTime;

    /**  */
    private Date whiteEndTime;

    /**  */
    private String whiteCreateId;

    /**  */
    private Date whiteCreateDate;

    /**  */
    private String whiteUpdateId;

    /**  */
    private Date whiteUpdateDate;

    /**  */
    private String whiteDeleteId;

    /**  */
    private Date whiteDeleteDate;

    /**  */
    private String status;

    /**  */
    private Integer tenantId;

    /**  */
    private String channelId;

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.AUTO_ID
     */
    public Integer getAutoId() {
        return autoId;
    }

    /**
     * Sets the value
     *
     * @param autoId the value for CMS_WHITELIST.AUTO_ID
     */
    public void setAutoId(Integer autoId) {
        this.autoId = autoId;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_USER
     */
    public String getWhiteUser() {
        return whiteUser;
    }

    /**
     * Sets the value
     *
     * @param whiteUser the value for CMS_WHITELIST.WHITE_USER
     */
    public void setWhiteUser(String whiteUser) {
        this.whiteUser = whiteUser == null ? null : whiteUser.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_BEGIN_TIME
     */
    public Date getWhiteBeginTime() {
        return whiteBeginTime;
    }

    /**
     * Sets the value
     *
     * @param whiteBeginTime the value for CMS_WHITELIST.WHITE_BEGIN_TIME
     */
    public void setWhiteBeginTime(Date whiteBeginTime) {
        this.whiteBeginTime = whiteBeginTime;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_END_TIME
     */
    public Date getWhiteEndTime() {
        return whiteEndTime;
    }

    /**
     * Sets the value
     *
     * @param whiteEndTime the value for CMS_WHITELIST.WHITE_END_TIME
     */
    public void setWhiteEndTime(Date whiteEndTime) {
        this.whiteEndTime = whiteEndTime;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_CREATE_ID
     */
    public String getWhiteCreateId() {
        return whiteCreateId;
    }

    /**
     * Sets the value
     *
     * @param whiteCreateId the value for CMS_WHITELIST.WHITE_CREATE_ID
     */
    public void setWhiteCreateId(String whiteCreateId) {
        this.whiteCreateId = whiteCreateId == null ? null : whiteCreateId.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_CREATE_DATE
     */
    public Date getWhiteCreateDate() {
        return whiteCreateDate;
    }

    /**
     * Sets the value
     *
     * @param whiteCreateDate the value for CMS_WHITELIST.WHITE_CREATE_DATE
     */
    public void setWhiteCreateDate(Date whiteCreateDate) {
        this.whiteCreateDate = whiteCreateDate;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_UPDATE_ID
     */
    public String getWhiteUpdateId() {
        return whiteUpdateId;
    }

    /**
     * Sets the value
     *
     * @param whiteUpdateId the value for CMS_WHITELIST.WHITE_UPDATE_ID
     */
    public void setWhiteUpdateId(String whiteUpdateId) {
        this.whiteUpdateId = whiteUpdateId == null ? null : whiteUpdateId.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_UPDATE_DATE
     */
    public Date getWhiteUpdateDate() {
        return whiteUpdateDate;
    }

    /**
     * Sets the value
     *
     * @param whiteUpdateDate the value for CMS_WHITELIST.WHITE_UPDATE_DATE
     */
    public void setWhiteUpdateDate(Date whiteUpdateDate) {
        this.whiteUpdateDate = whiteUpdateDate;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_DELETE_ID
     */
    public String getWhiteDeleteId() {
        return whiteDeleteId;
    }

    /**
     * Sets the value
     *
     * @param whiteDeleteId the value for CMS_WHITELIST.WHITE_DELETE_ID
     */
    public void setWhiteDeleteId(String whiteDeleteId) {
        this.whiteDeleteId = whiteDeleteId == null ? null : whiteDeleteId.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.WHITE_DELETE_DATE
     */
    public Date getWhiteDeleteDate() {
        return whiteDeleteDate;
    }

    /**
     * Sets the value
     *
     * @param whiteDeleteDate the value for CMS_WHITELIST.WHITE_DELETE_DATE
     */
    public void setWhiteDeleteDate(Date whiteDeleteDate) {
        this.whiteDeleteDate = whiteDeleteDate;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.STATUS
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value
     *
     * @param status the value for CMS_WHITELIST.STATUS
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.TENANT_ID
     */
    public Integer getTenantId() {
        return tenantId;
    }

    /**
     * Sets the value
     *
     * @param tenantId the value for CMS_WHITELIST.TENANT_ID
     */
    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * Gets the value 
     *
     * @return the value of CMS_WHITELIST.CHANNEL_ID
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * Sets the value
     *
     * @param channelId the value for CMS_WHITELIST.CHANNEL_ID
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }
}