package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.util.Date;
/**
 * 微博接受消息bean
 * <AUTHOR>
 *
 */
public class WeiboMsgInfo implements Serializable{

	private String weiboId;
	
	private String channelCode;
	
	private String source;
	
	private String sendAccount;
	
	private String acceptedAccount;
	
	private MsgType msgType;
	
	private String createTime;
	
	private String content;
	
	//评论数
	private int comments;
	//转发数
	private int reposts;
	//喜欢数
	private int attitudes;
	
	public int getComments() {
		return comments;
	}

	public void setComments(int comments) {
		this.comments = comments;
	}

	public int getReposts() {
		return reposts;
	}

	public void setReposts(int reposts) {
		this.reposts = reposts;
	}

	public int getAttitudes() {
		return attitudes;
	}

	public void setAttitudes(int attitudes) {
		this.attitudes = attitudes;
	}

	public String getWeiboId() {
		return weiboId;
	}

	public void setWeiboId(String weiboId) {
		this.weiboId = weiboId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSendAccount() {
		return sendAccount;
	}

	public void setSendAccount(String sendAccount) {
		this.sendAccount = sendAccount;
	}

	public String getAcceptedAccount() {
		return acceptedAccount;
	}

	public void setAcceptedAccount(String acceptedAccount) {
		this.acceptedAccount = acceptedAccount;
	}

	public MsgType getMsgType() {
		return msgType;
	}

	public void setMsgType(MsgType msgType) {
		this.msgType = msgType;
	}

	public static enum MsgType {
		text, image, voice, video, position
	}
	
}
