package cn.sh.ideal.mgw.mediasend.emailsend;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.URLDataSource;
import javax.mail.Address;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.utils.EmailConnecter;

public class SimpleMailSender {
	private static final Logger log = Logger.getLogger(EmailSender.class);
	public boolean sendHtmlMail(MailSenderInfo mailInfo) throws Exception{
		try{
			MyAuthenticator authenticator = null;   
		      //如果需要身份认证，则创建一个密码验证器   
		      
		      // 根据邮件会话属性和密码验证器构造一个发送邮件的session    
		      Session sendMailSession = EmailConnecter.getSMTPSession(mailInfo.getMailServerHost(), mailInfo.getMailServerPort(), mailInfo.isValidate(),mailInfo.getIsTLS(),mailInfo.getUserName(), mailInfo.getPassword());
		      MimeMessage msg = new MimeMessage(sendMailSession); 
		      
		      msg.setFrom(new InternetAddress(mailInfo.getFromAddress(),mailInfo.getSenderName()));
		      msg.setRecipient(Message.RecipientType.TO, new InternetAddress(mailInfo.getToAddress())); 
		      msg.setSubject(mailInfo.getSubject());
		      msg.setSentDate(new Date()); 
		      
		      MimeMultipart allPart = new MimeMultipart("mixed");
		      MimeBodyPart content = createContent(mailInfo);
		      String attachFiles[] = mailInfo.getAttachFileNames();
		      for(int i=0;attachFiles!=null&&i<attachFiles.length;i++){
		    	try {
					MimeBodyPart attach = this.createAttachment(mailInfo.getAttachUrl(), mailInfo.getAttachPath(), attachFiles[i]);
					  allPart.addBodyPart(attach);
				} catch (Exception e) {
					log.error("", e);
					e.printStackTrace();
				}
		      }
		      allPart.addBodyPart(content);
		      msg.setContent(allPart); 
		      msg.saveChanges();
		      Transport.send(msg);
		} catch(Exception e) {
			e.printStackTrace();
			log.error("", e);
			return false;
		}
		return true;
	}
	//创建附件
	private MimeBodyPart createAttachment(String attachUrl,String attachPath,String fileName) throws Exception {
		MimeBodyPart attachmentPart = new MimeBodyPart();
		String newFileName = fileName;
		fileName = URLEncoder.encode(fileName, "UTF-8");
		fileName = fileName.replaceAll("\\+", "%20");
		log.info("附件名称:"+fileName);
		String path = attachUrl+attachPath+fileName;
		log.info("路径:"+path);
		URLDataSource   ds = new URLDataSource(new URL(path));
		attachmentPart.setDataHandler(new DataHandler(ds));
		attachmentPart.setFileName(MimeUtility.encodeText(newFileName));
		return attachmentPart;
	}
	private MimeBodyPart createContent(MailSenderInfo mailInfo) throws Exception {
		MimeBodyPart mailBody = new MimeBodyPart();  
		MimeMultipart body = new MimeMultipart("related");
		List imgList = mailInfo.getContentImgs();
		for(int i=0; imgList!=null && i<imgList.size();i++){
			MimeBodyPart jpgBody = new MimeBodyPart();
			
			String imgUrl = (String)imgList.get(i);
			
	    	String fileName = imgUrl.substring(imgUrl.lastIndexOf("/")+1,imgUrl.length());
	    	if("".equals(fileName)){
	    		continue;
	    	}
	    	String new_fileName = URLEncoder.encode(fileName, "UTF-8");
	    	new_fileName = new_fileName.replaceAll("\\+", "%20");
	    	String filePath = imgUrl.substring(0,imgUrl.lastIndexOf("/")+1);
	    	String newImgUrl = filePath+new_fileName;
	    	
	    	mailInfo.setHtmlContent(mailInfo.getHtmlContent().replaceAll(imgUrl, "cid:"+newImgUrl));
	    	URLDataSource   ds = new URLDataSource(new URL(newImgUrl));
			DataHandler dh = new DataHandler(ds);
			jpgBody.setFileName(MimeUtility.encodeText(fileName));
			jpgBody.setText(MimeUtility.encodeText(fileName));
			jpgBody.setDataHandler(dh);  
			jpgBody.setHeader("Content-ID","<"+newImgUrl+">");
			body.addBodyPart(jpgBody);  
		}
		
		MimeMultipart contentMultipart = new MimeMultipart("alternative");
		MimeBodyPart htmlContent = new MimeBodyPart(); 
		htmlContent.setContent(mailInfo.getHtmlContent(), "text/html;charset=gbk"); 
		MimeBodyPart textContent = new MimeBodyPart();
		textContent.setContent(mailInfo.getTextContent(),"text/plain;charset=gbk");
		contentMultipart.addBodyPart(htmlContent);
		contentMultipart.addBodyPart(textContent);
		
		MimeBodyPart htmlPart = new MimeBodyPart();  
		htmlPart.setContent(contentMultipart);
		body.addBodyPart(htmlPart);
		mailBody.setContent(body);
		return mailBody;
	}
	private MimeBodyPart createContentBak(MailSenderInfo mailInfo) throws Exception {
		MimeBodyPart contentBody = new MimeBodyPart(); 
		MimeMultipart contentMulti = new MimeMultipart("alternative"); 
		MimeBodyPart htmlBody = new MimeBodyPart();
		htmlBody.setContent(mailInfo.getHtmlContent(), "text/html;charset=gbk"); 
		contentMulti.addBodyPart(htmlBody);
		
//		MimeBodyPart textBody = new MimeBodyPart();
//		textBody.setContent(mailInfo.getTextContent(),"text/plain;charset=gbk");
//		contentMulti.addBodyPart(textBody);
		
		
		List imgList = mailInfo.getContentImgs();
		
		for(int i=0; imgList!=null && i<imgList.size();i++){
			MimeBodyPart jpgBody = new MimeBodyPart();
			String imgUrl = (String)imgList.get(i);
	    	String fileName = imgUrl.substring(imgUrl.lastIndexOf("/")+1,imgUrl.length());
	    	URLDataSource   ds = new URLDataSource(new URL(imgUrl));
			DataHandler dh = new DataHandler(ds);
			jpgBody.setFileName(fileName);
			jpgBody.setText(fileName);
			jpgBody.setDataHandler(dh);  
			jpgBody.setHeader("Content-ID","<"+imgUrl+">");
			contentMulti.addBodyPart(jpgBody);  
		}
		contentBody.setContent(contentMulti); 
		return contentBody; 
	}
	    /**   
	      * 以HTML格式发送邮件   
	      * @param mailInfo 待发送的邮件信息   
	      */    
	    public boolean sendHtmlMailbak(MailSenderInfo mailInfo) throws Exception{    
	      // 判断是否需要身份认证    
	      MyAuthenticator authenticator = null;   
	      Properties pro = mailInfo.getProperties();   
	      //如果需要身份认证，则创建一个密码验证器     
	      if (mailInfo.isValidate()) {    
	        authenticator = new MyAuthenticator(mailInfo.getUserName(), mailInfo.getPassword());   
	      }    
	      // 根据邮件会话属性和密码验证器构造一个发送邮件的session    
	      Session sendMailSession = Session.getInstance(pro,authenticator);    
	      try {    
	      // 根据session创建一个邮件消息    
	      Message mailMessage = new MimeMessage(sendMailSession);    
	      // 创建邮件发送者地址    
	      Address from = new InternetAddress(mailInfo.getFromAddress());    
//	      // 设置邮件消息的发送者    
	      mailMessage.setFrom(from);    
//	      // 创建邮件的接收者地址，并设置到邮件消息中    
	      Address to = new InternetAddress(mailInfo.getToAddress());    
//	      // Message.RecipientType.TO属性表示接收者的类型为TO    
	      mailMessage.setRecipient(Message.RecipientType.TO,to);    
//	      // 设置邮件消息的主题    
	      mailMessage.setSubject(mailInfo.getSubject());    
//	      // 设置邮件消息发送的时间    
	      mailMessage.setSentDate(new Date());    
	      
	      MimeMultipart mp = new MimeMultipart();

	      mp.setSubType("relate");

	      MimeBodyPart htmlmbp= new MimeBodyPart();
	      MimeBodyPart textmbp= new MimeBodyPart();
	      htmlmbp.setContent(mailInfo.getHtmlContent(),"text/html;charset=gbk");
	      textmbp.setContent(mailInfo.getTextContent(),"text/plain;charset=gbk");
	      mp.addBodyPart(textmbp);
	      mp.addBodyPart(htmlmbp);
	      
	      for(int i=0;mailInfo.getContentImgs() != null&& i<mailInfo.getContentImgs().size();i++){
	    	  MimeBodyPart imgmp = new MimeBodyPart();
	    	  String imgUrl = (String)mailInfo.getContentImgs().get(i);
	    	  String fileName = imgUrl.substring(imgUrl.lastIndexOf("/")+1,imgUrl.length());
	    	  URLDataSource   ds = new URLDataSource(new URL(imgUrl));
	    	  imgmp.setFileName(fileName);
	    	  imgmp.setDataHandler(new DataHandler(ds));
	    	  imgmp.setHeader("Content-ID",imgUrl);
	    	  mp.addBodyPart(imgmp);
	    	  }
	      String attach[] = mailInfo.getAttachFileNames();
	      for (int i=0;attach != null && i < attach.length ; i++){
	    	  URLDataSource   ds1 = new URLDataSource(new URL(mailInfo.getAttachUrl()+"/"+mailInfo.getAttachPath()+"/"+URLEncoder.encode(attach[i],"gbk")));
	    	  DataHandler  dh1 = new DataHandler(ds1); 
	    	  MimeBodyPart attachMp = new MimeBodyPart();  
	    	  attachMp.setFileName(MimeUtility.encodeText(attach[i]));
	    	  attachMp.setDataHandler(dh1);
	    	  mp.addBodyPart(attachMp);
	      }
	      mailMessage.setContent(mp);
	      // 发送邮件    
	      Transport.send(mailMessage);    
	      return true;    
	      } catch (MessagingException ex) {
	          ex.printStackTrace();
	          return false;
	      }    
	    }
	    
	    public static void main(String arg[]){
	    	SimpleMailSender send = new SimpleMailSender();
	    	MailSenderInfo mailInfo= new MailSenderInfo();
	    	mailInfo.setMailServerHost("smtp.189.cn");    
  		    mailInfo.setMailServerPort("25");    
  		    mailInfo.setValidate(true);    
  		    mailInfo.setUserName("18018508287");    
  		    mailInfo.setPassword("test123457");//您的邮箱密码    
  		    mailInfo.setFromAddress("<EMAIL>");    
  		    mailInfo.setToAddress("<EMAIL>");    
  		    mailInfo.setSubject("test");    
  		    mailInfo.setTextContent("设置邮箱内容 如http://www.guihua.org 中国桂花网 是中'国最大桂花网站==");    
  		    mailInfo.setHtmlContent("<html><head><title></title></head><body><b> see the following jpg : it is a car!</b><br/><br/><IMG SRC='http://************:8080/uploadFile/userImage/test.jpg' ><br/><IMG SRC='http://************:8080/uploadFile/logo/yxfw.jpg' ><br/><IMG SRC='http://************:8080/uploadFile/logo/fg1.png' ><br/><IMG SRC='http://************:8080/uploadFile/logo/fg2.png' ><br/><b> end of jpg</b></body></html>");
//  		    List list = new ArrayList();
//  		    list.add("http://************:8080/uploadFile/userImage/test.jpg");
//		    list.add("http://************:8080/uploadFile/logo/yx fw.jpg");
//  		    list.add("http://************:8080/uploadFile/logo/fg1.png");
//  		    list.add("http://************:8080/uploadFile/logo/fg2.png");
//	    	mailInfo.setContentImgs(list);
//	    	String attachs[] = {"comm.txt","133940274092186_测试图片.jpg","截图1340182586.png","133963669523433_测试.jpg"};
//	    	mailInfo.setAttachFileNames(attachs);
//	    	mailInfo.setAttachPath("/userImage/");
//	    	mailInfo.setAttachUrl("http://************:8080/uploadFile");
	    	try{
	    		send.sendHtmlMail(mailInfo);
	    	}catch(Exception e){
	    		e.printStackTrace();
	    	}
	    }
}
