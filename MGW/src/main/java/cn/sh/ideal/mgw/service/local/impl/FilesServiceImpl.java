package cn.sh.ideal.mgw.service.local.impl;

import static cn.sh.ideal.mgw.base.Constants.PARAM_APPID;
import static cn.sh.ideal.mgw.base.Constants.PARAM_APP_SECRET;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;

import javax.annotation.Resource;

import cn.sh.ideal.transcoding.model.req.TranscodingRequestDTO;
import cn.sh.ideal.transcoding.service.TranscodingService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.service.local.FilesService;
import cn.sh.ideal.mgw.utils.IdealFtpClient;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.TokenUtil;
import cn.sh.ideal.mgw.utils.Util;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.SysParam;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayMobilePublicMultiMediaClient;
import com.alipay.api.AlipayMobilePublicMultiMediaDownloadRequest;
@Service("filesService")
public class FilesServiceImpl implements FilesService {
	private static final Logger log = Logger.getLogger(FilesServiceImpl.class);
	

	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private SysInitService initService;

	@Autowired
	private TranscodingService transcodingService;

	/**
	 * 获取媒体文件
	 * 
	 * @param accessToken
	 *            接口访问凭证
	 * @param media_id
	 *            媒体文件id
	 * @param savePath
	 *            文件在服务器上的存储路径
	 * */
	public String downloadMedia(String account, String mediaId, String savePath) {
		String filePath = null;
		String accessToken = "";
		ChannelConfig channelConfig = null;
		// 拼接请求地址
		String requestUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, "WX.GET_MEDIA_URL");
		if(redisDao==null)
			log.info("redisDao 是tm空的!!!!!!!!!!");
		channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + account);
		if (channelConfig == null)
			throw new RuntimeException("appid and secret is null or fromuser not found");
		JSONObject accountConfig = JSON.parseObject(channelConfig.getAccountConfig());
//		JSONObject accountConfig = JSON.parseObject("{\"appId\":\"wx3badcacdb0143c66\",\"appSecret\":\"61c5570b3f3d9144954a1969ed5ecc32\",\"token\":\"idealweixinserver20130802\",\"aesKey\":\"\",\"oldAesKey\":\"\"}");
		String appid = accountConfig.getString(PARAM_APPID);
		String secret = accountConfig.getString(PARAM_APP_SECRET);
		
		try {
			accessToken = new TokenUtil("WX", appid, secret, redisDao).getToken();
//			accessToken ="yAluidpblu_ApRni-g3oknYxwikk2wgXs5KaaA4J4G22lQkKLT6FJ6lCe8us3o1NuAx_berAZ76bOQuW2xobyvrrwvgXj6tCiOaMqc-1pWQ";
			
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		log.debug(requestUrl + accessToken + "&media_id=" + mediaId);
		System.out.println(requestUrl + accessToken + "&media_id=" + mediaId);
		try {
			URL url = new URL(requestUrl + accessToken + "&media_id=" + mediaId);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setDoInput(true);
			conn.setRequestMethod("GET");

			if (!savePath.endsWith("/")) {
				savePath += "/";
			}
			
			// 根据内容类型获取扩展名
			String fileExt = getFileEndWitsh(conn.getHeaderField("Content-Type"));
			// 将mediaId作为文件名
			String filename = saveFile(mediaId + fileExt, conn.getInputStream(), savePath, "");
			//收到的消息为语音消息时转换为mp3格式
			if(".amr".equals(fileExt)){
				
				JSONObject json=new JSONObject();
				//SysParam ftpPathParam=initService.getSysParam(Constants.FTP_PATH); 
				  
				
				//json.put("path", ftpPathParam==null?null:ftpPathParam.getParamValue());
				json.put("fileName", mediaId);
				json.put("sourceFileType", fileExt);
				json.put("targetFileType", ".mp3");
				json.put("ftppath", "/"+savePath);
				SysParam param = initService.getSysParam("FTP_UPLOAD_URL");
				

				json.put("ftpConfig", param.getParamValue()+":"+param.getParamDesc());
				TranscodingRequestDTO requestDTO = new TranscodingRequestDTO();
				requestDTO.setFileName(mediaId);
				requestDTO.setSourceFileType(fileExt);
				requestDTO.setTargetFileType(".mp3");
				requestDTO.setFtpPath("/"+savePath);
				requestDTO.setFtpConfig( param.getParamValue()+":"+param.getParamDesc());
				transcodingService.transcoding(requestDTO);

				//FIXME
//				NetUtil.send(initService.assemblyUrl(Constants.SYS_PARAM_TRANSCODING_ADDRESS, "/transcoding.do"),NetUtil.POST, json.toJSONString());
				fileExt=".mp3";
			}
			filePath = savePath + mediaId + fileExt;
			conn.disconnect();
			String info = String.format("下载媒体文件成功，filePath=" + filePath);
			log.debug(info);
		} catch (Exception e) {
			filePath = null;
			String error = String.format("下载媒体文件失败：%s", e);
			log.error(error,e);
		}
		SysParam sysParam=initService.getSysParam(Constants.IMG_HTTP_URL);
		
		return sysParam.getParamValue()+filePath;
	}

	/**
	 * 根据内容类型判断文件扩展名
	 * 
	 * @param contentType
	 *            内容类型
	 * @return
	 */
	public  String getFileEndWitsh(String contentType) {
		String fileEndWitsh = "";
		if ("image/jpeg".equals(contentType))
			fileEndWitsh = ".jpg";
		else if ("audio/mpeg".equals(contentType))
			fileEndWitsh = ".mp3";
		else if ("audio/amr".equals(contentType))
			fileEndWitsh = ".amr";
		else if ("video/mp4".equals(contentType))
			fileEndWitsh = ".mp4";
		else if ("video/mpeg4".equals(contentType))
			fileEndWitsh = ".mp4";
		return fileEndWitsh;
	}
	
	public  String getFileContainWitsh(String contentType) {
		String fileEndWitsh = "";
		if(contentType.contains("image/jpeg"))
			fileEndWitsh = ".jpg";
		else if(contentType.contains("audio/amr"))
			fileEndWitsh = ".amr";
		return fileEndWitsh;
	}

	/**
	 * 上传文件到服务器
	 * @param fileName
	 * @param in
	 * @param pathFile
	 * @param model
	 * @param mailFileSize
	 * @return
	 */
	public String saveFile(String fileName, InputStream in, String pathFile, String mailFileSize) {
		// 将附件上传到指定FTP目录上去
		IdealFtpClient ftpClient = null;
		String fileName2 = "";
		try {
			if (!"".equals(Util.filter(mailFileSize))) {
				if (in.available() > Integer.parseInt(mailFileSize) * 1024 * 1024) {
					return "";
				}
			}
			ftpClient = new IdealFtpClient();
			SysParam param = initService.getSysParam("FTP_UPLOAD_URL");

			String code_desc = param.getParamDesc();

			String[] ftp = code_desc.split(":");

			ftpClient.connectServer(param.getParamValue(), ftp[0], ftp[1],ftp[2]);

			ftpClient.changeDir(pathFile);

			if (ftpClient.isExists(fileName)) {
				fileName2 = "[1]" + fileName;
			} else {
				fileName2 = fileName;
			}

			ftpClient.uploadFile(in, fileName2);

			ftpClient.close();

		} catch (Exception e1) {
			log.error("附件上传FTP出错:" + e1.getMessage(), e1);
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
		return fileName2;
	}

	public static void main(String[] args) {
		System.out.println((new Date()).getTime());
	}

	@Override
	public String downloadAlipayMedia(String account, String mediaId,
			String savePath) throws Exception {

		ChannelConfig channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + account);
		if(channelConfig == null)
			throw new Exception("channel config not found.");
		

		JSONObject accountConfig = JSONObject.parseObject(channelConfig.getAccountConfig());
		String privateKey = accountConfig.getString("privateKey");
		
		String filename = mediaId + ".jpg";
		
		FileOutputStream fileOutputStream = new FileOutputStream(filename);
		
		AlipayMobilePublicMultiMediaClient client = new AlipayMobilePublicMultiMediaClient("https://openfile.alipay.com/chat/multimedia.do", account, privateKey);
		
		AlipayMobilePublicMultiMediaDownloadRequest downloadRequest = new AlipayMobilePublicMultiMediaDownloadRequest();
		downloadRequest.setBizContent("{\"mediaId\":\"" + mediaId + "\"}");
		downloadRequest.setOutputStream(fileOutputStream);
		client.execute(downloadRequest);
		FileInputStream inputStream = new FileInputStream(filename);
		
		saveFile(filename, inputStream, savePath, "");
		
		File file = new File(filename);
		file.delete();
		
		return initService.getSysParam(Constants.IMG_HTTP_URL).getParamValue() + savePath + "/" + filename;
	}
	
	/**
	 * 微博私信图片
	 * @param accessToken
	 * @param fid
	 * @param savePath
	 * @return
	 */
	public String downloadWeiBoSXMedia(String accessToken, String fid, String savePath) {
		String filePath = null;
		
		// 拼接请求地址
		String requestUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, "WB_PICURL");
		log.info(requestUrl + "access_token=" + accessToken + "&fid=" + fid);
		System.out.println(requestUrl + "access_token=" + accessToken + "&fid=" + fid);
		try {
			URL url = new URL(requestUrl + "access_token=" + accessToken + "&fid=" + fid);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setDoInput(true);
			conn.setRequestMethod("GET");

			if (!savePath.endsWith("/")) {
				savePath += "/";
			}
			log.info("###############_____________");
			log.info(conn.getHeaderFields());
			log.info("###############_____________");
			// 根据内容类型获取扩展名
			String fileExt = getFileEndWitsh(conn.getHeaderField("Content-Type"));
			if(StringUtils.isEmpty(fileExt))
				fileExt = getFileContainWitsh(conn.getHeaderField("Content-Type"));
			// 将mediaId作为文件名
			String filename = saveFile(fid + fileExt, conn.getInputStream(), savePath, "");
			log.info("###############fileExt=" + fileExt + "___filename=" + filename + "___savePath=" + savePath);
			//收到的消息为语音消息时转换为mp3格式
			if(".amr".equals(fileExt)){				
				JSONObject json=new JSONObject();
				json.put("fileName", fid);
				json.put("sourceFileType", fileExt);
				json.put("targetFileType", ".mp3");
				json.put("ftppath", "/"+savePath);
				SysParam param = initService.getSysParam("FTP_UPLOAD_URL");
				json.put("ftpConfig", param.getParamValue()+":"+param.getParamDesc());
				TranscodingRequestDTO requestDTO = new TranscodingRequestDTO();
				requestDTO.setFileName(fid);
				requestDTO.setSourceFileType(fileExt);
				requestDTO.setTargetFileType(".mp3");
				requestDTO.setFtpPath("/"+savePath);
				requestDTO.setFtpConfig( param.getParamValue()+":"+param.getParamDesc());
				transcodingService.transcoding(requestDTO);
				//FIXME
//				NetUtil.send(initService.assemblyUrl(Constants.SYS_PARAM_TRANSCODING_ADDRESS, "/transcoding.do"),NetUtil.POST, json.toJSONString());
				fileExt=".mp3";
			}
			filePath = savePath + fid + fileExt;
			conn.disconnect();
			String info = String.format("下载媒体文件成功，filePath=" + filePath);
			log.debug(info);
		} catch (Exception e) {
			filePath = null;
			String error = String.format("下载媒体文件失败：%s", e);
			log.error(error,e);
		}
		SysParam sysParam=initService.getSysParam(Constants.IMG_HTTP_URL);		
		return sysParam.getParamValue()+filePath;
	}
}
