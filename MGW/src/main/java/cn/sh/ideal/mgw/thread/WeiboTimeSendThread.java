package cn.sh.ideal.mgw.thread;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.mgw.dao.WeiboTimeDao;
import cn.sh.ideal.mgw.model.response.WeiboSendResponse;
import cn.sh.ideal.mgw.service.WeiboMessageService;
import cn.sh.ideal.model.MessageInfoSend;
import cn.sh.ideal.util.DateUtils;


/**
 * 微博定时发送
 * <AUTHOR>
 *
 */
public class WeiboTimeSendThread implements Runnable{
	
	private JSONObject jsonConfig;
	private WeiboTimeDao weiboTimeDao;
	private WeiboMessageService weiboMessageService;
	public static final Logger log = LoggerFactory.getLogger(WeiboTimeSendThread.class);
	
	
	public WeiboTimeSendThread(JSONObject jsonConfig,ApplicationContext context){
		this.jsonConfig = jsonConfig;
		this.weiboTimeDao = (WeiboTimeDao) context.getBean("weiboTimeDao", WeiboTimeDao.class); 
		this.weiboMessageService = (WeiboMessageService) context.getBean("weiboMessageService", WeiboMessageService.class); 
	}

	@Override
	public void run() {
		while(true){
			try {
			//查询数据
			String tenantCode = jsonConfig.getString("tenantCode");
			String sendAccount = jsonConfig.getString("uid");
			String channelCode = "1005";
			MessageInfoSend messageSend = new MessageInfoSend();
			messageSend.setTenantCode(tenantCode);
			messageSend.setChannelCode(channelCode);
			messageSend.setSendAccount(sendAccount);
			
			List<MessageInfoSend> messageSendList = weiboTimeDao.queryList(messageSend);
			
			if(messageSendList.size() > 0){
				log.info("本次定时发送量为：" + messageSendList.size());
				for(MessageInfoSend messageSendSig : messageSendList){
					//判断是否需要发送
					if(sendReady(messageSendSig)){
						//改掉定时状态
						messageSendSig.setIsTime("0");
						WeiboSendResponse sendResult = weiboMessageService.weibonewSend(messageSendSig);
						
						if("0".equals(sendResult.getResultCode())){
							//发送成功,更新发送表
							weiboTimeDao.updateTimeSendStatus(messageSendSig);
							log.info("微博定时发送成功，发送id：" + messageSendSig.getId());
						}
					}
					
				}
			}
		} catch (Exception e) {
			log.error("微博定时发送失败！",e);
		}
			try {
				Thread.currentThread();
				Thread.sleep(30000);
			} catch (InterruptedException e) {
				log.error("微博定时发送分支线程异常！",e);
			}
		}
	}

	
		private boolean sendReady(MessageInfoSend messageSendSig){
			boolean flag = false;
			String dateReady =  messageSendSig.getDate();
			if(StringUtils.isEmpty(dateReady)){
				flag = false;
				 log.error("有一条定时微博发送失败,发送时间为空:[id:" + messageSendSig.getId() + "]");
			}
			
			//获取数据库时间
			String currentTime = weiboTimeDao.getSystemTime();
			
			try {
				Date dateReadyDate = DateUtils.str2Date(dateReady,"yyyy-MM-dd HH:mm:ss");
				Date currentTimeDate = DateUtils.str2Date(currentTime,"yyyy-MM-dd HH:mm:ss");
				//当前时间大于发送时间，发送微博
				if(currentTimeDate.getTime() > dateReadyDate.getTime()){
					flag = true;
				}
				
			} catch (Exception e) {
				log.error("微博定时发送，时间格式转换异常【dateReady:"+ dateReady +",currentTime:" + currentTime +",id:" + messageSendSig.getId() + "】",e);
				flag = false;
			}
			
			return flag;
		}
}
