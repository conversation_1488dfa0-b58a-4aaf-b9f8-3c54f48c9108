package cn.sh.ideal.mgw.utils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import cn.sh.ideal.mgw.model.return0x.Return0xField;
import cn.sh.ideal.mgw.model.return0x.Return0xModel;
import cn.sh.ideal.mgw.model.return0x.Return0xProperty;
import cn.sh.ideal.mgw.model.return0x.Return0xResponse;
import cn.sh.ideal.mgw.model.return0x.Return0xResult;


public class Return0xUtil {
	
	private static final Logger log = Logger.getLogger(Return0xUtil.class);

	/**
	 * 组转响应xml为响应实体
	 * returnStr	为响应xml
	 * nestedStr	为嵌套元素，响应xml有可能为<response></response>或<request></request>
	 */
	public static Return0xModel getReturn0xModel(String returnStr,String nestedStr){
		try {
			Return0xModel returnModel = new Return0xModel();
			XmlObject returnXMLObj = XmlObject.parse(returnStr);
			List<XmlObject> commandObjList = returnXMLObj.getSubXmlObjects("command");
			if(commandObjList != null && commandObjList.size() > 0){
				returnModel.setCommand(commandObjList.get(0).getValue().toString());
			}			
			List<XmlObject> resultObjList = returnXMLObj.getSubXmlObjects("result");
			if(resultObjList != null && resultObjList.size() > 0){
				XmlObject rObj = resultObjList.get(0);
				if(rObj != null && "result".equals(rObj.getName())){
					Return0xResult r0xResult = new Return0xResult();
					r0xResult.setCode(rObj.getChildValue("code"));
					if(!StringUtils.isEmpty(rObj.getChildValue("reason")))
						r0xResult.setReason(rObj.getChildValue("reason"));
					returnModel.setResult(r0xResult);
				}
			}
			
			Return0xResponse r0xResponse = new Return0xResponse();
			List<Return0xField> r0xProtocolfields = new ArrayList<Return0xField>();
			List<Return0xProperty> r0xPropertys = new ArrayList<Return0xProperty>();
			
			List<XmlObject> responseObjList = returnXMLObj.getSubXmlObjects(nestedStr);
			if(responseObjList != null && responseObjList.size() > 0){
				for(int j = 0;j < responseObjList.size();j++){
					XmlObject responseObj = responseObjList.get(j);
					//组装protocolfields节点
					List<XmlObject> protocolfieldsList = responseObj.getSubXmlObjects("protocolfields");
					if(protocolfieldsList != null && protocolfieldsList.size() > 0){
						XmlObject rObj = protocolfieldsList.get(0);
						if(rObj != null && "protocolfields".equals(rObj.getName())){
							List<XmlObject> fieldObjList = rObj.getSubXmlObjects("field");							
							//循环protocolfields节点下field元素
							for(int i = 0;i < fieldObjList.size(); i++){
								XmlObject fieldObject = fieldObjList.get(i);
								Return0xField r0xField = new Return0xField();
								if(!StringUtils.isEmpty(fieldObject.getAttributeValue("key")))
									r0xField.setKey(fieldObject.getAttributeValue("key").toString());
								if(!StringUtils.isEmpty(fieldObject.getAttributeValue("value")))
									r0xField.setValue(fieldObject.getAttributeValue("value").toString());
								//加入r0xProtocolfields列表
								r0xProtocolfields.add(r0xField);
							}
							r0xResponse.setProtocolfields(r0xProtocolfields);
						}
					}
					//组装reqitem节点
					List<XmlObject> reqitemList = responseObj.getSubXmlObjects("reqitem");
					if(reqitemList != null && reqitemList.size() > 0){
						List<Return0xModel> r0xReqitemList = new LinkedList<Return0xModel>();
						for(int i = 0; i< reqitemList.size(); i++){
							XmlObject rObj = reqitemList.get(i);
							List<XmlObject> imccObjList = rObj.getSubXmlObjects("imcc");
							XmlObject imccObj = imccObjList.get(0);
//							log.info("reqitem = " + imccObj.toFormatXml());
							Return0xModel r0m = getReturn0xModel(imccObj.toFormatXml(),"request");
							r0xReqitemList.add(r0m);
						}
						r0xResponse.setReqitem(r0xReqitemList);
					}
					//组装sessionpropertys节点
					List<XmlObject> sessionpropertysList = responseObj.getSubXmlObjects("sessionpropertys");
					if(sessionpropertysList != null && sessionpropertysList.size() > 0){
						XmlObject rObj = sessionpropertysList.get(0);
						if(rObj != null && "sessionpropertys".equals(rObj.getName())){
							List<XmlObject> propertyObjList = rObj.getSubXmlObjects("property");		
							//循环sessionpropertys节点下property元素
							for(int i = 0;i < propertyObjList.size(); i++){
								XmlObject propertyObject = propertyObjList.get(i);
								Return0xProperty r0xProperty = new Return0xProperty();
								if(!StringUtils.isEmpty(propertyObject.getAttributeValue("key")))
									r0xProperty.setKey(propertyObject.getAttributeValue("key").toString());
								if(!StringUtils.isEmpty(propertyObject.getAttributeValue("value")))
									r0xProperty.setValue(propertyObject.getAttributeValue("value").toString());
								//加入r0xPropertys列表
								r0xPropertys.add(r0xProperty);
							}
							r0xResponse.setSessionpropertys(r0xPropertys);
						}				
					}
					//组装msg节点
					List<XmlObject> msgObjList = responseObj.getSubXmlObjects("msg");
					if(msgObjList != null && msgObjList.size() > 0){
						XmlObject msgObj = msgObjList.get(0);
						r0xResponse.setMsg(msgObj.getValue().toString());
					}
				}
			}
			returnModel.setResponse(r0xResponse);
//			System.out.println(returnXMLObj);
			return returnModel;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	} 
}
