package cn.sh.ideal.mgw.adapter;

import java.io.Serializable;

import cn.sh.ideal.model.MessageInfo;

/**
 * 消息适配器抽象类
 * 
 * <AUTHOR>
 * 2015-3-6
 *
 */
public abstract class MsgAdapter implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 将多媒体消息转化成Message格式
	 * 
	 * @param channelCode
	 * @param data
	 * @return
	 */
	public abstract MessageInfo media2Message(String channelCode,Object data);
	
	/**
	 * 将Message转换成多媒体格式
	 * 
	 * @param message
	 * @return
	 */
	public abstract Object message2Media(MessageInfo message);
	
}
