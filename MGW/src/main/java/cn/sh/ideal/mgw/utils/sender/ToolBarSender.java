package cn.sh.ideal.mgw.utils.sender;


import cn.sh.ideal.mgw.utils.NetUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class ToolBarSender implements Sender {

	public JSONObject send(String json, String url) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JSONObject customSend(String json, String token) throws Exception {
		//FIXME
		return JSONObject.parseObject(NetUtil.send(token, NetUtil.POST, json));
	}

	@Override
	public JSON multiSend(String json, String token) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}


}
