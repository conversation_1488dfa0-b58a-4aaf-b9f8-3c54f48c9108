package cn.sh.ideal.mgw.service.dubbo.channel;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.req.MessageHandleRequest;
import cn.sh.ideal.imr.resp.MessageHandleResponse;
import cn.sh.ideal.imr.service.IMRService;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.MsgConvertUtil;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.model.PlatformMessage;
import cn.sh.ideal.mgw.model.PlatformMessage.MsgType;
import cn.sh.ideal.mgw.model.TextMessage;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.req.WechatRequestDTO;
import cn.sh.ideal.mgw.service.channel.WechatService;
import cn.sh.ideal.mgw.service.local.BlackService;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.utils.MessageUtil;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.SHA1;
import cn.sh.ideal.mgw.utils.TokenUtil;
import cn.sh.ideal.mgw.utils.XmlParser;
import cn.sh.ideal.mgw.utils.sender.WechatSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.util.EmojiConverter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.thoughtworks.xstream.XStream;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.dubboService.channel
 * @typeName WechatServiceImpl
 * <AUTHOR> Zhou
 * @Description:
 * @date 2016年3月31日 下午5:18:10
 * @version
 */
@Component("wechatService")
public class WechatServiceImpl implements WechatService {

	private static final Logger log = LoggerFactory
			.getLogger(WechatServiceImpl.class);

	private final String CHANNELNAME = "WX";

	private final String CHANNELCODE = "1003";

	@Resource(name = "sensitiveService")
	private SensitiveService sensitiveService;

	@Resource(name = "blackService")
	private BlackService blackService;

	@Autowired
	private SysInitService initService;

	@Autowired
	private LocalSessionService sessionService;

	@Autowired
	private IMRService imrService;

	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;

	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> sredisDao;

	@Autowired
	private MessageInfoDao messageInfoDao;

	@Value("#{config['outAgent']}")
	private String outAgent;
	@Value("#{config['media.imrUrl']}")
	private String imrUrl;
	@Value("#{config['media.mirUrl']}")
	private static String mirUrl;


	static XStream xstream = new XStream();
	static {
		xstream.alias("xml", PlatformMessage.class);
		xstream.alias("XML", PlatformMessage.class);
		xstream.ignoreUnknownElements();
		xstream.autodetectAnnotations(true);
	}


	/*
	 * @see
	 * cn.sh.ideal.mgw.server.channel.WechatService#post(cn.sh.ideal.mgw.server
	 * .model.req.WechatRequestDTO)
	 */
	@Override
	public String post(WechatRequestDTO wechatRequestDTO) {

		long beginTime = new Date().getTime();

		String result = "";

		try {
			String[] sortingStatus = { "1", "6" };
			String signature = wechatRequestDTO.getSignature();
			String appid = wechatRequestDTO.getAppid();
			String timestamp = wechatRequestDTO.getTimestamp();
			String nonce = wechatRequestDTO.getNonce();
			String imitation = wechatRequestDTO.getImitation();
			String token = wechatRequestDTO.getToken();
			String tenantCode = wechatRequestDTO.getTenantCode();
			String secret = wechatRequestDTO.getSecret();

			if (checkSignature(token, timestamp, nonce, signature)) {

				String xmlContent = wechatRequestDTO.getXmlContent();

				log.info("用户请求:" + xmlContent);

				// 解析xml
				Map<String, String> map = XmlParser.getXmlElmentValue(xmlContent);
				// 转换IMR可用实体
				PlatformMessage platformMessage = (PlatformMessage) xstream.fromXML(xmlContent);
				log.info("After xstream.fromXML() :"+ platformMessage.toString());
				// 模板消息发送回执.不做处理屏蔽掉
				if ("TEMPLATESENDJOBFINISH".equals(platformMessage.getEventType())) {

					// FIXME 这里返回空串合适吗?
					return "";
				}
				// 获取fromUser
				String fromUserName = map.get("FromUserName");
				String toUserName = map.get("ToUserName");

				// 如果渠道未启用
				log.info("判断发送进程");

				if (StringUtils.isEmpty(tenantCode)) {
					log.info("租户id为空");
				}

				//TODO  这一块代码原MGW就存在,但貌似没作用,暂时注释掉了
//				JSONObject nickjson = new JSONObject();
//				nickjson.put(Constants.PARAM_PID, CHANNELNAME);
//				nickjson.put(Constants.PARAM_PLATFORM_ID, toUserName);
//				nickjson.put(Constants.PARAM_OPENID, fromUserName);

				RequestSessionbean requestSessionbean = new RequestSessionbean(fromUserName, toUserName, CHANNELCODE, tenantCode);
				SensitiveLog sensitiveLog = new SensitiveLog(CHANNELCODE,tenantCode, fromUserName, toUserName, platformMessage.getContent());
				if (sensitiveService.filter(sensitiveLog)) {
					// response.getWriter().print("");
					// FIXME
					log.info("微信消息已经被过滤" + JSON.toJSONString(sensitiveLog));
					return "";

				} else if (blackService.blackService(CHANNELCODE, fromUserName,tenantCode)) {

					TextMessage resultMsg = new TextMessage(this.initService.getSysParam("BLACK_REPLY").getParamValue());
					String res = MessageUtil.getResultEx(fromUserName,
							toUserName, resultMsg);
					log.info("微信消息已经黑名单被过滤");
					log.info("黑名单返回微信消息:" + res);
					return res;

				} else {
					// FIXME 这一块的逻辑交给SI处理
					// response.getWriter().print("");
					// response.getWriter().flush();

					log.info("Before media2Message"+ JSON.toJSONString(requestSessionbean));
					MessageInfo message = MsgConvertUtil.media2Message(CHANNELCODE, platformMessage);
					log.info("After media2Message :"+ JSON.toJSONString(message));
					if (StringUtils.isEmpty(message.getSkillType())) {
						ChannelConfig channelConfig = (ChannelConfig) sredisDao.readValue(Constants.KEY_CHANNEL_INFO+ message.getAcceptedAccount());

						requestSessionbean.setSkillType(channelConfig.getRealTime());
					}


					SessionInfo sessioninfo = sessionService.getSession(requestSessionbean);



					log.info("wechat调用SM返回的sessionInfo: "+JSON.toJSONString(sessioninfo));
					// platformMessage.setNickname(sessioninfo.getNickname());
					if (sessioninfo != null) {
						message.setNickname(sessioninfo.getNickname());
						message.setSessionId(sessioninfo.getSessionId());
						message.setTenantCode(sessioninfo.getTenantCode());

						message.setCreateTime(new Date(platformMessage.getCreateTime() * 1000));

						if (ArrayUtils.contains(sortingStatus,sessioninfo.getStatus()) || outAgent.equals(platformMessage.getContent())) {

							log.info(fromUserName + "请求IMR:url" + imrUrl);

							String requestJson = platformMessage.toString();
							platformMessage.setMsg(requestJson);

							/*
							 *
							 */
							cn.sh.ideal.imr.entity.PlatformMessage pm = new cn.sh.ideal.imr.entity.PlatformMessage();
							pm.setCreateTime(platformMessage.getCreateTime());
							pm.setEventId(platformMessage.getEventId());
							pm.setEventType(platformMessage.getEventType());
							pm.setOpenId(platformMessage.getOpenId());
							pm.setToUser(platformMessage.getToUser());
							pm.setMsgType(platformMessage.getMsgType().name());
							pm.setMsgId(platformMessage.getMsgId());
							pm.setSource(platformMessage.getSource());
							pm.setContent(platformMessage.getContent());

							log.info("请求IMR服务,param : "+JSON.toJSONString(pm));
							MessageHandleResponse messageHandle = imrService.messageHandle(new MessageHandleRequest(pm));


							if(messageHandle!=null && messageHandle.getResponseMessage()!=null){
								result = JSON.toJSONString(messageHandle.getResponseMessage().toXMLString());
								log.info(fromUserName + "结束请求IMR,返回值 : "+JSON.toJSONString(result));
							}else{
								log.info("******IMR 返回值为空******");
							}

							// 获取接受结束时间
							long endTime = new Date().getTime();
							if (result != null && !"".equals(result)) {
								if ((endTime - beginTime) > 4000 || "0".equals(imitation)) {

									// 转换xml变为json格式
									String json = MessageUtil.jsonToWechat(result);
									log.info("转换后的微信消息(json格式) : "+json);
									// 获取微信的accresstoken
									String accresstoken = new TokenUtil(CHANNELNAME, appid, secret,sredisDao).getToken();
									// 调用客服发送接口
									JSONObject customSend = new WechatSender().customSend(json,accresstoken);
									log.info("调用客服发送接口返回值 :" +customSend.toJSONString());
								} else {
									return result;
								}
							} else {
								return "";
							}
						} else {

							try {
								// 封装消息体
								String content = message.getContent();
								message.setMessageSource("1");
								if ("text".equals(message.getMsgType())) {

									message.setContent(EmojiConverter.encode(message.getContent()));

								} else if (platformMessage.getEventType() != null && "click".equals(platformMessage.getEventType().toLowerCase())) {
									// 在数据库增加CLICK_REPLY配置. 读取配置回复
									TextMessage resultMsg = new TextMessage(this.initService.getSysParam("CLICK_REPLY").getParamValue());
									log.info("***TextMessage*** : "+JSON.toJSONString(resultMsg));
									String res = MessageUtil.getResultEx(fromUserName,toUserName, resultMsg);
									log.info("After MessageUtil.getResultEx() : result :"+res);
									message.setMessageSource("3");
									log.info("messageInfo 入库: "+JSON.toJSONString(message));
									this.messageInfoDao.insert(message);

									return res;
								}
								// 正常人工进入的消息设置为1

								log.info("messageInfo 入库: "+JSON.toJSONString(message));
								this.messageInfoDao.insert(message);
								message.setContent(content);


								String messageString = JSON.toJSONString(message);

								redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,message);

								log.info("wechat messageInfo write in redis : "+ messageString);


							} catch (Exception e) {
								log.error("存储数据异常!", e);
							}
						}
					} else {
						log.info("调用SM,返回值空");
					}
				}
			}else{
				log.info("******校验消息合法性失败******");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "";
	}

	/**
	 * 验证消息合法性
	 *
	 * @param token
	 * @param timestamp
	 * @param nonce
	 * @param signature
	 * @return
	 */
	public Boolean checkSignature(String token, String timestamp, String nonce,
			String signature) {

		String[] str = { token, timestamp, nonce };
		Arrays.sort(str);
		String bigStr = str[0] + str[1] + str[2];
		// // 计算sha-1摘要，返回相应的十六进制字符串
		String digest = new SHA1().getDigestOfString(bigStr.getBytes())
				.toLowerCase();
		if (digest.equals(signature)) {
			return true;
		} else {
			return false;
		}
	}

}
