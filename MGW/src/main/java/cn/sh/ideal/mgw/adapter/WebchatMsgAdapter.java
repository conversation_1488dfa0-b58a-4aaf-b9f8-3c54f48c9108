package cn.sh.ideal.mgw.adapter;

import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_APPOINTMENT_ACCOUNT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_APPOINTMENT_NAME;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_APPOINTMENT_TIME;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_APP_ID;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_CONTENT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_ISDEFAULT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_MESSAGE_ID;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_MSG_TYPE1;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_REPLY_CHANNEL_CODE;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_SERVER_COUNT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_SESSION_ID;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_USER_ACCOUNT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_WORK_NO;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_ACCEPT_ACCOUNT;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_ACCOUNT;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_BUSINESS_TYPE;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_CHANNEL_CODE;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_DATA;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_LOCATION_X;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_LOCATION_Y;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_MESSAGE;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_MSG;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_NICKNAME;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_PARAM;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_PARAMS;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_SEND_ACCOUNT;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_SKILL_QUEUE;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_TENANT_CODE;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_TEXT;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_URL;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_USER_ID;

import org.apache.commons.lang.StringUtils;

import cn.sh.ideal.mgw.model.PlatformMessage;
import cn.sh.ideal.model.MessageInfo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 在线客服消息适配器
 * 
 * <AUTHOR>
 * 
 */
public class WebchatMsgAdapter extends MsgAdapter {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;

		if (null != data) {
			message = new MessageInfo();
			JSONObject json = JSONObject.parseObject((data.toString()));
			System.out.println("json="+json);
			message.setChannelCode(channelCode);
			message.setUserId(json.getString(MSG_PARAM_USER_ID));
			message.setTenantCode(json.getString(MSG_PARAM_TENANT_CODE));
			message.setSkillQueue(json.getString(MSG_PARAM_SKILL_QUEUE));
			message.setNickname(json.getString(MSG_PARAM_NICKNAME));
			message.setBusinessType(json.getString(MSG_PARAM_BUSINESS_TYPE));
			message.setFollowData(json.getString(MSG_PARAM_PARAMS));

			// 分解消息类型
			JSONObject jsonMsg = json.getJSONObject(MSG_PARAM_MSG);
			System.out.println("jsonMsg="+jsonMsg);
			toMsg(jsonMsg, message);

			JSONArray jsonAcount = json.getJSONArray(MSG_PARAM_ACCOUNT);
			JSONArray jsonReplyAccount = new JSONArray();
			JSONObject jsonTemp = null;

			message.setSendAccount(jsonAcount.getJSONObject(0).getString(MSG_PARAM_SEND_ACCOUNT));
			message.setAcceptedAccount((jsonAcount.getJSONObject(0).getString(MSG_PARAM_ACCEPT_ACCOUNT)));

			// 拼接replayChannelCode
			for (int i = 0; i < jsonAcount.size(); i++) {
				jsonTemp = new JSONObject();
				jsonTemp.put(MEDIA_PARAM_REPLY_CHANNEL_CODE, jsonAcount.getJSONObject(i).get(MSG_PARAM_CHANNEL_CODE));
				jsonTemp.put(MEDIA_PARAM_USER_ACCOUNT, jsonAcount.getJSONObject(i).get(MSG_PARAM_SEND_ACCOUNT));
				jsonTemp.put(MEDIA_PARAM_SERVER_COUNT, jsonAcount.getJSONObject(i).get(MSG_PARAM_ACCEPT_ACCOUNT));
				jsonTemp.put(MEDIA_PARAM_ISDEFAULT, "");

				jsonReplyAccount.add(jsonTemp);
			}
			message.setReplyAccount(jsonReplyAccount.toJSONString());
		}
		return message;
	}

	@Override
	public JSONObject message2Media(MessageInfo message) {

		JSONObject object = new JSONObject();
		JSONObject objectTemp = null;
		JSONArray objectReplyAccount = new JSONArray();
		

		if (null != message) {
			
			JSONArray objectAccount = new JSONArray();
			
			object.put(MSG_PARAM_CHANNEL_CODE, message.getChannelCode());
			object.put(MEDIA_PARAM_APP_ID, "");
			object.put(MSG_PARAM_TENANT_CODE, message.getTenantCode());
			object.put(MSG_PARAM_USER_ID, message.getAcceptedAccount());
			object.put(MSG_PARAM_ACCEPT_ACCOUNT, message.getAcceptedAccount());
			object.put(MEDIA_PARAM_WORK_NO, message.getWorkNo());
			object.put(MEDIA_PARAM_SESSION_ID, message.getSessionId());
			object.put(MSG_PARAM_BUSINESS_TYPE, message.getBusinessType());
			object.put(MSG_PARAM_PARAMS, message.getFollowData());
			object.put(MEDIA_PARAM_MESSAGE_ID, message.getMessageId());

			// 拼接account
			if (!StringUtils.isEmpty(message.getReplyAccount())) {
				objectReplyAccount = JSONArray.parseArray(message.getReplyAccount());
				for (int i = 0; i < objectReplyAccount.size(); i++) {
					objectTemp = new JSONObject();
					objectTemp.put(MSG_PARAM_CHANNEL_CODE, objectReplyAccount.getJSONObject(i).get(MEDIA_PARAM_REPLY_CHANNEL_CODE));
					objectTemp.put(MSG_PARAM_SEND_ACCOUNT, objectReplyAccount.getJSONObject(i).get(MEDIA_PARAM_USER_ACCOUNT));
					objectTemp.put(MSG_PARAM_ACCEPT_ACCOUNT, objectReplyAccount.getJSONObject(i).get(MEDIA_PARAM_SERVER_COUNT));

					objectAccount.add(objectTemp);
				}
			}
			object.put(MSG_PARAM_ACCOUNT, objectAccount);
			object.put("sendType", message.getSendType());
			toMedia(message, object);

		}
		return object;
	}

	/**
	 * <AUTHOR>
	 * @param msg参数体
	 *            ，media转成message对象
	 * @return
	 */
	public void toMsg(JSONObject msg, MessageInfo message) {

		String msgType = msg.getString(MEDIA_PARAM_MSG_TYPE1);
		message.setMsgType(msgType);
		JSONObject obj = new JSONObject();

		// 判断消息类型
		switch (PlatformMessage.MsgType.valueOf(msgType)) {
		case appointment:
			obj.put(MEDIA_PARAM_APPOINTMENT_TIME, msg.get(MEDIA_PARAM_APPOINTMENT_TIME));
			obj.put(MEDIA_PARAM_APPOINTMENT_ACCOUNT, msg.get(MEDIA_PARAM_APPOINTMENT_ACCOUNT));
			obj.put(MEDIA_PARAM_APPOINTMENT_NAME, msg.get(MEDIA_PARAM_APPOINTMENT_NAME));
			obj.put(MSG_PARAM_TEXT, msg.get(msg.get(MEDIA_PARAM_CONTENT)));
			message.setContent(obj.toString());
			break;
		case text:
		case notice:
			message.setContent((msg.getString(MEDIA_PARAM_CONTENT)));
			break;
		case audio:
			
		case image:
			message.setContent(msg.getString(MSG_PARAM_URL));
			
			break;
		case file:
			
			StringBuilder urlb =new StringBuilder();
			JSONArray url=JSONArray.parseArray(msg.getString(MSG_PARAM_URL));
			for (int i = 0; i < url.size(); i++) {
				urlb.append(url.getJSONObject(i).getString("fileUrl")+";");
			}
			JSONObject urljson=new JSONObject();
			urljson.put("url", urlb.toString());
			message.setContent(urljson.toJSONString());

			break;

		case location:
			obj.put(MSG_PARAM_LOCATION_X, msg.get(MSG_PARAM_LOCATION_X));
			obj.put(MSG_PARAM_LOCATION_Y, msg.get(MSG_PARAM_LOCATION_Y));
			message.setContent(obj.toString());
			break;

		case news:
			obj.put(MSG_PARAM_URL, msg.get(MSG_PARAM_URL));
			obj.put(MSG_PARAM_TEXT, msg.get(msg.get(MEDIA_PARAM_CONTENT)));
			message.setContent(obj.toString());
			break;
		case notice_refresh:
			obj.put(MSG_PARAM_MESSAGE, msg.get(MEDIA_PARAM_CONTENT));
			obj.put(MSG_PARAM_PARAM, msg.get(MSG_PARAM_PARAM));
			message.setContent(obj.toString());
			break;
		case msgLink:
			message.setMsgType("msgLink");
			obj.put(MSG_PARAM_URL, msg.get(MSG_PARAM_URL));
			if (msg.containsKey(MSG_PARAM_DATA)) {
				obj.put(MSG_PARAM_DATA, msg.get(MSG_PARAM_DATA));
			}

			message.setContent(msg.getString("content"));
			break;
		default:
			throw new NullPointerException("请查看消息类型" + msgType);
		}

	}

	/**
	 * <AUTHOR>
	 * @param msg参数体
	 *            ，message对象转成media
	 * @return
	 */
	public void toMedia(MessageInfo message, JSONObject object) {

		JSONObject objectMsg = null;
		JSONObject objectCotent = new JSONObject();
		if (message != null) {

			String content = message.getContent();
			if (content.startsWith("{")) {
				objectCotent = JSONObject.parseObject(message.getContent());
			}

			switch (PlatformMessage.MsgType.valueOf(message.getMsgType())) {
			case appointment:
				objectMsg = new JSONObject();
				objectMsg.put(MEDIA_PARAM_APPOINTMENT_TIME, objectCotent.get(MEDIA_PARAM_APPOINTMENT_TIME));
				objectMsg.put(MEDIA_PARAM_APPOINTMENT_ACCOUNT, objectCotent.get(MEDIA_PARAM_APPOINTMENT_ACCOUNT));
				objectMsg.put(MEDIA_PARAM_APPOINTMENT_NAME, objectCotent.get(MEDIA_PARAM_APPOINTMENT_NAME));
				objectMsg.put(MEDIA_PARAM_CONTENT, objectCotent.get(MSG_PARAM_TEXT));
				break;
			case text:
				objectMsg = new JSONObject();
				objectMsg.put(MEDIA_PARAM_CONTENT, message.getContent());
				break;
			case image:
				objectMsg = new JSONObject();
				objectMsg.put(MSG_PARAM_URL, content);
				break;
			case audio:
			case file:
				
				objectMsg = new JSONObject();
				
				objectMsg.put(MSG_PARAM_URL, objectCotent.getString(MSG_PARAM_URL));
				break;
			case location:
				objectMsg = new JSONObject();
				objectMsg.put(MSG_PARAM_LOCATION_X, objectCotent.get(MSG_PARAM_LOCATION_X));
				objectMsg.put(MSG_PARAM_LOCATION_Y, objectCotent.get(MSG_PARAM_LOCATION_Y));
				break;
			case news:
				objectMsg = new JSONObject();
				objectMsg.put(MEDIA_PARAM_CONTENT, objectCotent.get(MSG_PARAM_TEXT));
				objectMsg.put(MSG_PARAM_URL, objectCotent.get(MSG_PARAM_URL));
			default:
				throw new NullPointerException("暂不支持该消息类型" + message.getMsgType());
			}
			objectMsg.put(MEDIA_PARAM_MSG_TYPE1, message.getMsgType());
			object.put(MSG_PARAM_MSG, objectMsg);
		}
	}
}
