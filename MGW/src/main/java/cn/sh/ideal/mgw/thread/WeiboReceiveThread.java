package cn.sh.ideal.mgw.thread;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import weibo4j.Timeline;
import weibo4j.http.AccessToken;
import weibo4j.http.HttpClient;
import weibo4j.http.Response;
import weibo4j.model.Comment;
import weibo4j.model.CommentWapper;
import weibo4j.model.PostParameter;
import weibo4j.model.Status;
import weibo4j.model.StatusWapper;
import weibo4j.model.WeiboException;
import weibo4j.util.WeiboConfig;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.MsgConvertUtil;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.dao.CommonDao;
import cn.sh.ideal.mgw.dao.MediaReceiveDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.dao.MessageInfoSendDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageLogDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageTempDao;
import cn.sh.ideal.mgw.dao.WeiboCommentsDao;
import cn.sh.ideal.mgw.model.MediaModel;
import cn.sh.ideal.mgw.model.ReceiveMessageLog;
import cn.sh.ideal.mgw.model.ReceiveMessageTemp;
import cn.sh.ideal.mgw.model.UserTimelineIds;
import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.service.WeiboCommentsSumService;
import cn.sh.ideal.mgw.service.dubbo.WeiboCommentsServiceImpl;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.service.local.impl.BaseService;
import cn.sh.ideal.mgw.utils.HttpUtil;
import cn.sh.ideal.mgw.utils.IdealFtpClient;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.Util;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.MessageInfoSend;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.WeiboComentsSum;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

public class WeiboReceiveThread implements Runnable {

	public WeiboReceiveThread() {
		super();
	}

	private static final Logger log = LoggerFactory.getLogger(WeiboReceiveThread.class);
	
	private WeiboCommentsDao weiboCommentsDao;
	private MediaReceiveDao mediaReceiveDao;
	private SensitiveService sensitiveService;
	private LocalSessionService LocalsessionService;

	private ReceiveMessageLogDao receiveMessageLogDao;
	private ReceiveMessageTempDao receiveMessageTempDao;
	
	@Autowired
	private MessageInfoSendDao messageInfoSendDao;

	private  JSONObject jsonInfo;
	private CommonDao commonDao;
	private SysInitService initService;
	
	private String accessTokens;
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private WeiboCommentsSumService weiboCommentsSumService;
	
	
	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;
	
	private final String channelCode = "1005";

	private HttpClient client = new HttpClient();
	SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Autowired
	private MessageInfoDao messageInfoDao;
	private static String mirUrl = (String) PropertiesUtil.getProperty(PropertiesUtil.APP, "media.mirUrl");

	public WeiboReceiveThread(JSONObject json, ApplicationContext context) {
		this.jsonInfo = json;
		this.commonDao = context.getBean("commonDao", CommonDao.class);
		this.mediaReceiveDao = context.getBean("mediaReceiveDao", MediaReceiveDao.class);
		this.weiboCommentsDao = context.getBean("weiboCommentsDao", WeiboCommentsDao.class);
		this.messageInfoSendDao = context.getBean("messageInfoSendDao", MessageInfoSendDao.class);
		
		this.weiboCommentsSumService = context.getBean("weiboCommentsSumService", WeiboCommentsSumService.class);
		this.redisDao = context.getBean("redisDao", RedisDao.class);
		this.redisMessage = context.getBean("redisDao",RedisDao.class);
		this.receiveMessageLogDao = context.getBean("receiveMessageLogDao", ReceiveMessageLogDao.class);
		this.receiveMessageTempDao = context.getBean("receiveMessageTempDao", ReceiveMessageTempDao.class);

		this.messageInfoDao = context.getBean("messageInfoDao", MessageInfoDao.class);
		this.sensitiveService = context.getBean("sensitiveService", SensitiveService.class);
		this.LocalsessionService = context.getBean("LocalsessionService", LocalSessionService.class);
		this.initService = context.getBean("sysInitService",
				SysInitService.class);
	}

public void run() {
		
		String uid = jsonInfo.getString("uid");
		  log.info("uid=="+uid);
		//String uid = "5379187935";
		//String accessToken = "2.00jVWCsF0AoYz555936c5d19WOtaRC";//(String) redisDao.readValue("weibo_accesstoken_" + uid);
	   // String accessToken = "2.00nYS2PEJ_wqpCa35601d493eqnd2E";//(String) redisDao.readValue("weibo_accesstoken_" + uid);
	   String accessToken="";
	   try {
		   accessToken = (String)redisDao.readValue("weibo2_accesstoken_" +uid);
		   log.info("accessToken=="+accessToken);
		   accessTokens = accessToken;
			
		try {
			// 定时刷新微博评论数接口
			log.error("定时刷新微博评论数接口调用开始");
			getCommentsSum(accessTokens);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("定时刷新微博评论数接口调用失败", e);
		}
		
		try {
			// 获取@我的评论 这个用
			log.info("@我的评论开始");
			getCommentMention(accessTokens);
			log.info("@我的评论结束");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("调用@我的评论接口失败", e);
		}
		try {
			// 获取@我的微博
			log.info("@我的微博开始");
			String access_tokens = "2.00Phm2nBbkPrcB06dda69b61obc_HC";
			String uidAt = "1645365377";
			 getStatusMentition(access_tokens,uidAt);
			 log.info("@我的微博结束");
			// 获取用户发布的微博
			//getUserTimeline(uid, access_token);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("调用用户@我的微博接口失败", e);
		}

		try {
			// 根据微博id获取评论列表
			//getCommentsById(access_token);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("根据微博id获取评论接口调用失败", e);
		}
		try {
			// 获取某个用户收到的评论列表 这个用 19
			log.info("获取收到的评论开始");
			getCommentsByUser(uid, accessTokens);
			log.info("获取收到的评论结束");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("获取某个用户收到的评论列表接口调用失败", e);
		}
	    
		/*try {
			//返回微博的全部转发微博列表  21
			log.info("获取转发微博列表开始");
			getReportStatusByWbid(accessToken);
			log.info("获取转发微博列表结束");
		} catch (Exception e) {
			e.printStackTrace();
			log.info("返回微博的全部转发微博列表接口失败"+e);
		}*/

		log.info("----------结束接收微博------------" + jsonInfo.getString("uid") + ":" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
		
		//根据微博id获取该微博的点赞数评论数，转发数字
		
		} catch (Exception e) {
			log.error("token值为空：uid="+uid,e);
		}
		}

	public MediaModel queryWeibo(String access_token) throws WeiboException {
		Timeline tm = new Timeline();
		tm.setToken(access_token);
		MediaModel model = new MediaModel();
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		// 判断是否是第一次获取微博，否则获取最新的微博信息。
		try {
			StatusWapper status = null;

			msgLog.setChannelAccount(jsonInfo.getString("uid"));
			msgLog.setTenantId(jsonInfo.getString("tenant"));
			String maxWeiboId = receiveMessageLogDao.getChannelMessageId(msgLog);
			client.setToken(access_token);
			if ((maxWeiboId == null ? maxWeiboId = "" : maxWeiboId).equals("")) {
				Response res = client.get(WeiboConfig.getValue("baseURL") + "statuses/mentions.json");
				JSONObject json = (JSONObject) JSONObject.parse(res.asJSONObject().toString());
				status = constructWapperStatus(json);

			} else {
				Response res = client.get(WeiboConfig.getValue("baseURL") + "statuses/mentions.json?since_id=" + maxWeiboId);
				JSONObject json = (JSONObject) JSONObject.parse(res.asJSONObject().toString());

				status = constructWapperStatus(json);
			}
			log.info("-----------开始收取对应账号的微博信息---------------");
			for (Status s : status.getStatuses()) {

				// 图片作为附件处理上传到FTP上

				String weiboId = s.getId().toString(); // 微博ID
				String weiboText = s.getText(); // 微博内容
				String nickName = s.getUser().getName();
				String senderId = s.getUser().getId().toString();
				String reciveName = jsonInfo.getString("nickName"); // 接收人
				String sendTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(s.getCreatedAt());
				String reciveTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

				String forwardNum = Integer.valueOf(s.getRepostsCount()).toString();
				String commentNum = Integer.valueOf(s.getCommentsCount()).toString();
				String fansNum = Integer.valueOf(s.getUser().getFollowersCount()).toString();
				// 给模板赋值
				model.setTaskId(commonDao.newMessageId());
				model.setMediaContent(weiboText);
				model.setUid(senderId);
				model.setMediaId(weiboId);
				model.setMediaSender(nickName);
				model.setMediaReceiver(reciveName);
				model.setMediaSendTime(sendTime);
				model.setMediaReceiveTime(reciveTime);
				model.setChannelType("3");// 微博渠道
				model.setMediaFowardNum(forwardNum);
				model.setMediaCommentsNum(commentNum);
				model.setMediaFunsNum(fansNum);
				model.setMediaFileName(s.getThumbnailPic());// 微博小图设置在该字段中
				model.setMediaFilePath(s.getBmiddlePic()); // 微博中图设置在该字段中

				msgLog.setChannelMessageId(weiboId);
				msgLog.setMessageData(weiboText);
				msgLog.setChannelAccount(jsonInfo.getString("uid"));
				msgLog.setMessageId(model.getTaskId());
				msgLog.setTenantId(jsonInfo.getString("tenant"));

				receiveMessageLogDao.insert(msgLog);
				JSONArray picUrls = JSONArray.parseArray(s.getPicUrls().toString());

				InsertMessage(model, jsonInfo.getString("tenant"), picUrls);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return model;
	}

	/**
	 * 获取用户发布的微博列表id
	 * @throws WeiboException 
	 * 
	 */

	public void getUserTimelineIds(String uid,String token) throws WeiboException{
		UserTimelineIds ids =getUserTimelineIdsByUid(uid,token);
		parseWbIds(ids,Constants.WB_GET_USER_TIMELINESIDS,uid);
	}
	
	/**
	 * 获取用户发布的微博列表
	 * 
	 * @throws WeiboException
	 * 
	 */

	public void getUserTimeline(String uid, String token) throws WeiboException {
		StatusWapper status = getUserTimelineByUid(uid, token);
		parseWbContent(status, Constants.WB_GET_USER_TIMELINET);
	}
	
	/**
	 * 返回一条微博的全部转发微博列表。
	 * 
	 * @throws WeiboException
	 * 
	 */

	public void getReportStatusByWbid(String token) throws WeiboException {

		
		// 获取微博id
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		//msgLog.setOperType(Constants.WB_GET_USER_TIMELINESIDS);
		//msgLog.setTenantId(jsonInfo.getString("tenant"));
		//msgLog.setChannelAccount(jsonInfo.getString("uid"));
		//msgLog.setTenantId("530000");
		//msgLog.setChannelAccount(uid);
		
		//到发布的微博中去找微博id
		
		List<WeiboMsgInfo> weiboMsgInfoList = null;
		MessageInfoSend messageInfoSend = new MessageInfoSend();
		messageInfoSend.setType("1");
		messageInfoSend.setMsgType("weibo");
		
		weiboMsgInfoList = messageInfoSendDao.historyInfo(messageInfoSend);
		
		log.info("weiboMsgInfoList大小为：："+weiboMsgInfoList.size());
		
		for(WeiboMsgInfo weiboMsgInfo:weiboMsgInfoList){
			
			String weiboid =weiboMsgInfo.getWeiboId();
			
			try {
				
				Date date=new Date();
				DateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String time=format.format(date);
				log.info("时间为："+time+"---微博id:"+weiboid);
				if(StringUtils.isNotBlank(weiboid)){
					StatusWapper status = getRepostTimeline(weiboid, token);
					parseWbContentZf(status, Constants.WB_GET_USER_TIMELINESSTATUS);
				}
				Thread.sleep(600000);
			} catch (Exception e) {
				// TODO: handle exception
			}
			
		}		

	}

	/**
	 * 获取指定微博的转发微博列表
	 * 
	 * @return list of the user_timeline
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/user_timeline
	 * @since JDK 1.5
	 */
	public StatusWapper getRepostTimeline(String wbid, String token) throws WeiboException {
		client.setToken(token);
		long maxId = getMaxId(Constants.WB_GET_USER_TIMELINESSTATUS,wbid);
		log.info("进入getRepostTimeline方法：wbid="+wbid);
		if (maxId == 0) {
			try {
				log.info("maxId等于0：maxId="+maxId);
				return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/repost_timeline.json?id=" + wbid));
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
		} else {
			log.info("maxId不等于0：maxId="+maxId);
			return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/repost_timeline.json?id=" + wbid+"&since_id="+maxId));
		}
	}
	
	
	
	/**
	 * 获取某个用户收到的评论列表
	 * @param token:access_token
	 * @throws WeiboException 
	 * 
	 */
	
	public void getCommentsByUser(String uid,String token) throws WeiboException {
		CommentWapper commentWapper =getCommentByUser(token,uid);
		parseComment(commentWapper,Constants.WB_GET_COMMENT_LIST_BYUSER);
	}
	
	/**
	 * 解析微博列表ids
	 */
	public void parseWbIds(UserTimelineIds wbIds,String type,String uid) {
		List<String> ids=wbIds.getStatusesIds();
		for (String id:ids) {
			MediaModel model = new MediaModel();
			model.setTaskId(commonDao.newMessageId());
			//插入数据库
			insertReceiveMsgLog(model.getTaskId(), "", id, type);
		}
	}
	
	/**
	 * 获取某个用户最新发表的微博列表ID
	 * 
	 * @return user_timeline IDS
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/statuses/user_timeline
	 * @since JDK 1.5
	 */
	public UserTimelineIds getUserTimelineIdsByUid(String uid,String token)throws WeiboException {
		client.setToken(token);
		long maxId=getMaxId(Constants.WB_GET_USER_TIMELINESIDS);
		if(maxId==0){
			return new UserTimelineIds(client.get(
					WeiboConfig.getValue("baseURL")+ "statuses/user_timeline/ids.json?uid="+uid));
			
		}else{
			return new UserTimelineIds(client.get(
					WeiboConfig.getValue("baseURL")+ "statuses/user_timeline/ids.json?uid="+uid+"&since_id="+maxId));
		}

	}
	
	/**
	 * 获取用户收到的评论列表
	 * 
	 * @param token
	 *            access_token
	 * @param uid
	 *            微博原始id
	 * @return list of Comment
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/comments/show
	 * @since JDK 1.5
	 */
	public CommentWapper getCommentByUser(String token,String uid) throws WeiboException {
		long maxId=getMaxId(Constants.WB_GET_COMMENT_LIST_BYUSER);
		client.setToken(token);
		if(maxId==0){
			return Comment.constructWapperComments(client.get(
					WeiboConfig.getValue("baseURL") + "comments/to_me.json"));
		}else{
			return Comment.constructWapperComments(client.get(
					WeiboConfig.getValue("baseURL") + "comments/to_me.json?since_id="+maxId));
			
		}
	}
	
	/**
	 * 根据微博id获取评论列表
	 * 
	 * @param token
	 *            :access_token
	 * 
	 */
	public void getCommentsById(String token) throws WeiboException {
		// 获取微博id
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		msgLog.setOperType(Constants.WB_GET_USER_TIMELINET);
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setChannelAccount(jsonInfo.getString("uid"));

		List<ReceiveMessageLog> msgList = receiveMessageLogDao.query(msgLog);
		for (ReceiveMessageLog list : msgList) {
			String chMsgId = list.getChannelMessageId();
			if (StringUtils.isNotBlank(chMsgId)) {
				// 根据微博id获取评论列表
				CommentWapper commentWapper = getCommentById(chMsgId, token);
				parseComment(commentWapper, Constants.WB_GET_COMMENT_LIST);
			}
		}

	}

	/**
	 * 获取最新的提到当前登录用户的评论，即@我的评论9
	 * 
	 * @param token
	 * @throws WeiboException
	 * 
	 */
	public void getCommentMention(String token) throws WeiboException {
		CommentWapper commentWapper = getCommentMentions(token);
		parseComment(commentWapper, Constants.WB_GETCOMMENTMENTIONS);
	}
	
	/**
	 * 根据微博id获取评论数和转发数 //根据微博id获取该微博的点赞数评论数，转发数字
	 */
	public void getCommentsSum(String token){
		//先从历史数据库中获取历史微博信息，获取微博id
		/*List<WeiboMsgInfo> weiboMsgInfoList = null;
		MessageInfoSend messageInfoSend = new MessageInfoSend();
		messageInfoSend.setType("1");
		messageInfoSend.setMsgType("weibo");
		weiboMsgInfoList = messageSendDao.historyInfo(messageInfoSend);
		System.out.println("大小为："+weiboMsgInfoList.size());*/
		
		 WeiboComentsSum weibocoment = new WeiboComentsSum();
			 weibocoment.setAccessTokens(accessTokens);
			 List<WeiboComentsSum> weiboComentsList = weiboCommentsSumService.weiboComentSumSend(weibocoment);
			 log.info("根据微博id获取评论数和转发数结束");
	}

	/**
	 * 获取最新的提到登录用户的微博列表，即@我的微博
	 * 
	 * @throws WeiboException
	 * 
	 */

	public void getStatusMentition(String token,String uid) throws WeiboException {
		StatusWappers status = getMentions(token,uid);
		parseWbContentAtWeibo(status, Constants.WB_GETMENTIONS);
	}

	/**
	 * 获取某个用户最新发表的微博列表
	 * 
	 * @return list of the user_timeline
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/user_timeline
	 * @since JDK 1.5
	 */
	public StatusWapper getUserTimelineByUid(String uid, String token) throws WeiboException {
		client.setToken(token);
		long maxId = getMaxId(Constants.WB_GET_USER_TIMELINET);
		if (maxId == 0) {
			return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/user_timeline.json?uid=" + uid));
		} else {
			return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/user_timeline.json?uid=" + uid + "&since_id=" + maxId));

		}

	}

	/**
	 * 获取最新的提到当前登录用户的评论，即@我的评论
	 * 
	 * @return list of Comment
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/comments/mentions
	 * @since JDK 1.5
	 */
	public CommentWapper getCommentMentions(String token) throws WeiboException {
		client.setToken(token);
		long maxId = getMaxId(Constants.WB_GETCOMMENTMENTIONS);
		if (maxId == 0) {
			log.info(WeiboConfig.getValue("baseURL") + "comments/mentions.json");
			return Comment.constructWapperComments(client.get(WeiboConfig.getValue("baseURL") + "comments/mentions.json"));

		} else {
			log.info(WeiboConfig.getValue("baseURL") + "comments/mentions.json?since_id=" + maxId);
			return Comment.constructWapperComments(client.get(WeiboConfig.getValue("baseURL") + "comments/mentions.json?since_id=" + maxId));
		}
	}

	/**
	 * 获取最新的提到登录用户的微博列表，即@我的微博
	 * 
	 * @return list of Status
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.0
	 * @see http://open.weibo.com/wiki/2/statuses/mentions
	 * @since JDK 1.5
	 */
	public StatusWappers getMentions(String token,String uid) throws WeiboException {
		client.setToken(token);
		long maxId = getMaxId(Constants.WB_GETMENTIONS);
		if (maxId == 0) {
			log.info(WeiboConfig.getValue("baseURL") + "statuses/mentions/other.json");
			//return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/mentions.json"));
			return Statuss.constructWapperStatus(client.get("https://c.api.weibo.com/2/statuses/mentions/other.json?uid="+uid));
		} else {
			log.info(WeiboConfig.getValue("baseURL") + "statuses/mentions/other.json?since_id=" + maxId);
			//return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/mentions.json?since_id=" + maxId));
			//return Status.constructWapperStatus(client.get(WeiboConfig.getValue("baseURL") + "statuses/mentions/other.json?since_id=" + maxId));
			return Statuss.constructWapperStatus(client.get("https://c.api.weibo.com/2/statuses/mentions/other.json?uid="+uid+"&since_id="+maxId));
		}
	}

	public static void main(String[] args) {
		WeiboReceiveThread wb = new WeiboReceiveThread();
		try {

			// @我的评论
			// wb.getCommentMention("2.00jVWCsFJ_wqpC143c6301566JghbB");
			// CommentWapper
			// ss=wb.getCommentMentions("2.00jVWCsFJ_wqpC143c6301566JghbB");
			// StatusWapper
			// ss=wb.getUserTimelineByUid("5445845963","2.00N_NYwFUrIfxC2c9d35703eZGxspB");
			CommentWapper ss = wb.getCommentById("3875327304497189", "2.00jVWCsFJ_wqpC143c6301566JghbB");
			// StatusWapper ss=wb.getRepostTimeline("3851458946955601",
			// "2.00N_NYwFUrIfxC2c9d35703eZGxspB");
			// CommentWapper
			// ss=wb.getCommentToMe("2.00N_NYwFUrIfxC2c9d35703eZGxspB");
			// Comment ss
			// =wb.replyComment("3851493231267982","3851490311943979","第san次回复评论","2.00N_NYwFUrIfxC2c9d35703eZGxspB");
			// CommentWapper
			// ss=wb.getCommentTimeline("2.00N_NYwFUrIfxC2c9d35703eZGxspB");
			// StatusWapper
			// ss=wb.getMentions("2.00jVWCsFJ_wqpC143c6301566JghbB");
			String sss = JSONObject.toJSONString(ss);
			System.out.println("sss.size()=" + sss.length());
			System.out.println("resule=" + sss);
		} catch (WeiboException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 根据微博ID返回某条微博的评论列表
	 * 
	 * @param id
	 *            需要查询的微博ID
	 * @param token
	 *            access_token
	 * @param maxCommentId
	 *            当前的评论id
	 * @return list of Comment
	 * @throws WeiboException
	 *             when Weibo service or network is unavailable
	 * @version weibo4j-V2 1.0.1
	 * @see http://open.weibo.com/wiki/2/comments/show
	 * @since JDK 1.5
	 */
	public CommentWapper getCommentById(String wbid, String token) throws WeiboException {
		long wbId = Long.parseLong(wbid);
		long maxId = getMaxId(Constants.WB_GET_COMMENT_LIST, wbid);
		client.setToken(token);
		if (maxId == 0) {
			return Comment.constructWapperComments(client.get(WeiboConfig.getValue("baseURL") + "comments/show.json?id=" + wbId));

		} else {
			return Comment.constructWapperComments(client.get(WeiboConfig.getValue("baseURL") + "comments/show.json?id=" + wbId + "&since_id=" + maxId));

		}
	}

	/**
	 * 
	 * 判断是否是第一次信息
	 * 
	 * @return
	 */
	public long getMaxId(String operType, String msgId) {
		ReceiveMessageTemp msgTmp = new ReceiveMessageTemp();
		// 判断是否是第一次获取信息，否则获取最新的信息。
		msgTmp.setChannelAccount(jsonInfo.getString("uid"));
		msgTmp.setTenantId(jsonInfo.getString("tenant"));
		msgTmp.setOperType(operType);
		msgTmp.setChannelMessageId(msgId);
		String maxId = receiveMessageTempDao.getMessageTempId(msgTmp);
		maxId = maxId == null ? "0" : maxId;
		long maxid = Long.parseLong(maxId);
		return maxid;
	}

	/**
	 * 
	 * 判断是否是第一次信息
	 * 
	 * @return
	 */
	public long getMaxId(String operType) {
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		// 判断是否是第一次获取信息，否则获取最新的信息。
		msgLog.setChannelAccount(jsonInfo.getString("uid"));
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setOperType(operType);
		String maxId = receiveMessageLogDao.getChannelMessageId(msgLog);
		maxId = maxId == null ? "0" : maxId;
		long maxid = Long.parseLong(maxId);
		return maxid;
	}

	/**
	 * 解析评论
	 * 
	 * @param commentWapper
	 * @return
	 */

	public void parseComment(CommentWapper commentWapper, String reqType) {
		log.info("-----------开始解析微博评论列表---------------");
		for (Comment comment : commentWapper.getComments()) {
			MediaModel model = new MediaModel();
		
			String wbId = comment.getStatus().getId();// 微博id
			String commentId = Long.valueOf(comment.getId()).toString();// 评论id
			String content = comment.getText();// 评论内容
			weibo4j.org.json.JSONArray picUrls = comment.getStatus().getPicUrls(); //如果原微博有图片的话，获取图片信息
			String userId = comment.getUser().getId();// 评论人Id
			String senderName = comment.getUser().getName();// 评论人昵称
			String sendTime = sf.format(comment.getCreatedAt());// 评论时间
			Status status =comment.getStatus();
			String reciveName = jsonInfo.getString("uid"); // 接收人
			String reciveTime = sf.format(new Date());
			String mid = comment.getMid();
			String source = comment.getSource();
			//获取用户的头像信息
			String headimgUrl = comment.getUser().getProfileImageUrl();
			
			
			if(Constants.WB_GETCOMMENTMENTIONS.equals(reqType)){
				//获取原始微博的用户id
				String YuanWeiboUserid = status.getUser().getId();
				model.setYuanWeiboUserid(YuanWeiboUserid);
			}
			

			model.setWbId(wbId);
			model.setMediaReceiver(reciveName);
			model.setTaskId(commonDao.newMessageId());
			model.setMediaId(commentId);
			model.setMediaContent(content);
			model.setMediaSender(senderName);
			
			model.setMediaSendTime(sendTime);
			model.setCoSource(source);
			model.setMediaReceiver(reciveName);
			model.setMediaReceiveTime(reciveTime);
			String picUrlss = comment.getStatus().getPicUrls().toString(); //如果原微博有图片的话，获取图片信息
			model.setUid(userId);
			model.setMid(mid);
			model.setChannelType("3");// 微博渠道
			model.setOperType(reqType);// 评论
			model.setPicUrls(picUrls);
			model.setStatus(status);
			//微博原图originalPic
			model.setPicUrls(picUrls);
			//获取用户的头像信息
			model.setHeadimgUrl(headimgUrl);
			
			Comment replyCom = comment.getReplycomment();
			if (replyCom != null) {
				model.setOperType(Constants.WB_GET_REPLYCOMMENT);// 回复
				model.setRepId(replyCom.getIdstr());// 评论id
			}
			insertReceiveMsgLog(model.getTaskId(), content, commentId, reqType);
			if (Constants.WB_GET_COMMENT_LIST.equals(reqType)) {
				insertReceiveMsgTemp(wbId, commentId, reqType);
			}
			MessageInfo mi = insertMessage(model);
			/*if(mi.getContent() !=null){
				JSONObject jsonReq = JSONObject.parseObject(mi.getContent());
				String param = jsonReq.getString("param");
				JSONObject contentReq = JSONObject.parseObject(param);
				//微博id
				String weiboId = contentReq.getString("weiboid");
				
				
				
				jsonReq.put("picurlss",mi.getPicUrls().toString());
				if(weiboId!=null){
					WeiboComentsSum weiboComentsSum = new WeiboComentsSum();
					weiboComentsSum.setId(weiboId);
					List<WeiboComentsSum> weiboComentsList = weiboCommentsDao.query(weiboComentsSum);
					 if(weiboComentsList.size()>0){
						// mi.setComments(weiboComentsList.get(0).getComments());
						//mi.setReposts(weiboComentsList.get(0).getReposts());
						//mi.setAttitudes(weiboComentsList.get(0).getAttitudes());
						 
						 jsonReq.put("commentss",weiboComentsList.get(0).getComments());
						 jsonReq.put("repostss",weiboComentsList.get(0).getReposts());
						 jsonReq.put("attitudess",weiboComentsList.get(0).getAttitudes());
					 }else{
						 jsonReq.put("commentss",0);
						 jsonReq.put("repostss",0);
						 jsonReq.put("attitudess",0);
					 }
					 mi.setContent(jsonReq.toString());
					//然后通过微博id去调用喜欢数接口
					 WeiboComentsSum weibocoment = new WeiboComentsSum();
					 weibocoment.setIds(weiboId);
					 weibocoment.setAccessTokens(accessTokens);
					 List<WeiboComentsSum> weiboComentsList = weiboCommentsSumService.weiboComentSumSend(weibocoment);
					 if(weiboComentsList.size()>0){
						 mi.setComments(weiboComentsList.get(0).getComments());
						 mi.setReposts(weiboComentsList.get(0).getReposts());
						 mi.setAttitudes(weiboComentsList.get(0).getAttitudes());
					 }
					
					
				}
			}*/
			
			
			try {
				if(redisMessage==null){
					System.out.println("null");
				}
				//mi.setWorkNo("6001");
				String messageString = JSON.toJSONString(mi);
				
				redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,mi);
				//MessageInfo mis1 = redisMessage.listrPop(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//MessageInfo mis =  redisMessage.readValue(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//MessageInfo mis1 =   (MessageInfo) redisDao.getKeysByPattern(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//String messageStrings = JSON.toJSONString(mis1);
				//log.info("wechat messageInfo write in redisss : "+ messageStrings);
				//MessageInfo mis1 =	redisMessage.listlPop(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//String messageStrings1 = JSON.toJSONString(mis1);
				//log.info("wechat messageInfo write in redisss : "+ JSON.toJSONString(mis1));
				
				
				//MessageInfo mis2 = redisMessage.listrPop(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//MessageInfo mis3 = redisMessage.setPop(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//MessageInfo mis4 = redisMessage.(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				//MessageInfo mis5 = redisMessage.listrPop(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE);
				log.info("wechat messageInfo write in redis : "+ messageString);
			
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}

	}

	/**
	 * 解析微博内容
	 * 
	 * @param statusWapper
	 * @param reqType
	 */

	public void parseWbContent(StatusWapper statusWapper, String reqType) {
		for (Status s : statusWapper.getStatuses()) {
			
			String weiboId = s.getId().toString(); // 微博ID
			// 获取该微博的评论列表
			// this.getCommentById(weiboId,);
			MediaModel model = new MediaModel();

			String weiboText = s.getText(); // 微博内容
			String nickName = s.getUser().getName();
			String senderId = s.getUser().getId().toString();
			String reciveName = jsonInfo.getString("uid"); // 接收人
			String sendTime = sf.format(s.getCreatedAt());
			String reciveTime = sf.format(new Date());
			String geo = s.getGeo();// 地理信息字段
			String mid = s.getMid();
			weibo4j.org.json.JSONArray picUrls = s.getPicUrls();
			String forwardNum =Integer.valueOf(s.getRepostsCount()).toString();// 转发数
			String commentNum = Integer.valueOf(s.getCommentsCount()).toString();
			String fansNum = Integer.valueOf(s.getUser().getFollowersCount()).toString(); // 粉丝数
			String smallPic = s.getThumbnailPic();// 缩略图，小图
			String middlePic = s.getBmiddlePic();// 中图
			String originalPic = s.getOriginalPic();// 原图
			// 给模板赋值
			model.setUid(senderId);

			model.setTaskId(commonDao.newMessageId());
			model.setMediaId(weiboId);
			model.setMediaContent(weiboText);
			model.setMediaSender(nickName);
			model.setMediaSendTime(sendTime);
			model.setMediaReceiver(reciveName);
			model.setMediaReceiveTime(reciveTime);

			model.setGeo(geo);
			model.setMid(mid);
			model.setMediaFowardNum(forwardNum);
			model.setMediaCommentsNum(commentNum);
			model.setPicUrls(picUrls);
			model.setSmallPic(smallPic);// 微博小图设置在该字段中
			model.setMiddlePic(middlePic); // 微博中图设置在该字段中
			model.setOriginalPic(originalPic); // 微博原图设置在该字段中
			model.setWbSource(s.getSource());
			model.setVisible(s.getVisible());
			model.setChannelType("3");// 微博渠道
			model.setMediaFunsNum(fansNum);
			model.setOperType(reqType);// 
			// 上传图片
			uploadFile(model);
			insertReceiveMsgLog(model.getTaskId(), weiboText, weiboId, reqType);
			if (Constants.WB_GET_USER_TIMELINESSTATUS.equals(reqType)) {
				//insertReceiveMsgTemp(weiboId, beforeWbid, reqType);
				insertReceiveMsgTemp(weiboId,model.getTaskId(), reqType);
			}
			MessageInfo mi = insertMessage(model);
			String messageString = JSON.toJSONString(mi);
			redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,mi);
			log.info("wechat messageInfo write in redis : "+ messageString);
		}
	}
	
	
	/**
	 * 艾特我的微博 解析内容
	 * 
	 * @param statusWapper
	 * @param reqType
	 */
	public void parseWbContentAtWeibo(StatusWappers statusWapper, String reqType) {
		for (Statuss s : statusWapper.getStatuses()) {
			//String beforeWbid=s.getRetweetedStatus().getMid();
			String weiboId = s.getId().toString(); // 微博ID
			// 获取该微博的评论列表
			// this.getCommentById(weiboId,);
			MediaModel model = new MediaModel();

			String weiboText = s.getText(); // 微博内容
			//原来微博类容
			if(s.getRetweetedStatus().getText()!=null && !"".equals(s.getRetweetedStatus().getText())){
				String yuanWeibo = s.getRetweetedStatus().getText();
				String yuanpicUrl = s.getRetweetedStatus().getBmiddlePic();
				String picIds = s.getRetweetedStatus().getPicIds();
				
				model.setPicIds(picIds);
				model.setYuanWeibo(yuanWeibo);
				//picIds.substring(beginIndex, endIndex)
				System.out.println("dfad");
			}
			String nickName = s.getUser().getName();
			String senderId = s.getUser().getId().toString();
			String reciveName = jsonInfo.getString("uid"); // 接收人
			String sendTime = sf.format(s.getCreatedAt());
			String reciveTime = sf.format(new Date());
			String geo = s.getGeo();// 地理信息字段
			String mid = s.getMid();
			//weibo4j.org.json.JSONArray picUrls = s.getPicUrls();//s.getPicUrls();
			String picUrls1 = s.getThumbnailPic();//s.getPicUrls();
			String forwardNum =Integer.valueOf(s.getRepostsCount()).toString();// 转发数
			String commentNum = Integer.valueOf(s.getCommentsCount()).toString();
			String fansNum = Integer.valueOf(s.getUser().getFollowersCount()).toString(); // 粉丝数
			String smallPic = s.getThumbnailPic();// 缩略图，小图
			String middlePic = s.getBmiddlePic();// 中图
			String originalPic = s.getOriginalPic();// 原图
			//获取用户头像信息
			String headimgUrl = s.getUser().getProfileImageUrl();
			//Status status = s.getStatus();
			
			
			//如果有多个图片的话，就查询多个图片
			//获取图片的pic_ids
			
			// 给模板赋值
			model.setUid(senderId);

			model.setTaskId(commonDao.newMessageId());
			model.setMediaId(weiboId);
			model.setMediaContent(weiboText);
			model.setMediaSender(nickName);
			model.setMediaSendTime(sendTime);
			model.setMediaReceiver(reciveName);
			model.setMediaReceiveTime(reciveTime);

			model.setGeo(geo);
			model.setMid(mid);
			model.setMediaFowardNum(forwardNum);
			model.setMediaCommentsNum(commentNum);
			//model.setPicUrls(picUrls);
			model.setSmallPic(smallPic);// 微博小图设置在该字段中
			model.setMiddlePic(middlePic); // 微博中图设置在该字段中
			model.setOriginalPic(originalPic); // 微博原图设置在该字段中
			model.setWbSource(s.getSource());
			model.setVisible(s.getVisible());
			model.setChannelType("3");// 微博渠道
			model.setMediaFunsNum(fansNum);
			model.setOperType(reqType);// 
			model.setHeadimgUrl(headimgUrl);
			
			// 上传图片
			uploadFiles(model);
			insertReceiveMsgLog(model.getTaskId(), weiboText, weiboId, reqType);
			if (Constants.WB_GETMENTIONS.equals(reqType)) {
				//insertReceiveMsgTemp(weiboId, beforeWbid, reqType);
				insertReceiveMsgTemp(weiboId,model.getTaskId(),reqType);
			}
			MessageInfo mi = insertMessage(model);
			String messageString = JSON.toJSONString(mi);
			redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,mi);
			log.info("wechat messageInfo write in redis艾特我的微博方法: "+ messageString);
		}
	}
	
	
	/**
	 * 转发微博解析微博内容
	 * 
	 * @param statusWapper
	 * @param reqType
	 */
	
	public void parseWbContentZf(StatusWapper statusWapper, String reqType) {
		for (Status s : statusWapper.getStatuses()) {
			String beforeWbid=s.getRetweetedStatus().getMid();
			String weiboId = s.getId().toString(); // 微博ID
			// 获取该微博的评论列表
			// this.getCommentById(weiboId,);
			MediaModel model = new MediaModel();

			String weiboText = s.getText(); // 转发的微博内容
			String nickName = s.getUser().getName();
			String senderId = s.getUser().getId().toString();
			
			//获取原始微博的图片信息
			
			String reciveName = jsonInfo.getString("uid"); // 接收人
			String sendTime = sf.format(s.getCreatedAt());
			String reciveTime = sf.format(new Date());
			String geo = s.getGeo();// 地理信息字段
			String mid = s.getMid();
			
			//获取原始微博的图片信息
			weibo4j.org.json.JSONArray picUrls = s.getRetweetedStatus().getPicUrls();
			String forwardNum =Integer.valueOf(s.getRepostsCount()).toString();// 转发数
			String commentNum = Integer.valueOf(s.getCommentsCount()).toString();
			String fansNum = Integer.valueOf(s.getUser().getFollowersCount()).toString(); // 粉丝数
			String smallPic = s.getThumbnailPic();// 缩略图，小图
			String middlePic = s.getBmiddlePic();// 中图
			String originalPic = s.getOriginalPic();// 原图
			Status status = s.getRetweetedStatus();
			// 给模板赋值
			model.setUid(senderId);
			
			model.setYuanWeiboId(beforeWbid);
			model.setTaskId(commonDao.newMessageId());
			model.setMediaId(weiboId);
			model.setMediaContent(weiboText);
			model.setMediaSender(nickName);
			model.setMediaSendTime(sendTime);
			model.setMediaReceiver(reciveName);
			model.setMediaReceiveTime(reciveTime);
			model.setStatus(status);

			model.setGeo(geo);
			model.setMid(mid);
			model.setMediaFowardNum(forwardNum);
			model.setMediaCommentsNum(commentNum);
			model.setPicUrls(picUrls);
			model.setSmallPic(smallPic);// 微博小图设置在该字段中
			model.setMiddlePic(middlePic); // 微博中图设置在该字段中
			model.setOriginalPic(originalPic); // 微博原图设置在该字段中
			model.setWbSource(s.getSource());
			model.setVisible(s.getVisible());
			model.setChannelType("3");// 微博渠道
			model.setMediaFunsNum(fansNum);
			model.setOperType(reqType);// 
			// 上传图片
			uploadFile(model);
			insertReceiveMsgLog(model.getTaskId(), weiboText, weiboId, reqType);
			if (Constants.WB_GET_USER_TIMELINESSTATUS.equals(reqType)) {
				//insertReceiveMsgTemp(weiboId, beforeWbid, reqType);
				insertReceiveMsgTemp(beforeWbid, weiboId, reqType);
			}
			MessageInfo mi = insertMessage(model);
			String messageString = JSON.toJSONString(mi);
			redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,mi);
			log.info("wechat messageInfo write in redis转发 : "+ messageString);
		}
	}

	/**
	 * 上传图片
	 * 
	 * @param model
	 */
	public void uploadFile(MediaModel model) {

		// 解析图片URL并上传到FTP服务器上
		weibo4j.org.json.JSONArray imageList = model.getPicUrls();
		try {

			// 获取当前时间(精确到天)

			String fileName = "";
			for (int i = 0; i < imageList.length(); i++) {
				String currentReceiveDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
				String path = "/weiboReceiveFile/" + currentReceiveDate + "/" + model.getMediaId() + "/";
				String url = imageList.get(i).toString();

				if (url.lastIndexOf("/") + 1 != 0) {
					fileName = url.substring(url.lastIndexOf("/") + 1);
				}
				InputStream in = PaserPictureUrl(url);
				if (in != null) {
					saveFile(fileName, in, path, model);
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error("附件上传FTP出错:this.getClass().getName()+uploadFile" + e.getMessage());

		}
	}
	public void uploadFiles (MediaModel model) {

		// 解析图片URL并上传到FTP服务器上
		String imageList = model.getOriginalPic();
		try {

			// 获取当前时间(精确到天)

			String fileName = "";
		//	for (int i = 0; i < imageList.length(); i++) {
				String currentReceiveDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
				String path = "/weiboReceiveFile/" + currentReceiveDate + "/" + model.getMediaId() + "/";
				String url = imageList;

				if (url.lastIndexOf("/") + 1 != 0) {
					fileName = url.substring(url.lastIndexOf("/") + 1);
				}
				InputStream in = PaserPictureUrl(url);
				if (in != null) {
					saveFile(fileName, in, path, model);
				}
		//	}

		} catch (Exception e) {
			e.printStackTrace();
			log.error("附件上传FTP出错:this.getClass().getName()+uploadFile" + e.getMessage());

		}
	}

	/**
	 * 将接收到的信息插入到log表
	 * 
	 * @param msgId
	 * @param content
	 * @param chMsgId
	 */
	public void insertReceiveMsgLog(String msgId, String content, String chMsgId, String operType) {
		ReceiveMessageLog msgLog = new ReceiveMessageLog();
		msgLog.setChannelMessageId(chMsgId);
		msgLog.setMessageData(content);
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setChannelAccount(jsonInfo.getString("uid"));
		msgLog.setMessageId(msgId);
		msgLog.setOperType(operType);
		msgLog.setChannelId("1005");
		receiveMessageLogDao.insert(msgLog);
	}

	/**
	 * 将接收到的信息插入到temp表
	 * 
	 * @param msgId
	 * @param content
	 * @param chMsgId
	 */
	public void insertReceiveMsgTemp(String chMsgId, String msgTempId, String operType) {
		ReceiveMessageTemp msgLog = new ReceiveMessageTemp();
		msgLog.setChannelMessageId(chMsgId);
		msgLog.setMessageTempId(msgTempId);
		msgLog.setTenantId(jsonInfo.getString("tenant"));
		msgLog.setChannelAccount(jsonInfo.getString("uid"));
		msgLog.setOperType(operType);
		//msgLog.setcha
		receiveMessageTempDao.insert(msgLog);
	}

	/**
	 * 将接收到的信息插入messageinfo表
	 * 
	 * @param model
	 * @return
	 */
	public MessageInfo insertMessage(MediaModel model) {
		MessageInfo message = null;
		// 微博信息不需要拦截等操作，直接插入到多媒体正式表
		model.setMediaIsAssign("0");
		model.setMediaIsDo("1");
		//weibo 消息type
		String weiboMessageType ="";
		// 获取租户id
		String tenant = jsonInfo.getString("tenant");
		RequestSessionbean requestSessionbean = new RequestSessionbean(model.getUid(), jsonInfo.getString("uid"), Platform.SINAMB.getCode(), tenant,model.getHeadimgUrl());
		log.info("user----------:"+jsonInfo.getString("uid"));
		log.info("jsonInfo----:"+jsonInfo);
		ChannelConfig channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + jsonInfo.getString("uid"));
		//requestSessionbean.setSkillType(channelConfig.getRealTime());
		
		// TODO 区分不同的微博消息类型 ,session save skill_type
		if(model.getOperType().equals(Constants.WB_GETMENTIONS) || model.getOperType().equals(Constants.WB_GETCOMMENTMENTIONS) || model.getOperType().equals(Constants.WB_GET_USER_TIMELINESSTATUS)){
			String wbGetAtMe = PropertiesUtil.getProperty(PropertiesUtil.WEIBO, "wbGetAtMe");
			requestSessionbean.setSkillType(wbGetAtMe);
			weiboMessageType = wbGetAtMe;
		}else if(model.getOperType().equals(Constants.WB_GET_COMMENT_LIST_BYUSER) || model.getOperType().equals(Constants.WB_GET_COMMENT_LIST)){
			String wbGetComments = PropertiesUtil.getProperty(PropertiesUtil.WEIBO, "wbGetComments");
			requestSessionbean.setSkillType(wbGetComments);
			weiboMessageType = wbGetComments;
		}else{
			requestSessionbean.setSkillType(channelConfig.getRealTime());
			weiboMessageType = channelConfig.getRealTime();
		}

		requestSessionbean.setNickname(model.getMediaSender());
		//商业类型
		if(model.getOperType()!=null && !"".equals(model.getOperType())){
			requestSessionbean.setBusinessType(model.getOperType());
		}
		model.getOperType();
		ReceiveMessageLog receivelog =new ReceiveMessageLog();
		
		receivelog.setChannelMessageId(model.getMid());
		receivelog.setChannelAccount(jsonInfo.getString("uid"));
		receivelog.setTenantId(tenant);
		receivelog.setOperType(model.getOperType());
		ReceiveMessageLog receiveMessageLog = receiveMessageLogDao.loadByChannelMessageId(receivelog);
		if (receiveMessageLog != null) {
			model.setHtmlContent(receiveMessageLog.getMessageData());
		}
		SensitiveLog sensitiveLog = new SensitiveLog(Platform.SINAMB.getCode(), tenant, model.getUid(), jsonInfo.getString("uid"), model.getMediaContent());
		if (sensitiveService.filter(sensitiveLog)) {
			log.info("评论消息已经被过滤" + JSON.toJSONString(sensitiveLog));
			message = null;
		} else {
			log.info("请求的requestSessionbean" + JSON.toJSONString(requestSessionbean));
			try {
				//增加weibo的ID区分会话类型
				requestSessionbean.setIdCard(model.getMediaId());
				//微博昵称传递
				requestSessionbean.setNickname(model.getMediaSender());
				SessionInfo sessioninfo = LocalsessionService.getSession(requestSessionbean);
				message = MsgConvertUtil.media2Message(Platform.SINAMB.getCode(), model);
				message.setSessionId(sessioninfo.getSessionId());
				message.setTenantCode(sessioninfo.getTenantCode());
				System.out.println(message.getContent());
				if(model.getPicUrls()!=null){
					message.setPicUrls(model.getPicUrls().toString());
				}
				
				message.setSkillType(weiboMessageType);
				
				JSONObject jsonReq = JSONObject.parseObject(message.getContent());
				String param = jsonReq.getString("param");
				JSONObject contentReq = JSONObject.parseObject(param);
				//微博id
				String weiboId = contentReq.getString("weiboid");
				
				log.info("Constants.WB_GETCOMMENTMENTIONS==="+Constants.WB_GETCOMMENTMENTIONS+"::reTtype="+model.getOperType());
				if(Constants.WB_GETCOMMENTMENTIONS.equals(model.getOperType()) && !"".equals(model.getYuanWeiboUserid())){
					jsonReq.put("YuanWeiboUserid",model.getYuanWeiboUserid());
				}
				if(Constants.WB_GETMENTIONS.equals(model.getOperType())){
					jsonReq.put("YuanWeiboUserid",model.getUid());
					
					if(model.getYuanWeibo()!=null && !"".equals(model.getYuanWeibo())){
						jsonReq.put("picurlsIds",model.getPicIds());
						jsonReq.put("yuanWeibo",model.getYuanWeibo());
					}
					
				}
				if(message.getPicUrls()!=null){
					jsonReq.put("picurls",message.getPicUrls().toString());
				}
				
				 message.setContent(jsonReq.toString());
				 try {
					 messageInfoDao.insert(message);
				} catch (Exception e) {
					log.info(""+e);
				}
				
				if(weiboId!=null){
					WeiboComentsSum weiboComentsSum = new WeiboComentsSum();
					weiboComentsSum.setId(weiboId);
					List<WeiboComentsSum> weiboComentsList = weiboCommentsDao.query(weiboComentsSum);
					 if(weiboComentsList.size()>0){
						// mi.setComments(weiboComentsList.get(0).getComments());
						//mi.setReposts(weiboComentsList.get(0).getReposts());
						//mi.setAttitudes(weiboComentsList.get(0).getAttitudes());
						 
						 jsonReq.put("comments",weiboComentsList.get(0).getComments());
						 jsonReq.put("reposts",weiboComentsList.get(0).getReposts());
						 jsonReq.put("attitudes",weiboComentsList.get(0).getAttitudes());
					 }else{
						 jsonReq.put("comments",0);
						 jsonReq.put("reposts",0);
						 jsonReq.put("attitudes",0);
					 }
					 message.setContent(jsonReq.toString());
				}
			} catch (Exception e) {
				System.out.println("yichang:---------"+e);
			}
			
		}
		return message;
	}

	/**
	 * 给MIR发送HTTP请求
	 * 
	 * @param msgInfo
	 */
	public void sendContentToMir(MessageInfo msgInfo) {
		String messageString = JSON.toJSONString(msgInfo);
		// 去掉转码时候的转义符
		JSONObject messageJson = JSON.parseObject(messageString);
		messageJson.put("content", BaseService.getJsonObj(messageJson.getString("content")));
		// 发送json
		//FIXME NetUtil
		String result = NetUtil.send(initService.getSysParam(Constants.MIR_ADDRESS).getParamValue() + mirUrl, "POST", messageJson.toJSONString());

		log.info(result + "result");
	}

	// 解析weibo
	public void InsertMessage(MediaModel model, String tenant, JSONArray picUrls) {
		try {
			// mediaReceiveDao.WeiboaddInitTask(model);
			String result = "";
			// 微博信息不需要拦截等操作，直接插入到多媒体正式表
			model.setMediaIsAssign("0");
			model.setMediaIsDo("1");
			try {
				RequestSessionbean requestSessionbean = new RequestSessionbean(model.getUid(), jsonInfo.getString("uid"), channelCode, tenant,model.getHeadimgUrl());
				SensitiveLog sensitiveLog = new SensitiveLog(channelCode, tenant, model.getUid(), jsonInfo.getString("uid"), model.getMediaContent());
				if (sensitiveService.filter(sensitiveLog)) {
					log.info("微博消息已经被过滤" + JSON.toJSONString(sensitiveLog));
				} else {
					log.info("请求的requestSessionbean" + JSON.toJSONString(requestSessionbean));

					SessionInfo sessioninfo = LocalsessionService.getSession(requestSessionbean);
					// 封装消息体

					MessageInfo message = MsgConvertUtil.media2Message(channelCode, model);
					message.setSessionId(sessioninfo.getSessionId());
					message.setTenantCode(sessioninfo.getTenantCode());
					message.setAcceptedAccount(jsonInfo.getString("uid"));
					messageInfoDao.insert(message);
					String messageString = JSON.toJSONString(message);
					// 去掉转码时候的转义符
					JSONObject messageJson = JSON.parseObject(messageString);
					messageJson.put("content", BaseService.getJsonObj(messageJson.getString("content")));
					// 发送json
					result = NetUtil.send(initService.getSysParam(Constants.MIR_ADDRESS).getParamValue() + mirUrl, "POST", messageJson.toJSONString());

					log.info(result + "result");
				}

			} catch (Exception e) {
				log.error("存储数据异常!", e);
			}

		} catch (Exception e) {
			// mediaReceiveDao.addReceiveErrorLog("3",e.getMessage(),
			// model.getMediaReceiver(), model.getMediaSender(), "2",
			// model.getMediaTitle(), model.getMediaContent(),
			// model.getTaskId());
			log.error(e.getMessage(), e);
		}
	}

	// 解析获取到的图片URL,并以IO流形式返回
	public InputStream PaserPictureUrl(String urlPath) throws IOException {
		URL url;
		java.io.BufferedInputStream bis = null;
		try {
			if (Util.NullToString(urlPath).equals("")) {
				return null;
			} else {
				url = new URL(urlPath);
				bis = new BufferedInputStream(url.openStream());
			}

		} catch (MalformedURLException e) {
			e.printStackTrace();
		}

		return bis;
	}

	private String saveFile(String fileName, InputStream in, String pathFile, MediaModel model) {
		// 将附件上传到指定FTP目录上去
		IdealFtpClient ftpClient = null;
		String fileName2 = "";
		try {
			ftpClient = new IdealFtpClient();

			List list = commonDao.getSysParam("FTP_UPLOAD_URL");
			Map map = (Map) list.get(0);
			String code_desc = (String) map.get("CODE_DESC");

			String[] ftp = code_desc.split(":");

			ftpClient.connectServer((String) map.get("CODE_VALUE"), ftp[0], ftp[1], ftp[2]);
			// ftpClient.connectServer("127.0.0.1", "yuankang", "yuankang");
			ftpClient.changeDir(pathFile);

			if (ftpClient.isExists(fileName)) {
				fileName2 = "[1]" + fileName;
			} else {
				fileName2 = fileName;
			}

			ftpClient.uploadFile(in, fileName2);

			ftpClient.close();

			// 记录上传路径
			model.setMediaFileName(fileName2);
			model.setMediaFilePath(pathFile);
			mediaReceiveDao.addTaskFile(model);

		} catch (Exception e1) {
			log.error("附件上传FTP出错:" + e1.getMessage());
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
		return fileName2;
	}

	public AccessToken getAccessTokenByCode(String code, String client_id, String client_secret, String redirect_uri) throws WeiboException {
		HttpClient client = new HttpClient();
		return new AccessToken(client.post(WeiboConfig.getValue("accessTokenURL"), new PostParameter[] { new PostParameter("client_id", client_id), new PostParameter("client_secret", client_secret),
				new PostParameter("grant_type", "authorization_code"), new PostParameter("code", code), new PostParameter("redirect_uri", redirect_uri) }, false));
	}

	public static StatusWapper constructWapperStatus(JSONObject jsonStatus) throws WeiboException, weibo4j.org.json.JSONException {
		com.alibaba.fastjson.JSONArray statuses = null;
		try {
			if (jsonStatus.containsKey("statuses")) {
				statuses = jsonStatus.getJSONArray("statuses");
			}
			if (jsonStatus.containsKey("reposts")) {
				statuses = jsonStatus.getJSONArray("reposts");
			}
			int size = statuses.size();
			List<Status> status = new ArrayList<Status>(size);
			for (int i = 0; i < size; i++) {
				status.add(new Status(statuses.getJSONObject(i).toString()));
			}
			long previousCursor = jsonStatus.getLong("previous_cursor");
			long nextCursor = jsonStatus.getLong("next_cursor");
			long totalNumber = jsonStatus.getLong("total_number");
			String hasvisible = jsonStatus.getString("hasvisible");
			return new StatusWapper(status, previousCursor, nextCursor, totalNumber, hasvisible);
		} catch (JSONException jsone) {
			throw new WeiboException(jsone);
		}
	}

}
