package cn.sh.ideal.mgw.model;

import java.io.Serializable;

//qq会话接入对象
public class QQJoinBean  implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String openId;
	
	private String hostImNumber;
	
	private String source;
	
	private MsgType msgType;
	
	private String content;
	
	private String channel;
	
	private String userType;
	
	private String imUserNick;
	
	
	public String getImUserNick() {
		return imUserNick;
	}

	public void setImUserNick(String imUserNick) {
		this.imUserNick = imUserNick;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public MsgType getMsgType() {
		return msgType;
	}

	public void setMsgType(MsgType msgType) {
		this.msgType = msgType;
	}

	public static enum MsgType {
		text, image, voice, shortvideo,video, location, position,link, event,appointment,audio,file,news,notice,notice_refresh,email,msgLink
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}


	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getHostImNumber() {
		return hostImNumber;
	}

	public void setHostImNumber(String hostImNumber) {
		this.hostImNumber = hostImNumber;
	}
}
