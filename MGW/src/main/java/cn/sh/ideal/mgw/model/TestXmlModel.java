package cn.sh.ideal.mgw.model;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.util.StringUtils;

import cn.sh.ideal.mgw.utils.HttpUtil;


public class TestXmlModel {

	//命令标记 如0x04
	private String commandValue;
	
	private String msgValue;	

	private Map<String,String> fieldsValue;
	
	public String getMsgValue() {
		return msgValue;
	}

	public void setMsgValue(String msgValue) {
		this.msgValue = msgValue;
	}

	public Map<String, String> getFieldsValue() {
		return fieldsValue;
	}

	public void setFieldsValue(Map<String, String> fieldsValue) {
		this.fieldsValue = fieldsValue;
	}

	public String getCommandValue() {
		return commandValue;
	}

	public void setCommandValue(String commandValue) {
		this.commandValue = commandValue;
	}

	public String returnXmlStr(){
		StringBuffer fieldsStr = new StringBuffer();
		for (Map.Entry<String, String> entry : fieldsValue.entrySet()) {  		
			fieldsStr.append("<field key=\"" + entry.getKey() + "\" value=\"" + entry.getValue() + "\"/>");
		}  
		String msgStr = "";
		if(!StringUtils.isEmpty(msgValue)){
			msgStr = "<msg>" + msgValue + "</msg>";
		}
		StringBuffer sbStr = new StringBuffer("<?xml version=\"1.0\" encoding=\"gbk\"?>");
		sbStr.append("<imcc>"
					+ "<command>" + commandValue + "</command>"
					+ "<request>"
					+ "	<protocolfields>"
					+ fieldsStr
					+ "	</protocolfields>"
					+ msgStr
					+ "</request>"
					+ "</imcc>");
		return sbStr.toString();
	}
	
	public static void main(String[] args){
		Map<String,String> temp = new LinkedHashMap<String,String>();
		temp.put("SystemId", "10000");
		temp.put("LoginUserId", "133");					
		temp.put("SubCommand", "1");
		temp.put("FromUserType", "1");
		temp.put("FromUserName", "kf8");					
		temp.put("ToUserType", "1");
		temp.put("ToUserName", "user1");
		temp.put("MsgType", "0");
		temp.put("SendType", "0");
		TestXmlModel txm = new TestXmlModel();
		txm.setCommandValue("0x04");
		txm.setFieldsValue(temp);
//		txm.setMsgValue("testtest");
		System.out.println(txm.returnXmlStr());
		String sr=HttpUtil.sendPost("http://www.szhtp.com/common/postmessage", txm.returnXmlStr());
	}
}
