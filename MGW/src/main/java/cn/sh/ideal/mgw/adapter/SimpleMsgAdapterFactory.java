package cn.sh.ideal.mgw.adapter;

import java.io.Serializable;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.init.InitService;

/**
 * 简单消息适配器工厂类
 * 
 * <AUTHOR>
 * 
 */
@Component("simpleMsgAdapterFactory")
public class SimpleMsgAdapterFactory extends MsgAdapterFactory {


	@Autowired
	private RedisDao<String, Serializable> redisDao;
	@Resource(name = "initService")
	private InitService initService;

	@Override
	public MsgAdapter create(String channelCode) {
		Map<String, MsgAdapter> map=(Map<String, MsgAdapter>) redisDao.readValue(Constants.KEY_ADAPTER_MAP);
		if(map==null){
			initService.init();
			map=(Map<String, MsgAdapter>) redisDao.readValue(Constants.KEY_ADAPTER_MAP);
		}
		MsgAdapter adapter = map.get(channelCode);

		if (null == adapter)
			throw new NullPointerException("渠道不合法:" + channelCode);

		return adapter;
	}

}
