package cn.sh.ideal.mgw.utils.sender;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.activation.MimetypesFileTypeMap;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.MsgType;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;



public class YichatSender implements Sender{

	private static final Logger log = Logger.getLogger(YichatSender.class);


	public JSONObject customSend(String json, String token) {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_YICHAT
				+ ".CUSTOM_URL");
		if (url == null || "".equals(url.trim())) {
			throw new NullPointerException("没有找到[" + Constants.PLATFORM_YICHAT + "]平台信息");
		}

		JSONObject jsonObject = JSONObject.parseObject(json);
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype").toUpperCase());
		JSONObject mediaObj = new JSONObject();
		switch (type) {
		case TEXT:
		case NEWS:
			json = json.replaceAll("content_source_url", "url");
		case MUSIC:
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, json));
		case VIDEO:
		case VOICE:
		case IMAGE:
			JSONObject resultObj = uploadFile(jsonObject.getJSONObject(type.getName()), type, token);
			if(resultObj.containsKey("errcode")) {
				return resultObj;
			}
			mediaObj = new JSONObject();
			mediaObj.put("media_id", resultObj.getString("media_id"));
			jsonObject.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		default :
			throw new IllegalArgumentException("not supported msgtype");
		}
	}
	
	public JSON multiSend(String json, String token) throws Exception{
		return sendAll(json, token);
	}
	

	private JSON sendAll(String json, String token) throws Exception{
		JSONObject jsonObject = JSONObject.parseObject(json);
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype").toUpperCase());
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_YICHAT
				+ ".SENDALL_URL");
		JSONObject mediaObj = null;
		switch (type) {
		case TEXT:
			jsonObject.put("group","");
			System.out.println("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		case MPNEWS:
			
			JSONArray array = jsonObject.getJSONArray("articles");
			if(array == null || array.isEmpty())
				throw new NullPointerException("articles is null"); 
			JSONObject itemObj = null;
			for (int i = 0; i < array.size(); i++) {
				JSONObject requestUploadObj = new JSONObject();
				itemObj = array.getJSONObject(i);
				String picurl = itemObj.getString("picurl");
				if(picurl == null || "".equals(picurl)) 
					continue;
				requestUploadObj.put("url", picurl);
				requestUploadObj.put("fileName", picurl.substring(picurl.lastIndexOf("/")));
				JSONObject object = uploadFile(requestUploadObj, MsgType.IMAGE, token);
				if(object.containsKey("errcode")) {
					throw new Exception(object.toJSONString());
				}
				itemObj.put("thumb_media_id", object.get("media_id"));
			}
			String uploadNewsUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_YICHAT
					+ ".UPLOADNEWS_URL");
			//FIXME
			JSONObject uploadResult = JSONObject.parseObject(NetUtil.send(uploadNewsUrl + token, NetUtil.POST, jsonObject.toJSONString()));
			if(uploadResult.containsKey("errcode")) {
				throw new Exception(uploadResult.toJSONString());
			}
			mediaObj = new JSONObject();
			jsonObject.put("group","");
			mediaObj.put("media_id", uploadResult.getString("media_id"));
			jsonObject.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		case MPVIDEO:
		case VOICE:
		case IMAGE:
			JSONObject resultObj = uploadFile(jsonObject.getJSONObject(type.getName()), type, token);
			if(resultObj.containsKey("errcode")) {
				return resultObj;
			}
			if(type == MsgType.MPVIDEO) {
				String uploadurl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_YICHAT
						+ ".UPLOADVIDEO_URL");
				JSONObject requestObj = new JSONObject();
				JSONObject videoObj = jsonObject.getJSONObject(type.getName());
				requestObj.put("media_id", resultObj.getString("media_id"));
				requestObj.put("title", videoObj.getString("title") == null ? "" : videoObj.getString("title"));
				requestObj.put("description", videoObj.getString("description") == null ? "" : videoObj.getString("description"));
				JSONObject responseObj = JSONObject.parseObject(
				//FIXME
				NetUtil.send(uploadurl + token, NetUtil.POST, requestObj.toJSONString()));
				if(responseObj.containsKey("errcode")) {
					return responseObj;
				}
				resultObj.put("media_id", responseObj.getString("media_id"));
			}
			mediaObj = new JSONObject();
			jsonObject.put("group","");
			mediaObj.put("media_id", resultObj.getString("media_id"));
			jsonObject.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		default:
			throw new IllegalArgumentException("msgtype not match");
		}
		
	}
	

	public JSONObject uploadFile(JSONObject json, MsgType type, String token) {
		String url =  PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_YICHAT
				+ ".MEDIA_URL");

		String fileurl = "";
		String fileName = "";
		if (json.containsKey("media_id")) {
			String fUrl = json.getString("media_id");
			String regex = "/([^/]+)$";

			Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(fUrl);
			if (m.find()) {
				fileurl=fUrl;
				fileName = m.group(1);

			}

		} else {
			fileurl = json.getString("url");
			fileName = json.getString("fileName");
		}
		Map<String, String> fileMap = new HashMap<String, String>();
		fileMap.put("media", fileName);

		String respStr = formUpload(url + token + "&type=" + type.getName().replaceAll("mp", ""),
				fileMap, fileurl);

		return JSONObject.parseObject(respStr);
	}
	
	private String formUpload(String urlStr, Map<String, String> fileMap,String fileurl) {
		String res = "";
		HttpURLConnection conn = null;
		String BOUNDARY = "---------------------------123821742118716"; // boundary就是request头和上传文件内容的分隔符
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(30000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn
					.setRequestProperty("User-Agent",
							"Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type",
					"multipart/form-data; boundary=" + BOUNDARY);

			OutputStream out = new DataOutputStream(conn.getOutputStream());

			// file
			if (fileMap != null) {
				Iterator<Entry<String, String>> iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Entry<String, String> entry = (Entry<String, String>) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();
					String contentType = new MimetypesFileTypeMap()
							.getContentType(file);
					if (filename.toLowerCase().endsWith(".png")) {
						contentType = "image/png";
					} else if(filename.toLowerCase().endsWith(".mp3")) {
						contentType = "audio/x-mpeg";
					} else if(filename.toLowerCase().endsWith(".wma")) {
						contentType = "application/mswma";
					} else if(filename.toLowerCase().endsWith(".mp4")) {
						contentType = "video/mp4";
					} else if (contentType == null || contentType.equals("")) {
						contentType = "application/octet-stream";
					}

					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append(
							"\r\n");
					strBuf.append("Content-Disposition: form-data; name=\""
							+ inputName + "\"; filename=\"" + filename
							+ "\"\r\n");
					strBuf.append("Content-Type:" + contentType + "\r\n\r\n");

					out.write(strBuf.toString().getBytes());

					URL httpUrl = new URL(fileurl);
					

					InputStream in = httpUrl.openStream();
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}

			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();

			// 读取返回数据
			StringBuffer strBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(
					conn.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				strBuf.append(line).append("\n");
			}
			res = strBuf.toString();
			reader.close();
			reader = null;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			return "{\"errcode\":\"-1\", \"errmsg\":\"" + e.getMessage() + "\"}";
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return res;
	}

}
