package cn.sh.ideal.mgw.excutor;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;




import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.thread.WeiboReceiveThread;
import cn.sh.ideal.model.ChannelConfig;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

@Component("weiboReceiveExecutor")
public class WeiboReceiveExcutor extends Thread implements ApplicationContextAware {

	@Autowired
	private ChannelConfigDao channelConfigDao;
	@Autowired
	//private MediaReceiveDao mediaReceiveDao;
	@Resource(name = "receiveWeiboThreadPool")
	private ThreadPoolTaskExecutor threadPool;
	
	private ApplicationContext context;

	protected static final Logger log = Logger.getLogger(WeiboReceiveExcutor.class);

	
	
	
	@Override
	public void setApplicationContext(ApplicationContext arg0)
			throws BeansException {
		this.context=arg0;
		
	}

	/**
	 * 初始化操作
	 */
	@PostConstruct
	public void init() {
		try {
		} catch (Exception e) {
			log.error("初始化异常!", e);
		}
	}

	/**
	 * 微博接收执行方法
	 */
	public void receive() {
		try {
			receiveMedia();
		} catch (Exception e) {
			log.error("微博接收器执行异常!", e);
		}
	}

	/**
	 * 接收微博
	 */
	private void receiveMedia() {
		log.debug("微博接收器开始执行......");
		threadPool.execute(new Runnable() {

			@Override
			public void run() {
				ThreadGroup tg = null;
				try {
					Thread.sleep(60000);
					tg = new ThreadGroup("weibo");
				} catch (InterruptedException e1) {

					e1.printStackTrace();
				}
				while (true) {
					log.info("**************进入微博收取:*************");
					try {

						ChannelConfig channelConfig = new ChannelConfig();
						channelConfig.setChannelCode("1005");
						channelConfig.setChannelEnable("1");

						List<ChannelConfig> mailList = channelConfigDao.query(channelConfig);

						Thread threads[] = new Thread[tg.activeCount()];
						tg.enumerate(threads);

						for (int i = 0; i < mailList.size(); i++) {
							ChannelConfig info = mailList.get(i);
							JSONObject infoJson = JSON.parseObject(info.getAccountConfig());
							infoJson.put("tenant", info.getTenantCode());
							infoJson.put("uid", info.getChannelAccount());
							boolean f = false;
							String name = info.getChannelAccount();
							for (int j = 0; threads != null
									&& j < threads.length; j++) {
								Thread thread = threads[j];
								if (thread.getName().equals(name)) {
									f = true;
									break;
								}
							}

							if (!f) {
								WeiboReceiveThread rt = new WeiboReceiveThread(infoJson,context);
								Thread th = new Thread(tg, rt, name);
								th.start();
							}

						}
						// Thread.sleep(60000);
					} catch (Exception e) {
						log.error(e.getMessage(), e);
					}

					try {
						Thread.sleep(1200000);
						//Thread.sleep(3000);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}

			}

		});
	}

}
