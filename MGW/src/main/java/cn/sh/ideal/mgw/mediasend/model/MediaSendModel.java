package cn.sh.ideal.mgw.mediasend.model;

import java.util.List;

public class MediaSendModel {
	private int id ;
	//多媒体任务ID
	private String messageId = "";
	//发送人
	private String sender = "";
	//接收人
	private String receiver = "";
	//标题
	private String title = "";
	//抄送人
	private String copyer = "";
	//html内容
	private String htmlContent = "";
	//文字内容
	private String textContent = "";
	//类型
	private String type = "";
	//状态
	private String status = "";
	//发送人名称
	private String senderName = "";
	//是否需要会话保持
	private String isKeep = "";
	//附件
	private String attchFiles[];
	//附件路径
	private String attachPath = "";
	//内容图片
	private List contentImgs;
	
	//JSON格式的图片路径
	private String jsonImgs = "";
	//JSON格式的附件
	private String jsonAttchFiles = "";
	//工号
	private String workNo;
	
	
	//重发格式的图片路径
	private String dateFileNames ="";
	//重发格式的图片附件
	private String dateContentImages ="";
	//信息类型
	private String infoType;
	
	private String reason;//失败原因
	
	private String sessionId;//会话ID
	
	private String channelId;
	
	private String tenantId;
	
	private String toolbarId;
	
	private String sendNum;
	
	private String sendType = "0";
	
	
	public String getReason() {
		return reason;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getInfoType() {
		return infoType;
	}
	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}
	public String getSenderName() {
		return senderName;
	}
	public void setSenderName(String senderName) {
		this.senderName = senderName;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getSender() {
		return sender;
	}
	public void setSender(String sender) {
		this.sender = sender;
	}
	public String getReceiver() {
		return receiver;
	}
	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getCopyer() {
		return copyer;
	}
	public void setCopyer(String copyer) {
		this.copyer = copyer;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}

	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String[] getAttchFiles() {
		return attchFiles;
	}
	public void setAttchFiles(String[] attchFiles) {
		this.attchFiles = attchFiles;
	}
	public List getContentImgs() {
		return contentImgs;
	}
	public void setContentImgs(List contentImgs) {
		this.contentImgs = contentImgs;
	}
	public String getHtmlContent() {
		return htmlContent;
	}
	public void setHtmlContent(String htmlContent) {
		this.htmlContent = htmlContent;
	}
	public String getTextContent() {
		return textContent;
	}
	public void setTextContent(String textContent) {
		this.textContent = textContent;
	}
	
	public String getMessageId() {
		return messageId;
	}
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}
	public String getAttachPath() {
		return attachPath;
	}
	public void setAttachPath(String attachPath) {
		this.attachPath = attachPath;
	}
	public String getJsonImgs() {
		return jsonImgs;
	}
	public void setJsonImgs(String jsonImgs) {
		this.jsonImgs = jsonImgs;
	}
	public String getJsonAttchFiles() {
		return jsonAttchFiles;
	}
	public void setJsonAttchFiles(String jsonAttchFiles) {
		this.jsonAttchFiles = jsonAttchFiles;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getDateFileNames() {
		return dateFileNames;
	}
	public void setDateFileNames(String dateFileNames) {
		this.dateFileNames = dateFileNames;
	}
	public String getDateContentImages() {
		return dateContentImages;
	}
	public void setDateContentImages(String dateContentImages) {
		this.dateContentImages = dateContentImages;
	}
	public String getIsKeep() {
		return isKeep;
	}
	public void setIsKeep(String isKeep) {
		this.isKeep = isKeep;
	}
	/**
	 * @return the channelId
	 */
	public String getChannelId() {
		return channelId;
	}
	/**
	 * @param channelId the channelId to set
	 */
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
	/**
	 * @return the tenantId
	 */
	public String getTenantId() {
		return tenantId;
	}
	/**
	 * @param tenantId the tenantId to set
	 */
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	/**
	 * @return the toolbarId
	 */
	public String getToolbarId() {
		return toolbarId;
	}
	/**
	 * @param toolbarId the toolbarId to set
	 */
	public void setToolbarId(String toolbarId) {
		this.toolbarId = toolbarId;
	}
	public String getSendNum() {
		return sendNum;
	}
	public void setSendNum(String sendNum) {
		this.sendNum = sendNum;
	}
	public String getSendType() {
		return sendType;
	}
	public void setSendType(String sendType) {
		this.sendType = sendType;
	}
	
	
}
