package cn.sh.ideal.mgw.service.local.impl;

import java.net.URLEncoder;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import weibo4j.Users;
import weibo4j.model.User;
import weibo4j.model.WeiboException;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.MessageService;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.sm.resp.RespCreateSession;
import cn.sh.ideal.sm.resp.RespGetSession;
import cn.sh.ideal.sm.resp.RespUpdateSessionUserActive;
import cn.sh.ideal.sm.service.SessionService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;



@Service("LocalsessionService")
public  class LocalSessionServiceImpl implements LocalSessionService {

	private static final Logger log = LoggerFactory.getLogger(LocalSessionServiceImpl.class);

	@Autowired
	private MessageService messageService;
	
	@Autowired
	private SysInitService initService;
	
	@Autowired
	private SessionService smService;

	private static String sessionUrl = (String) PropertiesUtil.getProperty(PropertiesUtil.APP, "media.sessionManager");
	private static String customerUrl = (String) PropertiesUtil.getProperty(PropertiesUtil.APP, "media.customerUrl");

	/**
	 * 查询或创建信息状态
	 * 
	 * @param methodName
	 * @param json
	 * @return
	 */
	public SessionInfo getSession(RequestSessionbean requestSessionbean) {

		SessionInfo sessioninfo = null;
		try {
			// 获取查询session
			
			SessionData sessionData = new SessionData(requestSessionbean);
			
//			JSONObject reslutStatus = this.sessionManager("getSession", requestSessionbean);
			RespGetSession session = smService.getSession(sessionData);
			
			if ("0".equals(session.getResultCode())&&session.getData()!=null) {
				sessioninfo = session.getData();
				log.info("*********invoke smService.getSession() return "+JSON.toJSONString(sessioninfo)+"*********");
				if (StringUtils.isEmpty(sessioninfo.getNickname())) {
					JSONObject nickjson = new JSONObject();
					JSONObject clientInfo = new JSONObject();
					switch (sessionData.getChannelCode()) {
					case "1003":
					case "1004":
						Platform platfrom = Platform.codeOf(sessionData.getChannelCode());
						nickjson.put(Constants.PARAM_PID, platfrom.getName());
						nickjson.put(Constants.PARAM_PLATFORM_ID, sessionData.getAcceptedAccount());
						nickjson.put(Constants.PARAM_OPENID, sessionData.getSendAccount());

						clientInfo = queryClientInfo(nickjson.toJSONString());
						String nickname = "";
						if (clientInfo.containsKey("nickname")) {
							nickname = clientInfo.getString("nickname");
							nickname = URLEncoder.encode(nickname, "utf-8");
							sessionData.setNickname(nickname);
							sessioninfo.setNickname(nickname);
						}
						break;
					case "1005":
						sessionData.setNickname(requestSessionbean.getNickname());
						sessioninfo.setNickname(requestSessionbean.getNickname());
					default:
						break;
					}
				}

			} else {
				
				
				JSONObject nickjson = new JSONObject();
				JSONObject clientInfo = new JSONObject();
				switch (sessionData.getChannelCode()) {
				case "1003":
				case "1004":
					Platform platfrom = Platform.codeOf(sessionData.getChannelCode());
					nickjson.put(Constants.PARAM_PID, platfrom.getName());
					nickjson.put(Constants.PARAM_PLATFORM_ID, sessionData.getAcceptedAccount());
					nickjson.put(Constants.PARAM_OPENID, sessionData.getSendAccount());

					clientInfo = queryClientInfo(nickjson.toJSONString());
					String nickname = "";
					if (clientInfo.containsKey("nickname")) {
						nickname = clientInfo.getString("nickname");
						nickname = URLEncoder.encode(nickname, "utf-8");
						sessionData.setNickname(nickname);
						clientInfo.put("nickname",nickname);
					}
					break;
					
					//微博昵称，加入客户识别里面
				case "1005":
					clientInfo.put("nickname", requestSessionbean.getNickname());
				default:
					break;
					/**
				case "1020":
					Platform platfrom1 = Platform.codeOf(sessionData.getChannelCode());
					nickjson.put(Constants.PARAM_PID, platfrom1.getName());
					nickjson.put(Constants.PARAM_PLATFORM_ID, sessionData.getAcceptedAccount());
					nickjson.put(Constants.PARAM_OPENID, sessionData.getSendAccount());

					String token = queryWeiboInfo(nickjson.toJSONString());
					log.info("##########local_token=" + token);
					Users um = new Users();
					um.client.setToken(token);
					try {
						User user = um.showUserById(sessionData.getAcceptedAccount());
						log.info("##########headimgurl=" + user.getProfileImageUrl());
						clientInfo.put("headimgurl", user.getProfileImageUrl());
						log.info("##########" + user.toString());
					} catch (WeiboException e) {
						e.printStackTrace();
					}
					//获取头像路径方法
					//XXXXService
					*/
				
				}
				/**
				 * {"account":"", "channel":"", "nickName":"", "tenantCode":"",
				 * "queryType":"" }
				 */
				clientInfo.put("account", sessionData.getSendAccount());
				clientInfo.put("serverAccount", sessionData.getAcceptedAccount());
				clientInfo.put("channel", sessionData.getChannelCode());
				clientInfo.put("tenantCode", sessionData.getTenantCode());
				clientInfo.put("queryType", "10000");
				if(requestSessionbean.getHeanimgUrl()!=null && !"".equals(requestSessionbean.getHeanimgUrl())){
					clientInfo.put("headimgurl", requestSessionbean.getHeanimgUrl());
				}
				//天猫京东评论，以nickname不带*的用户识别客户，此时nickName字段与account字段互换
				if("3002".equals(sessionData.getChannelCode())){
					JSONObject jsonContent = JSONObject.parseObject(sessionData.getContent());
					String nickName = jsonContent.getString("nickname");
					if(StringUtils.isNotEmpty(nickName) && !nickName.contains("*")){
						clientInfo.put("account", nickName);
						clientInfo.put("nickName",sessionData.getSendAccount());
					}
				}

				String url =initService.getSysParam(Constants.CMS_ADDRESS).getParamValue() + customerUrl;
				
				
				JSONObject resultjson = null;
				
				if (StringUtils.isEmpty(initService.getSysParam(Constants.CUSTOMER_IDENTIFICATION).getParamValue()) || "1".equals(initService.getSysParam(Constants.CUSTOMER_IDENTIFICATION).getParamValue())) {
					//FIXME NetUtil
					try {
						String resultString = NetUtil.send(url, NetUtil.POST, clientInfo.toJSONString(), "application/json");
						
						resultjson = (JSONObject) JSONObject.parse(resultString);

						if(!resultjson.getString("resultCode").equals("-1")){
							sessionData.setCustomerId(resultjson.getJSONObject("data").getString("customerId"));
							
							log.info("客户识别返回正常");
						}
					
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				RespCreateSession createSession = smService.createSession(sessionData);
				
				if (createSession != null && "0".equals(createSession.getResultCode())) {
					sessioninfo = createSession.getData();
					sessioninfo.setNickname(clientInfo.getString("nickname"));

					log.info("***调用SM创建会话成功***, "+JSON.toJSONString(sessioninfo));
				}else{
					log.info("***调用SM创建会话失败***,createSession :"+JSON.toJSONString(createSession));
				}
				
			}
			
			//新逻辑
			sessioninfo.setCustomerId(sessionData.getCustomerId());
			requestSessionbean.setSessionId(sessioninfo.getSessionId());
			updateSessionUserActive(requestSessionbean);
			
		} catch (Exception e) {
			log.error("获取session异常", e);
		}

		return sessioninfo;
	}

	public void updateSessionUserActive(RequestSessionbean requestSessionbean) {
		try {
			
			SessionData sessionData = new SessionData(requestSessionbean);
			log.info("更新用户活动,param"+JSON.toJSONString(sessionData));
			RespUpdateSessionUserActive updateSessionUserActive = smService.updateSessionUserActive(sessionData);
			if(updateSessionUserActive.getResultCode().equals(Constants.SUCCESS_CODE)){
				log.info("*********更新用户活动成功*********");
			}else{
				log.info("*********更新用户活动失败*********");
			}

		} catch (Exception e) {
			log.error("更新用户缓存时间失败");
		}

	}

	public void updateSessionAgActive(RequestSessionbean requestSessionbean) {
		try {
			SessionData sessionData = new SessionData(requestSessionbean);
			log.info("更新坐席活动,param"+JSON.toJSONString(sessionData));
			smService.updateSessionAgActive(sessionData);
		} catch (Exception e) {
			log.error("更新用户缓存时间失败");
		}

	}

	/**
	 * 获取会话信息状态
	 * 
	 * @param methodName
	 * @param json
	 * @return
	 */
//	public JSONObject sessionManager(String methodName, RequestSessionbean requestSessionbean) {
//
//		smService.getSession(sessionData)
//		String url = initService.getSysParam(Constants.SM_ADDRESS).getParamValue() + sessionUrl;
//		String resultString = "";
//		try {
//			String requestSession = JSON.toJSONString(requestSessionbean);
//			resultString = NetUtil.send(url + methodName, NetUtil.POST, requestSession, "application/json");
//			log.debug("获取session返回:" + resultString);
//		} catch (Exception e) {
//			log.error("调用session:{}接口异常", methodName);
//		}
//		return JSONObject.parseObject(resultString);
//	}

	public JSONObject queryClientInfo(String jsonString) {

		String result;
		try {
			result = messageService.userInfo(jsonString);
			JSONObject json = JSONObject.parseObject(result);

			return json;
		} catch (Exception e) {
			log.error("", e);
		}

		return null;

	}

	public String queryWeiboInfo(String jsonString){
		
		String result;
		try {
			result = messageService.weiboUserInfo(jsonString);
			return result;
		} catch (Exception e) {
			log.error("", e);
		}

		return null;
	}

	


}
