package cn.sh.ideal.mgw.adapter;

import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_TEXT;
import cn.sh.ideal.mgw.mediasend.model.MediaSendModel;
import cn.sh.ideal.mgw.model.SmsL;
import cn.sh.ideal.model.MessageInfo;

/**
 * 短信消息适配器
 * 
 * <AUTHOR> 2015-3-6
 * 
 */
public class SmsMsgAdapter extends MsgAdapter {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;
		if(data != null){
			message = new MessageInfo();
			SmsL smsL = (SmsL)data;
			message.setChannelCode(smsL.getChannelCode());
			message.setContent(smsL.getSmsContent());
			message.setAcceptedAccount(smsL.getServerAccount());
			message.setSendAccount(smsL.getMobileNumber());
			message.setMsgType(MSG_PARAM_TEXT);
		}
		
		return message;
	}

	@Override
	public MediaSendModel message2Media(MessageInfo message) {
		MediaSendModel model = null;

		if (null != message) {
			model = new MediaSendModel();
			model.setSender(message.getSendAccount());
			model.setReceiver(message.getAcceptedAccount());
			model.setWorkNo(message.getWorkNo());
			model.setTextContent(message.getContent());
			model.setSendType(message.getSendType());
		}

		return model;
	}

}
