/**
 * 
 */
package cn.sh.ideal.mgw.service.dubbo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.config.annotation.Service;

import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.dao.ChannelDao;
import cn.sh.ideal.mgw.init.InitService;
import cn.sh.ideal.mgw.model.Sensitive;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.response.ChannelResponseDTO;
import cn.sh.ideal.mgw.service.ChannelService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.model.ChannelConfig;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.dubboService
 * @typeName ChannelServiceImpl
 * <AUTHOR>   
 * 
 * @Description:   
 * @date 2016年3月30日 下午5:24:48
 * @version 
 */
@Component("channelService")
public class ChannelServiceImpl implements ChannelService{

	private static final Logger log = Logger.getLogger(ChannelServiceImpl.class);

	@Autowired
	private ChannelDao channelDao;
	
	@Resource(name = "sensitiveService")
	private SensitiveService sensitiveService;

	@Autowired
	private InitService initService;
	
	@Autowired
	private SysInitService sysInitService;
	
	/* 
	 * @see cn.sh.ideal.mgw.server.ChannelService#getChannelInfoByTenantCode(cn.sh.ideal.model.ChannelConfig)
	 */
	@Override
	public ChannelResponseDTO getChannelInfoByTenantCode(ChannelConfig channelConfig) {
		log.info("读取渠道扩展信息：" + channelConfig.getTenantCode());
		try {
			List<ChannelConfig> channelConfigList = channelDao.getChannelConfigsByTenantId(channelConfig.getTenantCode());

			List<Map<String, String>> array = new ArrayList<Map<String, String>>();
			Map<String, String> channelMap = null;
			for (ChannelConfig config : channelConfigList) {
				channelMap = new HashMap<String, String>();
				channelMap.put("chCode", config.getChannelCode());
				channelMap.put("chName", config.getChannelName());
				channelMap.put("chAccount", config.getChannelAccount());
				array.add(channelMap);
			}
			log.info("渠道扩展信息"+array);
			return new ChannelResponseDTO(ChannelResponseDTO.SUCCESS_CODE,"渠道信息获取成功!",array);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("渠道信息获取失败");
			return new ChannelResponseDTO(ChannelResponseDTO.ERROR_CODE,"渠道信息获取失败");
		}
		
	}

	/* 
	 * @see cn.sh.ideal.mgw.server.ChannelService#isSensitive(cn.sh.ideal.mgw.server.model.SensitiveLog)
	 */
	@Override
	public ChannelResponseDTO isSensitive(SensitiveLog sensitiveLog) {
		
		log.info("敏感词接口信息：" + sensitiveLog.getContent()+sensitiveLog.getChannelCode());
		Sensitive sensitive=null;
		
		try {
			ChannelResponseDTO channelResponseDTO = new ChannelResponseDTO(ChannelResponseDTO.SUCCESS_CODE, "敏感词调用成功");
			channelResponseDTO.setIsSensitive(false);
			sensitive=sensitiveService.getSensitive(sensitiveLog);
			if(sensitive!=null){
				channelResponseDTO.setIsSensitive(true);
			}
			return channelResponseDTO;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("获取失败",e);
			return new ChannelResponseDTO(ChannelResponseDTO.ERROR_CODE,"敏感词调用失败");
		}
		
	}

	/* 
	 * @see cn.sh.ideal.mgw.server.ChannelService#syncChannelConfig()
	 */
	@Override
	public ChannelResponseDTO syncChannelConfig() {
		try {
			
			initService.initChannelConfig();
			initService.initAdapterMap();
			sysInitService.initChannelInfo();
			
			return new ChannelResponseDTO(ChannelResponseDTO.SUCCESS_CODE, "渠道同步接口调用成功");
		} catch (Exception e) {
			log.error("同步渠道信息异常", e);
			return new ChannelResponseDTO(ChannelResponseDTO.ERROR_CODE, "渠道同步接口调用失败");
		}
	}

}
