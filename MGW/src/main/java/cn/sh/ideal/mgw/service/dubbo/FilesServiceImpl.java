/**
 * 
 */
package cn.sh.ideal.mgw.service.dubbo;

import static cn.sh.ideal.mgw.base.Constants.ERROR_CODE;
import static cn.sh.ideal.mgw.base.Constants.PARAM_APPID;
import static cn.sh.ideal.mgw.base.Constants.PARAM_APP_SECRET;

import java.io.Serializable;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.dao.QrCodeDao;
import cn.sh.ideal.mgw.model.QrCode;
import cn.sh.ideal.mgw.model.req.QrCodeRequestDto;
import cn.sh.ideal.mgw.model.response.QrCodeResponseDTO;
import cn.sh.ideal.mgw.service.FilesService;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.TokenUtil;
import cn.sh.ideal.model.ChannelConfig;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.dubboService
 * @typeName FilesServiceImpl
 * <AUTHOR> Zhou
 * @Description:  
 * @date 2016年3月30日 下午5:25:43
 * @version 
 */
@Component("fileService")
public class FilesServiceImpl implements FilesService{

	private static final Logger log = Logger.getLogger(FilesServiceImpl.class);
	
	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	@Autowired
	private QrCodeDao qrCodeDao;
	
	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;

	/* 
	 * @see cn.sh.ideal.mgw.server.FilesService#getQrCode(cn.sh.ideal.mgw.server.model.req.QrCodeRequestDto)
	 */
	@Override
	public QrCodeResponseDTO getQrCode(QrCodeRequestDto requestObj) {
		
		QrCodeResponseDTO result = new QrCodeResponseDTO();
		
		try {
			
			Platform platform = Platform.codeOf(requestObj.getChannelCode());
			String platformId = requestObj.getPlatformId();
//			String openid = requestObj.getOpenid();

			String actionName = requestObj.getActionName();
			//TODO 下面一句可能有问题
			String userId = StringUtils.isNotEmpty(requestObj.getUserId()) ? requestObj.getUserId() : requestObj.getClientId();
			String usageType = requestObj.getUsageType();
			String fromChannel = requestObj.getFromChannel();

			ChannelConfig channelConfig = new ChannelConfig();
			channelConfig.setChannelAccount(platformId);
			channelConfig.setChannelCode(platform.getCode());

			List<ChannelConfig> channelConfigs = channelConfigDao.query(channelConfig);

			if (channelConfigs == null || channelConfigs.isEmpty()) {
				throw new NullPointerException("没有找到对应渠道或platformId不存在.");
			}

			JSONObject accountObj = JSONObject.parseObject(channelConfigs.get(0).getAccountConfig());

			String respStr = "";

			if (platform == Platform.ALIPAY) {

			} else {
				/** wechat or easychat */

				String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, platform.getName() + ".QRCODE_URL");

				String appid = accountObj.getString(PARAM_APPID);
				String secret = accountObj.getString(PARAM_APP_SECRET);

				if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(secret))
					throw new NullPointerException("appid or secret is null.");

				String token = new TokenUtil(platform.getName(), appid, secret, redisDao).getToken();

				// 封装提交请求的json 格式:{"expire_seconds": 1800, "action_name":
				// "QR_SCENE", "action_info": {"scene": {"scene_id": 123}}}
				JSONObject js = new JSONObject();
				// actionName 判断是要申请临时二维码还是永久二维码 QR_SCENE临时 QR_LIMIT_SCENE永久
				String sceneId = "";
				if (StringUtils.isEmpty(actionName) || "QR_SCENE".equals(actionName)) {
					js.put("expire_seconds", "1800");
					js.put("action_name", "QR_SCENE");
					// 生成场景ID
					sceneId = qrCodeDao.nextQrcodeVal();
				} else {
					js.put("action_name", "QR_LIMIT_SCENE");
					sceneId = qrCodeDao.getNextSceneId(channelConfigs.get(0).getTenantCode());
				}
				js.put("action_info", JSONObject.parse("{\"scene\": {\"scene_id\": " + Long.parseLong(sceneId) + "}}"));
				log.info("request json [" + js.toJSONString() + "]");
				//FIXME NetUtil
				respStr = NetUtil.send(url + token, NetUtil.POST, js.toJSONString());
				String ticket = "";
				// 插入
				JSONObject json = JSONObject.parseObject(respStr);
				if (json.containsKey("ticket")) {
					ticket = json.getString("ticket");
					respStr = getQrcodeImage(platform, URLEncoder.encode(ticket, "utf-8"));
					result.setQrcodeImgUrl(respStr);
				}

				QrCode qrcode = new QrCode(channelConfigs.get(0).getChannelAccount() + "", channelConfigs.get(0).getTenantCode(), userId, js.getString("action_name"), sceneId, new Date(), usageType,
						ticket, fromChannel);
				qrCodeDao.insert(qrcode);
			}
			result.setResultCode(QrCodeResponseDTO.SUCCESS_CODE);
			result.setResultMsg("获取二维码成功");
			log.info(Thread.currentThread().getName() + " response MessageController.getQrcode[" + result.toString() + "]");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			result.setResultCode(ERROR_CODE);
			result.setResultMsg(e.getMessage());
			result.setQrcodeImgUrl("");
			return result; 
		}
		
	}

	public String getQrcodeImage(Platform platform, String ticket) {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, platform.getName() + ".QRCODE_IMG");
		// String respStr = NetUtil.send(url + ticket, NetUtil.GET, "");
		return url + ticket;
	}

}
