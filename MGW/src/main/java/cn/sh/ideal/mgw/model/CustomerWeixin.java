package cn.sh.ideal.mgw.model;

import java.io.Serializable;

public class CustomerWeixin implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_CUSTOMER_WEIXIN.AUTO_ID
     *
     * @mbggenerated
     */
    private Integer autoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_CUSTOMER_WEIXIN.CHANNEL
     *
     * @mbggenerated
     */
    private String channel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_CUSTOMER_WEIXIN.OPEN_ID
     *
     * @mbggenerated
     */
    private String openId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_CUSTOMER_WEIXIN.COMMON_ID
     *
     * @mbggenerated
     */
    private String commonId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_CUSTOMER_WEIXIN.CUSTOMER_ID
     *
     * @mbggenerated
     */
    private Integer customerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table CMS_CUSTOMER_WEIXIN
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_CUSTOMER_WEIXIN.AUTO_ID
     *
     * @return the value of CMS_CUSTOMER_WEIXIN.AUTO_ID
     *
     * @mbggenerated
     */
    public Integer getAutoId() {
        return autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_CUSTOMER_WEIXIN.AUTO_ID
     *
     * @param autoId the value for CMS_CUSTOMER_WEIXIN.AUTO_ID
     *
     * @mbggenerated
     */
    public void setAutoId(Integer autoId) {
        this.autoId = autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_CUSTOMER_WEIXIN.CHANNEL
     *
     * @return the value of CMS_CUSTOMER_WEIXIN.CHANNEL
     *
     * @mbggenerated
     */
    public String getChannel() {
        return channel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_CUSTOMER_WEIXIN.CHANNEL
     *
     * @param channel the value for CMS_CUSTOMER_WEIXIN.CHANNEL
     *
     * @mbggenerated
     */
    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_CUSTOMER_WEIXIN.OPEN_ID
     *
     * @return the value of CMS_CUSTOMER_WEIXIN.OPEN_ID
     *
     * @mbggenerated
     */
    public String getOpenId() {
        return openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_CUSTOMER_WEIXIN.OPEN_ID
     *
     * @param openId the value for CMS_CUSTOMER_WEIXIN.OPEN_ID
     *
     * @mbggenerated
     */
    public void setOpenId(String openId) {
        this.openId = openId == null ? null : openId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_CUSTOMER_WEIXIN.COMMON_ID
     *
     * @return the value of CMS_CUSTOMER_WEIXIN.COMMON_ID
     *
     * @mbggenerated
     */
    public String getCommonId() {
        return commonId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_CUSTOMER_WEIXIN.COMMON_ID
     *
     * @param commonId the value for CMS_CUSTOMER_WEIXIN.COMMON_ID
     *
     * @mbggenerated
     */
    public void setCommonId(String commonId) {
        this.commonId = commonId == null ? null : commonId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_CUSTOMER_WEIXIN.CUSTOMER_ID
     *
     * @return the value of CMS_CUSTOMER_WEIXIN.CUSTOMER_ID
     *
     * @mbggenerated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_CUSTOMER_WEIXIN.CUSTOMER_ID
     *
     * @param customerId the value for CMS_CUSTOMER_WEIXIN.CUSTOMER_ID
     *
     * @mbggenerated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }
}