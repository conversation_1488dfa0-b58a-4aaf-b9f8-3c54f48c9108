package cn.sh.ideal.mgw.service.local.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.dao.SensitiveDao;
import cn.sh.ideal.mgw.dao.SensitiveLogDao;
import cn.sh.ideal.mgw.model.Sensitive;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.service.local.SensitiveService;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.localService.impl
 * @typeName SensitiveServiceImpl
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月29日 下午10:58:50
 * @version 
 */
@Service("sensitiveService")
public class SensitiveServiceImpl implements SensitiveService {

	private final Logger log= LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	private SensitiveDao sensitiveDao;
	@Autowired
	private SensitiveLogDao  sensitiveLogDao;
	@Autowired
	private RedisDao<String, Sensitive> redisDao;
	
	private static final String SENSTIVE_KEY = "SENSITIVEWORDS:";

	public Sensitive getSensitive(SensitiveLog sensitiveLog) {
		try {
			if(sensitiveLog == null || sensitiveLog.getContent() == null  || "".equals(sensitiveLog.getContent())){
				return null;
			}
			List<Sensitive> sensitiveList = redisDao.listRangeAll(SENSTIVE_KEY.concat(sensitiveLog.getTenantCode()));
			String content = new String(sensitiveLog.getContent());
			if (sensitiveList!=null&&sensitiveList.size()>0) {
				
				for(Sensitive sensitiveItem:sensitiveList){
					String matchingType = sensitiveItem.getMatchingType();
					if("1".equals(matchingType)){
						if(content.contains(sensitiveItem.getSensitiveWord())){
							return sensitiveItem;
						}
					}else{
						String sensitiveWord = sensitiveItem.getSensitiveWord();
						String regEx_html = "<[^>]+>";
				        Pattern p_html=Pattern.compile(regEx_html,Pattern.CASE_INSENSITIVE); 
				        Matcher m_html=p_html.matcher(content); 
				        content = m_html.replaceAll("");
						content = m_html.replaceAll("");
						if(content.equals(sensitiveWord)){
							return sensitiveItem;
						}
					}
				}
			}
			
		} catch (Exception e) {
			log.error("获取敏感词异常",e);
		}

		return null;
	}
	
	
	
	
	@Override
	public Boolean filter(SensitiveLog sensitiveLog) {
		try {
			Sensitive sensitive=getSensitive(sensitiveLog);
			if (sensitive!=null) {
				
				sensitiveLog.setSensitiveId(String.valueOf(sensitive.getAutoId()));
				sensitiveLog.setSensitiveWord(sensitive.getSensitiveWord());
				sensitiveLogDao.insert(sensitiveLog);
				return true;
			}
			
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			return false;
		}
	
		
		return false;
	}




	@Override
	public void init() {
		log.info("加载敏感词……");
		Sensitive sensitive=new Sensitive();
		sensitive.setStatus("1");
		
		Map<String,List<Sensitive>> maps = new HashMap<String,List<Sensitive>>();
		try {
			Set<String> okeys = redisDao.getKeysByPattern(SENSTIVE_KEY.concat("*"));
			//移除已删除数据
			for(String okey : okeys){
				redisDao.deleteValue(okey);
			}
			List<Sensitive> sensitiveList=sensitiveDao.query(sensitive);
			if (sensitiveList!=null&&sensitiveList.size()>0) {
				for(Sensitive sensitiveItem:sensitiveList){
					if(null == sensitiveItem.getTenantCode()) continue;
					List<Sensitive> list = maps.get(sensitiveItem.getTenantCode());
					if(null == list){
						list = new ArrayList<Sensitive>();
						maps.put(sensitiveItem.getTenantCode(), list);
					}
					
					list.add(sensitiveItem);
				}
				
				for(String key : maps.keySet()){
					redisDao.listrPush(SENSTIVE_KEY.concat(key), maps.get(key));
//					okeys.remove(SENSTIVE_KEY.concat(key));
				}
				
			}
			
		} catch (Exception e) {
			log.error("初始化敏感词异常",e);
		}
	}

}
