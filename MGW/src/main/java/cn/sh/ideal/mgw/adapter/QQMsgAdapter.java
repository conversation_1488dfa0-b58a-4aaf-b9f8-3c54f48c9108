package cn.sh.ideal.mgw.adapter;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_CONTENT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_MSG_TYPE;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_TO_USER;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_URL;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.model.PlatformMessage;
import cn.sh.ideal.mgw.model.QQJoinBean;
import cn.sh.ideal.model.MessageInfo;

import com.alibaba.fastjson.JSONObject;

/**
 * QQ消息适配器
 * 
 * <AUTHOR>
 * 2015-11-25
 *
 */
public class QQMsgAdapter extends MsgAdapter{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static final Logger log = Logger.getLogger(QQMsgAdapter.class);
	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;
		
		if(null != data){
			message = new MessageInfo();
			QQJoinBean qqJoinBean = (QQJoinBean)data;
			
			message.setChannelCode(channelCode);
			message.setSource(qqJoinBean.getSource());
			message.setSendAccount(qqJoinBean.getOpenId());
			message.setAcceptedAccount(qqJoinBean.getHostImNumber());
			message.setMsgType(qqJoinBean.getMsgType().toString());
			
			this.assembSendContent(qqJoinBean, message);
		}
		return message;
	}

	@Override
	public JSONObject message2Media(MessageInfo message) {
		JSONObject object = new JSONObject();
		JSONObject extParam = null;
		
		object.put(MEDIA_PARAM_TO_USER, message.getAcceptedAccount());
		object.put(MEDIA_PARAM_MSG_TYPE, message.getMsgType());
		object.put("sendType", message.getSendType());
		if(PlatformMessage.MsgType.text.toString().equals(message.getMsgType())){
			extParam = new JSONObject();
			extParam.put(MEDIA_PARAM_CONTENT, message.getContent());
		}else if(PlatformMessage.MsgType.image.toString().equals(message.getMsgType()) ||
				PlatformMessage.MsgType.voice.toString().equals(message.getMsgType())){
			extParam = new JSONObject();
			extParam.put(MSG_PARAM_URL, message.getContent());
			
		}
		
		object.put(message.getMsgType(), extParam);
		
		return object;
	}
	
	/**
	 * 拼装Message对象的content字段
	 * 
	 * @param pm
	 * @param message
	 */
	private void assembSendContent(QQJoinBean qqJoin,MessageInfo message){
		switch (qqJoin.getMsgType()) {
			case text:message.setContent(qqJoin.getContent());break;

			case image:     
			case voice:  
			case video:
			case shortvideo:
			case location:
			case event:
			default:
				throw new NullPointerException("暂不支持该消息类型" + qqJoin.getMsgType());
		}
	}
	
}
