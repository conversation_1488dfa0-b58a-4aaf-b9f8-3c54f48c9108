package cn.sh.ideal.mgw.service.dubbo;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.alibaba.dubbo.config.annotation.Service;

import cn.sh.ideal.mgw.service.SessionService;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.dubboService
 * @typeName SessionServiceImpl
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月30日 下午5:26:31
 * @version 
 */
@Component("sessionService")
public class SessionServiceImpl implements SessionService{

	/* 
	 * @see cn.sh.ideal.mgw.server.SessionService#closeQQSession()
	 */
	@Override
	public Map<String, Object> closeQQSession() {
		return null;
	}

}
