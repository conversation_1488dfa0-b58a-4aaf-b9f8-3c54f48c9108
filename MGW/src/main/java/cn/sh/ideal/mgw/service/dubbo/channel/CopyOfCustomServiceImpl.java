/**
 * 
 */
package cn.sh.ideal.mgw.service.dubbo.channel;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.req.MessageHandleRequest;
import cn.sh.ideal.imr.resp.MessageHandleResponse;
import cn.sh.ideal.imr.service.IMRService;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.response.CustomResponseDTO;
import cn.sh.ideal.mgw.service.channel.CustomService;
import cn.sh.ideal.mgw.service.local.BlackService;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.XmlObject;
import cn.sh.ideal.mgw.utils.sender.ToolBarSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.util.EmojiConverter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.dubboService.channel
 * @typeName CustomServiceImpl
 * <AUTHOR> Zhou
 * @Description:  
 * @date 2016年4月1日 下午2:24:24
 * @version 
 */
@Component("customService")
public class CopyOfCustomServiceImpl implements CustomService {

	private static final Logger log = Logger.getLogger(CopyOfCustomServiceImpl.class);
	
	@Resource(name = "blackService")
	private BlackService blackService;
	
	@Resource(name = "sensitiveService")
	private SensitiveService sensitiveService;
	
	@Autowired
	private SysInitService initService;
	
	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	@Autowired
	private MessageInfoDao messageInfoDao;
	
	@Autowired
	private LocalSessionService sessionService;
	
	String[] selfServiceStatus = { "1", "6" };
	
	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;

	
	@Autowired
	private IMRService imrService;
	
	@Value("#{config['outAgent']}")
	private  String outAgent;
	
	/**
	 * 调用imr拼接url
	 */
	@Value("#{config['media.imrUrl']}")
	private String imrUrl;
	/**
	 * 调用mir拼接url
	 */
	@Value("#{config['media.imrUrl']}")
	private String mirUrl;

	@Value("#{config['EC.coding.url']}")
	private String codingUrl;
	
	@Override
	public CustomResponseDTO receiveCustomMessage(MessageInfo requestMessage) {
		
		CustomResponseDTO result = new CustomResponseDTO();
		
		
		try {
			
			log.info("推送自有渠道信息:" + JSONObject.toJSONString(requestMessage));
			
			SensitiveLog sensitiveLog = new SensitiveLog(requestMessage);
			
			if (sensitiveService.filter(sensitiveLog)) {
				result.setResultCode("-1");
				result.setResultMsg("包含敏感词");
				log.info("消息已经被过滤" + JSON.toJSONString(sensitiveLog));
			} else if (blackService.blackService(requestMessage.getChannelCode(), requestMessage.getSendAccount(), requestMessage.getTenantCode())) {
				result.setResultCode("-1");
				result.setResultMsg("消息已经被过滤");
				// 黑名单被过滤时返回一个消息
				log.info("在线客服消息已经黑名单被过滤,发送账号:" + requestMessage.getSendAccount());

			}else {

				ChannelConfig channelConfig = new ChannelConfig();

				channelConfig.setTenantCode(requestMessage.getTenantCode());
				// 渠道状态为启用的
				channelConfig.setChannelEnable("1");
				channelConfig.setChannelCode(requestMessage.getChannelCode());
				channelConfig.setChannelAccount(requestMessage.getAcceptedAccount());

				List<ChannelConfig> channelInfoList = channelConfigDao.query(channelConfig);
				if (channelInfoList != null && channelInfoList.size() > 0) {
					channelConfig = channelInfoList.get(0);
				} else {
					result.setResultCode("-1");
					result.setResultMsg("渠道配置不存在");
					return result;
				}
				RequestSessionbean requestSessionbean = new RequestSessionbean(requestMessage);
				if(StringUtils.isEmpty(requestMessage.getSkillType())){
					requestSessionbean.setSkillType(channelConfig.getRealTime());
				}
				SessionInfo sessioninfo = sessionService.getSession(requestSessionbean);
				
				
				String channelCode = requestMessage.getChannelCode();
				if (Constants.SELF_ENABLED.equals(channelConfig.getSelfType()) && (ArrayUtils.contains(selfServiceStatus, sessioninfo.getStatus()) || outAgent.equals(requestMessage.getContent()))) {
					// 自助

					Object contentObj = getJsonObj(requestMessage.getContent());
					PlatformMessage platformMessage = null;
					if (contentObj instanceof JSONObject) {

						platformMessage = JSONObject.toJavaObject((JSONObject) contentObj, PlatformMessage.class);
					} else {
						platformMessage = new PlatformMessage();
						platformMessage.setContent((String) contentObj);
					}
					platformMessage.setFollowData(requestMessage.getFollowData());
					//FIXME 网关自己的platformMessage里的msgType是对象,而IMR的这个model里msgType是String
//					platformMessage.setMsgType(PlatformMessage.MsgType.valueOf(requestMessage.getMsgType()));
					platformMessage.setMsgType(requestMessage.getMsgType());
					platformMessage.setToUser(requestMessage.getAcceptedAccount());
					platformMessage.setOpenId(requestMessage.getSendAccount());
					MessageHandleResponse messageHandle = imrService.messageHandle(new MessageHandleRequest(platformMessage));
					//FIXME
//					String resultXml = NetUtil.send(initService.getSysParam(Constants.IMR_ADDRESS).getParamValue() + imrUrl, "POST", JSONObject.toJSONString(platformMessage));
					if(messageHandle!=null&&messageHandle.getResponseMessage()!=null){
						JSONObject requestObj = new JSONObject();
						requestObj.put("userId", requestMessage.getUserId());
						requestObj.put("acceptedAccount", requestMessage.getSendAccount());
						requestObj.put("sendAccount", requestMessage.getAcceptedAccount());
						requestObj.put("tenantCode", requestMessage.getTenantCode());
						requestObj.put("workNo", "");
						requestObj.put("messageId", "");
						requestObj.put("sessionId", sessioninfo.getSessionId());
						requestObj.put("channelCode", channelCode);
						requestObj.put("businessType", requestMessage.getBusinessType());
						requestObj.put("appId", "");
						requestObj.put("account", new JSONArray());
						String msgType = messageHandle.getResponseMessage().getMsgType().toLowerCase();
						requestObj.put("msgType", msgType.toLowerCase());
						
						JSONObject msgObj = new JSONObject();
						String xmlString = messageHandle.getResponseMessage().toXMLString();
						XmlObject xmlObject = XmlObject.parse(xmlString);
						switch (msgType) {
						case "text":
							requestObj.put("content", xmlObject.getChildValue("Content"));
							break;

						case "news":
							msgObj.put("articlesCount", xmlObject.getChildValue("ArticlesCount"));

							List<XmlObject> articles = xmlObject.getUniqueSubXmlObject("Articles").getSubXmlObjects("item");
							JSONArray itemAry = new JSONArray();
							for (XmlObject item : articles) {
								JSONObject itemObj = new JSONObject();
								itemObj.put("title", item.getChildValue("Title"));
								itemObj.put("description", item.getChildValue("Description"));
								itemObj.put("picurl", item.getChildValue("PicUrl"));
								itemObj.put("url", item.getChildValue("Url"));
								itemAry.add(itemObj);
							}

							msgObj.put("articles", itemAry);
							requestObj.put("content", msgObj);

							break;
						default:
							break;
						}
						
						String url = JSONObject.parseObject(channelConfig.getAccountConfig()).getString("url");
						
						if (url.indexOf(";") > 0) {
							String[] urls = url.split(";");
							for (int i = 0; i < urls.length; i++) { 
								new ToolBarSender().customSend(requestObj.toJSONString(), urls[i]);
							}

						} else {
							new ToolBarSender().customSend(requestObj.toJSONString(), url);
						}
						
					}
					
					
					
					
//					if (!StringUtils.isEmpty(resultXml)) {
//						// 点对点回复消息
//						XmlObject xmlObject = XmlObject.parse(resultXml);
//
//						JSONObject requestObj = new JSONObject();
//						requestObj.put("userId", requestMessage.getUserId());
//						requestObj.put("acceptedAccount", requestMessage.getSendAccount());
//						requestObj.put("sendAccount", requestMessage.getAcceptedAccount());
//						requestObj.put("tenantCode", requestMessage.getTenantCode());
//						requestObj.put("workNo", "");
//						requestObj.put("messageId", "");
//						requestObj.put("sessionId", sessioninfo.getSessionId());
//						requestObj.put("channelCode", channelCode);
//						requestObj.put("businessType", requestMessage.getBusinessType());
//						requestObj.put("appId", "");
//						requestObj.put("account", new JSONArray());
//
//						JSONObject msgObj = new JSONObject();
//
//						MsgType msgType = MsgType.valueOf(xmlObject.getChildValue("MsgType").toUpperCase());
//
//						requestObj.put("msgType", msgType.getName());
//
//						switch (msgType) {
//						case TEXT:
//							requestObj.put("content", xmlObject.getChildValue("Content"));
//							break;
//
//						case NEWS:
//							msgObj.put("articlesCount", xmlObject.getChildValue("ArticlesCount"));
//
//							List<XmlObject> articles = xmlObject.getUniqueSubXmlObject("Articles").getSubXmlObjects("item");
//							JSONArray itemAry = new JSONArray();
//							for (XmlObject item : articles) {
//								JSONObject itemObj = new JSONObject();
//								itemObj.put("title", item.getChildValue("Title"));
//								itemObj.put("description", item.getChildValue("Description"));
//								itemObj.put("picurl", item.getChildValue("PicUrl"));
//								itemObj.put("url", item.getChildValue("Url"));
//								itemAry.add(itemObj);
//							}
//
//							msgObj.put("articles", itemAry);
//							requestObj.put("content", msgObj);
//
//							break;
//						default:
//							break;
//						}
//
//						String url = JSONObject.parseObject(channelConfig.getAccountConfig()).getString("url");
//						
//						if (url.indexOf(";") > 0) {
//							String[] urls = url.split(";");
//							for (int i = 0; i < urls.length; i++) { 
//								new ToolBarSender().customSend(requestObj.toJSONString(), urls[i]);
//							}
//
//						} else {
//							new ToolBarSender().customSend(requestObj.toJSONString(), url);
//						}
//					}

				} else {
					// 人工

					try {
///////////////////////////////////////////////////////////////////////////////////////////////////////////
						
//						requestMessage.setSessionId(sessioninfo.getSessionId());
//						requestMessage.setSource("1");
//						String content = requestMessage.getContent();
//						if ("text".equals(requestMessage.getMsgType())) {
//
//							requestMessage.setContent(EmojiConverter.encode(requestMessage.getContent()));
//						}
//						requestMessage.setMessageSource("1");
//						this.messageInfoDao.insert(requestMessage);
//						requestMessage.setContent(content);
//						String messageString = JSON.toJSONString(requestMessage);
//
//						// 去掉转码时候的转义符
//						JSONObject messageJson = JSON.parseObject(messageString);
//						//FIXME 这段是拼接 调用MIR接口需要参数的代码,暂时不修改 等到修改dubbo调用接口时再拼装参数对象
//						messageJson.put("content", getJsonObj(messageJson.getString("content")));
//						JSONArray replyAccount = JSONObject.parseArray(messageJson.getString("replyAccount"));
//
//						messageJson.put("followData", messageJson.getString("followData"));
//						messageJson.put("replyAccount", replyAccount);
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////						
						
//						MessageReceiveRequest messageReceiveRequest = new MessageReceiveRequest();

						requestMessage.setSessionId(sessioninfo.getSessionId());
						requestMessage.setSkillType(sessioninfo.getSkillType());
						requestMessage.setSource("1");
						requestMessage.setMessageSource("1");
						if ("text".equals(requestMessage.getMsgType())) {
							requestMessage.setContent(EmojiConverter.encode(requestMessage.getContent()));
						}
						
						if(StringUtils.isEmpty(requestMessage.getSessionId())){
							requestMessage.setSessionId(sessioninfo.getSessionId());
						}
						this.messageInfoDao.insert(requestMessage );


						if(StringUtils.isNotEmpty(sessioninfo.getCustomerId()))
							requestMessage.setCustomerId(sessioninfo.getCustomerId());
							
							
						redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE, requestMessage);

//						messageReceiveRequest.setMessageInfo(messageInfo);
// 						MessageReceiveResponse receive = mirService.receive(messageReceiveRequest);
						//FIXME NetUtil
						// 发送json
						
//						String resultString = NetUtil.send(initService.getSysParam(Constants.MIR_ADDRESS).getParamValue() + mirUrl, "POST", messageJson.toJSONString());
						//FIXME 
						//把dubbo服务返回的数据添加到result里
//						result = JSONObject.parseObject(resultString);
						log.info("messageInfo存入redis , "+JSON.toJSONString(requestMessage));
						
						/*
						 * coding 相关的逻辑
						 */
						if("4".equals(requestMessage.getSkillType())){
							JSONObject param = new JSONObject();
							param.put("sessionId", requestMessage.getSessionId());
							NetUtil.send(codingUrl, NetUtil.POST, param.toJSONString());
						}
						
						
					} catch (Exception e) {
						log.error("自定义渠道接收消息异常", e);
					}
				}

				result.setResultCode("0");
				result.setResultMsg("消息已存入任务池");
				result.setSessionId(sessioninfo.getSessionId());
				
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setResultCode("-1");
			result.setResultMsg("消息接收失败");
			return result;
		}
		
	}
	
	/**
	 * 判断字符串是否是jsonObject
	 * 
	 * @param jsonString
	 * @return
	 */
	public static Object getJsonObj(String jsonString) {
		if (jsonString.startsWith("{")) {
			JSONObject jsonObject = JSONObject.parseObject(jsonString);
			return jsonObject;
		} else {
			return jsonString;
		}

	}
	
	public void reloadSensitive(){
		this.sensitiveService.init();
	}
}
