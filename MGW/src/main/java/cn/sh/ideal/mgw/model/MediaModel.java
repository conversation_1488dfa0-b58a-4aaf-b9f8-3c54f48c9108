package cn.sh.ideal.mgw.model;

import weibo4j.model.Comment;
import weibo4j.model.Source;
import weibo4j.model.Status;
import weibo4j.model.Visible;
import weibo4j.org.json.JSONArray;

public class MediaModel {
	private Integer id;
     private String taskId ="";
     
     private String uid="";
     private String mediaId ="";
     
     private String mediaTitle ="";
     
     private String mediaContent ="";
     
     private String htmlContent ="";
     
     private String mediaSender ="";
     
     private String mediaReceiver ="";
     
     private String mediaSendTime ="";
     
     private String mediaReceiveTime ="";
     
     private String channelType ="";
     
     private String mediaBusinessType ="";
     
     private String interceptType ="";
     
     private String infoType ="";
     
     private String mediaIsAssign ="";
     
     private String mediaAssigner ="";
     
     private String mediaAssignTime ="";
     
     private String mediaIsDo ="";
     
     private String mediaDoer ="";
     
     private String mediaDoTime ="";
     
     private String mediaCoper ="";
     
     private String mediaFowardNum ="";
     
     private String mediaCommentsNum ="";
     
     private String mediaFunsNum ="";
     
     private String mediaMessageType ="";
     
     private String mediaFileName ="";
     
     private String mediaFilePath ="";
     
     private String mediaTxtName ="";
     
     private String mediaTxtPath ="";
     
     private String replyContent ="";
     
     private String isReply ="";//自动回复是否成功
     
     private String isAutoReply ="";//是否自动回复
     
     private String fileNames ="";//自动回复内嵌图片名称
     private String isTop = "0";//是否置顶
     private String sessionType;
     private String isToolbar = "1";//0：是 1：否
     private String userID = "";// 用户id
     private String wbId="";//微博id
     private String repId="";//回复id
     private String coSource="";//评论来源
     private boolean favorited=false;//微博是否已收藏
     private boolean truncated=false;//微博是否被截断
     private String geo="";          //微博地理信息，保存经纬度，
     private String mid="";			 //mid
     private String annotations="";     //微博元数据，没有时不返回此字段
     private String operType="";     //操作类型
     private Source wbSource=null;//微博来源
     private Visible visible=null;//微博可见性
     private String smallPic="";//微博小图
     private String middlePic="";//微博中图
     private String originalPic="";//微博原图
     private Comment replyComment=null;//回复评论
     private Status status=null;//微博内容
	 private  JSONArray picUrls=new JSONArray();
	 //原始微博的用户id,用于前台区分跳转userid
	 private String YuanWeiboUserid="";
	 private String yuanWeibo;
	 
	 public String getYuanWeibo() {
		return yuanWeibo;
	}

	public void setYuanWeibo(String yuanWeibo) {
		this.yuanWeibo = yuanWeibo;
	}

	//@中原微博的图片ids
	 private String picIds;
	 
	 public String getPicIds() {
		return picIds;
	}

	public void setPicIds(String picIds) {
		this.picIds = picIds;
	}

	//用户的头像信息
	 public String headimgUrl="";
    
    

	public String getHeadimgUrl() {
		return headimgUrl;
	}

	public void setHeadimgUrl(String headimgUrl) {
		this.headimgUrl = headimgUrl;
	}

	public String getYuanWeiboUserid() {
		return YuanWeiboUserid;
	}

	public void setYuanWeiboUserid(String yuanWeiboUserid) {
		YuanWeiboUserid = yuanWeiboUserid;
	}

	//转发的微博的图片
    private  JSONArray yuanWeiboPicUrls=new JSONArray();
    private String yuanWeiboId="";
    public String getYuanWeiboId() {
		return yuanWeiboId;
	}

	public void setYuanWeiboId(String yuanWeiboId) {
		this.yuanWeiboId = yuanWeiboId;
	}

	public JSONArray getYuanWeiboPicUrls() {
		return yuanWeiboPicUrls;
	}

	public void setYuanWeiboPicUrls(JSONArray yuanWeiboPicUrls) {
		this.yuanWeiboPicUrls = yuanWeiboPicUrls;
	}

	public String getUserID() {
		return userID;
	}

	public void setUserID(String userID) {
		this.userID = userID;
	}

     public String getIsToolbar() {
		return isToolbar;
	}

	public void setIsToolbar(String isToolbar) {
		this.isToolbar = isToolbar;
	}
	 public String getSessionType() {
		return sessionType;
	}
	 
	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}



	public void setSessionType(String sessionType) {
		this.sessionType = sessionType;
	}

	public String getIsTop() {
		return isTop;
	}

	public void setIsTop(String isTop) {
		this.isTop = isTop;
	}

	public String getFileNames() {
		return fileNames;
	}

	public void setFileNames(String fileNames) {
		this.fileNames = fileNames;
	}

	public String getMediaFileName() {
		return mediaFileName;
	}

	public void setMediaFileName(String mediaFileName) {
		this.mediaFileName = mediaFileName;
	}

	public String getMediaFilePath() {
		return mediaFilePath;
	}

	public void setMediaFilePath(String mediaFilePath) {
		this.mediaFilePath = mediaFilePath;
	}

	public String getMediaTxtName() {
		return mediaTxtName;
	}

	public void setMediaTxtName(String mediaTxtName) {
		this.mediaTxtName = mediaTxtName;
	}

	public String getMediaTxtPath() {
		return mediaTxtPath;
	}

	public void setMediaTxtPath(String mediaTxtPath) {
		this.mediaTxtPath = mediaTxtPath;
	}

	public String getTaskId() {
		return taskId;
	 }

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getMediaId() {
		return mediaId;
	}

	public void setMediaId(String mediaId) {
		this.mediaId = mediaId;
	}

	public String getMediaTitle() {
		return mediaTitle;
	}

	public void setMediaTitle(String mediaTitle) {
		this.mediaTitle = mediaTitle;
	}

	public String getMediaContent() {
		return mediaContent;
	}

	public void setMediaContent(String mediaContent) {
		this.mediaContent = mediaContent;
	}

	public String getMediaSender() {
		return mediaSender;
	}

	public void setMediaSender(String mediaSender) {
		this.mediaSender = mediaSender;
	}

	public String getMediaReceiver() {
		return mediaReceiver;
	}

	public void setMediaReceiver(String mediaReceiver) {
		this.mediaReceiver = mediaReceiver;
	}

	public String getMediaSendTime() {
		return mediaSendTime;
	}

	public void setMediaSendTime(String mediaSendTime) {
		this.mediaSendTime = mediaSendTime;
	}

	public String getMediaReceiveTime() {
		return mediaReceiveTime;
	}

	public void setMediaReceiveTime(String mediaReceiveTime) {
		this.mediaReceiveTime = mediaReceiveTime;
	}

	

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getInfoType() {
		return infoType;
	}

	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}

	public String getMediaBusinessType() {
		return mediaBusinessType;
	}

	public void setMediaBusinessType(String mediaBusinessType) {
		this.mediaBusinessType = mediaBusinessType;
	}

	public String getMediaIsAssign() {
		return mediaIsAssign;
	}

	public void setMediaIsAssign(String mediaIsAssign) {
		this.mediaIsAssign = mediaIsAssign;
	}

	public String getMediaAssigner() {
		return mediaAssigner;
	}

	public void setMediaAssigner(String mediaAssigner) {
		this.mediaAssigner = mediaAssigner;
	}

	public String getMediaAssignTime() {
		return mediaAssignTime;
	}

	public void setMediaAssignTime(String mediaAssignTime) {
		this.mediaAssignTime = mediaAssignTime;
	}

	public String getMediaIsDo() {
		return mediaIsDo;
	}

	public void setMediaIsDo(String mediaIsDo) {
		this.mediaIsDo = mediaIsDo;
	}

	public String getMediaDoer() {
		return mediaDoer;
	}

	public void setMediaDoer(String mediaDoer) {
		this.mediaDoer = mediaDoer;
	}

	public String getMediaDoTime() {
		return mediaDoTime;
	}

	public void setMediaDoTime(String mediaDoTime) {
		this.mediaDoTime = mediaDoTime;
	}

	public String getMediaCoper() {
		return mediaCoper;
	}

	public void setMediaCoper(String mediaCoper) {
		this.mediaCoper = mediaCoper;
	}

	public String getMediaFowardNum() {
		return mediaFowardNum;
	}

	public void setMediaFowardNum(String mediaFowardNum) {
		this.mediaFowardNum = mediaFowardNum;
	}

	public String getMediaCommentsNum() {
		return mediaCommentsNum;
	}

	public void setMediaCommentsNum(String mediaCommentsNum) {
		this.mediaCommentsNum = mediaCommentsNum;
	}

	public String getMediaFunsNum() {
		return mediaFunsNum;
	}

	public void setMediaFunsNum(String mediaFunsNum) {
		this.mediaFunsNum = mediaFunsNum;
	}

	public String getMediaMessageType() {
		return mediaMessageType;
	}

	public void setMediaMessageType(String mediaMessageType) {
		this.mediaMessageType = mediaMessageType;
	}

	public String getInterceptType() {
		return interceptType;
	}

	public void setInterceptType(String interceptType) {
		this.interceptType = interceptType;
	}

	public String getHtmlContent() {
		return htmlContent;
	}

	public void setHtmlContent(String htmlContent) {
		this.htmlContent = htmlContent;
	}

	public String getReplyContent() {
		return replyContent;
	}

	public void setReplyContent(String replyContent) {
		this.replyContent = replyContent;
	}

	public String getIsReply() {
		return isReply;
	}

	public void setIsReply(String isReply) {
		this.isReply = isReply;
	}

	public String getIsAutoReply() {
		return isAutoReply;
	}

	public void setIsAutoReply(String isAutoReply) {
		this.isAutoReply = isAutoReply;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public boolean isFavorited() {
		return favorited;
	}

	public void setFavorited(boolean favorited) {
		this.favorited = favorited;
	}

	public boolean isTruncated() {
		return truncated;
	}

	public void setTruncated(boolean truncated) {
		this.truncated = truncated;
	}

	public String getGeo() {
		return geo;
	}

	public void setGeo(String geo) {
		this.geo = geo;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getAnnotations() {
		return annotations;
	}

	public void setAnnotations(String annotations) {
		this.annotations = annotations;
	}

	public JSONArray getPicUrls() {
		return picUrls;
	}

	public void setPicUrls(JSONArray picUrls) {
		this.picUrls = picUrls;
	}

	public String getOperType() {
		return operType;
	}

	public void setOperType(String operType) {
		this.operType = operType;
	}

	public String getCoSource() {
		return coSource;
	}

	public void setCoSource(String coSource) {
		this.coSource = coSource;
	}

	public Source getWbSource() {
		return wbSource;
	}

	public void setWbSource(Source wbSource) {
		this.wbSource = wbSource;
	}

	public Visible getVisible() {
		return visible;
	}

	public void setVisible(Visible visible) {
		this.visible = visible;
	}

	public String getSmallPic() {
		return smallPic;
	}

	public void setSmallPic(String smallPic) {
		this.smallPic = smallPic;
	}

	public String getMiddlePic() {
		return middlePic;
	}

	public void setMiddlePic(String middlePic) {
		this.middlePic = middlePic;
	}

	public String getOriginalPic() {
		return originalPic;
	}

	public void setOriginalPic(String originalPic) {
		this.originalPic = originalPic;
	}

	public Comment getReplyComment() {
		return replyComment;
	}

	public void setReplyComment(Comment replyComment) {
		this.replyComment = replyComment;
	}

	public String getWbId() {
		return wbId;
	}

	public void setWbId(String wbId) {
		this.wbId = wbId;
	}

	public String getRepId() {
		return repId;
	}

	public void setRepId(String repId) {
		this.repId = repId;
	}

	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	
}
