package cn.sh.ideal.mgw.adapter;

import java.util.HashMap;
import java.util.Map;


/**
 * 常量类
 * 
 * <AUTHOR>
 * 
 * 2015-3-6
 *
 */
public class Constants {
	//参数名
	public static final String MSG_PARAM_LOCATION_X = "x";
	public static final String MSG_PARAM_LOCATION_Y = "y";
	//经度
	public static final String MSG_PARAM_LOCATION_LNG = "longitude";
	//纬度
	public static final String MSG_PARAM_LOCATION_LAT = "latitude";
	
	
	public static final String MSG_PARAM_EVENT_TYPE = "eventType";
	public static final String MSG_PARAM_EVENT_ID = "eventId";
	public static final String MSG_PARAM_SUBJECT = "subject";
	public static final String MSG_PARAM_ATTACHMENTS = "attachments";
	public static final String MSG_PARAM_COTENT_HTML = "contentHtml";
	public static final String MSG_PARAM_TEXT = "text";
	public static final String MSG_PARAM_USER_ID = "userId";
	public static final String MSG_PARAM_CREATEED_TIME ="createdTime";
	public static final String MSG_PARAM_WEIBOID = "weiboid";
	public static final String MSG_PARAM_WEIBOINFO = "wbinfo";
	public static final String MSG_PARAM_WEIBOCONTENT = "weiboContent";
	public static final String MSG_PARAM_COMMENTID = "commentId";
	public static final String MSG_PARAM_REPLYID = "replyId";
	public static final String MSG_PARAM_REPLYCOMMENT= "peplycomment";
	public static final String MSG_PARAM_SOURCE = "source";
	public static final String MSG_PARAM_FAVOURITED = "favorited";
	public static final String MSG_PARAM_TRUNCATED = "truncated";
	public static final String MSG_PARAM_GEO = "geo";
	public static final String MSG_PARAM_MID = "mid";
	public static final String MSG_PARAM_REPOSTS_COUNT = "repostsCount";
	public static final String MSG_PARAM_COMMENTS_COUNT = "commentsCount";
	public static final String MSG_PARAM_ANNOTATIONS = "annotations";
	public static final String MSG_PARAM_PIC_IDS = "picIds";
	public static final String MSG_PARAM_TENANT_CODE = "tenantCode";
	public static final String MSG_PARAM_SKILL_QUEUE = "skillQueue";
	public static final String MSG_PARAM_NICKNAME = "nickname";
	public static final String MSG_PARAM_ACCOUNT = "account";
	public static final String MSG_PARAM_BUSINESS_TYPE = "businessType";
	public static final String MSG_PARAM_PARAMS = "params";
	public static final String MSG_PARAM_PARAM = "param";
	public static final String MSG_PARAM_MSG = "msg";
	public static final String MSG_PARAM_SEND_ACCOUNT = "sendAccount";
	public static final String MSG_PARAM_ACCEPT_ACCOUNT = "acceptedAccount";
	public static final String MSG_PARAM_CHANNEL_CODE = "channelCode";
	public static final String MSG_PARAM_URL = "url";
	public static final String MSG_PARAM_ARTICLES = "articles";
	public static final String MSG_PARAM_MESSAGE ="message";
	public static final String MSG_PARAM_DATA ="data";
	public static final String MSG_PARAM_OPERTYPE ="operType";
	public static final String MSG_PARAM_THUMBNAIL_PIC ="thumbnailPic";//微博小图
	public static final String MSG_PARAM_BMIDDLE_PIC ="bmiddlePic";//微博中图
	public static final String MSG_PARAM_ORIGINAL_PIC ="originalPic";//微博原图
	public static final String MSG_PARAM_BUTTON ="button";//微博原图
	
	
	
	
	public static final String MEDIA_PARAM_TO_USER = "touser";
	public static final String MEDIA_PARAM_MSG_TYPE = "msgtype";
	public static final String MEDIA_PARAM_MSG_TYPE1 = "msgType";
	public static final String MEDIA_PARAM_CONTENT = "content";
	public static final String MEDIA_PARAM_MEDIA_ID = "media_id";
	public static final String MEDIA_PARAM_ACCESS_TOKEN = "access_token";
	public static final String MEDIA_PARAM_COMMENT = "comment";
	public static final String MEDIA_PARAM_ID = "id";
	public static final String MEDIA_PARAM_CID = "cid";
	public static final String MEDIA_PARAM_APP_ID = "appId";
	public static final String MEDIA_PARAM_COMMENT_ORI = "comment_ori";
	public static final String MEDIA_PARAM_RIP= "rip";
	public static final String MEDIA_PARAM_REPLY_CHANNEL_CODE= "replyChannelCode";
	public static final String MEDIA_PARAM_USER_ACCOUNT= "userAccount";
	public static final String MEDIA_PARAM_SERVER_COUNT= "serverAccount";
	public static final String MEDIA_PARAM_ISDEFAULT= "isDefault";
	public static final String MEDIA_PARAM_APPOINTMENT_TIME= "appointementTime";
	public static final String MEDIA_PARAM_APPOINTMENT_ACCOUNT= "appointementAccount";
	public static final String MEDIA_PARAM_APPOINTMENT_NAME= "appointementName";
	public static final String MEDIA_PARAM_WORK_NO ="workNo";
	public static final String MEDIA_PARAM_SESSION_ID ="sessionId";
	public static final String MEDIA_PARAM_MESSAGE_ID ="messageId";
	
	public static final String MEDIA_TYPE_EMAIL = "email";
	public static final String MEDIA_TYPE_WEIBO = "weibo";
	
	/**
	 * 微博请求参数
	 */

	public static final String WB_PARAM_IDS="ids";
	public static final String WB_PARAM_WITHOUTMENTION = "without_mention";
	public static final String WB_PARAM_STATUS="status";
	public static final String WB_PARAM_LAT="lat";
	public static final String WB_PARAM_LONGS="longs";
	public static final String WB_PARAM_PIC="pic";
	public static final String WB_PARAM_UID="uid";
	public static final String WB_PARAM_UIDS="uids";
	public static final String WB_PARAM_TYPE="type";
	public static final String WB_PARAM_LANGUAGE="language";
	public static final String WB_PARAM_VISIBLE="visible";
	public static final String WB_PARAM_LISTID="list_id";
	
	
}
