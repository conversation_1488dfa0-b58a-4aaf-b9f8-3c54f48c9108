package cn.sh.ideal.mgw.utils;


import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;

public class EncryptUtil {
	public final static String md5(String s) {
        char hexDigits[]={'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};       
        try {
            byte[] btInput = s.getBytes();
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
	
	/**
	 * 字符串解密
	 * @param sSrc
	 *         需解密字符串
	 * @param sKey
	 *         解密方式
	 *         
	 * @return 解密后字符串
	 * */
	public static String aesDecrypt(String sSrc, String sKey) {
		try {
			if (sKey.length() > 16) {
				sKey = sKey.substring(0, 16);
			}
			byte[] raw = sKey.getBytes("ASCII");
			SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
			Cipher cipher = Cipher.getInstance("AES");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec);
			byte[] encrypted1 = hex2byte(sSrc);
			byte[] original = cipher.doFinal(encrypted1);
			String originalString = new String(original);
			return originalString;
		} catch (Exception ex) {
			System.out.println(ex.toString());
			return null;
		}
	}
	
	/**
	 * 加密字符串
	 * 
	 * @param sSrc
	 *         待加密字符串
	 * @param sKey
	 *         加密方式
	 *  
	 * @return 加密后字符串       
	 * */
	public static String aesEncrypt(String sSrc, String sKey) {
		try {
			if (sKey.length() > 16) {
				sKey = sKey.substring(0, 16);
			}
			byte[] raw = sKey.getBytes("ASCII");
			SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
			Cipher cipher = Cipher.getInstance("AES");
			cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
			byte[] encrypted = cipher.doFinal(sSrc.getBytes());
			return byte2hex(encrypted).toLowerCase();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public static byte[] hex2byte(String strhex) {
		if (strhex == null) {
			return null;
		}
		int l = strhex.length();
		if (l % 2 == 1) {
			return null;
		}
		byte[] b = new byte[l / 2];
		for (int i = 0; i != l / 2; i++) {
			b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2),
					16);
		}
		return b;
	}
	
	public static String byte2hex(byte[] b) {
		String hs = "";
		String stmp = "";
		for (int n = 0; n < b.length; n++) {
			stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
			if (stmp.length() == 1) {
				hs = hs + "0" + stmp;
			} else {
				hs = hs + stmp;
			}
		}
		return hs.toUpperCase();
	}
	
	public static String decodeStr(String decodeStr, String encode) throws UnsupportedEncodingException{
		Base64 base64 = new Base64();  
        byte[] debytes = base64.decodeBase64(decodeStr.getBytes()); 
        if(!StringUtils.isEmpty(encode))
        	return new String(debytes,encode);
        else
        	return new String(debytes);
	}
	
	/** 
     * 加密 
     *  
     * @param pwd 
     * @return 
	 * @throws UnsupportedEncodingException 
     * @see [类、类#方法、类#成员] 
     */  
    public static String encodeStr(String encodeStr,String encode) throws UnsupportedEncodingException  
    {  
        Base64 base64 = new Base64();  
        byte[] enbytes = null;
        if(!StringUtils.isEmpty(encode)){
        	if("UTF-8".equalsIgnoreCase(CharacterUtil.getEncoding(encodeStr))){
        		encodeStr = new String(encodeStr.getBytes(),"utf-8");
        	}
        	enbytes = base64.encodeBase64Chunked(encodeStr.getBytes(encode)); 
        }        	
        else{
        	enbytes = base64.encodeBase64Chunked(encodeStr.getBytes()); 
        }
        return new String(enbytes);  
    }  
	
}
