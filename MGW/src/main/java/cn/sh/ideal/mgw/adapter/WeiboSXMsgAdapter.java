package cn.sh.ideal.mgw.adapter;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.base.WeiboInf;
import cn.sh.ideal.mgw.model.MediaModel;
import cn.sh.ideal.mgw.model.PlatformMessage;
import cn.sh.ideal.mgw.model.WeiboArticleData;
import cn.sh.ideal.mgw.model.WeiboButtonData;
import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.mgw.model.WeiboSubButtonData;
import cn.sh.ideal.mgw.model.req.WeiboInfoRequestDTO;
import cn.sh.ideal.mgw.service.local.FilesService;
import cn.sh.ideal.mgw.service.local.impl.BaseService;
import cn.sh.ideal.mgw.utils.sender.WeiboSender;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.util.SpringContextUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_EVENT_ID;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_EVENT_TYPE;

/**
 * 微博私信消息适配器
 * 
 * <AUTHOR>
 * 2015-3-9
 *
 */
public class WeiboSXMsgAdapter extends MsgAdapter{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4433223024779186658L;
	private static final Logger log = Logger.getLogger(WeiboSender.class);
	private SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;
		if(null != data){
			if(data instanceof WeiboInfoRequestDTO){
				message = new MessageInfo();
				WeiboInfoRequestDTO wird = (WeiboInfoRequestDTO)data;
				message.setChannelCode(channelCode);
				message.setSource("1");
				message.setSendAccount(wird.getSender_id());
				message.setAcceptedAccount(wird.getReceiver_id());
				message.setMsgType(wird.getType());
				try {
					message.setCreateTime(sf.parse(sf.format(new Date(wird.getCreated_at()))));
				} catch (ParseException e) {
					e.printStackTrace();
				}
				this.assembSendContent(wird, message);
			}
		}
		return message;
	}

	@Override
	public JSONObject message2Media(MessageInfo message) {		
		JSONObject json = new JSONObject();		
		json.put(Constants.MSG_PARAM_ACCEPT_ACCOUNT, message.getAcceptedAccount());
		json.put(Constants.MEDIA_PARAM_MSG_TYPE1, message.getMsgType());	
		
		if("text".equals(message.getMsgType())){
			json.put(Constants.MEDIA_PARAM_CONTENT, message.getContent());
			json.put(Constants.MSG_PARAM_OPERTYPE, WeiboInf.WB_GETREPLYMSG.getCode());
		}
		else if("image".equals(message.getMsgType())){
			json.put(Constants.MEDIA_PARAM_CONTENT, message.getContent());
			json.put(Constants.MSG_PARAM_OPERTYPE, WeiboInf.WB_GETREPLYMSG.getCode());
		}
		json.put("sendType", message.getSendType());
		return json;
	}
	
	private List<WeiboButtonData> assembMenuButton(JSONArray obj){
		List buttons = new ArrayList(); 
		for(int i=0;i<obj.size();i++){
			JSONObject singleObj = obj.getJSONObject(i);
			WeiboButtonData wbd = new WeiboButtonData();
			wbd.setType(singleObj.getString("type"));
			if("view".equals(wbd.getType()))
				wbd.setUrl(singleObj.getString("url"));
			else
				wbd.setKey(singleObj.getString("key"));
			wbd.setName(singleObj.getString("name"));
			buttons.add(wbd);
		}		
		return buttons;
	}
	
	/**
	 * 拼装Message对象的content字段
	 * 
	 * @param media
	 * @param message
	 */
	private void assembSendContent(WeiboInfoRequestDTO media,MessageInfo message){
		log.info("############media.getType=" + media.getType());
		if(WeiboMsgInfo.MsgType.text.toString().equals(media.getType())){
			log.info("############media.getText___=" + media.getText());
			message.setContent(media.getText());
		}					
		else if(WeiboMsgInfo.MsgType.voice.toString().equals(media.getType())){
			try {	
				log.info("############media.voice=" + media.getText());
				JSONObject obj = new JSONObject();
				obj.put("text", media.getText());
				obj.put("vfid", media.getData().getVfid());
				obj.put("tovfid", media.getData().getTovfid());
				log.info("############vfid=" + media.getData().getVfid() + "___tovfid=" + media.getData().getTovfid() + "___token=" + media.getToken() );
				FilesService filesService =(FilesService)SpringContextUtil.getBean("filesService");
				message.setContent(filesService.downloadWeiBoSXMedia(media.getToken(), media.getData().getTovfid(), "mediaFile"));
				log.info("############filesService=" + filesService);			
			} catch (Exception e) {
				log.error("############filesService=" + e.getMessage());	
				e.printStackTrace();
			}
		}					
		else if(WeiboMsgInfo.MsgType.image.toString().equals(media.getType())){
			JSONObject obj = new JSONObject();				
			try {		
				log.info("############media.getText=" + media.getText());
				obj.put("text", media.getText());
				obj.put("vfid", media.getData().getVfid());
				log.info("############media.getData().getTovfid()=" + media.getData().getTovfid());
				obj.put("tovfid", media.getData().getTovfid());		
				log.info("############vfid=" + media.getData().getVfid() + "___tovfid=" + media.getData().getTovfid());
				FilesService filesService =(FilesService)SpringContextUtil.getBean("filesService");
				log.info("############filesService=" + filesService);			
				message.setContent(filesService.downloadWeiBoSXMedia(media.getToken(), media.getData().getTovfid(), "mediaFile"));
			} catch (Exception e) {
				log.error("############filesService=" + e.getMessage());	
				e.printStackTrace();
			}
			
		}					
		else if(WeiboMsgInfo.MsgType.position.name().equals(media.getType())){
			JSONObject obj = new JSONObject();
			obj.put("text", media.getText());
			obj.put("longitude", media.getData().getLongitude());
			obj.put("latitude", media.getData().getLatitude());
			message.setContent(obj.toJSONString());
		}else if("event".equals(media.getType())){
				JSONObject object = new JSONObject();

			try {
				object.put(MSG_PARAM_EVENT_TYPE, media.getData().getSubtype());
				object.put(MSG_PARAM_EVENT_ID, media.getData().getKey());

				message.setContent(object.toJSONString());
			} catch (Exception e) {
				log.error("############filesService=" + e.getMessage(),e);
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 拼装Message对象的content字段
	 * 
	 * @param pm
	 * @param message
	 */
	private void assembSendContent(PlatformMessage pm,MessageInfo message){
		
		switch (pm.getMsgType()) {
			case text:message.setContent(pm.getContent());break;
			case image:message.setContent(pm.getPicUrl());break;
			case voice:message.setContent("");break;
			case location:{
				JSONObject object = new JSONObject();
				object.put(Constants.MSG_PARAM_LOCATION_X, pm.getX());
				object.put(Constants.MSG_PARAM_LOCATION_Y, pm.getY());
				
				message.setContent(object.toJSONString());break;
			}
	
			default:
				throw new NullPointerException("暂不支持该消息类型" + pm.getMsgType());
		}
	}
}
