package cn.sh.ideal.mgw.init;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.AlipayMsgAdapter;
import cn.sh.ideal.mgw.adapter.EmailMsgAdapter;
import cn.sh.ideal.mgw.adapter.MsgAdapter;
import cn.sh.ideal.mgw.adapter.QQMsgAdapter;
import cn.sh.ideal.mgw.adapter.SmsMsgAdapter;
import cn.sh.ideal.mgw.adapter.WebchatMsgAdapter;
import cn.sh.ideal.mgw.adapter.WechatMsgAdapter;
import cn.sh.ideal.mgw.adapter.WeiboMsgAdapter;
import cn.sh.ideal.mgw.adapter.WeiboSXMsgAdapter;
import cn.sh.ideal.mgw.adapter.YichatMsgAdapter;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.service.local.BlackService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.model.ChannelConfig;

/**
 * 系统配置初始化服务类
 * 
 * <AUTHOR>
 * 
 */
@Service("initService")
public class InitService {
	private static final Logger log = Logger.getLogger(InitService.class);
	
	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private SysInitService sysInitService;
	
	@Autowired
	private SensitiveService sensitiveService;
	
	@Autowired
	private BlackService blackService;


	/**
	 * 初始化操作
	 */
	@PostConstruct
	public void init() {
		Set<String> keys = redisDao.getKeysByPattern(Constants.KEY_CHANNEL_INFO+"*");
		Set<String> channelkeys = redisDao.getKeysByPattern(Constants.KEY_OWN_CHANNEL_INFO+"*");
		if(channelkeys!=null&&channelkeys.size()>0)
		keys.addAll(channelkeys);
		for (String key : keys) {
			redisDao.deleteValue(key);
		}
		redisDao.deleteValue(Constants.KEY_ADAPTER_MAP);
		sysInitService.init(true, false, false, true,false);
		initChannelConfig();
		initAdapterMap();
		sensitiveService.init();
		blackService.init();
	}

	/**
	 * 初始化渠道配置
	 */
	public void initChannelConfig() {
		log.info("加载渠道配置……");
		ChannelConfig channelConfig = new ChannelConfig();
		channelConfig.setChannelEnable("1");
		List<ChannelConfig> list = channelConfigDao.query(channelConfig);
		
		
		for (ChannelConfig info : list) {
			redisDao.saveValue(Constants.KEY_CHANNEL_INFO + info.getChannelAccount(), info);
			if("1".equals(info.getChannelType())||"2".equals(info.getChannelType())){
			redisDao.saveValue(Constants.KEY_OWN_CHANNEL_INFO + info.getChannelCode(), info);
			}
			//新浪微博粉丝服务平台token
			if(Platform.SINAWEIBO.getCode().equals(info.getChannelCode())){
				log.info("新浪微博渠道accountConfig=" + info.getAccountConfig());
				JSONObject accountConfig = JSONObject.parseObject(info.getAccountConfig());
				log.info("sinaweibo_accesstoken_=" + accountConfig.getString("token") + "___channelAccount=" + info.getChannelAccount());
				redisDao.saveValue("sinaweibo_accesstoken_" + info.getChannelAccount(),accountConfig.getString("token"));
			}
		}
	}


	/**
	 * 初始化渠道配置
	 */
	public void initAdapterMap() {
		log.info("加载自有渠道配置……");

		HashMap<String, MsgAdapter> adapterMap = new HashMap<String, MsgAdapter>();
		// 非自有渠道适配器初始化
		adapterMap.put(Platform.WX.getCode(), new WechatMsgAdapter());
		adapterMap.put(Platform.YX.getCode(), new YichatMsgAdapter());
		adapterMap.put(Platform.EMAIL.getCode(), new EmailMsgAdapter());
		adapterMap.put(Platform.SMS.getCode(), new SmsMsgAdapter());
		adapterMap.put(Platform.CSSMS.getCode(), new SmsMsgAdapter());
		adapterMap.put(Platform.SINAWEIBO.getCode(), new WeiboSXMsgAdapter());
		adapterMap.put(Platform.SINAMB.getCode(), new WeiboMsgAdapter());
		adapterMap.put(Platform.ALIPAY.getCode(), new AlipayMsgAdapter());
		adapterMap.put(Platform.QQ.getCode(), new QQMsgAdapter());

		Set<String> keys = redisDao.getKeysByPattern(Constants.KEY_OWN_CHANNEL_INFO+"*");
		for (String key : keys) {

			adapterMap.put(key.replace(Constants.KEY_OWN_CHANNEL_INFO, ""), new WebchatMsgAdapter());
		}

		redisDao.saveValue(Constants.KEY_ADAPTER_MAP, adapterMap);
	}
}
