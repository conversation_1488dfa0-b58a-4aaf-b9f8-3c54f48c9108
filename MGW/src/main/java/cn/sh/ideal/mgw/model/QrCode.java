package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.util.Date;

public class QrCode implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.ID
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.TENANT_CHANNEL_ID
     *
     * @mbggenerated
     */
    private String channelAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.TENANT_CODE
     *
     * @mbggenerated
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.PLATFORM_USER
     *
     * @mbggenerated
     */
    private String platformUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.ACTION_NAME
     *
     * @mbggenerated
     */
    private String actionName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.SCENE_ID
     *
     * @mbggenerated
     */
    private String sceneId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.CREATE_DATE
     *
     * @mbggenerated
     */
    private Date createDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.USAGE_TYPE
     *
     * @mbggenerated
     */
    private String usageType;

    private String ticket;
    
    private String fromChannelAccount;
    
    public QrCode() {
		super();
	}

	public QrCode(String channelAccount, String tenantCode,
			String platformUser, String actionName, String sceneId,
			Date createDate, String usageType,String ticket,String fromChannelAccount) {
		super();
		this.channelAccount = channelAccount;
		this.tenantCode = tenantCode;
		this.platformUser = platformUser;
		this.actionName = actionName;
		this.sceneId = sceneId;
		this.createDate = createDate;
		this.usageType = usageType;
		this.ticket=ticket;
		this.fromChannelAccount=fromChannelAccount;
		
	}

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table MGW_QRCODE
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.ID
     *
     * @return the value of MGW_QRCODE.ID
     *
     * @mbggenerated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.ID
     *
     * @param id the value for MGW_QRCODE.ID
     *
     * @mbggenerated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.TENANT_CHANNEL_ID
     *
     * @return the value of MGW_QRCODE.TENANT_CHANNEL_ID
     *
     * @mbggenerated
     */
    public String getChannelAccount() {
        return channelAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.TENANT_CHANNEL_ID
     *
     * @param tenantChannelId the value for MGW_QRCODE.TENANT_CHANNEL_ID
     *
     * @mbggenerated
     */
    public void setChannelAccount(String channelAccount) {
        this.channelAccount = channelAccount == null ? null : channelAccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.TENANT_CODE
     *
     * @return the value of MGW_QRCODE.TENANT_CODE
     *
     * @mbggenerated
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.TENANT_CODE
     *
     * @param tenantCode the value for MGW_QRCODE.TENANT_CODE
     *
     * @mbggenerated
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.PLATFORM_USER
     *
     * @return the value of MGW_QRCODE.PLATFORM_USER
     *
     * @mbggenerated
     */
    public String getPlatformUser() {
        return platformUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.PLATFORM_USER
     *
     * @param platformUser the value for MGW_QRCODE.PLATFORM_USER
     *
     * @mbggenerated
     */
    public void setPlatformUser(String platformUser) {
        this.platformUser = platformUser == null ? null : platformUser.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.ACTION_NAME
     *
     * @return the value of MGW_QRCODE.ACTION_NAME
     *
     * @mbggenerated
     */
    public String getActionName() {
        return actionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.ACTION_NAME
     *
     * @param actionName the value for MGW_QRCODE.ACTION_NAME
     *
     * @mbggenerated
     */
    public void setActionName(String actionName) {
        this.actionName = actionName == null ? null : actionName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.SCENE_ID
     *
     * @return the value of MGW_QRCODE.SCENE_ID
     *
     * @mbggenerated
     */
    public String getSceneId() {
        return sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.SCENE_ID
     *
     * @param sceneId the value for MGW_QRCODE.SCENE_ID
     *
     * @mbggenerated
     */
    public void setSceneId(String sceneId) {
        this.sceneId = sceneId == null ? null : sceneId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.CREATE_DATE
     *
     * @return the value of MGW_QRCODE.CREATE_DATE
     *
     * @mbggenerated
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.CREATE_DATE
     *
     * @param createDate the value for MGW_QRCODE.CREATE_DATE
     *
     * @mbggenerated
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.USAGE_TYPE
     *
     * @return the value of MGW_QRCODE.USAGE_TYPE
     *
     * @mbggenerated
     */
    public String getUsageType() {
        return usageType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.USAGE_TYPE
     *
     * @param usageType the value for MGW_QRCODE.USAGE_TYPE
     *
     * @mbggenerated
     */
    public void setUsageType(String usageType) {
        this.usageType = usageType == null ? null : usageType.trim();
    }

	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}

	public String getFromChannelAccount() {
		return fromChannelAccount;
	}

	public void setFromChannelAccount(String fromChannelAccount) {
		this.fromChannelAccount = fromChannelAccount;
	}
    
	
	
    
    
}