package cn.sh.ideal.mgw.service.local.impl;


import java.io.Serializable;
import java.util.List;

import javax.annotation.Resource;

import static cn.sh.ideal.mgw.base.Constants.PARAM_APPID;
import static cn.sh.ideal.mgw.base.Constants.PARAM_APP_SECRET;
import static cn.sh.ideal.mgw.base.Constants.PARAM_OPENID;
import static cn.sh.ideal.mgw.base.Constants.PARAM_PID;
import static cn.sh.ideal.mgw.base.Constants.PARAM_PLATFORM_ID;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;







import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.base.Platform;
//import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.service.local.MessageService;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.TokenUtil;
import cn.sh.ideal.model.ChannelConfig;

import com.alibaba.fastjson.JSONObject;
@Service("messageService")
public class MessageServiceImpl implements MessageService {
	private static final Logger log = Logger.getLogger(MessageServiceImpl.class);
	
	@Autowired
	private ChannelConfigDao channelConfigDao;

	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;
	
	@Override
	public String userInfo(String requestJson) throws Exception {


			// log request
			log.info(Thread.currentThread().getName() + " request MessageServiceImpl.userinfo[" + requestJson + "]");

			JSONObject requestObj = JSONObject.parseObject(requestJson);
			Platform platform = Platform.valueOf(requestObj.getString(PARAM_PID));
			String platformId = requestObj.getString(PARAM_PLATFORM_ID);
			String openid = requestObj.getString(PARAM_OPENID);

			ChannelConfig channelConfig = new ChannelConfig();
			channelConfig.setChannelAccount(platformId);
			channelConfig.setChannelCode(platform.getCode());

			List<ChannelConfig> channelConfigs = channelConfigDao.query(channelConfig);

			if (channelConfigs == null || channelConfigs.isEmpty()) {
				throw new NullPointerException("没有找到对应渠道或platformId不存在.");
			}

			JSONObject accountObj = JSONObject.parseObject(channelConfigs.get(0).getAccountConfig());

			String respStr = "";

			if (platform == Platform.ALIPAY) {
				/** alipay */

				throw new IllegalArgumentException("no supported alipay.");
			} else {
				/** wechat or yichat */

				String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, platform.getName() + ".USER_URL");

				String appid = accountObj.getString(PARAM_APPID);
				String secret = accountObj.getString(PARAM_APP_SECRET);

				if (StringUtils.isEmpty(appid))
					throw new NullPointerException("appid or secret is null.");

				String token = new TokenUtil(platform.getName(), appid, secret, redisDao).getToken();

				respStr = NetUtil.send(url + token + "&openid=" + openid, NetUtil.GET, "");

			}
			log.info(Thread.currentThread().getName() + " response MessageServiceImpl.userinfo[" + respStr + "]");
		
			return respStr;
	}
	
	public String weiboUserInfo(String requestJson) throws Exception {
		// log request
		log.info(Thread.currentThread().getName() + " request MessageServiceImpl.weiboUserInfo[" + requestJson + "]");

		JSONObject requestObj = JSONObject.parseObject(requestJson);
		Platform platform = Platform.valueOf(requestObj.getString(PARAM_PID));
		String platformId = requestObj.getString(PARAM_PLATFORM_ID);
//		String openid = requestObj.getString(PARAM_OPENID);

		ChannelConfig channelConfig = new ChannelConfig();
		channelConfig.setChannelAccount(platformId);
		channelConfig.setChannelCode(platform.getCode());

		List<ChannelConfig> channelConfigs = channelConfigDao.query(channelConfig);

		if (channelConfigs == null || channelConfigs.isEmpty()) {
			throw new NullPointerException("没有找到对应渠道或platformId不存在.");
		}

		JSONObject accountObj = JSONObject.parseObject(channelConfigs.get(0).getAccountConfig());
		
		return accountObj.getString("token");
	}

}
