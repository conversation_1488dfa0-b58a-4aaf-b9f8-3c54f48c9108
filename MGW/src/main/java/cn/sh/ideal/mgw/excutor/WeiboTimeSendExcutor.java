package cn.sh.ideal.mgw.excutor;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.thread.WeiboTimeSendThread;
import cn.sh.ideal.model.ChannelConfig;

/**
 * 微博定时发送
 * <AUTHOR>
 *
 */
@Component("weiboTimeSendExcutor")
public class WeiboTimeSendExcutor extends Thread implements ApplicationContextAware{

	private ApplicationContext applicationContext;
	@Autowired
	private ChannelConfigDao channelConfigDao;
	@Resource(name="weiboSendThreadPool")
	private ThreadPoolTaskExecutor threadPool;
	
	protected static final Logger log = Logger.getLogger(WeiboTimeSendExcutor.class);
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	/**
	 * 微博定时发送执行方法
	 */
	public void receive() {
		try {
			WeiboTimeSend();
		} catch (Exception e) {
			log.error("微博定时发送执行方法!", e);
		}
	}
	
	public void WeiboTimeSend(){
		threadPool.execute(new Runnable() {
			
			@Override
			public void run() {
				ThreadGroup tg = null;
				try {
					Thread.sleep(30000);
					tg = new ThreadGroup("weiboTime");
				} catch (InterruptedException e) {
					log.error("",e);
				}
				
				while(true){
					try {
						ChannelConfig channelConfig = new ChannelConfig();
						channelConfig.setChannelCode("1005");
						channelConfig.setChannelEnable("1");
						List<ChannelConfig> weiboConfigList = channelConfigDao.query(channelConfig);
						
						Thread[] threads = new Thread[tg.activeCount()];
						tg.enumerate(threads);
						
						for(ChannelConfig weiboConfig : weiboConfigList){
							JSONObject jsonAccountConfig  = JSONObject.parseObject(weiboConfig.getAccountConfig());
							jsonAccountConfig.put("tenantCode", weiboConfig.getTenantCode());
							boolean flag = false;
							
							String nameAccont = weiboConfig.getChannelAccount();
							//未授权微博直接跳出
							if(StringUtils.isEmpty(nameAccont)){
								break;
							}
							for(int i=0;threads != null && threads.length > i;i++){
								Thread thead = threads[i];
								if(thead.getName().equals(nameAccont)){
									flag = true;
									break;
								}
							}
							
							if(!flag){
								WeiboTimeSendThread wst = new WeiboTimeSendThread(jsonAccountConfig,applicationContext);
								Thread th = new Thread(tg,wst,nameAccont);
								th.start();
							}
							
						}
					} catch (Exception e) {
						log.error("微博定时发送执行器异常",e);
					}
					
				}
				
			}
		});
	}
}
