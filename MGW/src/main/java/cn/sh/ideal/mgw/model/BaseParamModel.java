package cn.sh.ideal.mgw.model;

import java.io.Serializable;

public class BaseParamModel implements Serializable{
	private String msgType;
	private String paramName;
	public String getMsgType() {
		return msgType;
	}
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public String getParamName() {
		return paramName;
	}
	public void setParamName(String paramName) {
		this.paramName = paramName;
	}
	@Override
	public String toString() {
		return "BaseParamModel [msgType=" + msgType + ", paramName="
				+ paramName + "]";
	}
	
	

}
