package cn.sh.ideal.mgw.model;

import java.io.Serializable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
 * @ClassName: BaseModel
 * @Description: TODO(Model基类)
 * <AUTHOR>
 * @date 2014-6-10 下午2:38:36
 * 
 */
public class BaseModel implements Serializable {
	protected static Logger log = LoggerFactory.getLogger(BaseModel.class);
	private static final long serialVersionUID = 1L;
	protected Integer startRow = 0;// 当前页面开始行
	protected Integer pageSize = 10;// 每页显示多少条
	protected Integer rowNum; // 总共多少条
	protected Integer pageNum = 1;// 当前页
	protected String orderFieldName;// 要排序的字段名
	protected String orderByRule;// 排序规则
	protected String letterIndex;// 字母索引
	//protected String beginTime;// 开始时间
	//protected String endTime;// 结束时间
	protected String tableName;// 表名
	protected String tableType;// 类型
	protected String totalNumber;

	public Integer getStartRow() {
		return startRow;
	}

	public void setStartRow(Integer startRow) {
		this.startRow = startRow;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getRowNum() {
		return rowNum;
	}

	public void setRowNum(Integer rowNum) {
		this.rowNum = rowNum;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getOrderFieldName() {
		return orderFieldName;
	}

	public void setOrderFieldName(String orderFieldName) {
		this.orderFieldName = orderFieldName;
	}

	public String getOrderByRule() {
		return orderByRule;
	}

	public void setOrderByRule(String orderByRule) {
		this.orderByRule = orderByRule;
	}

	public String getLetterIndex() {
		return letterIndex;
	}

	public void setLetterIndex(String letterIndex) {
		this.letterIndex = letterIndex;
	}

	/*public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
*/
	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getTableType() {
		return tableType;
	}

	public void setTableType(String tableType) {
		this.tableType = tableType;
	}

	public String getTotalNumber() {
		return totalNumber;
	}

	public void setTotalNumber(String totalNumber) {
		this.totalNumber = totalNumber;
	}

}
