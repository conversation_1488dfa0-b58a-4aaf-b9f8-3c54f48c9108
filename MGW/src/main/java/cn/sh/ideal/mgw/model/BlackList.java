package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class BlackList implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.AUTO_ID
     *
     * @mbggenerated
     */
    private BigDecimal autoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_USER
     *
     * @mbggenerated
     */
    private String blackUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_BEGIN_TIME
     *
     * @mbggenerated
     */
    private String blackBeginTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_END_TIME
     *
     * @mbggenerated
     */
    private String blackEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_CREATE_ID
     *
     * @mbggenerated
     */
    private String blackCreateId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_CREATE_DATE
     *
     * @mbggenerated
     */
    private Date blackCreateDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_UPDATE_ID
     *
     * @mbggenerated
     */
    private String blackUpdateId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_UPDATE_DATE
     *
     * @mbggenerated
     */
    private Date blackUpdateDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_DELETE_ID
     *
     * @mbggenerated
     */
    private String blackDeleteId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_DELETE_DATE
     *
     * @mbggenerated
     */
    private Date blackDeleteDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.STATUS
     *
     * @mbggenerated
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.CHANNEL_ID
     *
     * @mbggenerated
     */
    private String channelId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.TENANT_CODE
     *
     * @mbggenerated
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_END_DATE
     *
     * @mbggenerated
     */
    private Date blackEndDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACKLIST.BLACK_BEGIN_DATE
     *
     * @mbggenerated
     */
    private Date blackBeginDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table CMS_BLACKLIST
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.AUTO_ID
     *
     * @return the value of CMS_BLACKLIST.AUTO_ID
     *
     * @mbggenerated
     */
    public BigDecimal getAutoId() {
        return autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.AUTO_ID
     *
     * @param autoId the value for CMS_BLACKLIST.AUTO_ID
     *
     * @mbggenerated
     */
    public void setAutoId(BigDecimal autoId) {
        this.autoId = autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_USER
     *
     * @return the value of CMS_BLACKLIST.BLACK_USER
     *
     * @mbggenerated
     */
    public String getBlackUser() {
        return blackUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_USER
     *
     * @param blackUser the value for CMS_BLACKLIST.BLACK_USER
     *
     * @mbggenerated
     */
    public void setBlackUser(String blackUser) {
        this.blackUser = blackUser == null ? null : blackUser.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_BEGIN_TIME
     *
     * @return the value of CMS_BLACKLIST.BLACK_BEGIN_TIME
     *
     * @mbggenerated
     */
    public String getBlackBeginTime() {
        return blackBeginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_BEGIN_TIME
     *
     * @param blackBeginTime the value for CMS_BLACKLIST.BLACK_BEGIN_TIME
     *
     * @mbggenerated
     */
    public void setBlackBeginTime(String blackBeginTime) {
        this.blackBeginTime = blackBeginTime == null ? null : blackBeginTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_END_TIME
     *
     * @return the value of CMS_BLACKLIST.BLACK_END_TIME
     *
     * @mbggenerated
     */
    public String getBlackEndTime() {
        return blackEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_END_TIME
     *
     * @param blackEndTime the value for CMS_BLACKLIST.BLACK_END_TIME
     *
     * @mbggenerated
     */
    public void setBlackEndTime(String blackEndTime) {
        this.blackEndTime = blackEndTime == null ? null : blackEndTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_CREATE_ID
     *
     * @return the value of CMS_BLACKLIST.BLACK_CREATE_ID
     *
     * @mbggenerated
     */
    public String getBlackCreateId() {
        return blackCreateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_CREATE_ID
     *
     * @param blackCreateId the value for CMS_BLACKLIST.BLACK_CREATE_ID
     *
     * @mbggenerated
     */
    public void setBlackCreateId(String blackCreateId) {
        this.blackCreateId = blackCreateId == null ? null : blackCreateId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_CREATE_DATE
     *
     * @return the value of CMS_BLACKLIST.BLACK_CREATE_DATE
     *
     * @mbggenerated
     */
    public Date getBlackCreateDate() {
        return blackCreateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_CREATE_DATE
     *
     * @param blackCreateDate the value for CMS_BLACKLIST.BLACK_CREATE_DATE
     *
     * @mbggenerated
     */
    public void setBlackCreateDate(Date blackCreateDate) {
        this.blackCreateDate = blackCreateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_UPDATE_ID
     *
     * @return the value of CMS_BLACKLIST.BLACK_UPDATE_ID
     *
     * @mbggenerated
     */
    public String getBlackUpdateId() {
        return blackUpdateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_UPDATE_ID
     *
     * @param blackUpdateId the value for CMS_BLACKLIST.BLACK_UPDATE_ID
     *
     * @mbggenerated
     */
    public void setBlackUpdateId(String blackUpdateId) {
        this.blackUpdateId = blackUpdateId == null ? null : blackUpdateId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_UPDATE_DATE
     *
     * @return the value of CMS_BLACKLIST.BLACK_UPDATE_DATE
     *
     * @mbggenerated
     */
    public Date getBlackUpdateDate() {
        return blackUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_UPDATE_DATE
     *
     * @param blackUpdateDate the value for CMS_BLACKLIST.BLACK_UPDATE_DATE
     *
     * @mbggenerated
     */
    public void setBlackUpdateDate(Date blackUpdateDate) {
        this.blackUpdateDate = blackUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_DELETE_ID
     *
     * @return the value of CMS_BLACKLIST.BLACK_DELETE_ID
     *
     * @mbggenerated
     */
    public String getBlackDeleteId() {
        return blackDeleteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_DELETE_ID
     *
     * @param blackDeleteId the value for CMS_BLACKLIST.BLACK_DELETE_ID
     *
     * @mbggenerated
     */
    public void setBlackDeleteId(String blackDeleteId) {
        this.blackDeleteId = blackDeleteId == null ? null : blackDeleteId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_DELETE_DATE
     *
     * @return the value of CMS_BLACKLIST.BLACK_DELETE_DATE
     *
     * @mbggenerated
     */
    public Date getBlackDeleteDate() {
        return blackDeleteDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_DELETE_DATE
     *
     * @param blackDeleteDate the value for CMS_BLACKLIST.BLACK_DELETE_DATE
     *
     * @mbggenerated
     */
    public void setBlackDeleteDate(Date blackDeleteDate) {
        this.blackDeleteDate = blackDeleteDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.STATUS
     *
     * @return the value of CMS_BLACKLIST.STATUS
     *
     * @mbggenerated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.STATUS
     *
     * @param status the value for CMS_BLACKLIST.STATUS
     *
     * @mbggenerated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.CHANNEL_ID
     *
     * @return the value of CMS_BLACKLIST.CHANNEL_ID
     *
     * @mbggenerated
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.CHANNEL_ID
     *
     * @param channelId the value for CMS_BLACKLIST.CHANNEL_ID
     *
     * @mbggenerated
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.TENANT_CODE
     *
     * @return the value of CMS_BLACKLIST.TENANT_CODE
     *
     * @mbggenerated
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.TENANT_CODE
     *
     * @param tenantCode the value for CMS_BLACKLIST.TENANT_CODE
     *
     * @mbggenerated
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_END_DATE
     *
     * @return the value of CMS_BLACKLIST.BLACK_END_DATE
     *
     * @mbggenerated
     */
    public Date getBlackEndDate() {
        return blackEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_END_DATE
     *
     * @param blackEndDate the value for CMS_BLACKLIST.BLACK_END_DATE
     *
     * @mbggenerated
     */
    public void setBlackEndDate(Date blackEndDate) {
        this.blackEndDate = blackEndDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACKLIST.BLACK_BEGIN_DATE
     *
     * @return the value of CMS_BLACKLIST.BLACK_BEGIN_DATE
     *
     * @mbggenerated
     */
    public Date getBlackBeginDate() {
        return blackBeginDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACKLIST.BLACK_BEGIN_DATE
     *
     * @param blackBeginDate the value for CMS_BLACKLIST.BLACK_BEGIN_DATE
     *
     * @mbggenerated
     */
    public void setBlackBeginDate(Date blackBeginDate) {
        this.blackBeginDate = blackBeginDate;
    }
}