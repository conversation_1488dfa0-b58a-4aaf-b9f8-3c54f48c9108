package cn.sh.ideal.mgw.service.dubbo;

import static cn.sh.ideal.mgw.base.Constants.PARAM_RESULT_MSG;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import cn.sh.ideal.mgw.thread.WeiboReceiveThread;
import cn.sh.ideal.mgw.utils.NetUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.Platform;
import cn.sh.ideal.mgw.base.WeiboInf;
import cn.sh.ideal.mgw.dao.*;
import cn.sh.ideal.mgw.model.StatusCount;
import cn.sh.ideal.mgw.model.UserCounts;
import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.mgw.model.req.WeiboChannelRequest;
import cn.sh.ideal.mgw.model.response.WeiboChannelResponse;
import cn.sh.ideal.mgw.model.response.WeiboSendResponse;
import cn.sh.ideal.mgw.service.WeiboCommentsSumService;
import cn.sh.ideal.mgw.service.WeiboMessageService;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.sender.WeiboSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.MessageInfoSend;
import cn.sh.ideal.model.WeiboComentsSum;
import cn.sh.ideal.util.BaseController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;

import weibo4j.http.HttpClient;
import weibo4j.http.ImageItem;
import weibo4j.model.Comment;
import weibo4j.model.PostParameter;
import weibo4j.model.Status;
import weibo4j.model.StatusWapper;
import weibo4j.model.WeiboException;
import weibo4j.org.json.JSONArray;
import weibo4j.util.WeiboConfig;

import javax.annotation.Resource;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import static cn.sh.ideal.mgw.base.Constants.PARAM_RESULT_MSG;

@Component("weiboMessageService")
public class WeiboMessageServiceImpl extends BaseController implements WeiboMessageService{

	private static final Logger log = Logger.getLogger(WeiboMessageServiceImpl.class);

	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	private static HttpClient client = new HttpClient();
	
	@Autowired
	private WeiboCommentsSumService weiboCommentsSumService;

	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;

	@Autowired
	private WeiboCommentsDao weiboCommentsDao;
	
	@Autowired
	private MessageInfoSendDao messageSendDao;
	@Autowired
	private MessageInfoDao messageInfoDao;
	@Autowired
	private WeiboTimeDao weiboTimeDao;

	/**
	 * 新点对点发送接口
	 * 
	 * @param message
	 * @return
	 */
	@ResponseBody
	public WeiboSendResponse weibonewSend(MessageInfoSend message) {
		JSONObject resultObj = super.getSuccessJsonObj();
		JSONObject json=new JSONObject();
		WeiboSendResponse weiboResponse = new WeiboSendResponse();
		try {
			log.info(Thread.currentThread().getName() + " weibonewSend [" + JSONObject.toJSONString(message) +"]");
			Platform platform = Platform.codeOf(message.getChannelCode());
			switch (platform) {
				case SINAMB:
					//1.验证token
					
					String accessToken = (String)redisDao.readValue("weibo2_accesstoken_" + message.getSendAccount());
					log.info("get weibo accessToken from redis:"+accessToken);
					if(StringUtils.isEmpty(accessToken)){
						throw new RuntimeException("can not find weibo accessToken, sendAccount:"+ message.getSendAccount());
					}else{
						//定时任务判断
						if(isTimeTask(message)){
							weiboTimeDao.insert(message);
							log.info(Thread.currentThread().getName() + " weiboTime [微博消息已存入库，等待发送]");
							weiboResponse.setResultCode(Constants.SUCCESS_CODE);
							weiboResponse.setResultMsg("定时消息已经缓存！");
						}else{

						//获取content 解析请求类型
						Object  mObject= message2Media(message);
						

						resultObj = customSend(JSON.toJSONString(mObject), accessToken);
	
						String msgInfo = (String) resultObj.get(Constants.WB_MSG_INFO);
				
						// 请求参数格式有误
						if (null != msgInfo && !"".equals(msgInfo)) {
							resultObj = super.getErrorJsonObj(msgInfo);
							message.setStatus("-1");//参数请求格式有误
							weiboResponse.setResultCode(Constants.ERROR_CODE);
							weiboResponse.setResultMsg(msgInfo);
						} else {
							//请求参数格式无误的情况,并且调用微博API没有发送异常
							
							String resWbId = resultObj.getString(Constants.WB_RES_WEIBOID);
							String resComId = resultObj.getString(Constants.WB_RES_COMID);
							String resReplyId = resultObj.getString(Constants.WB_RES_REPLAYID);
							List<UserCounts> resUserCount = (List<UserCounts>)resultObj.get(Constants.WB_RES_USERCOUNT);
							List<StatusCount> resStatusCount = (List<StatusCount>)resultObj.get(Constants.WB_RES_STATUSCOUNT);
							
							String resError = resultObj.getString(Constants.WB_RES_ERROR);
							//调用微博API发生异常
							if(resError!=null && !"".equals(resError)){
								resultObj = super.getErrorJsonObj(resError);
								message.setStatus("-1");
								weiboResponse.setResultCode(Constants.ERROR_CODE);
								weiboResponse.setResultMsg(resError);
							}else {
								
								WeiboInf weiBoType=WeiboInf.codeOf(message.getType());
								switch (weiBoType) {
								//发布一条新微博
								case WB_UPDATESTATUS:
									if(resWbId!=null&& !"".equals(resWbId)){
										message.setStatus("0");
										message.setWeiboId(resWbId);
										resultObj = super.getSuccessJsonObj();
										resultObj.remove(PARAM_RESULT_MSG);
										resultObj.put(PARAM_RESULT_MSG, resWbId);
										
										json.put("resultCode",0);
										json.put("resultMsg", "resWbId");
										
										weiboResponse.setResultCode(Constants.SUCCESS_CODE);
										weiboResponse.setResultMsg("success");
									}else{
										message.setStatus("-1");
										resultObj.put(PARAM_RESULT_MSG, resWbId);
										resultObj.put("resultCode", -1);
										resultObj.put("resultMsg", "发布微博失败！");
										resultObj = super.getErrorJsonObj("发布微博失败！");
										json.put("resultCode","-1");
										json.put("resultMsg", "发布微博失败！");
										weiboResponse.setResultCode(Constants.ERROR_CODE);
										weiboResponse.setResultMsg("发布微博失败！");
									}
									break;
										//对一条微博进行评论
								case WB_CREATRCOMMENT:
									if(resComId!=null&& !"".equals(resComId)){
										message.setStatus("0");
										message.setCommentId(resComId);
										message.setWeiboId(resWbId);
										resultObj = super.getSuccessJsonObj();
										resultObj.remove(PARAM_RESULT_MSG);
										resultObj.put(PARAM_RESULT_MSG, resComId);
										json.put("resultCode",0);
										json.put("resultMsg", "resWbId");
										weiboResponse.setResultCode(Constants.SUCCESS_CODE);
										weiboResponse.setResultMsg("success");
									}else{
										json.put("resultCode","-1");
										json.put("resultMsg", "评论微博失败！");
										resultObj = super.getErrorJsonObj("评论微博失败！");
									}
									break;
								//回复一条评论
								case WB_REPLAYCOMMENT:
									if(resReplyId!=null&& !"".equals(resReplyId)){
										message.setStatus("0");
										message.setReplyId(resReplyId);
										message.setCommentId(resComId);
										message.setWeiboId(resWbId);
										resultObj = super.getSuccessJsonObj();
										resultObj.remove(PARAM_RESULT_MSG);
										resultObj.put(PARAM_RESULT_MSG, resReplyId);
										
										weiboResponse.setResultCode(Constants.SUCCESS_CODE);
										weiboResponse.setResultMsg("success");
									}else{
										message.setStatus("-1");
										resultObj = super.getErrorJsonObj("回复评论失败！");
									}
									break;
								//根据微博ID删除指定微博
									/*case WB_DESTROY:
									if(resWbId!=null&& !"".equals(resWbId)){
										message.setStatus("0");
										message.setWeiboId(resWbId);
										
										resultObj = super.getSuccessJsonObj();
										resultObj.remove(PARAM_RESULT_MSG);
										resultObj.put(PARAM_RESULT_MSG, resWbId);
									}else{
										message.setStatus("-1");
										resultObj = super.getErrorJsonObj("删除微博失败！");
									}
									break;
								//批量获取用户的粉丝数、关注数、微博数 
								case WB_GETUSERCOUNT:
									if(resUserCount!=null && resUserCount.size()!=0){
										message.setStatus("0");
										
										net.sf.json.JSONArray jsonArr = net.sf.json.JSONArray.fromObject(resUserCount);
										message.setResult(jsonArr.toString());
										resultObj = super.getSuccessJsonObj();
										resultObj.remove(PARAM_RESULT_MSG);
										resultObj.put(PARAM_RESULT_MSG, jsonArr.toString());
									}else{
										message.setStatus("-1");
										resultObj = super.getErrorJsonObj("获取用户粉丝数、关注数、微博数失败！");
									}
									break;
									//微博评论数，转发数统计接口
								case WB_GETSTATUSCOUNT:
									if(resStatusCount!=null && resStatusCount.size()!=0){
										message.setStatus("0");
										net.sf.json.JSONArray jsonArr = net.sf.json.JSONArray.fromObject(resStatusCount);
										message.setResult(jsonArr.toString());
										resultObj = super.getSuccessJsonObj();
										resultObj.remove(PARAM_RESULT_MSG);
										resultObj.put(PARAM_RESULT_MSG, jsonArr.toString());
									}else{
										message.setStatus("-1");
										resultObj = super.getErrorJsonObj("获取微博评论数，转发数统计接口失败！");
									}
								break;
									*/
									
									
								/*default:
									resultObj = super.getErrorJsonObj("opertype is not match");
									log.error("opertype is not match");
									throw new IllegalArgumentException("opertype is not match"); */
								}
								
								
								this.messageSendDao.insert(message);

							if(weiBoType.equals(WeiboInf.WB_REPLAYCOMMENT)){
								//插入消息表
								MessageInfo messageInfo = new MessageInfo();
								messageInfo.setSendAccount(message.getSendAccount());
								messageInfo.setChannelCode(message.getChannelCode());
								messageInfo.setContent(message.getContent());
								log.info("content的值为：-----"+message.getContent());
								messageInfo.setMessageSource("2");
								messageInfo.setMsgType(message.getMsgType());
								messageInfo.setNickname(message.getNickname());
								messageInfo.setWorkNo(message.getWorkNo());
								messageInfo.setSendTime(new Date());
								messageInfo.setSessionId(message.getSessionId());
								messageInfo.setSource("2");
								messageInfo.setTenantCode(message.getTenantCode());
								messageInfoDao.insert(messageInfo);
								}
							}
						}
						}
			}
					break;
				default:
					weiboResponse.setResultCode(Constants.ERROR_CODE);
					weiboResponse.setResultMsg("ChannelCode is not match");
					weiboResponse.setData(message);
					log.error("ChannelCode is not match");
					return weiboResponse;
				}

			weiboResponse.setData(message);
		} catch (Exception e) {
			log.error("发布微博失败:", e);
			weiboResponse.setData(message);
			weiboResponse.setResultCode(Constants.ERROR_CODE);
			weiboResponse.setResultMsg("发布微博失败:"+ e.getMessage());
		}
		return weiboResponse;
	}

	/**
	 * 定时
	 * @param message
	 * @return
	 * @throws
	 */
	public boolean isTimeTask(MessageInfoSend message){
		JSONObject jsonContent = JSONObject.parseObject(message.getContent());
		 String operType = jsonContent.getString("operType");
		 WeiboInf weiBoType=WeiboInf.codeOf(operType);
		return WeiboInf.WB_UPDATESTATUS.equals(weiBoType) && "1".equals(message.getIsTime());
	}


	public static byte[] readFileImage(String filename) throws IOException {
		//String[] imgList=filename.split(",");
		//byte[] totalByte=new byte[0];
		//for(int i=0;i<imgList.length;i++){
			//读取ftp地址
//			filename=PropertiesUtil.getProperty(PropertiesUtil.WEIBO, "FTP_URL")+filename;
			log.info("发送微博图片地址--："+filename);
			//new一个URL对象  
	        URL url = new URL(filename);  
	        
	        //打开链接  
	        HttpURLConnection conn = (HttpURLConnection)url.openConnection();  
	        //设置请求方式为"GET"  
	      //  conn.setRequestMethod("GET");  
	        //超时响应时间为5秒  
	        conn.setConnectTimeout(5 * 1000);  
	        //通过输入流获取图片数据  
	        InputStream inStream = conn.getInputStream();  
	        BufferedInputStream bufferedInputStream = new BufferedInputStream(inStream);
//			int len = bufferedInputStream.available();
//			byte[] bytes = new byte[len];
//			int r = bufferedInputStream.read(bytes);
//			if (len != r) {
//				bytes = null;
//				throw new IOException("读取文件不正确");
//			}
//			inStream.close();
//			bufferedInputStream.close();
	        byte[] bytes = new byte[1024];
	        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
	        int len = 0;
	        while((len = bufferedInputStream.read(bytes)) != -1){
	        	outStream.write(bytes, 0, len);
	        }
	        inStream.close();

		return outStream.toByteArray();
	}
	 public static byte[] byteMerger(byte[] byte_1, byte[] byte_2){  
	        byte[] byte_3 = new byte[byte_1.length+byte_2.length];  
	        System.arraycopy(byte_1, 0, byte_3, 0, byte_1.length);  
	        System.arraycopy(byte_2, 0, byte_3, byte_1.length, byte_2.length);  
	        return byte_3;  
	    }  
	
	/**
	 * 发送消息
	 * @param reqStr
	 * @param token
	 * @return
	 */
	public  JSONObject customSend(String reqStr,String token){
		JSONObject obj = JSON.parseObject(reqStr);
		JSONObject resultObj=new JSONObject();
		//获取微博请求接口
		String type=(String)obj.get(Constants.MSG_PARAM_OPERTYPE);
		WeiboInf reqType=WeiboInf.codeOf(type);
		Status wbStatus=null;
		switch (reqType) {
		//发布一条新微博
		case WB_UPDATESTATUS:
			try {
				String status=obj.getString(Constants.WB_PARAM_STATUS);//要发布的微博文本内容
				if(status==null || "".equals(status)){
					resultObj.put(Constants.WB_MSG_INFO, "微博内容不能为空！");
				}else if(status.length()>140){
					resultObj.put(Constants.WB_MSG_INFO, "微博内容不能超过140个汉字！");
				}else{
					//String status = java.net.URLEncoder.encode(s, "utf-8");
					int visible=0;//微博的可见性，0：所有人能看，1：仅自己可见，2：密友可见，3：指定分组可见，默认为0。
					
					String visibleStr=obj.getString(Constants.WB_PARAM_VISIBLE);
					if(visibleStr!=null&&!"".equals(visibleStr)){
						visible=Integer.parseInt(visibleStr);
					}
					String listId=obj.getString(Constants.WB_PARAM_LISTID);//微博的保护投递指定分组ID，只有当visible参数为3时生效且必选。 
					if(visible==3){
						if(listId==null||"".equals(listId)){
							resultObj.put(Constants.WB_MSG_INFO, "请指定分组！");
							break;
						}
					}else{
						listId="";
					}
					Float lat=new Float(0.0);
					Float longs=new Float(0.0);
					String latStr=obj.getString(Constants.WB_PARAM_LAT);
					if(latStr!=null&&!"".equals(latStr)){
						lat=Float.parseFloat(latStr);//纬度
					}
					
					String longStr=obj.getString(Constants.WB_PARAM_LONGS);
					if(longStr!=null&&!"".equals(longStr)){
						longs=Float.parseFloat(longStr);//经度
					}
					
					String annotations=obj.getString(Constants.WB_PARAM_ANNOTATIONS);//元数据
					String pic=obj.getString(Constants.WB_PARAM_PIC);//图片信息
					
					//解析pic字段，单图或是多图
					// TODO
					if(StringUtils.isNotEmpty(pic)){
						String[] pics = pic.split(";");
						if(pics.length > 1){
							//TODO 多图处理
							resultObj.put(Constants.WB_MSG_INFO,"发布多图微博失败");
						}else if(pics.length == 1){
							//单图处理
							byte[] content = readFileImage(pic);
							log.info("发布微博上传的图片内容长度:" + content.length);
							ImageItem item = new ImageItem("pic", content);
							wbStatus=WeiboSender.uploadStatus(status, item, lat, longs,token,listId, visible,annotations);
						}
						
					}else{
						wbStatus=WeiboSender.updateStatus(status,lat, longs,listId,visible,annotations,token);
					}
						resultObj.put(Constants.WB_RES_WEIBOID, wbStatus!=null?wbStatus.getId():"");
					}
			} catch (Exception e) {
				log.error("发布微博失败：", e);
				resultObj.put(Constants.WB_RES_ERROR, "FAILE");
				resultObj.put("resultCode", "-1");
				resultObj.put(Constants.WB_MSG_INFO,e.getMessage());
			}
			break;
		//对一条微博进行评论
		case WB_CREATRCOMMENT:
			String id=obj.getString(Constants.WB_PARAM_ID);//需要评论的微博ID。
			if(id==null || "".equals(id)){
				resultObj.put(Constants.WB_MSG_INFO, "微博id不能为空！");
				break;
			}
			String comment=obj.getString(Constants.WB_PARAM_COMMENT);//评论内容
			if(comment==null || "".equals(comment)){
				resultObj.put(Constants.WB_MSG_INFO, "评论内容不能为空！");
				break;
			}else if(comment.length()>140){
				resultObj.put(Constants.WB_MSG_INFO, "评论内容不能超过140个汉字！");
				break;
			}
			String commentOriStr=obj.getString(Constants.WB_PARAM_COMMENT_ORI);
			Integer comment_ori=new Integer(0);
			if(commentOriStr!=null && !"".equals(commentOriStr)){
				comment_ori=Integer.parseInt(commentOriStr);//当评论转发微博时，是否评论给原微博，0：否、1：是，默认为0。 
			}
			try {
				Comment com=WeiboSender.createComment(comment, id, comment_ori, token);
				resultObj.put(Constants.WB_RES_COMID, com!=null?com.getIdstr():"");
				resultObj.put(Constants.WB_RES_WEIBOID, id);
			} catch (WeiboException e) {
				resultObj.put(Constants.WB_RES_ERROR, "FAILE");
				log.error("微博评论失败", e);
				throw new RuntimeException(e.getMessage());
			}
			break;
		//回复一条评论
		case WB_REPLAYCOMMENT:
			String cid=obj.getString(Constants.WB_PARAM_CID);//需要回复的评论ID。
			if(cid==null || "".equals(cid)){
				resultObj.put(Constants.WB_MSG_INFO, "评论id不能为空！");
				break;
			}
			String wbId=obj.getString(Constants.WB_PARAM_ID);//需要评论的微博ID。
			if(wbId==null || "".equals(wbId)){
				resultObj.put(Constants.WB_MSG_INFO, "微博id不能为空！");
				break;
			}
			String reComment=obj.getString(Constants.WB_PARAM_COMMENT);//回复评论内容 
			if(reComment==null || "".equals(reComment)){
				resultObj.put(Constants.WB_MSG_INFO, "回复内容不能为空！");
				break;
			}else if(reComment.length()>140){
				resultObj.put(Constants.WB_MSG_INFO, "回复内容不能超过140个汉字！");
				break;
			}
			String withoutMentionStr=obj.getString(Constants.WB_PARAM_WITHOUTMENTION);
			Integer withoutMention=new Integer(0);
			if(withoutMentionStr!=null && !"".equals(withoutMentionStr)){
				withoutMention =Integer.parseInt(withoutMentionStr);
			}
			Integer commentOri=new Integer(0);
			String comOriStr=obj.getString(Constants.WB_PARAM_COMMENT_ORI);
			if(comOriStr!=null && !"".equals(comOriStr)){
				commentOri=Integer.parseInt(comOriStr);//当评论转发微博时，是否评论给原微博，0：否、1：是，默认为0。 
			}
			try {
				Comment reCom=WeiboSender.replyComment(cid, wbId, reComment, withoutMention, commentOri, token);
				resultObj.put(Constants.WB_RES_REPLAYID, reCom!=null?reCom.getIdstr():"");
				resultObj.put(Constants.WB_RES_COMID, cid);
				resultObj.put(Constants.WB_RES_WEIBOID, wbId);
			} catch (WeiboException e) {
				resultObj.put(Constants.WB_RES_ERROR, "FAILE");
				log.error("微博回复评论失败", e);
				throw new RuntimeException(e.getMessage());
			}
			break;
			
		//根据微博ID删除指定微博
		case WB_DESTROY:
			String desId=obj.getString(Constants.WB_PARAM_ID);//需要删除的微博ID。
			if(desId==null || "".equals(desId)){
				resultObj.put(Constants.WB_MSG_INFO, "微博id不能为空！");
				break;
			}
			try {
				Status status =WeiboSender.destroy(desId, token);
				resultObj.put(Constants.WB_RES_WEIBOID, status!=null?status.getId():"");
			} catch (WeiboException e) {
				resultObj.put(Constants.WB_RES_ERROR, "FAILE");
				log.error("删除微博失败", e);
				throw new RuntimeException(e.getMessage());
			}
			break;
			//批量获取用户的粉丝数、关注数、微博数
		case WB_GETUSERCOUNT:
			String uids=obj.getString(Constants.WB_PARAM_UIDS);//需要获取数据的用户UID，多个之间用逗号分隔，最多不超过100个。
			String[] str=(uids!=null && !"".equals(uids))?uids.split(","):null;
			if(str.length>100){
				resultObj.put(Constants.WB_MSG_INFO, "用户UID"+Constants.WB_MSG_EXTCOUNT);
				break;
			}
			try {
				List<UserCounts> usList=WeiboSender.getUserCount(uids, token);
				resultObj.put(Constants.WB_RES_USERCOUNT, usList);
				
			} catch (Exception e) {
				resultObj.put(Constants.WB_RES_ERROR, "FAILE");
				log.error("获取用户信息失败", e);
				throw new RuntimeException(e.getMessage());
			}
			
			break;
		//批量获取指定微博的转发数评论数
		case WB_GETSTATUSCOUNT:
			String ids=obj.getString(Constants.WB_PARAM_IDS);//需要获取数据的微博ID，多个之间用逗号分隔，最多不超过100个。
			String[] wbIds=(ids!=null && !"".equals(ids))?ids.split(","):null;
			if(wbIds.length>100){
				resultObj.put(Constants.WB_MSG_INFO, "微博ID"+Constants.WB_MSG_EXTCOUNT);
				break;
			}
			try {
				JSONArray wbList=WeiboSender.getStatusesCount(ids, token);
				List<StatusCount> list=new ArrayList<StatusCount>();
				for(int i=0;i<wbList.length();i++){
					list.add(new StatusCount(wbList.getJSONObject(i)));
				}
				resultObj.put(Constants.WB_RES_STATUSCOUNT, list);
			} catch (Exception e) {
				resultObj.put(Constants.WB_RES_ERROR, "FAILE");
				log.error("获取微博信息失败", e);
				throw new RuntimeException(e.getMessage());
			}
			break;
		/*
		
		//获取微博官方表情的详细信息
		case WB_GETEMOTIONS:
			String emoType=obj.getString(Constants.WB_PARAM_TYPE);//表情类别，face：普通表情、ani：魔法表情、cartoon：动漫表情，默认为face。
			String language=obj.getString(Constants.WB_PARAM_LANGUAGE);//语言类别，cnname：简体、twname：繁体，默认为cnname。 
			if(null==emoType|| "".equals(emoType)){
				emoType="face";
			}
			if(null == language || "".equals(language)){
				language="cnname";
			}
			try {
				List<Emotion> emList=WeiboSender.getEmotions(emoType, language, token);
				resultObj.put(Constants.WB_RESULT, emList);
			} catch (Exception e) {
				log.error("获取微博官方表情信息失败", e);
				throw new RuntimeException(e.getMessage());
			}
			break;*/
		default:
			log.error("not supported msgtype");
			throw new IllegalArgumentException("not supported msgtype");
		}
		return resultObj;
		
	}
	
	
	public JSONObject message2Media(MessageInfoSend message) {
		
		JSONObject resJson = new JSONObject();
		
		JSONObject content=(JSONObject)BaseController.getJsonObj(message.getContent());
		JSONObject param=new JSONObject();
		//微博接口请求类型
		String  operType=(String)content.get(Constants.MSG_PARAM_OPERTYPE);
		WeiboInf weiBoType=WeiboInf.codeOf(operType);
		param=JSON.parseObject(content.get(Constants.WB_MSG_PARAM).toString());
		switch (weiBoType) {
		//发布一条新微博
		case WB_UPDATESTATUS:
			resJson.put(Constants.WB_PARAM_STATUS,param.get(Constants.WB_PARAM_STATUS));
			resJson.put(Constants.WB_PARAM_VISIBLE,param.get(Constants.WB_PARAM_VISIBLE));
			resJson.put(Constants.WB_PARAM_LISTID,param.get(Constants.WB_PARAM_LISTID));
			resJson.put(Constants.WB_PARAM_LAT, param.get(Constants.WB_PARAM_LAT));
			resJson.put(Constants.WB_PARAM_LONGS, param.get(Constants.WB_PARAM_LONGS));
			resJson.put(Constants.WB_PARAM_PIC, param.get(Constants.WB_PARAM_PIC));
			resJson.put(Constants.WB_PARAM_ANNOTATIONS,param.get(Constants.WB_PARAM_ANNOTATIONS));
			resJson.put(Constants.WB_PARAM_RIP,param.get(Constants.WB_PARAM_RIP));
			message.setType("1");
			break;
		//对一条微博进行评论
		case WB_CREATRCOMMENT:
			resJson.put(Constants.WB_PARAM_COMMENT,param.get(Constants.WB_PARAM_COMMENT));
			resJson.put(Constants.WB_PARAM_ID,param.get(Constants.WB_PARAM_ID));
			resJson.put(Constants.WB_PARAM_COMMENT_ORI,param.get(Constants.WB_PARAM_COMMENT_ORI));
			resJson.put(Constants.WB_PARAM_RIP, param.get(Constants.WB_PARAM_RIP));
			message.setType("2");
			break;
		//回复一条评论
		case WB_REPLAYCOMMENT:
			resJson.put(Constants.WB_PARAM_CID,param.get(Constants.WB_PARAM_CID));
			resJson.put(Constants.WB_PARAM_ID,param.get(Constants.WB_PARAM_ID));
			resJson.put(Constants.WB_PARAM_COMMENT,param.get(Constants.WB_PARAM_COMMENT));
			resJson.put(Constants.WB_PARAM_WITHOUTMENTION,param.get(Constants.WB_PARAM_WITHOUTMENTION));
			resJson.put(Constants.WB_PARAM_COMMENT_ORI,param.get(Constants.WB_PARAM_COMMENT_ORI));
			resJson.put(Constants.WB_PARAM_RIP, param.get(Constants.WB_PARAM_RIP));
			resJson.put(Constants.PARAM_NICKNAME, message.getNickname());
			message.setType("3");
			break;
		//根据微博ID删除指定微博
		case WB_DESTROY:
			resJson.put(Constants.WB_PARAM_ID,param.get(Constants.WB_PARAM_ID));
			message.setType("4");
			break;
			//批量获取用户的粉丝数、关注数、微博数
		case WB_GETUSERCOUNT:
			resJson.put(Constants.WB_PARAM_UIDS,param.get(Constants.WB_PARAM_UIDS));
			message.setType("6");
			break;
		case WB_GETSTATUSCOUNT:
			resJson.put(Constants.WB_PARAM_IDS,param.get(Constants.WB_PARAM_IDS));
			message.setType("5");
			break;
	/*	//批量获取指定微博的转发数评论数
		
		
		//获取微博官方表情的详细信息
		case WB_GETEMOTIONS:
			resJson.put(Constants.WB_PARAM_TYPE,param.get(Constants.WB_PARAM_TYPE));
			resJson.put(Constants.WB_PARAM_LANGUAGE,param.get(Constants.WB_PARAM_LANGUAGE));
			break;*/
		default:
			log.error("暂不支持该接口类型");
			throw new RuntimeException("暂不支持该接口类型");
		}
		resJson.put(Constants.MSG_PARAM_OPERTYPE, operType);
		return resJson;
	}


	
	
	/**
	 * 分页查询历史已发微博数据
	 */
	public PageInfo<WeiboMsgInfo> historyInfo(int pageNum, int pageSize,MessageInfoSend messageInfoSend){
		List<WeiboMsgInfo> weiboMsgInfoList = null;
		PageInfo<WeiboMsgInfo> page = null;
		try{
			
			PageHelper.startPage(pageNum, pageSize);			
			weiboMsgInfoList = messageSendDao.historyInfo(messageInfoSend);
			page = new PageInfo<WeiboMsgInfo>(weiboMsgInfoList);		
				
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			
			ChannelConfig channelConfig = new ChannelConfig();
			channelConfig.setChannelCode("1005");
			channelConfig.setChannelEnable("1");

			String account = null;
			List<ChannelConfig> mailList = channelConfigDao.query(channelConfig);
			if(mailList != null && mailList.size() > 0){
				ChannelConfig info = mailList.get(0);
				JSONObject infoJson = JSON.parseObject(info.getAccountConfig());
				account =  info.getChannelAccount();
			}			
			
			
			if(page.getList() != null & page.getList().size() > 0){
				for(WeiboMsgInfo weiboMsgInfo : page.getList()){
//					JSONObject jsonReq = JSONObject.parseObject(weiboMsgInfo.getContent());
//					String paramStr = jsonReq.getString("param");
//					JSONObject contentReq = JSONObject.parseObject(paramStr);
//					weiboMsgInfo.setContent(contentReq.getString("status"));
					weiboMsgInfo.setCreateTime(sdf.format(sdf.parse(weiboMsgInfo.getCreateTime())));
					
					WeiboComentsSum weiboComentsSumQuery = new WeiboComentsSum();
					weiboComentsSumQuery.setId(weiboMsgInfo.getWeiboId());
//					if(StringUtils.isNoneBlank(account)){
//						String accessToken = (String)redisDao.readValue("weibo2_accesstoken_" + account);
//						weiboComentsSumQuery.setAccessTokens(accessToken);
//					}						
						
					List<WeiboComentsSum> weiboComentsList = weiboCommentsDao.query(weiboComentsSumQuery);
							
					if(weiboComentsList != null && weiboComentsList.size() == 1){
						WeiboComentsSum weiboComentsSum = weiboComentsList.get(0);
						weiboMsgInfo.setComments(weiboComentsSum.getComments());
						weiboMsgInfo.setAttitudes(weiboComentsSum.getAttitudes());
						weiboMsgInfo.setReposts(weiboComentsSum.getReposts());
					}		
				}
			}			
			return page;
		}catch(Exception ex){
			log.error("WeiboMessageServiceImpl_historyInfo_error:",ex);
			return null;
		}
	}

    /**
     * 全网搜索功能
     * @param pageNum
     * @param pageSize
     * @param messageInfoSend
     * @return
     */
	public  JSONObject searchLimited(int pageNum, int pageSize,MessageInfoSend messageInfoSend) throws UnsupportedEncodingException {
        com.alibaba.fastjson.JSONArray weiboMsgInfoList =new com.alibaba.fastjson.JSONArray();
        JSONObject page = new JSONObject();
        com.alibaba.fastjson.JSONArray  statuses=null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sd2 = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
		log.info(messageInfoSend.getStartTime());
		log.info(messageInfoSend.getEndTime());
		// String accessToken = (String)redisDao.readValue("weibo2_accesstoken_" + messageInfoSend.getAcceptAccount());
		String accessToken = "2.00p7ClaCbkPrcB55a324bff2wbxMPB";
        StringBuffer url=new StringBuffer("https://c.api.weibo.com/2/search/statuses/limited.json?");
        url.append("access_token="+accessToken);
        url.append("&q="+ URLEncoder.encode(messageInfoSend.getContent(),"utf-8"));
		if(messageInfoSend.getStartTime()!=null) {

            url.append("&starttime=" + messageInfoSend.getStartTime().getTime()/1000);
		}
		if(messageInfoSend.getEndTime()!=null) {
			url.append("&endtime=" + messageInfoSend.getEndTime().getTime()/1000);
		}
        url.append("&page="+pageNum);
        url.append("&count="+pageSize);
        String result=NetUtil.send(url.toString(),"GET","");
		JSONObject resultJson =JSONObject.parseObject(result);
		if (resultJson.containsKey("statuses")) {
			statuses = resultJson.getJSONArray("statuses");
		}
		int size = statuses.size();
		List<Status> status = new ArrayList<Status>(size);
		JSONObject weiboMsgInfoJson=null;
		com.alibaba.fastjson.JSONArray listArray=new com.alibaba.fastjson.JSONArray();
		for (int i = 0; i < size; i++) {
			try {
				JSONObject statusJson=statuses.getJSONObject(i);
				weiboMsgInfoJson=new JSONObject();
				weiboMsgInfoJson.put("weiboId",statusJson.getLong("id"));
				weiboMsgInfoJson.put("acceptedAccount",statusJson.getJSONObject("user").getLong("id"));

				JSONObject content=new JSONObject();
				JSONObject param=new JSONObject();
				param.put("status",statusJson.getString("text"));
				param.put("pic",statusJson.getString("thumbnail_pic"));
				param.put("pic_ids",statusJson.getJSONArray("pic_ids"));
				content.put("param",param);
				weiboMsgInfoJson.put("sendAccount",messageInfoSend.getAcceptAccount());
				weiboMsgInfoJson.put("content",content);
				weiboMsgInfoJson.put("createTime",sdf.format(sd2.parse(statusJson.getString("created_at"))));
                weiboMsgInfoJson.put("source", statusJson.getString("source"));
                weiboMsgInfoJson.put("user", statusJson.getJSONObject("user"));
                weiboMsgInfoJson.put("reposts",statusJson.getInteger("reposts_count"));
				weiboMsgInfoJson.put("comments",statusJson.getInteger("comments_count"));
				weiboMsgInfoJson.put("attitudes",statusJson.getInteger("attitudes_count"));
				listArray.add(weiboMsgInfoJson);
			} catch (Exception e) {
				log.error(e.getMessage(),e);
			}
		}
        page.put("list",listArray);
        page.put("total",resultJson.getLong("total_number"));




        return page;
	}

    /**
     * 根据查询全网搜索的字符串获取pagelist对象
     *
     * @param resultString
     * @return
     */
    public PageInfo<WeiboMsgInfo> getSearchLimitedWeiboPage(String resultString){
        PageInfo<WeiboMsgInfo> page = null;

        JSONObject request =JSONObject.parseObject(resultString);

        request.getLong("total_number");
        return page;
    }



	/**
	 * 查询微博渠道账号
	 */
	@Override
	public WeiboChannelResponse queryWeiboChannelConfig(WeiboChannelRequest wcr) {
		log.info("request queryWeiboChannelConfig[" + JSONObject.toJSONString(wcr));
		WeiboChannelResponse weiboResp = new  WeiboChannelResponse();
		try {
			if(StringUtils.isEmpty(wcr.getTenantCode())){
				throw new RuntimeException("request do not contain tenantCode");
			}
			ChannelConfig config = new ChannelConfig();
			config.setTenantCode(wcr.getTenantCode());
			config.setChannelEnable("1");
			config.setChannelCode("1005");
			List<ChannelConfig>	configList = channelConfigDao.query(config);
			com.alibaba.fastjson.JSONArray jsonArray = new com.alibaba.fastjson.JSONArray();
			//解析微博账号
			for(ChannelConfig weiboConfig : configList){
				String uidWeibo = weiboConfig.getChannelAccount();
				if(StringUtils.isEmpty(uidWeibo)){
					log.info(" weibo ChannelConfig uid[channelAccount] is empty. id:" + weiboConfig.getId());
					continue;
				}
				String accountConfig = weiboConfig.getAccountConfig();
				if(StringUtils.isEmpty(accountConfig)){
					log.info(" weibo ChannelConfig [AccountConfig] is empty. id:" + weiboConfig.getId());
					continue;
				}
				JSONObject jsonAccountConfig = JSONObject.parseObject(accountConfig);
				String name = StringUtils.defaultString(jsonAccountConfig.getString("name"), "匿名");
				String id = String.valueOf(weiboConfig.getId());
				JSONObject weiboParam = new JSONObject();
				weiboParam.put("channelAccount", uidWeibo);
				weiboParam.put("name", name);
				weiboParam.put("id", id);
				jsonArray.add(weiboParam);
			}
			weiboResp.setData(jsonArray);
			weiboResp.setResultCode(Constants.SUCCESS_CODE);
			weiboResp.setResultMsg("success");
			
		} catch (Exception e) {
			log.error("",e);
			weiboResp.setResultCode(Constants.ERROR_CODE);
			weiboResp.setResultMsg(e.getMessage());
		}
		
		return weiboResp;
	}
	
	
	/**
	 * 转发微博功能
	 * @param messageInfoSend
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public  JSONObject transpondWeibo(MessageInfoSend messageInfoSend) throws UnsupportedEncodingException {
		com.alibaba.fastjson.JSONArray weiboMsgInfoList =new com.alibaba.fastjson.JSONArray();
        JSONObject page = new JSONObject();
        com.alibaba.fastjson.JSONArray  statuses=null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sd2 = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
		//log.info(messageInfoSend.getStartTime());
		//log.info(messageInfoSend.getEndTime());
		 String accessToken = (String)redisDao.readValue("weibo2_accesstoken_" + messageInfoSend.getSendAccount());
        //token
		//String accessToken = messageInfoSend.getAcceptAccount();
		String weiboid = messageInfoSend.getWeiboId();
      
        String content = messageInfoSend.getContent();
        
        if("".equals(messageInfoSend.getSendAccount()) && messageInfoSend.getSendAccount()==null){
        	page.put("resultCode","-1");
			page.put("resultMsg", "你好，转发人账号不能为空");
        }else if("".equals(weiboid) || messageInfoSend.getWeiboId()==null){
        	page.put("resultCode","-1");
			page.put("resultMsg", "你好，微博账号不能为空");
        }else if(messageInfoSend.getContent()==null || "".equals(content)){
        	page.put("resultMsg", "评论内容不能为空！");
		}else if(content.length()>140){
			page.put("resultMsg", "回复内容不能超过140个汉字！");
		}else{
			client.setToken(accessToken);
			 try {
				 Status com =new Status(client.post(WeiboConfig.getValue("baseURL") + "statuses/repost.json",
						 new PostParameter[] {
					 new PostParameter("status",messageInfoSend.getContent()),
					 new PostParameter("id", weiboid) }));
				/* StatusWapper status = Status.constructWapperStatus(client.post(WeiboConfig.getValue("baseURL") + "statuses/repost.json",
						 new PostParameter[] {
					 new PostParameter("status", "**********"),
					 new PostParameter("id", "****************") }));*/
				 //如果转发成功往表发表微博表中插入数据
				 MessageInfoSend message = new MessageInfoSend();
				 message.setChannelCode(messageInfoSend.getChannelCode());
				 message.setSendAccount(messageInfoSend.getSendAccount());
				 message.setAcceptAccount(com.getRetweetedStatus().getUser().getId());
				 message.setMsgType(messageInfoSend.getMsgType());
				 message.setType("4");  //表示转发
				 message.setWeiboId(com.getId());
				 message.setContent(com.getRetweetedStatus().toString());
				 message.setStatus("0");
				 message.setWorkNo(messageInfoSend.getWorkNo());
				 message.setTenantCode(messageInfoSend.getTenantCode());
				 
				 this.messageSendDao.insert(message);
				 page.put("resultCode","0");
				 page.put("resultMsg", "success");
			} catch (WeiboException e1) {
				// TODO Auto-generated catch block
				page.put("resultCode","-1");
				page.put("resultMsg", "微博转发失败:"+e1);
				e1.printStackTrace();
				log.info("微博转发失败:"+e1);
			}
		}
		return page;
	}

}
