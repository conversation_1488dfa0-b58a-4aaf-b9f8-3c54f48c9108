package cn.sh.ideal.mgw.model;

import weibo4j.org.json.JSONException;
import weibo4j.org.json.JSONObject;


public class StatusCount {

	private String id; //status id
	private int repostsCount;                            //转发数
	private int commentsCount;                           //评论数
	
	
	public StatusCount(JSONObject jsonObject) throws JSONException {
		super();
		this.id = jsonObject.getString("id");
		this.repostsCount = jsonObject.getInt("reposts");
		this.commentsCount = jsonObject.getInt("comments");
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public int getRepostsCount() {
		return repostsCount;
	}
	public void setRepostsCount(int repostsCount) {
		this.repostsCount = repostsCount;
	}
	public int getCommentsCount() {
		return commentsCount;
	}
	public void setCommentsCount(int commentsCount) {
		this.commentsCount = commentsCount;
	}
	
}
