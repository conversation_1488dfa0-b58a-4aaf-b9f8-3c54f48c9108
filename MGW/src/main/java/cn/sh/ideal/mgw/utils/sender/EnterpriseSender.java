package cn.sh.ideal.mgw.utils.sender;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

import javax.activation.MimetypesFileTypeMap;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.MsgType;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;



public class EnterpriseSender implements Sender{

	private static final Logger log = Logger.getLogger(EnterpriseSender.class);


	public JSONObject customSend(String json, String token) {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_QY + ".CUSTOM_URL");

		JSONObject msgObj = JSONObject.parseObject(json);
		
		String touser = msgObj.getString("touser");
		if(StringUtils.isEmpty(touser) || "[]".equals(touser)) {
			msgObj.put("touser", "@all");
		}
		
		MsgType type = MsgType.valueOf(msgObj.getString("msgtype").toUpperCase());
		
		switch (type) {
		case TEXT:
		case NEWS:
		case MUSIC:
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, msgObj.toJSONString()));
		case VIDEO:
		case VOICE:
		case IMAGE:
			JSONObject mediaObj = new JSONObject();
			JSONObject resultObj = uploadFile(msgObj.getJSONObject(type.getName()), type, token);
			if(resultObj.containsKey("errcode")) {
				return resultObj;
			}
			mediaObj = new JSONObject();
			mediaObj.put("media_id", resultObj.getString("media_id"));
			msgObj.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + msgObj.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, msgObj.toJSONString()));
		default :
			throw new IllegalArgumentException("not supported msgtype");
		}
	}
	
	public JSON multiSend(String json, String token) throws Exception{
		return customSend(json, token);
	}

	

	public JSONObject uploadFile(JSONObject json, MsgType type, String token) {
		String url =  PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_QY
				+ ".MEDIA_URL");

		String fileurl = json.getString("url");
		String fileName = json.getString("fileName");
		Map<String, String> fileMap = new HashMap<String, String>();
		fileMap.put("userfile", fileName);

		String respStr = formUpload(url + token + "&type=" + type.getName().replaceAll("mp", ""),
				fileMap, fileurl);

		return JSONObject.parseObject(respStr);
	}
	
	private String formUpload(String urlStr, Map<String, String> fileMap,String fileurl) {
		String res = "";
		HttpURLConnection conn = null;
		String BOUNDARY = "---------------------------123821742118716"; // boundary就是request头和上传文件内容的分隔符
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(30000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn
					.setRequestProperty("User-Agent",
							"Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type",
					"multipart/form-data; boundary=" + BOUNDARY);

			OutputStream out = new DataOutputStream(conn.getOutputStream());

			// file
			if (fileMap != null) {
				Iterator<Entry<String, String>> iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Entry<String, String> entry = (Entry<String, String>) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();
					String contentType = new MimetypesFileTypeMap()
							.getContentType(file);
					if (filename.toLowerCase().endsWith(".png")) {
						contentType = "image/png";
					} else if(filename.toLowerCase().endsWith(".mp3")) {
						contentType = "audio/x-mpeg";
					} else if(filename.toLowerCase().endsWith(".wma")) {
						contentType = "application/mswma";
					} else if(filename.toLowerCase().endsWith(".mp4")) {
						contentType = "video/mp4";
					} else if (contentType == null || contentType.equals("")) {
						contentType = "application/octet-stream";
					}

					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append(
							"\r\n");
					strBuf.append("Content-Disposition: form-data; name=\""
							+ inputName + "\"; filename=\"" + filename
							+ "\"\r\n");
					strBuf.append("Content-Type:" + contentType + "\r\n\r\n");

					out.write(strBuf.toString().getBytes());

					URL httpUrl = new URL(fileurl);
					

					InputStream in = httpUrl.openStream();
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}

			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();

			// 读取返回数据
			StringBuffer strBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(
					conn.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				strBuf.append(line).append("\n");
			}
			res = strBuf.toString();
			reader.close();
			reader = null;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			return "{\"errcode\":\"-1\", \"errmsg\":\"" + e.getMessage() + "\"}";
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return res;
	}

}
