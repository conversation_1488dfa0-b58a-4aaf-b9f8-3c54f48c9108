package cn.sh.ideal.mgw.dao;

import java.util.Map;

import cn.sh.ideal.mgw.model.MediaModel;


public interface MediaReceiveDao {
     public void addInitTask(MediaModel model) throws Exception;
     
     public void addInterceptTask(MediaModel model) throws Exception;
     
     public void addTask(MediaModel model) throws Exception;
     
     public String getMediaFlag(String mailAddress) throws Exception;
     
     public void updateMediaFlag(String time,String mailAddress) throws Exception;
     
     public void addTaskFile(MediaModel model) throws Exception;
     
     public void addTaskTxt(MediaModel model) throws Exception;
     
     public void addTaskPool(MediaModel model) throws Exception;
     
     public String getMediaSwitch(String channelType) throws Exception;
     
     public void addReplyLog(MediaModel model) throws Exception;
     
     public void addReceiveErrorLog(String mediaType,String errorMessage,String mailAddress,String media_sender,String pro_type,String media_title,String media_content,String task_id);
     
     public Map getSysDate() throws Exception;
     
     public void addReceiveMailLog(String mail_server,String begin_date,String end_date,String amount);
     
     public String getTaskId() throws Exception;
     
     public String getInfoType(MediaModel model) throws Exception;
     
     public String getWyInfo(Map map) throws Exception;

	public String selectMaxWeiboId(String account_Name);
	
	
}
