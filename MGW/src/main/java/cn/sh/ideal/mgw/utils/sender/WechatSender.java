package cn.sh.ideal.mgw.utils.sender;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.activation.MimetypesFileTypeMap;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.base.MsgType;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class WechatSender implements Sender {

	private static final Logger log = Logger.getLogger(WechatSender.class);

	public JSONObject customSend(String json, String token) {
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".CUSTOM_URL");
		if (url == null || "".equals(url.trim())) {
			throw new NullPointerException("没有找到[" + Constants.PLATFORM_WECHAT + "]平台信息");
		}

		JSONObject jsonObject = JSONObject.parseObject(json);
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype").toUpperCase());
		
		switch (type) {
		case TEXT:
		case NEWS:
		case MUSIC:
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, json));
		case VIDEO:
		case VOICE:
		case IMAGE:
			JSONObject mediaObj = new JSONObject();
			JSONObject resultObj = uploadFile(jsonObject.getJSONObject(type.getName()), type, token);
			if (resultObj.containsKey("errcode")) {
				return resultObj;
			}
			mediaObj = new JSONObject();
			mediaObj.put("media_id", resultObj.getString("media_id"));
			jsonObject.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME 
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		default:
			throw new IllegalArgumentException("not supported msgtype");
		}
	}

	public JSON multiSend(String json, String token) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(json);
		JSONArray touser = jsonObject.getJSONArray("touser");
		if (touser == null || touser.isEmpty()) {
			return sendAll(json, token);
		} else {
			return massSend(json, token);
		}
	}

	private JSON massSend(String json, String token) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(json);
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype").toUpperCase());
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".MASS_SEND");
		JSONObject mediaObj = null;
		switch (type) {
		case TEXT:
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		case MPNEWS:
			JSONArray array = jsonObject.getJSONArray("articles");
			if (array == null || array.isEmpty())
				throw new NullPointerException("articles is null");
			JSONObject itemObj = null;
			for (int i = 0; i < array.size(); i++) {
				JSONObject requestUploadObj = new JSONObject();
				itemObj = array.getJSONObject(i);
				String picurl = itemObj.getString("picurl");
				if (picurl == null || "".equals(picurl))
					continue;
				requestUploadObj.put("url", picurl);
				requestUploadObj.put("fileName", picurl.substring(picurl.lastIndexOf("/")));
				JSONObject object = uploadFile(requestUploadObj, MsgType.IMAGE, token);
				if (object.containsKey("errcode")) {
					throw new Exception(object.toJSONString());
				}
				itemObj.put("thumb_media_id", object.get("media_id"));
			}
			String uploadNewsUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".UPLOADNEWS_URL");
			//FIXME
			JSONObject uploadResult = JSONObject.parseObject(NetUtil.send(uploadNewsUrl + token, NetUtil.POST, jsonObject.toJSONString()));
			if (uploadResult.containsKey("errcode")) {
				throw new Exception(uploadResult.toJSONString());
			}
			mediaObj = new JSONObject();
			mediaObj.put("media_id", uploadResult.getString("media_id"));
			jsonObject.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		case MPVIDEO:
		case VOICE:
		case IMAGE:
			JSONObject resultObj = uploadFile(jsonObject.getJSONObject(type.getName()), type, token);
			if (resultObj.containsKey("errcode")) {
				return resultObj;
			}
			if (type == MsgType.MPVIDEO) {
				String uploadurl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".UPLOADVIDEO_URL");
				JSONObject requestObj = new JSONObject();
				JSONObject videoObj = jsonObject.getJSONObject(type.getName());
				requestObj.put("media_id", resultObj.getString("media_id"));
				requestObj.put("title", videoObj.getString("title") == null ? "" : videoObj.getString("title"));
				requestObj.put("description", videoObj.getString("description") == null ? "" : videoObj.getString("description"));
				//FIXME
				JSONObject responseObj = JSONObject.parseObject(NetUtil.send(uploadurl + token, NetUtil.POST, requestObj.toJSONString()));
				if (responseObj.containsKey("errcode")) {
					return responseObj;
				}
				resultObj.put("media_id", responseObj.getString("media_id"));
			}
			mediaObj = new JSONObject();
			mediaObj.put("media_id", resultObj.getString("media_id"));
			jsonObject.put(type.getName(), mediaObj);
			log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
			//FIXME
			return JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString()));
		case MUSIC:
		default:
			throw new IllegalArgumentException("not supported msgtype");
		}
	}

	private JSON sendAll(String json, String token) throws Exception {
		JSONArray resultArray = new JSONArray();
		JSONObject jsonObject = JSONObject.parseObject(json);
		MsgType type = MsgType.valueOf(jsonObject.getString("msgtype").toUpperCase());
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".SENDALL_URL");
		List<String> groups = getAllGroupIds(token, Constants.PLATFORM_WECHAT);
		JSONObject filterObj = new JSONObject();
		JSONObject mediaObj = null;
		switch (type) {
		case TEXT:
			for (int i = 0; i < groups.size(); i++) {
				filterObj.put("group_id", groups.get(i));
				jsonObject.put("filter", filterObj);
				log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
				//FIXME
				resultArray.add(JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString())));
			}
			break;
		case MPNEWS:

			JSONArray array = jsonObject.getJSONArray("articles");
			if (array == null || array.isEmpty())
				throw new NullPointerException("articles is null");
			JSONObject itemObj = null;
			for (int i = 0; i < array.size(); i++) {
				JSONObject requestUploadObj = new JSONObject();
				itemObj = array.getJSONObject(i);
				String picurl = itemObj.getString("picurl");
				if (picurl == null || "".equals(picurl))
					continue;
				requestUploadObj.put("url", picurl);
				requestUploadObj.put("fileName", picurl.substring(picurl.lastIndexOf("/")));
				JSONObject object = uploadFile(requestUploadObj, MsgType.IMAGE, token);
				if (object.containsKey("errcode")) {
					throw new Exception(object.toJSONString());
				}
				itemObj.put("thumb_media_id", object.get("media_id"));
			}
			String uploadNewsUrl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".UPLOADNEWS_URL");
			//FIXME
			JSONObject uploadResult = JSONObject.parseObject(NetUtil.send(uploadNewsUrl + token, NetUtil.POST, jsonObject.toJSONString()));
			if (uploadResult.containsKey("errcode")) {
				throw new Exception(uploadResult.toJSONString());
			}
			mediaObj = new JSONObject();
			for (int i = 0; i < groups.size(); i++) {
				filterObj.put("group_id", groups.get(i));
				jsonObject.put("filter", filterObj);
				mediaObj.put("media_id", uploadResult.getString("media_id"));
				jsonObject.put(type.getName(), mediaObj);
				log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
				//FIXME
				resultArray.add(JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString())));
			}
			break;
		case MPVIDEO:
		case VOICE:
		case IMAGE:
			JSONObject resultObj = uploadFile(jsonObject.getJSONObject(type.getName()), type, token);
			if (resultObj.containsKey("errcode")) {
				return resultObj;
			}
			if (type == MsgType.MPVIDEO) {
				String uploadurl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".UPLOADVIDEO_URL");
				JSONObject requestObj = new JSONObject();
				JSONObject videoObj = jsonObject.getJSONObject(type.getName());
				requestObj.put("media_id", resultObj.getString("media_id"));
				requestObj.put("title", videoObj.getString("title") == null ? "" : videoObj.getString("title"));
				requestObj.put("description", videoObj.getString("description") == null ? "" : videoObj.getString("description"));
				//FIXME
				JSONObject responseObj = JSONObject.parseObject(NetUtil.send(uploadurl + token, NetUtil.POST, requestObj.toJSONString()));
				if (responseObj.containsKey("errcode")) {
					return responseObj;
				}
				resultObj.put("media_id", responseObj.getString("media_id"));
			}
			mediaObj = new JSONObject();
			for (int i = 0; i < groups.size(); i++) {
				filterObj.put("group_id", groups.get(i));
				jsonObject.put("filter", filterObj);
				mediaObj.put("media_id", resultObj.getString("media_id"));
				jsonObject.put(type.getName(), mediaObj);
				log.info("request url[" + url + token + "] params[" + jsonObject.toJSONString() + "]");
				//FIXME
				resultArray.add(JSONObject.parseObject(NetUtil.send(url + token, NetUtil.POST, jsonObject.toJSONString())));
			}
			break;
		case MUSIC:
		default:
			throw new IllegalArgumentException("not supported msgtype");
		}
		return resultArray;
	}

	private static List<String> getAllGroupIds(String token, String platform) {
		List<String> resultList = new ArrayList<String>();
		String url = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, platform + ".GROUPS_URL");
		//FIXME
		JSONObject jsonObject = JSONObject.parseObject(NetUtil.send(url + token, NetUtil.GET, ""));
		JSONArray array = jsonObject.getJSONArray("groups");
		if (array != null && !array.isEmpty()) {
			for (int i = 0; i < array.size(); i++) {
				resultList.add(array.getJSONObject(i).getString("id"));
			}
		}
		return resultList;
	}

	public JSONObject uploadFile(JSONObject json, MsgType type, String token) {
		String wechaturl = PropertiesUtil.getProperty(PropertiesUtil.CONFIG, Constants.PLATFORM_WECHAT + ".MEDIA_URL");

		String fileurl = "";
		String fileName = "";
		if (json.containsKey("media_id")) {
			String fUrl = json.getString("media_id");
			String regex = "/([^/]+)$";

			Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(fUrl);
			if (m.find()) {
				fileurl=fUrl;
				
				 
				fileName = m.group(1);

			}

		} else {
			fileurl = json.getString("url");
			String fUrl = json.getString("url");
			try {
				
				URL url = new URL(fileurl);
				HttpURLConnection conn = (HttpURLConnection) url.openConnection();
				conn.setDoInput(true);
				conn.setRequestMethod("GET");
				System.out.println(conn.getContentType());
				String fileExt = getFileEndWitsh(conn.getContentType());
				
				String regex = "/([^/]+)$";

				Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
				Matcher m = p.matcher(fUrl);
				if (m.find()) {
					fileurl=fUrl;
					
					 
					fileName = m.group(1)+fileExt;

				}
			} catch (Exception e) {
				// TODO: handle exception
			}

			
			// 根据内容类型获取扩展名
		}
		Map<String, String> fileMap = new HashMap<String, String>();
		fileMap.put("userfile", fileName);

		String respStr = formUpload(wechaturl + token + "&type=" + type.getName().replaceAll("mp", ""), fileMap, fileurl);

		return JSONObject.parseObject(respStr);
	}

	private String formUpload(String urlStr, Map<String, String> fileMap, String fileurl) {
		String res = "";
		HttpURLConnection conn = null;
		String BOUNDARY = "---------------------------123821742118716"; // boundary就是request头和上传文件内容的分隔符
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(30000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);

			OutputStream out = new DataOutputStream(conn.getOutputStream());

			// file
			if (fileMap != null) {
				Iterator<Entry<String, String>> iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Entry<String, String> entry = (Entry<String, String>) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();
					String contentType = new MimetypesFileTypeMap().getContentType(file);
					if (filename.toLowerCase().endsWith(".png")) {
						contentType = "image/png";
					} else if (filename.toLowerCase().endsWith(".mp3")) {
						contentType = "audio/x-mpeg";
					} else if (filename.toLowerCase().endsWith(".wma")) {
						contentType = "application/mswma";
					} else if (filename.toLowerCase().endsWith(".mp4")) {
						contentType = "video/mp4";
					} else if (contentType == null || contentType.equals("")) {
						contentType = "application/octet-stream";
					}

					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"; filename=\"" + filename + "\"\r\n");
					strBuf.append("Content-Type:" + contentType + "\r\n\r\n");

					out.write(strBuf.toString().getBytes());

					URL httpUrl = new URL(fileurl);

					InputStream in = httpUrl.openStream();
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}

			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();

			// 读取返回数据
			StringBuffer strBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				strBuf.append(line).append("\n");
			}
			res = strBuf.toString();
			reader.close();
			reader = null;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			return "{\"errcode\":\"-1\", \"errmsg\":\"" + e.getMessage() + "\"}";
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return res;
	}
	public  String getFileEndWitsh(String contentType) {
		String fileEndWitsh = "";
		if ("image/jpeg".equals(contentType))
			fileEndWitsh = ".jpg";
		else if ("image/jpg".equals(contentType))
			fileEndWitsh = ".jpg";
		else if ("audio/mpeg".equals(contentType))
			fileEndWitsh = ".mp3";
		else if (contentType.contains("image/png"))
			fileEndWitsh = ".png";
		else if ("audio/amr".equals(contentType))
			fileEndWitsh = ".amr";
		else if ("video/mp4".equals(contentType))
			fileEndWitsh = ".mp4";
		else if ("video/mpeg4".equals(contentType))
			fileEndWitsh = ".mp4";
		return fileEndWitsh;
	}
	
}
