package cn.sh.ideal.mgw.adapter;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_CONTENT;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_MSG_TYPE;
import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_PARAM_TO_USER;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_ARTICLES;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_EVENT_ID;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_EVENT_TYPE;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_LOCATION_X;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_LOCATION_Y;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_URL;

import org.apache.log4j.Logger;

import cn.sh.ideal.mgw.model.PlatformMessage;
import cn.sh.ideal.mgw.service.local.FilesService;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.util.SpringContextUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 微信消息适配器
 * 
 * <AUTHOR>
 * 2015-3-6
 *
 */
public class WechatMsgAdapter extends MsgAdapter{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static final Logger log = Logger.getLogger(WechatMsgAdapter.class);
	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;
		
		if(null != data){
			message = new MessageInfo();
			PlatformMessage pm = (PlatformMessage)data;
			
			message.setChannelCode(channelCode);
			message.setSource(pm.getSource());
			message.setSendAccount(pm.getOpenId());
			message.setAcceptedAccount(pm.getToUser());
			message.setMsgType(pm.getMsgType().toString());
			
			this.assembSendContent(pm, message);
		}
		return message;
	}

	@Override
	public JSONObject message2Media(MessageInfo message) {
		JSONObject object = new JSONObject();
		JSONObject extParam = null;
		
		object.put(MEDIA_PARAM_TO_USER, message.getAcceptedAccount());
		object.put(MEDIA_PARAM_MSG_TYPE, message.getMsgType());
		if(PlatformMessage.MsgType.text.toString().equals(message.getMsgType())){
			extParam = new JSONObject();
			extParam.put(MEDIA_PARAM_CONTENT, message.getContent());
		}else if(PlatformMessage.MsgType.image.toString().equals(message.getMsgType()) ||
				PlatformMessage.MsgType.voice.toString().equals(message.getMsgType())){
			extParam = new JSONObject();
			extParam.put(MSG_PARAM_URL, message.getContent());
			
		}else if(PlatformMessage.MsgType.news.toString().equals(message.getMsgType())){
			extParam = new JSONObject();
			JSONArray ArrayContent = null;
			Object obj = JSON.parse(message.getContent());
			if (obj instanceof JSONObject) {
				JSONObject jsonContent = (JSONObject) obj;
				ArrayContent = new JSONArray();
				ArrayContent.add(jsonContent);
			}else if (obj instanceof JSONArray){
				ArrayContent = (JSONArray) obj;
			}
			extParam.put(MSG_PARAM_ARTICLES, ArrayContent);
			
		}
		
		object.put(message.getMsgType(), extParam);
		object.put("sendType", message.getSendType());
		return object;
	}
	
	/**
	 * 拼装Message对象的content字段
	 * 
	 * @param pm
	 * @param message
	 */
	private void assembSendContent(PlatformMessage pm,MessageInfo message){
		switch (pm.getMsgType()) {
			case text:message.setContent(pm.getContent());break;

			case image:     
			case voice:  
			case video:
			case shortvideo:
				
				try {
					FilesService filesService =(FilesService)SpringContextUtil.getBean("filesService");
					message.setContent(filesService.downloadMedia(pm.getToUser(), pm.getMediaId(), "mediaFile"));
					pm.setContent(message.getContent());
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
				break;
			case location:{
				JSONObject object = new JSONObject();
				object.put(MSG_PARAM_LOCATION_X, pm.getX());
				object.put(MSG_PARAM_LOCATION_Y, pm.getY());
				
				message.setContent(object.toJSONString());break;
			}
			case event:{
				JSONObject object = new JSONObject();
				object.put(MSG_PARAM_EVENT_TYPE, pm.getEventType());
				object.put(MSG_PARAM_EVENT_ID, pm.getEventId());
				
				message.setContent(object.toJSONString());break;
			}
	
			default:
				throw new NullPointerException("暂不支持该消息类型" + pm.getMsgType());
		}
	}
	
}
