package cn.sh.ideal.mgw.adapter;

import static cn.sh.ideal.mgw.adapter.Constants.MEDIA_TYPE_EMAIL;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_ATTACHMENTS;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_COTENT_HTML;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_SUBJECT;
import static cn.sh.ideal.mgw.adapter.Constants.MSG_PARAM_TEXT;
import cn.sh.ideal.mgw.mediasend.model.MediaSendModel;
import cn.sh.ideal.mgw.model.MediaModel;
import cn.sh.ideal.model.MessageInfo;

import com.alibaba.fastjson.JSONObject;

/**
 * 邮件消息适配器
 * 
 * <AUTHOR>
 * 2015-3-6
 *
 */
public class EmailMsgAdapter extends MsgAdapter{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;

		if(null != data){
			message = new MessageInfo();
			MediaModel media = (MediaModel)data;
			
			message.setChannelCode(channelCode);
			message.setSource("");
			message.setSendAccount(media.getMediaSender());
			message.setAcceptedAccount(media.getMediaReceiver());
			
			message.setMsgType(MEDIA_TYPE_EMAIL);
			
			JSONObject object = new JSONObject();
			object.put(MSG_PARAM_SUBJECT, media.getMediaTitle());
			object.put(MSG_PARAM_ATTACHMENTS, media.getMediaFilePath());
			object.put(MSG_PARAM_COTENT_HTML, media.getHtmlContent());
			object.put(MSG_PARAM_TEXT, media.getMediaContent());
			
			message.setContent(object.toJSONString());
			
		}
		return message;
	}

	@Override
	public MediaSendModel message2Media(MessageInfo message) {
		MediaSendModel model = null;
		
		if(null != message){
			model = new MediaSendModel();
			
			JSONObject content = JSONObject.parseObject(message.getContent());
			
			model.setSender(message.getSendAccount());
			model.setReceiver(message.getAcceptedAccount());
			model.setTitle(content.getString(MSG_PARAM_SUBJECT));
			model.setHtmlContent(content.getString(MSG_PARAM_COTENT_HTML));
			model.setTextContent(content.getString(MSG_PARAM_TEXT));
			model.setAttachPath(content.getString(MSG_PARAM_ATTACHMENTS));
			model.setSenderName(message.getNickname());
			model.setSendType(message.getSendType());
		}
		
		return model;
	}

}
