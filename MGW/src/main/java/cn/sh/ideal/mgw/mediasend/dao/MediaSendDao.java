package cn.sh.ideal.mgw.mediasend.dao;

import java.util.List;
import java.util.Map;

import org.springframework.util.StopWatch.TaskInfo;

import cn.sh.ideal.mgw.mediasend.model.MediaSendModel;

public interface MediaSendDao {
	/**
	 * 获取参数信息
	 */
	public Map queryParam(String code);
	/**
	 * 插入媒体发送表
	 * @param model
	 */
	public void insertMediaSend(MediaSendModel model) throws Exception;
	
	/**
	 * 修改多媒体任务信息
	 * @param model
	 * @throws Exception
	 */
	public void updateTask(MediaSendModel model) throws Exception;
	
	
	/**
	 * 发送失败数据查询
	 * @throws Exception
	 */
	public List getReSendMedia();
	
	/**
	 * 修改发送失败数据的状态为成功
	 * @throws Exception
	 */
	public void updateReSendStatus(String id);
	
	/**
	 * 修改发送次数
	 * @throws Exception
	 */
	public void updateReSendNum(String id);
	
	/**
	 * 获取邮件发送流水号
	 * @return
	 */
	public int getMediaSendId();
	
	/**
	 * 获取推送信息
	 * @param 
	 */
	public List getInfoPushMedia();
	
	/**
	 *修改推送次数
	 * @param 
	 */
	public void updateInfoPushNum(String sendId);
	
	/**
	 *修改推送状态
	 * @param 
	 */
	public void updateInfpPushStatus(String sendId,String status);
	
	/**
	 * 查询发送短信
	 * @return
	 */
	public List<MediaSendModel> query(MediaSendModel model);
	
	/**
	 * 深航查询失败短信
	 * @return
	 */
	public List<MediaSendModel> queryCsFailSms();
	/**
	 * 电信查询失败短信
	 * @return
	 */
	public List<MediaSendModel> queryTelecomFailSms();
	
	/**
	 * 更新mediasend表
	 * @param model
	 */
	public void update(MediaSendModel model);
	/**
	 * 根据消息ID返回记录
	 * @param messageId
	 * @return
	 */
	public List<MediaSendModel> loadByMessageId(String messageId);
}
