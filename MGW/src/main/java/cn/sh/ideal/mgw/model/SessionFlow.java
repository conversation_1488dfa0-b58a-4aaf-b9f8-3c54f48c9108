package cn.sh.ideal.mgw.model;

import java.util.ArrayList;
import java.util.List;


public class SessionFlow implements java.io.Serializable{

	private static final long serialVersionUID = 1L;
	
	private String id;
	
	private String menuKey;//菜单事件ID
	private String flowId;//流程ID
	private List <BaseParamModel> userParam = new ArrayList<BaseParamModel>();//提交参数
	private List <BaseParamModel> parameter = new ArrayList<BaseParamModel>();//保留用户参数
	private List <BaseParamModel> flowParam = new ArrayList<BaseParamModel>();//后台保存参数
	private List <BaseParamModel> flowParamTemp = new ArrayList<BaseParamModel>();//临时保存参数
	private String openId;//用户ID
	private long time;//进入会话时间
	private String errorType;//错误类型:0无限制,1次数错误,2超时错误
	private String parentMenuId;//菜单父ID
	private int errorValue;//流程规定的错误值
	private int errorNum;//错误次数
	private String  sId;//会话ID
	private List<Menu> menuList;
	private String contextMenu;//标准问
	private List<Menu> contextMenuList;//标准问列表
	private List<Menu> allMenuList;//全局的menu列表
	private String menu;//指令码
	private String platform;//平台标识
	
	private String isAgent;//是否接入人工坐席：0,自助;1,进入人工;2,人工
	private String agentText;//保存用户消息,推送给人工坐席
	private String toUser;
	private String tenantId;
	
	private String channelId;
	
	private Menu currentMenu;
	
	private int validSeconds = 300;
	
	private String nickname;
	
	
	public String getToUser() {
		return toUser;
	}
	public void setToUser(String toUser) {
		this.toUser = toUser;
	}

	public String getAgentText() {
		return agentText;
	}
	public void setAgentText(String agentText) {
		this.agentText = agentText;
	}
	public String getIsAgent() {
		return isAgent;
	}
	public void setIsAgent(String isAgent) {
		this.isAgent = isAgent;
	}
	
	public SessionFlow(){
		
	}
	/**
	 * @return the menuList
	 */
	public List<Menu> getMenuList() {
		return menuList;
	}
	/**
	 * @param menuList the menuList to set
	 */
	public void setMenuList(List<Menu> menuList) {
		this.menuList = menuList;
	}
	public String getsId() {
		return sId;
	}

	public void setsId(String sId) {
		this.sId = sId;
	}

	public int getErrorNum() {
		return errorNum;
	}

	public void setErrorNum(int errorNum) {
		this.errorNum = errorNum;
	}



	public int getErrorValue() {
		return errorValue;
	}
	public void setErrorValue(int errorValue) {
		this.errorValue = errorValue;
	}

	public String getErrorType() {
		return errorType;
	}


	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}


	public String getParentMenuId() {
		return parentMenuId;
	}


	public void setParentMenuId(String parentMenuId) {
		this.parentMenuId = parentMenuId;
	}


	public long getTime() {
		return time;
	}
	public void setTime(long time) {
		this.time = time;
	}
	
	public String getMenuKey() {
		return menuKey;
	}

	public void setMenuKey(String menuKey) {
		this.menuKey = menuKey;
	}

	public List<BaseParamModel> getUserParam() {
		return userParam;
	}

	public void setUserParam(List<BaseParamModel> userParam) {
		this.userParam = userParam;
	}


	public List<BaseParamModel> getFlowParam() {
		return flowParam;
	}

	public void setFlowParam(List<BaseParamModel> flowParam) {
		this.flowParam = flowParam;
	}

	public String getFlowId() {
		return flowId;
	}
	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}
	
	public String getOpenId() {
		return openId;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getContextMenu() {
		return contextMenu;
	}

	public void setContextMenu(String contextMenu) {
		this.contextMenu = contextMenu;
	}

	
	public List<BaseParamModel> getParameter() {
		return parameter;
	}
	public void setParameter(List<BaseParamModel> parameter) {
		this.parameter = parameter;
	}
	
	public List<Menu> getContextMenuList() {
		return contextMenuList;
	}
	public void setContextMenuList(List<Menu> contextMenuList) {
		this.contextMenuList = contextMenuList;
	}
	public String getMenu() {
		return menu;
	}
	public void setMenu(String menu) {
		this.menu = menu;
	}
	public String getPlatform() {
		return platform;
	}
	public void setPlatform(String platform) {
		this.platform = platform;
	}
	public List<BaseParamModel> getFlowParamTemp() {
		return flowParamTemp;
	}
	public void setFlowParamTemp(List<BaseParamModel> flowParamTemp) {
		this.flowParamTemp = flowParamTemp;
	}
	public List<Menu> getAllMenuList() {
		return allMenuList;
	}
	public void setAllMenuList(List<Menu> allMenuList) {
		this.allMenuList = allMenuList;
	}
	public String getSId() {
		return sId;
	}
	public void setSId(String id) {
		sId = id;
	}
	/**
	 * @return the tenantId
	 */
	public String getTenantId() {
		return tenantId;
	}
	/**
	 * @param tenantId the tenantId to set
	 */
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	/**
	 * @return the channelId
	 */
	public String getChannelId() {
		return channelId;
	}
	/**
	 * @param channelId the channelId to set
	 */
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
	/**
	 * @return the currentMenu
	 */
	public Menu getCurrentMenu() {
		return currentMenu;
	}
	/**
	 * @param currentMenu the currentMenu to set
	 */
	public void setCurrentMenu(Menu currentMenu) {
		this.currentMenu = currentMenu;
	}
	/**
	 * @return the validSeconds
	 */
	public int getValidSeconds() {
		return validSeconds;
	}
	/**
	 * @param validSeconds the validSeconds to set
	 */
	public void setValidSeconds(int validSeconds) {
		this.validSeconds = validSeconds;
	}
	
	public void setId(String id) {
		this.id = id;
	}
	
	public String getId() {
		return id;
	}
	/**
	 * @return the nickname
	 */
	public String getNickname() {
		return nickname;
	}
	/**
	 * @param nickname the nickname to set
	 */
	public void setNickname(String nickname) {
		this.nickname = nickname;
	}
	
	
}
