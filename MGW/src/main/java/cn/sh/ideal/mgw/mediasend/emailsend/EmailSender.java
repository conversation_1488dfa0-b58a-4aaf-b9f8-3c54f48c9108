package cn.sh.ideal.mgw.mediasend.emailsend;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.mediasend.model.MediaSendModel;
import cn.sh.ideal.model.ChannelConfig;

import com.alibaba.fastjson.JSONObject;

@Service
public class EmailSender implements IMediaSend {
	private static final Logger log = Logger.getLogger(EmailSender.class);
	@Autowired
	private ChannelConfigDao channelConfigDao;

	/**
	 * 以HTML格式发送邮件
	 * 
	 * @param mailInfo
	 *            待发送的邮件信息
	 */
	private boolean sendHtmlMail(MediaSendModel model) {

		ChannelConfig channelConfig = new ChannelConfig();
		channelConfig.setChannelAccount(model.getSender());
		List<ChannelConfig> list = channelConfigDao.query(channelConfig);
		if (list != null && list.size() > 0) {
			channelConfig = list.get(0);
		} else {
			log.info("未获取到渠道信息");
			return false;
		}
		JSONObject accountConfig = JSONObject.parseObject(channelConfig.getAccountConfig());
		MailSenderInfo info = new MailSenderInfo();
		info.setMailServerHost(accountConfig.getString(Constants.PARAM_SMTP_SERVER));
		info.setMailServerPort(accountConfig.getString(Constants.PARAM_SMTP_PORT));
		info.setUserName(model.getSender());
		info.setPassword(accountConfig.getString(Constants.PARAM_PASSWORD));
		info.setFromAddress(model.getSender());
		info.setToAddress(model.getReceiver());
		info.setSubject(model.getTitle());
		info.setHtmlContent(StringUtils.isEmpty(model.getHtmlContent()) ? model.getTextContent() : model.getHtmlContent());
		info.setTextContent(model.getTextContent());
		info.setContentImgs(model.getContentImgs());
		info.setAttachFileNames(model.getAttchFiles());
		info.setValidate(accountConfig.getBoolean("smtpSSL"));
		info.setIsTLS(accountConfig.getBoolean("smtpTLS"));
		info.setAttachPath(model.getAttachPath());
		info.setSenderName(model.getSenderName());
		info.setAttachUrl("");
		SimpleMailSender sender = new SimpleMailSender();
		try {
			return sender.sendHtmlMail(info);
		} catch (Exception e) {
			log.error("发送邮件失败", e);
			e.printStackTrace();
			return false;
		}
	}

	public boolean send(MediaSendModel model) {
		boolean flag = sendHtmlMail(model);
		return flag;
	}
}
