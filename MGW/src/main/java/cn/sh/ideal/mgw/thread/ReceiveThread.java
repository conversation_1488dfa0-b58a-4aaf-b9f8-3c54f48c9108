package cn.sh.ideal.mgw.thread;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.mail.BodyPart;
import javax.mail.Flags;
import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Part;
import javax.mail.Store;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import javax.mail.search.FlagTerm;
import javax.mail.search.SearchTerm;

import org.apache.commons.lang.StringUtils;
import org.htmlparser.Parser;
import org.htmlparser.beans.StringBean;
import org.nutz.json.Json;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.MsgConvertUtil;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.dao.CommonDao;
import cn.sh.ideal.mgw.dao.MediaReceiveDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageLogDao;
import cn.sh.ideal.mgw.model.MediaModel;
import cn.sh.ideal.mgw.model.ReceiveMessageLog;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.utils.EmailConnecter;
import cn.sh.ideal.mgw.utils.IdealFtpClient;
import cn.sh.ideal.mgw.utils.InterceptFactory;
import cn.sh.ideal.mgw.utils.NetUtil;
import cn.sh.ideal.mgw.utils.PropertiesUtil;
import cn.sh.ideal.mgw.utils.Util;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sun.mail.imap.IMAPFolder;

public class ReceiveThread implements Runnable {
	private static final Logger log = LoggerFactory.getLogger(ReceiveThread.class);
	private JSONObject jsonInfo;
	private CommonDao commonDao;
	private MediaReceiveDao mediaReceiveDao;
	private InterceptFactory interceptFactory;

	private MessageInfoDao messageInfoDao;

	private LocalSessionService sessionService;

	private SensitiveService sensitiveService;
	
	private ReceiveMessageLogDao receiveMessageLogDao;
	
	private SysInitService initService;
	private RedisDao<String, Serializable> redisDao;
	private final String channelCode = "1002";
	private final String channelName = "EMAIL";

	private static String mirUrl = (String) PropertiesUtil.getProperty(PropertiesUtil.APP, "media.mirUrl");

	public ReceiveThread(JSONObject json, ApplicationContext context) {
		this.jsonInfo = json;
		this.commonDao = context.getBean("commonDao", CommonDao.class);
		this.mediaReceiveDao = context.getBean("mediaReceiveDao", MediaReceiveDao.class);
		this.interceptFactory = context.getBean("interceptFactory", InterceptFactory.class);
		this.messageInfoDao = context.getBean("messageInfoDao", MessageInfoDao.class);
		this.sessionService = context.getBean("LocalsessionService", LocalSessionService.class);
		this.receiveMessageLogDao = context.getBean("receiveMessageLogDao", ReceiveMessageLogDao.class);
		this.sensitiveService = context.getBean("sensitiveService", SensitiveService.class);
		this.redisDao = context.getBean("redisDao", RedisDao.class);
		this.initService = context.getBean(SysInitService.class);
	}

	public void run() {

		Store store = null;
		IMAPFolder folder = null;

		Store storePop = null;
		Folder folderPop = null;
		String mailAddress="";
		try {
			String mailFileSize = jsonInfo.getString("mailFileSize");
			String mailPassWord = jsonInfo.getString("password");
			String mailServer = jsonInfo.getString("imapServer");
			mailAddress = jsonInfo.getString("mailAddress");
			String imapPort = jsonInfo.getString("imapPort");
			String mailAmount = jsonInfo.getString("mailAmount");
			Boolean isImapSSL = jsonInfo.getBoolean("imapSSL");
			log.info("json imapTLS" + jsonInfo.getBoolean("imapTLS"));
			Boolean isImapTLS = jsonInfo.getBoolean("imapTLS");
			String port = "";
			redisDao.saveValue("MAIL_LOCK_"+mailAddress, mailAddress);
			log.info("接入到的json消息" + jsonInfo.toJSONString());
			log.info("----------开始接收邮件------------" + mailAddress + ":" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

			if ("1".equals(isImapSSL)) {
				port = StringUtils.isEmpty(imapPort) ? "143" : imapPort;
			} else {
				port = StringUtils.isEmpty(imapPort) ? "993" : imapPort;
			}
			// 创建Session实例对象

			store = EmailConnecter.getIMAPStore(mailServer, port, isImapSSL,isImapTLS, mailAddress, mailPassWord);
			// 获得收件箱
			folder = (IMAPFolder) store.getFolder("INBOX");
			folder.open(Folder.READ_WRITE); // 打开收件箱

			// 设置搜索条件
			SearchTerm serSearchTerm = new FlagTerm(new Flags(Flags.Flag.SEEN), false);

			Message[] messages = folder.search(serSearchTerm);

			// 流量控制
			int mailNum = 0;

			int maxNum = Integer.parseInt(StringUtils.isEmpty(mailAmount) ? "10" : mailAmount);// 本次可接收量

			if (maxNum >= messages.length) {
				mailNum = messages.length;
			} else {
				mailNum = maxNum;
			}
			log.info("本次获取邮件数量:" + mailNum);

			// 获取当前时间(精确到天)
			String currentReceiveDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

			if (messages != null) {
				for (int j = 0; j < mailNum; j++) {
					messages[j].setFlag(Flags.Flag.SEEN, true);
					ReceiveMessageLog receiveMessageLog=new ReceiveMessageLog();
					receiveMessageLog.setTenantId(jsonInfo.getString("tenant"));
					receiveMessageLog.setChannelMessageId(getMessageId((MimeMessage)messages[j]));
					
					receiveMessageLog.setChannelAccount(mailAddress);
					receiveMessageLog.setChannelId("1002");
					
					if(receiveMessageLogDao.queryCount(receiveMessageLog)<=0){
						receiveMessageLogDao.insert(receiveMessageLog);
						parseMessage(messages[j], currentReceiveDate, mailFileSize, mailAddress, jsonInfo.getString("tenant"));
						log.info("----------结束接收邮件------------" + mailAddress + ":" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
					}
				}
			}
			

		} catch (Exception e) {
			e.printStackTrace();
			log.error("接收邮件出错:" + e.getMessage(), e);
		} finally {
			// 释放资源
			redisDao.deleteValue("MAIL_LOCK_"+mailAddress);
			if (folder != null && folder.isOpen()) {
				try {
					folder.close(true);
					folder = null;
				} catch (MessagingException e) {
					log.error("关闭folder出错:" + e.getMessage());
				}
			}
			if (folderPop != null && folderPop.isOpen()) {
				try {
					folderPop.close(true);
					folderPop = null;
				} catch (MessagingException e) {
					log.error("关闭folder出错:" + e.getMessage());
				}
			}
			if (store != null && store.isConnected()) {
				try {
					store.close();
					store = null;
				} catch (MessagingException e) {
					log.error("关闭store出错:" + e.getMessage());
				}
			}
			if (storePop != null && storePop.isConnected()) {
				try {
					storePop.close();
					storePop = null;
				} catch (MessagingException e) {
					log.error("关闭store出错:" + e.getMessage());
				}
			}

		}
	}

	// 获取message-id
	public String getMessageId(MimeMessage mimeMessage) throws Exception {
		String messageId = "";
		try {
			messageId = mimeMessage.getMessageID();
		} catch (MessagingException e) {
			throw new Exception("获取message-id出错:" + e.getMessage());
		}
		return messageId;
	}

	// 解析邮件
	public void parseMessage(Message messages, String currentReceiveDate, String mailFileSize, String mailAddress, String tenant) {
		// 是否是统一排队(暂时自己设置，后面再改)
		MediaModel model = new MediaModel();
		try {
			MimeMessage mimeMessage = (MimeMessage) messages;
			String allType = mimeMessage.getContentType();
			String mediaId=getMessageId(mimeMessage);
			/* 原始邮件存储begin */

			
			log.info(mediaId+ "解析开始");
			model.setTaskId(commonDao.newMessageId());
			model.setMediaTitle(getSubject(mimeMessage));
			model.setMediaId(mediaId);
			model.setMediaSender(getFrom(mimeMessage));
			model.setMediaReceiver(mailAddress);

			model.setMediaSendTime(getSentDate(mimeMessage));
			model.setChannelType("1");
			model.setMediaCoper(getMailAddress(mimeMessage, "cc"));

			// 获取内嵌图片对应的content-id
			Map innerMap = new HashMap();
			// 包含附件
			List<String> nameList = null;
			if (isContainAttach((Part) messages)) {
				// 附件上传到ftp中
				String pathFile = "/mailReceiveFile/" + currentReceiveDate + "/" + model.getTaskId() + "/";
				nameList = saveAttachMent((Part) messages, pathFile, model, mailFileSize, innerMap);
			}
			// 文本内容放入到ftp文件中
			String pathTxt = "/mailReceiveTxt/" + currentReceiveDate + "/";
			String txtName = model.getTaskId() + ".html";

			// 如果正文中含图片 将html正文中图片的src给替换掉
			StringBuffer htmlContent = new StringBuffer();
			getMailHtmlContent((Part) messages, htmlContent, allType);

			String contentHtml = htmlContent.toString();
			Set key = innerMap.keySet();
			for (Iterator it = key.iterator(); it.hasNext();) {
				String innerFileName = (String) it.next();
				String srcName = (String) innerMap.get(innerFileName);
				if (contentHtml.indexOf(srcName) > -1) {
					contentHtml = contentHtml.replace(srcName, innerFileName);
				}
			}
			// 获取邮件正文编码格式
			Pattern p = Pattern.compile("<meta[^>]*?charset=(\\w+[-]?\\w+[-]?\\w+)[\\W]*?.*>", Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(contentHtml);
			String charSet = "";
			if (m.find()) {
				charSet = m.group(1);
			}
			log.info("邮件正文编码格式:" + charSet);
			model.setHtmlContent(contentHtml);

			log.info(contentHtml);
			// 通过HTML 过滤出纯文字
			model.setMediaContent(parseHtml(contentHtml));

			// // 存入到数据库中
			// mediaReceiveDao.addInitTask(model);

			saveTxt(contentHtml, pathTxt, txtName, charSet);

			// 记录文本上传路径
			model.setMediaTxtPath(pathTxt);
			model.setMediaTxtName(txtName);
			mediaReceiveDao.addTaskTxt(model);
			/* 原始邮件存储end */

			/* 邮件解析begin */

			// 未被拦截任务记录到表中
			model.setMediaIsAssign("0");

			model.setMediaIsDo("1");
			model.setSessionType("1");

			RequestSessionbean requestSessionbean = new RequestSessionbean(model.getMediaSender(), model.getMediaReceiver(), channelCode, tenant);

			log.info(model.getMediaReceiver());
			ChannelConfig channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + model.getMediaReceiver());
			if(channelConfig!=null){
			requestSessionbean.setSkillType(channelConfig.getRealTime());
			}
			SensitiveLog sensitiveLog = new SensitiveLog(channelCode, tenant, model.getMediaSender(), model.getMediaReceiver(), model.getMediaContent());
			if (sensitiveService.filter(sensitiveLog)) {
				log.info("邮件消息已经被过滤" + JSON.toJSONString(sensitiveLog));
			} else {
				log.info("请求的requestSessionbean" + JSON.toJSONString(requestSessionbean));
				SessionInfo sessioninfo = sessionService.getSession(requestSessionbean);
				try {
					// 封装消息体
					JSONObject json = new JSONObject();
					json.put("fromuser", model.getMediaSender());
					json.put("touser", model.getMediaReceiver());
					json.put("subject", model.getMediaTitle());
					json.put("content", model.getMediaContent());
					String attachment = "";
					if (nameList != null && nameList.size() > 0) {
						for (String name : nameList) {
							attachment = attachment + jsonInfo.getString("filePath") + name + ";";
						}
					}
					json.put("attachment", attachment);
					json.put("contentHtml", jsonInfo.getString("filePath") + pathTxt + txtName);

					model.setMediaFilePath(attachment);
					model.setHtmlContent(jsonInfo.getString("filePath") + pathTxt + txtName);

					MessageInfo message = MsgConvertUtil.media2Message(channelCode, model);
					message.setSessionId(sessioninfo.getSessionId());
					message.setTenantCode(jsonInfo.getString("tenant"));
					
					messageInfoDao.insert(message);
					JSONObject messageJson = (JSONObject) JSONObject.toJSON(message);
					messageJson.put("content", JSONObject.parse(message.getContent()));
					// 发送json
					//FIXME NetUtil
//					String resultString = NetUtil.send(initService.getSysParam(Constants.MIR_ADDRESS).getParamValue() + mirUrl, "POST", messageJson.toJSONString());
//					log.info("ReceiveThread 发送MIR 返回消息:" + resultString);
					message.setContent(JSONObject.parse(message.getContent()).toString());
					redisDao.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,message);

				} catch (Exception e) {
					log.error("存储数据异常!", e);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			// mediaReceiveDao.addReceiveErrorLog("1", e.getMessage(),
			// mailAddress, model.getMediaSender(), "2",
			// model.getMediaTitle(), model.getMediaContent(),
			// model.getTaskId());
		}

	}

	// 获取邮件主题
	public String getSubject(MimeMessage mimeMessage) throws Exception {
		String subject = "";
		try {
			subject = Util.filter(mimeMessage.getSubject());
			if (!"".equals(subject)) {
				subject = MimeUtility.decodeText(subject);
			}
		} catch (Exception exce) {
			log.error(exce.getMessage(), exce);
			throw new Exception("获取邮件主题出错:" + exce.getMessage());
		}
		return subject;
	}

	// 获取发件人地址和姓名
	public String getFrom(MimeMessage mimeMessage) throws Exception {
		String from = "";
		try {
			InternetAddress address[] = (InternetAddress[]) mimeMessage.getFrom();
			from = address[0].getAddress();
			if (from == null)
				from = "";
			String personal = address[0].getPersonal();
			if (personal == null)
				personal = "";
			String fromaddr = personal + "<" + from + ">";
		} catch (Exception e) {
			throw new Exception("获取发件人地址出错:" + e.getMessage());
		}
		return from;
	}

	// 发送时间
	public String getSentDate(MimeMessage mimeMessage) throws Exception {
		String date = "";
		try {
			Date sendDate = mimeMessage.getSentDate();

			if (sendDate == null) {
				return "";
			}
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			date = format.format(sendDate);
		} catch (Exception e) {
			throw new Exception("获取发送时间出错:" + e.getMessage());
		}
		return date;
	}

	// 获得邮件的收件人，抄送，和密送的地址和姓名，根据所传递的参数的不同 "to"----收件人 "cc"---抄送人地址 "bcc"---密送人地址
	public String getMailAddress(MimeMessage mimeMessage, String type) throws Exception {
		String mailaddr = "";
		String addtype = type.toUpperCase();
		InternetAddress[] address = null;
		try {
			if (addtype.equals("TO") || addtype.equals("CC") || addtype.equals("BCC")) {
				if (addtype.equals("TO")) {
					address = (InternetAddress[]) mimeMessage.getRecipients(Message.RecipientType.TO);
				} else if (addtype.equals("CC")) {
					address = (InternetAddress[]) mimeMessage.getRecipients(Message.RecipientType.CC);
				} else {
					address = (InternetAddress[]) mimeMessage.getRecipients(Message.RecipientType.BCC);
				}
				if (address != null) {
					for (int i = 0; i < address.length; i++) {
						String email = address[i].getAddress();
						if (email == null)
							email = "";
						else {
							email = MimeUtility.decodeText(email);
						}
						String personal = address[i].getPersonal();
						if (personal == null)
							personal = "";
						else {
							personal = MimeUtility.decodeText(personal);
						}
						String compositeto = personal + "<" + email + ">";
						mailaddr += "," + compositeto;
					}
					mailaddr = mailaddr.substring(1);
				}
			} else {

			}
		} catch (Exception e) {
			throw new Exception("获取邮件的收件人，抄送，和密送的地址出错:" + e.getMessage());
		}
		return mailaddr;
	}

	// 是否包含附件
	public boolean isContainAttach(Part part) {
		boolean attachflag = false;
		try {
			String contentType = part.getContentType();
			if (part.isMimeType("multipart/*")) {
				Multipart mp = (Multipart) part.getContent();
				for (int i = 0; i < mp.getCount(); i++) {
					BodyPart mpart = mp.getBodyPart(i);
					String disposition = mpart.getDisposition();
					if ((disposition != null) && ((disposition.equals(Part.ATTACHMENT)) || (disposition.equals(Part.INLINE))))
						attachflag = true;
					else if (mpart.isMimeType("multipart/*")) {
						attachflag = isContainAttach((Part) mpart);
					} else {
						String contype = mpart.getContentType();
						if (contype.toLowerCase().indexOf("application") != -1)
							attachflag = true;
						if (contype.toLowerCase().indexOf("name") != -1)
							attachflag = true;
					}
				}
			} else if (part.isMimeType("message/rfc822")) {
				attachflag = isContainAttach((Part) part.getContent());
			}
		} catch (Exception e) {
			log.error("判断是否有附件出错:" + e.getMessage());
			e.printStackTrace();
		}
		return attachflag;
	}

	// 保存附件
	public List saveAttachMent(Part part, String pathFile, MediaModel model, String mailFileSize, Map innerMap) {
		String fileName = "";
		List nameList = new ArrayList<String>();
		try {
			if (part.isMimeType("multipart/*")) {
				Multipart mp = (Multipart) part.getContent();
				for (int i = 0; i < mp.getCount(); i++) {
					BodyPart mpart = mp.getBodyPart(i);
					String disposition = mpart.getDisposition();
					if ((disposition != null) && ((disposition.equals(Part.ATTACHMENT)) || (disposition.equals(Part.INLINE)))) {
						fileName = mpart.getFileName();

						if (!"".equals(Util.filter(fileName))) {
							if (fileName.toLowerCase().indexOf("gbk") != -1 || fileName.toLowerCase().indexOf("utf-8") != -1 || fileName.toLowerCase().indexOf("gb") != -1) {
								fileName = MimeUtility.decodeText(fileName);
							}
							String fileName2 = saveFile(fileName, mpart.getInputStream(), pathFile, model, mailFileSize);
							nameList.add(pathFile + fileName2);

							String srcName = "";
							Enumeration e = mpart.getAllHeaders();
							while (e.hasMoreElements()) {
								javax.mail.Header h = (javax.mail.Header) e.nextElement();
								if ("Content-ID".equals(h.getName())) {
									srcName = "cid:" + h.getValue().replaceAll("<", "").replaceAll(">", "");
								}
							}
							if (!"".equals(Util.filter(srcName)) && !"".equals(Util.filter(fileName2))) {
								innerMap.put(fileName2, srcName);
							}
						}

					} else if (mpart.isMimeType("multipart/*")) {
						saveAttachMent(mpart, pathFile, model, mailFileSize, innerMap);
					} else {
						fileName = mpart.getFileName();

						if ((!"".equals(Util.filter(fileName)))) {
							if ((fileName.toLowerCase().indexOf("gbk") != -1 || fileName.toLowerCase().indexOf("utf-8") != -1 || fileName.toLowerCase().indexOf("gb") != -1)) {
								fileName = MimeUtility.decodeText(fileName);
							}
							String fileName2 = saveFile(fileName, mpart.getInputStream(), pathFile, model, mailFileSize);
							nameList.add(pathFile + fileName2);

							String srcName = "";
							Enumeration e = mpart.getAllHeaders();
							while (e.hasMoreElements()) {
								javax.mail.Header h = (javax.mail.Header) e.nextElement();
								if ("Content-ID".equals(h.getName())) {
									srcName = "cid:" + h.getValue().replaceAll("<", "").replaceAll(">", "");
								}
							}
							if (!"".equals(Util.filter(srcName)) && !"".equals(Util.filter(fileName2))) {
								innerMap.put(fileName2, srcName);
							}
						}
//						return nameList;
					}
				}
			} else if (part.isMimeType("message/rfc822")) {
				saveAttachMent((Part) part.getContent(), pathFile, model, mailFileSize, innerMap);
			}
			return nameList;
		} catch (Exception e) {
			log.error("保存附件出错:" + e.getMessage());
			e.printStackTrace();
		}
		return nameList;
	}

	// 解析邮件，获取邮件 html信息
	public void getMailHtmlContent(Part part, StringBuffer htmlContent, String allType) throws Exception {
		try {
			String contenttype = part.getContentType();
			int nameindex = contenttype.indexOf("name");
			boolean conname = false;
			if (nameindex != -1)
				conname = true;
			if (allType.toLowerCase().contains("text/plain")) {
				if (part.isMimeType("text/plain") && !conname) {
					htmlContent.append((String) part.getContent());
				} else if (part.isMimeType("multipart/*")) {
					Multipart multipart = (Multipart) part.getContent();
					int counts = multipart.getCount();
					for (int i = 0; i < counts; i++) {
						getMailHtmlContent(multipart.getBodyPart(i), htmlContent, allType);
					}
				} else if (part.isMimeType("message/rfc822")) {
					getMailHtmlContent((Part) part.getContent(), htmlContent, allType);
				} else {

				}
			} else {
				if (part.isMimeType("text/html") && !conname) {
					htmlContent.append((String) part.getContent());
				} else if (part.isMimeType("multipart/*")) {
					Multipart multipart = (Multipart) part.getContent();
					int counts = multipart.getCount();
					for (int i = 0; i < counts; i++) {
						getMailHtmlContent(multipart.getBodyPart(i), htmlContent, allType);
					}
				} else if (part.isMimeType("message/rfc822")) {
					getMailHtmlContent((Part) part.getContent(), htmlContent, allType);
				} else {
				}
			}

		} catch (Exception e) {
			throw new Exception("获取邮件html信息出错:" + e.getMessage());
		}
	}

	// 过滤html信息
	public String parseHtml(String htmlContent) {
		String content = "";
		try {
			Parser parse = new Parser();
			parse.setInputHTML(htmlContent);
			StringBean sb = new StringBean();
			sb.setLinks(false);
			sb.setReplaceNonBreakingSpaces(true);
			parse.visitAllNodesWith(sb);
			content = sb.getStrings();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return content;
	}

	public void saveTxt(String content, String pathTxt, String txtName, String charSet) {
		InputStream in = null;
		try {
			in = new ByteArrayInputStream(content.getBytes(StringUtils.isEmpty(charSet) ? "UTF-8" : charSet));
		} catch (Exception e) {
			e.printStackTrace();
		}
		saveTxtFile(txtName, in, pathTxt);
	}

	private void saveTxtFile(String txtName, InputStream in, String pathTxt) {
		// 将附件上传到指定FTP目录上去
		IdealFtpClient ftpClient = null;
		try {
			ftpClient = new IdealFtpClient();

			List list = commonDao.getSysParam("FTP_UPLOAD_URL");

			Map map = (Map) list.get(0);

			String code_desc = (String) map.get("PARAM_DESC");

			String[] ftp = code_desc.split(":");

			ftpClient.connectServer((String) map.get("PARAM_VALUE"), ftp[0], ftp[1],ftp[2]);

			ftpClient.changeDir(pathTxt);

			ftpClient.uploadFile(in, txtName);

			ftpClient.close();
		} catch (Exception e1) {
			log.error("文本上传FTP出错:" + e1.getMessage(), e1);
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
	}

	// 自动回复告知邮件已收到请等待处理

	private String saveFile(String fileName, InputStream in, String pathFile, MediaModel model, String mailFileSize) {
		// 将附件上传到指定FTP目录上去
		IdealFtpClient ftpClient = null;
		String fileName2 = "";
		try {
			// 做两次转码
			fileName = URLEncoder.encode(fileName, "utf-8");
			if (!"".equals(Util.filter(mailFileSize))) {
				if (in.available() > Integer.parseInt(mailFileSize) * 1024 * 1024) {
					return "";
				}
			}
			ftpClient = new IdealFtpClient();

			List list = commonDao.getSysParam("FTP_UPLOAD_URL");

			Map map = (Map) list.get(0);

			String code_desc = (String) map.get("PARAM_DESC");

			String[] ftp = code_desc.split(":");

			ftpClient.connectServer((String) map.get("PARAM_VALUE"), ftp[0], ftp[1],ftp[2]);

			ftpClient.changeDir(pathFile);

			if (ftpClient.isExists(fileName)) {
				fileName2 = "[1]" + fileName;
			} else {
				fileName2 = fileName;
			}

			ftpClient.uploadFile(in, fileName2);

			ftpClient.close();

			// 记录上传路径
			model.setMediaFileName(fileName2);
			model.setMediaFilePath(pathFile);
			mediaReceiveDao.addTaskFile(model);
			fileName2 = URLEncoder.encode(fileName2, "utf-8");

		} catch (Exception e1) {
			log.error("附件上传FTP出错:" + e1.getMessage(), e1);
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
		return fileName2;
	}

	// public boolean isB() {
	// return b;
	// }
	//
	// public void setB(boolean b) {
	// this.b = b;
	// }

	public MediaReceiveDao getMediaReceiveDao() {
		return mediaReceiveDao;
	}

	public void setMediaReceiveDao(MediaReceiveDao mediaReceiveDao) {
		this.mediaReceiveDao = mediaReceiveDao;
	}

	public InterceptFactory getInterceptFactory() {
		return interceptFactory;
	}

	public void setInterceptFactory(InterceptFactory interceptFactory) {
		this.interceptFactory = interceptFactory;
	}

	public JSONObject getJson() {
		return jsonInfo;
	}

	public void setJson(JSONObject json) {
		this.jsonInfo = json;
	}

	public static void main(String[] args) {
	}

}
