package cn.sh.ideal.mgw.dao;

import java.io.Serializable;

import cn.sh.ideal.mgw.model.ReceiveMessageLog;

/**
 * 接受信息Dao
 * <AUTHOR>
 * @date 2014年5月27日
 */

public interface ReceiveMessageLogDao extends GenericDao<ReceiveMessageLog, Serializable> {

	
	/**
	 * 获取最新的channelmessageId
	 * 
	 * @return
	 */
	public String getChannelMessageId(ReceiveMessageLog receiveMessageLog);
	
	
	/**
	 * 根据channel_Message_Id
	 * 
	 */
	public ReceiveMessageLog loadByChannelMessageId(ReceiveMessageLog receiveMessageLog);
}
