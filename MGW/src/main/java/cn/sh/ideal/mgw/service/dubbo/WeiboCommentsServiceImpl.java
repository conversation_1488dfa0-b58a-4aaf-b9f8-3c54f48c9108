package cn.sh.ideal.mgw.service.dubbo;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;








import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import weibo4j.http.HttpClient;
import weibo4j.http.Response;
import weibo4j.model.WeiboException;
import weibo4j.util.WeiboConfig;


import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.dao.CommonDao;
import cn.sh.ideal.mgw.dao.MediaReceiveDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.dao.MessageInfoSendDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageLogDao;
import cn.sh.ideal.mgw.dao.ReceiveMessageTempDao;
import cn.sh.ideal.mgw.dao.WeiboCommentsDao;
import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.mgw.service.WeiboCommentsSumService;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.model.MessageInfoSend;
import cn.sh.ideal.model.WeiboComentsSum;
import cn.sh.ideal.util.BaseController;

/**
 * 获取微博评论数，点赞数，转发数接口
 * <AUTHOR>
 *
 */

@Component("weiboCommentsSumService")
public class WeiboCommentsServiceImpl extends BaseController implements WeiboCommentsSumService{
	
	@Autowired
	private WeiboCommentsDao weiboCommentsDao;
	
	private static final Logger log = Logger.getLogger(WeiboCommentsServiceImpl.class);
	
	private HttpClient client = new HttpClient();
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private MessageInfoSendDao messageSendDao;
	
	public WeiboCommentsDao getWeiboCommentsDao() {
		return weiboCommentsDao;
	}


	public void setWeiboCommentsDao(WeiboCommentsDao weiboCommentsDao) {
		this.weiboCommentsDao = weiboCommentsDao;
	}


	private ApplicationContext context;
	
	
	//获取微博评论数，转发数，点赞数
	@Override
	public List<WeiboComentsSum> weiboComentSumSend(WeiboComentsSum message) {
		String weiboids = "";
		List<WeiboComentsSum> weiboComentsList1 =  new ArrayList<WeiboComentsSum>();
		if(!"".equals(message.getId()) && null!= message.getId()){
			try {
				weiboids =message.getId();
				WeiboComentsSum wcs = new WeiboComentsSum();
				wcs.setId(weiboids);
				weiboComentsList1 = weiboCommentsDao.query(wcs);
			} catch (Exception e) {
				message.setMessge("获取数据异常："+e);
				weiboComentsList1.add(message);
			}
			
			if(weiboComentsList1.size()>0 && weiboComentsList1 !=null){
				return weiboComentsList1;
			}else{
				WeiboComentsSum wcs1 = new WeiboComentsSum();
				wcs1.setId(weiboids);
				wcs1.setAttitudes(0);
				wcs1.setReposts(0);
				wcs1.setComments(0);
				weiboComentsList1.add(wcs1);
				return weiboComentsList1;
			}
			
			
		}else{
			log.info("进入了定时刷新评论数的方法");
			List<WeiboMsgInfo> weiboMsgInfoList = null;
			MessageInfoSend messageInfoSend = new MessageInfoSend();
			messageInfoSend.setType("1");
			messageInfoSend.setMsgType("weibo");
			weiboMsgInfoList = messageSendDao.historyInfoquery(messageInfoSend);
			
			
			
			if(weiboMsgInfoList!=null && weiboMsgInfoList.size()>0){
				for(WeiboMsgInfo weiboMsgInfo:weiboMsgInfoList){
					weiboids +=weiboMsgInfo.getWeiboId()+",";
				}
				List<WeiboComentsSum>  listComents = new ArrayList<WeiboComentsSum>();
				log.info("微博原始id:为："+weiboids);
				 weiboids = weiboids.substring(0,weiboids.length()-1);
				 String[] ar = weiboids.split(",");
					int splitSize = 99;//分割的块大小  
			        Object[] subAry = splitAry(ar, splitSize);//分割后的子块数组  
			        log.info("数组的大小：===="+subAry.length);
			        for(Object obj: subAry){//打印输出结果  
			        	String b="";
			        	String weiboidss = "";
			            String[] aryItem = (String[]) obj;  
			             for(int i = 0; i < aryItem.length; i++){  
			                  b +=aryItem[i] + ",";  
			             }
			             weiboidss = b.substring(0, b.length()-1);
			            
			             
			             String access_token = message.getAccessTokens();
			 			
			 			//client.setToken(message.getAccessTokens());
			 			client.setToken(access_token);
			 			try {
			 				if(!"".equals(weiboidss)){
			 					log.info("weiboidss----------------------"+weiboidss);
			 					Response r =  client.get(WeiboConfig.getValue("baseURL") + "statuses/count.json?ids="+weiboidss);
			 					String weiboComents = r.getResponseAsString();
			 					JSONArray jsonarray = JSONArray.fromObject(weiboComents);  
			 					//把json数组转换成list对象
			 					listComents = JSONArray.toList(jsonarray, new WeiboComentsSum(), new JsonConfig());  
			 					
			 					//将数据入库
			 					if(listComents.size()>0 && listComents !=null){
			 						for(WeiboComentsSum weibocoments:listComents){
			 							//首先要根据微博id判断是否库里存在数据，如果存在，更新库里数据
			 							//weiboCommentsDao.query(t);
			 							try {
			 								List<WeiboComentsSum> weiboComentsList = weiboCommentsDao.query(weibocoments);
			 								if(weiboComentsList!=null && weiboComentsList.size()>0){
			 									weiboCommentsDao.update(weibocoments);
			 									//log.info("执行修改");
			 								}else{
			 									weiboCommentsDao.insert(weibocoments);
			 									//log.info("执行添加");
			 								}
			 								log.info("网数据库里插入数据完成");
			 								message.setMessge("操作成功");
			 							} catch (Exception e) {
			 								log.info("操作评论数数据库异常："+e);
			 								message.setMessge("执行异常");
			 								e.printStackTrace();
			 							}
			 							//如果不存在，就往库里插入数据
			 						}
			 					}
			 					System.out.println(listComents.size());
			 				//	WeiboComentsSum messages = JSONObject.parseObject(r.getResponseAsString(), WeiboComentsSum.class);
			 					//String r ='[{"id":3990981332879499,"comments":14,"reposts":0,"attitudes":0}]';
			 				}else{
			 					message.setMessge("微博ID不能为空");
			 				}
			 				
			 			} catch (WeiboException e) {
			 				// TODO Auto-generated catch block
			 				log.info("调用微博评论数信息异常"+e);
			 				e.printStackTrace();
			 				message.setMessge("异常"+e);
			 				listComents.add(message);
			 			}
			        }
			        return listComents;
			}else{
				log.info("历史微博数据为空！");
			}
		}
		return null;
	}

	
	/*public static CommentWapper constructWapperCommentss(Response res) throws WeiboException {
		//res.toString();
		String a = res.getResponseAsString().substring(1,res.getResponseAsString().length()-1);
		//String b = a.substring(0,a.length()-1);
		res.setResponseAsString(a);
		weibo4j.org.json.JSONObject json = res.asJSONObject(); //asJSONArray();
		//weibo4j.org.json.JSONObject json =res.asJSONObject();//asJSONArray();
		try {
			String comments = (String) json.get("comments");
			int size = comments.length();
			List<Comment> comment = new ArrayList<Comment>(size);
			for (int i = 0; i < size; i++) {
				comment.add(new Comment(comments.get));
			}
			long previousCursor = json.getLong("previous_curosr");
			long nextCursor = json.getLong("next_cursor");
			long totalNumber = json.getLong("total_number");
			String hasvisible = json.getString("hasvisible");
			return new CommentWapper(comment, previousCursor, nextCursor,totalNumber,hasvisible);
		} catch (JSONException jsone) {
			throw new WeiboException(jsone);
		}
	}*/
	
	
	
	public static void main(String arg[]){
		String weiboids="3991397420299700,3991401153052934,3991414285766818,3996438340043461,3996483227712453,"
+"3998579213265108";
		
		String[] ar = weiboids.split(",");
		System.out.println(ar.length);
		
		int splitSize = 5;//分割的块大小  
        Object[] subAry = splitAry(ar, splitSize);//分割后的子块数组  
           
        for(Object obj: subAry){//打印输出结果  
        	String b="";
        	String c = "";
            String[] aryItem = (String[]) obj;  
             for(int i = 0; i < aryItem.length; i++){  
                  b +=aryItem[i] + ",";  
             }
             c = b.substring(0, b.length()-1);
             System.out.println(c);
        }
	}
	
	
	
	
	private static Object[] splitAry(String[] ary, int subSize) {  
        int count = ary.length % subSize == 0 ? ary.length / subSize: ary.length / subSize + 1;  

        List<List<String>> subAryList = new ArrayList<List<String>>();  

        for (int i = 0; i < count; i++) {  
         int index = i * subSize;  
         List<String> list = new ArrayList<String>();  
         int j = 0;  
             while (j < subSize && index < ary.length) {  
                  list.add(ary[index++]);  
                  j++;  
             }  
         subAryList.add(list);  
        }  
          
        Object[] subAry = new Object[subAryList.size()];  
          
        for(int i = 0; i < subAryList.size(); i++){  
             List<String> subList = subAryList.get(i);  
             String[] subAryItem = new String[subList.size()];  
             for(int j = 0; j < subList.size(); j++){  
                 subAryItem[j] = subList.get(j).toString();  
             }  
             subAry[i] = subAryItem;  
        }  
          
        return subAry;  
       }
	
	
}
