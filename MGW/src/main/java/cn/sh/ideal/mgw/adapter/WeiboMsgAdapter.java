package cn.sh.ideal.mgw.adapter;


import java.text.ParseException;
import java.text.SimpleDateFormat;

import cn.sh.ideal.mgw.base.WeiboInf;
import cn.sh.ideal.mgw.model.MediaModel;
import cn.sh.ideal.mgw.model.PlatformMessage;
import cn.sh.ideal.mgw.service.local.impl.BaseService;
import cn.sh.ideal.model.MessageInfo;

import com.alibaba.fastjson.JSONObject;

/**
 * 微博消息适配器
 * 
 * <AUTHOR>
 * 2015-3-9
 *
 */
public class WeiboMsgAdapter extends MsgAdapter{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4433223024779186658L;
	private SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Override
	public MessageInfo media2Message(String channelCode, Object data) {
		MessageInfo message = null;
		if(null != data){
			if(data instanceof PlatformMessage){
				PlatformMessage pm = (PlatformMessage)data;
				message = new MessageInfo();
				message.setChannelCode(channelCode);
				message.setSource(pm.getSource());
				message.setSendAccount(pm.getOpenId());
				message.setAcceptedAccount(pm.getToUser());
				message.setMsgType(pm.getMsgType().toString());
				this.assembSendContent(pm, message);
			//微博类型
			}else if(data instanceof MediaModel){
				message = new MessageInfo();
				MediaModel media = (MediaModel)data;
				message.setMessageId(media.getTaskId());
				message.setChannelCode(channelCode);
				message.setNickname(media.getMediaSender());//发送方的昵称
				message.setSendAccount(media.getUid());//发送人的userid
				message.setAcceptedAccount(media.getMediaReceiver());//uid
				message.setUserId(media.getUserID());//用户id为空
				message.setBusinessType(media.getMediaBusinessType());//业务类型为空
				try {
					
					message.setCreateTime(sf.parse(media.getMediaSendTime()));//创建时间(渠道带过来的创建消息时间)
					message.setSendTime(sf.parse(media.getMediaReceiveTime()));//接收或发送消息时间
				} catch (ParseException e) {
					throw new RuntimeException("格式转化异常");
				}
				message.setMsgType(Constants.MEDIA_TYPE_WEIBO);
				this.assembSendContent(media, message);
			}
		}
		return message;
	}

	@Override
	public JSONObject message2Media(MessageInfo message) {
		
		JSONObject json = new JSONObject();
		JSONObject jsonContent=(JSONObject)BaseService.getJsonObj(message.getContent());
		JSONObject jsonReq=new JSONObject();
		//微博接口请求类型
		String  operType=(String)jsonContent.get(Constants.MSG_PARAM_OPERTYPE);
		WeiboInf weiBoType=WeiboInf.codeOf(operType);
		jsonReq=JSONObject.parseObject(jsonContent.get(Constants.MSG_PARAM_PARAM).toString());
		switch (weiBoType) {
		//发布一条新微博
		case WB_UPDATESTATUS:
			json.put(Constants.WB_PARAM_STATUS,jsonReq.get(Constants.WB_PARAM_STATUS));
			json.put(Constants.WB_PARAM_VISIBLE,jsonReq.get(Constants.WB_PARAM_VISIBLE));
			json.put(Constants.WB_PARAM_LISTID,jsonReq.get(Constants.WB_PARAM_LISTID));
			json.put(Constants.WB_PARAM_LAT, jsonReq.get(Constants.WB_PARAM_LAT));
			json.put(Constants.WB_PARAM_LONGS, jsonReq.get(Constants.WB_PARAM_LONGS));
			json.put(Constants.WB_PARAM_PIC, jsonReq.get(Constants.WB_PARAM_PIC));
			json.put(Constants.MSG_PARAM_ANNOTATIONS,jsonReq.get(Constants.MSG_PARAM_ANNOTATIONS));
			json.put(Constants.MEDIA_PARAM_RIP,jsonReq.get(Constants.MEDIA_PARAM_RIP));
			
			break;
		//对一条微博进行评论
		case WB_CREATRCOMMENT:
			json.put(Constants.MEDIA_PARAM_COMMENT,jsonReq.get(Constants.MEDIA_PARAM_COMMENT));
			json.put(Constants.MEDIA_PARAM_ID,jsonReq.get(Constants.MEDIA_PARAM_ID));
			json.put(Constants.MEDIA_PARAM_COMMENT_ORI,jsonReq.get(Constants.MEDIA_PARAM_COMMENT_ORI));
			json.put(Constants.MEDIA_PARAM_RIP, jsonReq.get(Constants.MEDIA_PARAM_RIP));
			break;
		//回复一条评论
		case WB_REPLAYCOMMENT:
			json.put(Constants.MEDIA_PARAM_CID,jsonReq.get(Constants.MEDIA_PARAM_CID));
			json.put(Constants.MEDIA_PARAM_ID,jsonReq.get(Constants.MEDIA_PARAM_ID));
			json.put(Constants.MEDIA_PARAM_COMMENT,jsonReq.get(Constants.MEDIA_PARAM_COMMENT));
			json.put(Constants.WB_PARAM_WITHOUTMENTION,jsonReq.get(Constants.WB_PARAM_WITHOUTMENTION));
			json.put(Constants.MEDIA_PARAM_COMMENT_ORI,jsonReq.get(Constants.MEDIA_PARAM_COMMENT_ORI));
			json.put(Constants.MEDIA_PARAM_RIP, jsonReq.get(Constants.MEDIA_PARAM_RIP));
			break;
		//根据微博ID删除指定微博
		case WB_DESTROY:
			json.put(Constants.MEDIA_PARAM_ID,jsonReq.get(Constants.MEDIA_PARAM_ID));
			break;
		//批量获取指定微博的转发数评论数
		case WB_GETSTATUSCOUNT:
			json.put(Constants.WB_PARAM_IDS,jsonReq.get(Constants.WB_PARAM_IDS));
			break;
		//批量获取用户的粉丝数、关注数、微博数
		case WB_GETUSERCOUNT:
			json.put(Constants.WB_PARAM_UIDS,jsonReq.get(Constants.WB_PARAM_UIDS));
			break;
		//获取微博官方表情的详细信息
		case WB_GETEMOTIONS:
			json.put(Constants.WB_PARAM_TYPE,jsonReq.get(Constants.WB_PARAM_TYPE));
			json.put(Constants.WB_PARAM_LANGUAGE,jsonReq.get(Constants.WB_PARAM_LANGUAGE));
			break;
		default:
			throw new RuntimeException("暂不支持该接口类型");
		}
		json.put(Constants.MSG_PARAM_OPERTYPE, operType);
		json.put("sendType", message.getSendType());
		return json;
	}
	
	/**
	 * 拼装Message对象的content字段
	 * 
	 * @param media
	 * @param message
	 */
	private void assembSendContent(MediaModel media,MessageInfo message){
		String operType=media.getOperType();
		WeiboInf weiBoType=WeiboInf.codeOf(operType);
		JSONObject jsonParam = new JSONObject();
		switch (weiBoType) {
		//@我的微博
		case WB_GETMENTIONS:
		//获取用户发布的微博列表
		case WB_GETUSERTIMELINEBYUID:
			jsonParam.put(Constants.MSG_PARAM_CREATEED_TIME, media.getMediaReceiveTime());
			jsonParam.put(Constants.MSG_PARAM_WEIBOID,media.getMediaId());
			jsonParam.put(Constants.MSG_PARAM_TEXT, media.getMediaContent());
			jsonParam.put(Constants.MSG_PARAM_SOURCE, media.getWbSource());//微博来源
			jsonParam.put(Constants.MSG_PARAM_FAVOURITED, media.isFavorited());//是否已收藏
			jsonParam.put(Constants.MSG_PARAM_TRUNCATED, media.isTruncated());//是否被截断
			jsonParam.put(Constants.MSG_PARAM_GEO, media.getGeo());
			jsonParam.put(Constants.MSG_PARAM_MID, media.getMid());
			jsonParam.put(Constants.MSG_PARAM_REPOSTS_COUNT, media.getMediaFowardNum());
			jsonParam.put(Constants.MSG_PARAM_COMMENTS_COUNT, media.getMediaCommentsNum());
			jsonParam.put(Constants.MSG_PARAM_THUMBNAIL_PIC, media.getSmallPic());
			jsonParam.put(Constants.MSG_PARAM_BMIDDLE_PIC, media.getMiddlePic());
			jsonParam.put(Constants.MSG_PARAM_ORIGINAL_PIC, media.getOriginalPic());
			jsonParam.put(Constants.WB_PARAM_VISIBLE, media.getVisible());
			jsonParam.put(Constants.MSG_PARAM_ANNOTATIONS, media.getAnnotations());
			jsonParam.put(Constants.MSG_PARAM_PIC_IDS, media.getPicUrls());
			
			JSONObject wbjsonsAt = new JSONObject();
			wbjsonsAt.put("text", media.getMediaContent());
			
			//TODO 原始微博微博图片
			wbjsonsAt.put("picurl", media.getPicUrls());
			jsonParam.put(Constants.MSG_PARAM_WEIBOINFO, wbjsonsAt);
			break;
		case WB_GETSTATUSREPORTTIMELINE:
			jsonParam.put(Constants.MSG_PARAM_CREATEED_TIME, media.getMediaReceiveTime());
			jsonParam.put(Constants.MSG_PARAM_WEIBOID,media.getYuanWeiboId());
			jsonParam.put(Constants.MSG_PARAM_TEXT, media.getMediaContent());
			jsonParam.put(Constants.MSG_PARAM_SOURCE, media.getWbSource());//微博来源
			jsonParam.put(Constants.MSG_PARAM_FAVOURITED, media.isFavorited());//是否已收藏
			jsonParam.put(Constants.MSG_PARAM_TRUNCATED, media.isTruncated());//是否被截断
			jsonParam.put(Constants.MSG_PARAM_GEO, media.getGeo());
			jsonParam.put(Constants.MSG_PARAM_MID, media.getMid());
			jsonParam.put(Constants.MSG_PARAM_REPOSTS_COUNT, media.getMediaFowardNum());
			jsonParam.put(Constants.MSG_PARAM_COMMENTS_COUNT, media.getMediaCommentsNum());
			jsonParam.put(Constants.MSG_PARAM_THUMBNAIL_PIC, media.getSmallPic());
			jsonParam.put(Constants.MSG_PARAM_BMIDDLE_PIC, media.getMiddlePic());
			jsonParam.put(Constants.MSG_PARAM_ORIGINAL_PIC, media.getOriginalPic());
			jsonParam.put(Constants.WB_PARAM_VISIBLE, media.getVisible());
			jsonParam.put(Constants.MSG_PARAM_ANNOTATIONS, media.getAnnotations());
			jsonParam.put(Constants.MSG_PARAM_PIC_IDS, media.getPicUrls());
			
			JSONObject wbjsonZF = new JSONObject();
			wbjsonZF.put("text", media.getStatus().getText());
			
			//TODO 原始微博微博图片
			wbjsonZF.put("picurl", media.getPicUrls());
			jsonParam.put(Constants.MSG_PARAM_WEIBOINFO, wbjsonZF);
			break;
		//@我的评论
		case WB_GETCOMMENTMENTIONS:
		//根据微博id获取评论列表
		case WB_GET_COMMENT_LIST:
		case WB_GETREPLYCOMMENT:
		case WB_GETCOMMENTSBYUSER:
			jsonParam.put(Constants.MSG_PARAM_CREATEED_TIME, media.getMediaReceiveTime());
			jsonParam.put(Constants.MSG_PARAM_COMMENTID, media.getMediaId());
			/*if(!media.getRepId().isEmpty()){
				jsonParam.put(Constants.MSG_PARAM_REPLYID, media.getMediaId());
				jsonParam.put(Constants.MSG_PARAM_COMMENTID, media.getRepId());
			}*/
			jsonParam.put(Constants.MSG_PARAM_TEXT, media.getMediaContent());
			jsonParam.put(Constants.MSG_PARAM_SOURCE, media.getCoSource());//微博来源
			jsonParam.put(Constants.MSG_PARAM_REPLYCOMMENT, media.getReplyComment());//微博来源
			jsonParam.put(Constants.MSG_PARAM_MID, media.getMid());//微博来源
			jsonParam.put(Constants.MSG_PARAM_WEIBOID, media.getWbId());
			JSONObject wbjson = new JSONObject();
			wbjson.put("text", media.getStatus().getText());
			
			//TODO 微博图片
			wbjson.put("picurl", media.getStatus().getPicUrls());
			jsonParam.put(Constants.MSG_PARAM_WEIBOINFO, wbjson);
			jsonParam.put(Constants.MSG_PARAM_WEIBOCONTENT, media.getHtmlContent());
			
			break;

		default:
			throw new NullPointerException("暂不支持该接口类型" + operType);
		}
	
		JSONObject json = new JSONObject();
		json.put(Constants.MSG_PARAM_OPERTYPE, operType);
		json.put(Constants.MSG_PARAM_PARAM, jsonParam);
		
		message.setContent(json.toJSONString());
	}
	
	/**
	 * 拼装Message对象的content字段
	 * 
	 * @param pm
	 * @param message
	 */
	private void assembSendContent(PlatformMessage pm,MessageInfo message){
		
		switch (pm.getMsgType()) {
			case text:message.setContent(pm.getContent());break;
			case image:message.setContent(pm.getPicUrl());break;
			case voice:message.setContent("");break;
			case location:{
				JSONObject object = new JSONObject();
				object.put(Constants.MSG_PARAM_LOCATION_X, pm.getX());
				object.put(Constants.MSG_PARAM_LOCATION_Y, pm.getY());
				
				message.setContent(object.toJSONString());break;
			}
	
			default:
				throw new NullPointerException("暂不支持该消息类型" + pm.getMsgType());
		}
	}
}
