package cn.sh.ideal.mgw.dao;

import java.util.List;

import cn.sh.ideal.mgw.model.BlackList;
import cn.sh.ideal.mgw.model.User;
import cn.sh.ideal.mgw.model.WhiteList;


/**
 * 一些公用Dao
 * 
 * <AUTHOR>
 * @date 2014年6月4日
 */
public interface CommonDao {
	/**
	 * 创建新的MessageId
	 * 
	 * @return
	 */
	public String newMessageId();
	
	
	public List getSysParam(String code);
	
	

	public String getSysParamValue(String code);
	/**
	 * 根据租户ID获取租户白名单
	 * 
	 * @param tenantId
	 * @return
	 */
	public List<WhiteList> getTenantWhiteList(String tenantId);
	/**
	 * 根据租户ID获取租户黑名单
	 * 
	 * @param tenantId
	 * @return
	 */
	public List<BlackList> getTenantBlackList(String tenantId);

	/**
	 * 获取签入总时长
	 * 
	 * @param user
	 * @return
	 */
	public int getUserSignTotailTime(User user);

	/**
	 * 获取示忙总时长
	 * 
	 * @param user
	 * @return
	 */
	public int getUserBusyTotailTime(User user);

}
