package cn.sh.ideal.mgw.utils.sender;

import cn.sh.ideal.mgw.base.Platform;



public class SenderFactory {
	
	private static WechatSender wechatSender;
	
	private static YichatSender yichatSender;
	
	private static WeiboSender weiboSender;
	
	private static AlipaySender AlipaySender;
	private static ToolBarSender toolBarSender;
	
	private static EnterpriseSender enterpriseSender;
	private static SmsLSender smsLSender;
	
	public static Sender getSender(String platform) {
		Platform p = Platform.valueOf(platform);
		switch (p) {
		case WX:
			if(wechatSender == null) {
				wechatSender = new WechatSender();
			}
			return wechatSender;
		case YX:
			if(yichatSender == null) {
				yichatSender = new YichatSender();
			}
			return yichatSender;
		case SINAWEIBO:
			if(weiboSender == null) {
				weiboSender = new WeiboSender();
			}
			return weiboSender;
		case ALIPAY:
			if(AlipaySender == null) {
				AlipaySender = new AlipaySender();
			}
			return AlipaySender;
		case QY:
			if(enterpriseSender == null) {
				enterpriseSender = new EnterpriseSender();
			}
			return enterpriseSender;
		case TOOLBAR:
		case SHWX:
		case SHAPP:
		case HAIRSMS:
		case SMS:
			if(smsLSender == null){
				smsLSender = new SmsLSender();
			}
			return smsLSender;
					
		case WEBCHAT:
			if(toolBarSender == null) {
				toolBarSender = new ToolBarSender();
			}
			return toolBarSender;
			
		default:
			return null;
		}
	}

	public static void main(String[] args) throws Exception{
		
		
		
		
//		System.out
//				.println(uploadFile(
//						"{\"msgtype\":\"image\", \"url\":\"http://localhost:8080/mp3server/2014-06-11_134437.png\", \"fileName\":\"2014-06-11_134437.png\"}",
//						"kfdQtqiwbnCeHABqq7Q8tRUcoIyD0YsznYiYGKgrpN1mZaaBjr04g7vmAbAdO_Vzlk8-woLWU6aJgWYbEtBOlw",
//						"WX"));
		
		//1970502306b3043b
		//oKz2-t15OanyB9-Mb1ciINtNRGus
		
		System.out
		.println(SenderFactory
				.getSender("ALIPAY")
				.customSend(
						"{\"touser\":\"2088302727792904\", \"fromuser\":\"2014032500004404\", \"msgtype\":\"text\", \"text\":{\"content\":\"测试标题\",\"title\":\"测试标题\", \"description\":\"测试详情\", \"picUrl\":\"http://png.findicons.com/static/images/logo/not_found.png\", \"url\":\"h/ttp://www.baidu.com\"}}",
						"MIICeQIBADANBgkqhkiG9w0BAQEFAASCAmMwggJfAgEAAoGBAMPxx+0x57gZ2bhM+ctAxR0ZaDclj5GMFSvSIoucfwdzYw9DTj8sJKOfhnCouuC8TLE7IE9JkoQ+1nkY2wFjLGYaDgrNxbVyaAaSr51PjKpTxISx9M1dqgKJSP0sOlJAo4b7kdkUjbbhrXnE0iSXQ5/VXNsVsLvnQxWWN+Lpe8y5AgMBAAECgYEAgDT2DIQHytTXORjcpJoHn0edgd/ItYID5D1FLzZBg52DJlxymTEH9CuP6qzsQdnTEG/QbzCDdpYq/Oc/fQPn6u0kY0/sZAeKYGFjRby4guAnzc8O2ibQ796Hw1kanKxHSOsqwztjdTLTSnzBpW7NvdH8k4mQ458qH1G9Lu2wZZECQQDqRLyT2r4ioJHgfcjeyQGDJPX/n1hFKPitZx32YLqxgcDU/eqos3t/T7skjBV2a8sKA7UT3SwnykGaEkplvcyVAkEA1h7y2O/R6PF6re8jW6u3VHwbkl9WJi/RgtwWc58058TX+vyba1pTtFHPCAgZ6E5ZoPDd/vttmWKFbrDAsYxSlQJBAKubFGReXN8yrgk7OtRve6scfRL2H4vx2k/zdFBRRRDYUm/zXFydtT5qIlVAREyN45p/YrqdTkYLc4iwE015/g0CQQCeRZQmjOaxdEXFad3N9hj2hyJuSLBKW9BXFU43OviFVTh82tobOVJkGJCbFDnqQHf6AfFmd/1kgrG0x0w4MUwdAkEAzC6pQuVYuOQGpp/va9tGbCIi37zAm9YLG619Xu25jBa8XNy8WZWuobCE+LamO+/oj3qOnyXDggOTvYVaKf8ASg=="));
		//String json="dfdfdfafa";
		//java.util.Date date = new java.util.Date();
		//System.out.println(date);
		/*XmlObject object = new XmlObject("XML");
		
		object.addSubXmlObject(new XmlObject("ToUserId","jfdfjs"));
		object.addSubXmlObject(new XmlObject("AppId","fjidjfa"));
		object.addSubXmlObject(new XmlObject("AgreementId","2013080800008888"));
		object.addSubXmlObject(new XmlObject("MsgType","text"));
		object.addSubXmlObject(new XmlObject("CreateTime",System.currentTimeMillis()));
		object.addSubXmlObject(new XmlObject("ArticleCount","1"));
		XmlObject object1 =  new XmlObject("Articles", "");
		XmlObject object2 =  new XmlObject("Item", "");
		object1.addSubXmlObject(object2);
		object.addSubXmlObject(object1);
	    object2.addSubXmlObject(new XmlObject("Title","這是標題"));
		object2.addSubXmlObject(new XmlObject("Desc","這是內容"));
		object2.addSubXmlObject(new XmlObject("ImageUrl","20885678888"));
		object2.addSubXmlObject(new XmlObject("Url","https://openapi.alipay.com/gateway.do"));
		object.addSubXmlObject(new XmlObject("Push","false"));
		
		System.out.println(object.toFormatXml());
		AlipayClient client = new DefaultAlipayClient( "https://openapi.alipay.com/gateway.do", "2013082400025341", "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMiAec6fsssguUoRN3oEVEnQaqBLZjeafXAxCbKH3MTJaXPmnXOtqFFqFtcB8J9KqyFI1+o6YBDNIdFWMKqOwDDWPKqtdo90oGav3QMi kjGYjIpe/gYYCQ/In/oVMVj326GmKrSpp0P+5LNCx59ajRpO8//rnOLd6h/tNxnfahAECgYEAusouMFfJGsIWvLEDbPIhkE7RNxpnVP/hQqb8sM0v2EkHrAk5wG4VNBvQwWe2QsAuY6jYNgdCPgTNL5fLaOnqkyy8IobrddtT/t3vDX96NNjHP4xfhnMbpGjkKZuljWKduK2FAh83eegrSH48TuWS87LjeZNHhr5x4C0KHeBTYekCQQD5cyrFuKua6GNG0dTj5gA67R9jcmtcDWgSsuIXS0lzUeGxZC4y/y/76l6S7jBYuGkz/x2mJaZ/b3MxxcGQ01YNAkEAzcRGLTXgTMg33UOR13oqXiV9cQbraHR/aPmS8kZxkJNYows3K3umNVjLhFGusstmLIY2pIpPNUOho1YYatPGgwJBANq8vnj64p/Hv6ZOQZxGB1WksK2Hm9TwfJ5I9jDu982Ds6DV9B0L4IvKjHvTGdnye234+4rB4SpGFIFEo+PXLdECQBiOPMW2cT8YgboxDx2E4bt8g9zSM5Oym2Xeqs+o4nKbcu96LipNRkeFgjwXN1708QuNNMYsD0nO+WIxqxZMkZsCQHtS+Jj/LCnQZgLKxXZAllxqSTlBln2YnBgk6HqHLp8Eknx2rUXhoxE1vD9tNmom6PiaZlQyukrQkp5GOMWDMkU=","object"); 
		// 封装好的接口请求对象，接口不同请求对象也不同
		AlipayMobilePublicMenuAddRequest request = new AlipayMobilePublicMenuAddRequest(); 
		 
		// 传入接口中规定的业务参数
		//request.setBizContent(GeneralMenuMockData.generalNormalMenu1()); 
		 
		// 封装好的接口响应对象，接口不同响应对象也不同
		AlipayMobilePublicMenuAddResponse response = null; try{ 
		 
		// 执行业务请求
		response = client.execute(request); } catch(AlipayApiException e) { 
		 
		// 业务调用异常处理 
		} 
		// 响应结果
		String result = response.getBody();
		  */

//		System.out
//		.println(SenderFactory
//				.getSender("YX")
//				.customSend(
//						"{\"touser\":\"1970502306b3043b\",\"image\":{\"url\":\"http://localhost:8080/mp3server/2014-06-11_134437.png\",\"fileName\":\"2014-06-11_134437.png\"},\"msgtype\":\"image\"}",
//						"7e24a344510946f494a3040d8ed7bbeb"));
		
//		System.out
//		.println(SenderFactory
//				.getSender("YX")
//				.customSend(
//						"{\"touser\":\"1970502306b3043b\",\"voice\":{\"url\":\"http://localhost:8080/mp3server/2.mp3\",\"fileName\":\"2.mp3\"},\"msgtype\":\"voice\"}",
//						"7e24a344510946f494a3040d8ed7bbeb"));
		
//		System.out
//		.println(SenderFactory
//				.getSender("YX")
//				.customSend(
//						"{\"touser\":\"1970502306b3043b\",\"video\":{\"url\":\"http://localhost:8080/mp3server/2.mp4\",\"fileName\":\"2.mp4\",\"title\":\"你好\", \"description\":\"测试一下\"},\"msgtype\":\"video\"}",
//						"7e24a344510946f494a3040d8ed7bbeb"));
//		System.out
//				.println(SenderFactory
//						.getSender("YX")
//						.customSend(
//								"{\"touser\":\"1970502306b3043b\",\"news\":{\"articles\": [{\"picurl\":\"http://localhost:8080/mp3server/2014-06-11_134437.png\",\"author\":\"abc\",\"title\":\"HELLO WORLD!\",\"content_source_url\":\"www.baidu.com\",\"content\":\"详细内容\",\"digest\":\"详细描述\",\"show_cover_pic\":\"1\"}]},\"msgtype\":\"news\"}",
//								"7e24a344510946f494a3040d8ed7bbeb"));
		
		
//		System.out
//				.println(SenderFactory.getSender("YX").multiSend(
//						"{\"touser\":[],\"text\":{\"content\":\"测试群发\"},\"msgtype\":\"text\"}",
//						"5a6d345a8e334c538aaaf1b5f046f43b"));
		
//		System.out
//				.println(multiSend(
//						"{\"touser\":[],\"image\":{\"url\":\"http://localhost:8080/mp3server/2014-06-11_134437.png\",\"fileName\":\"2014-06-11_134437.png\"},\"msgtype\":\"image\"}",
//						"kfdQtqiwbnCeHABqq7Q8tRUcoIyD0YsznYiYGKgrpN1mZaaBjr04g7vmAbAdO_Vzlk8-woLWU6aJgWYbEtBOlw",
//						"WX"));
//		System.out
//				.println(multiSend(
//						"{\"touser\":[],\"voice\":{\"url\":\"http://localhost:8080/mp3server/2.mp3\",\"fileName\":\"2.mp3\"},\"msgtype\":\"voice\"}",
//						"kfdQtqiwbnCeHABqq7Q8tRUcoIyD0YsznYiYGKgrpN1mZaaBjr04g7vmAbAdO_Vzlk8-woLWU6aJgWYbEtBOlw",
//						"WX"));
//		System.out
//				.println(multiSend(
//						"{\"touser\":[],\"mpvideo\":{\"url\":\"http://localhost:8080/mp3server/2.mp4\",\"fileName\":\"2.mp4\",\"title\":\"你好\", \"description\":\"测试一下\"},\"msgtype\":\"mpvideo\"}",
//						"K9Eaw6ppPnsd4N9sW10sAgQtpPGK9wgw1k35A4oV_qll0R0K7Jc0Kete0J8KBjHz6iP-WAHywEHEmomSYjHAVg",
//						"WX"));
//		System.out
//				.println(multiSend(
//						"{\"touser\":[],\"articles\": [{\"picurl\":\"http://localhost:8080/mp3server/2014-06-11_134437.png\",\"author\":\"abc\",\"title\":\"HELLO WORLD!\",\"content_source_url\":\"www.baidu.com\",\"content\":\"详细内容\",\"digest\":\"详细描述\",\"show_cover_pic\":\"1\"}],\"msgtype\":\"mpnews\"}",
//						"0jSQv_bQntONa3oVZWazBFqoChGAs63RZZMIw2SmEFGXizXc7DN2HzjxnzrHt34mth92cMoyfd3NWtjcGRQp0A",
//						"WX"));
	}
}
