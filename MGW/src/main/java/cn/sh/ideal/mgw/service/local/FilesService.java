package cn.sh.ideal.mgw.service.local;

import java.io.InputStream;

public interface FilesService {
	public String downloadMedia(String account, String mediaId, String savePath) throws Exception;
	
	public String downloadAlipayMedia(String account, String mediaId, String savePath) throws Exception;
	
	public String downloadWeiBoSXMedia(String accessToken, String fid, String savePath) throws Exception;

	public String getFileEndWitsh(String contentType);

	public String saveFile(String fileName, InputStream in, String pathFile, String mailFileSize);

}
