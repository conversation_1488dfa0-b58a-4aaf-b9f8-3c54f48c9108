package cn.sh.ideal.mgw.service.local;

import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;

import com.alibaba.fastjson.JSONObject;

public interface LocalSessionService {

	/**
	 * 查询或创建信息状态
	 * 
	 * @param methodName
	 * @param json
	 * @return
	 */
	public SessionInfo getSession(RequestSessionbean requestSessionbean) ;
	
	/**
	 * 更新用户会话超时时间
	 * @param requestSessionbean
	 */
	public void updateSessionUserActive(RequestSessionbean requestSessionbean);
	
	
	public void updateSessionAgActive(RequestSessionbean requestSessionbean);
	
}
