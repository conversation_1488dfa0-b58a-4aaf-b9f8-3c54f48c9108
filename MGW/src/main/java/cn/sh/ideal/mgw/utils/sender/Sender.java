package cn.sh.ideal.mgw.utils.sender;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public interface Sender {
	/**
	 * 点对点发送
	 * @param json
	 * @param token
	 */
	public JSONObject customSend(String json, String token) throws Exception;
	
	
	/**
	 * 群发
	 * @param json
	 * @param token
	 * @return
	 * @throws Exception
	 */
	public JSON multiSend(String json, String token) throws Exception;
	
}
