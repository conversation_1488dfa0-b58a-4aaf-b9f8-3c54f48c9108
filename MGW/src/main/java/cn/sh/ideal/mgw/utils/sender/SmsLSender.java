package cn.sh.ideal.mgw.utils.sender;

import cn.sh.ideal.mgw.excutor.smsL.action.SingletonClient;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;



public class SmsLSender implements Sender{

	@Override
	public JSONObject customSend(String json, String token) throws Exception {
		JSONObject jsonParam = JSONObject.parseObject(json);
		JSONObject resultJson = new JSONObject();
		
		String sendAccount = jsonParam.getString("sender");
		String acceptAccount[] = new String[]{jsonParam.getString("receiver")}; 
		String content = jsonParam.getString("textContent");
		Integer i = SingletonClient.getClient(sendAccount,token).sendSMS(acceptAccount,content, "", 5);
		if(i !=null && i.equals(0)){
			resultJson.put("resultCode", "0");
			resultJson.put("resultMsg", "消息发送成功！");
			
		}else{
			resultJson.put("resultCode", "1");
			resultJson.put("resultMsg", "消息发送失败！短信网关返回码：" + i);
		}
		return resultJson;
	}

	@Override
	public JSON multiSend(String json, String token) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

}
