/**
 * 
 */
package cn.sh.ideal.mgw.service.dubbo.channel;

import static cn.sh.ideal.mgw.base.Constants.PARAM_PRIVATE_KEY;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.req.MessageHandleRequest;
import cn.sh.ideal.imr.resp.MessageHandleResponse;
import cn.sh.ideal.imr.service.IMRService;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.adapter.MsgConvertUtil;
import cn.sh.ideal.mgw.base.Constants;
import cn.sh.ideal.mgw.dao.ChannelConfigDao;
import cn.sh.ideal.mgw.dao.MessageInfoDao;
import cn.sh.ideal.mgw.model.TextMessage;
import cn.sh.ideal.mgw.model.WeiboMsgInfo;
import cn.sh.ideal.mgw.model.req.SensitiveLog;
import cn.sh.ideal.mgw.model.req.WeiboInfoRequestDTO;
import cn.sh.ideal.mgw.model.response.CustomResponseDTO;
import cn.sh.ideal.mgw.model.response.WeiboResponseDTO;
import cn.sh.ideal.mgw.service.channel.CustomService;
import cn.sh.ideal.mgw.service.channel.WeiboService;
import cn.sh.ideal.mgw.service.local.BlackService;
import cn.sh.ideal.mgw.service.local.LocalSessionService;
import cn.sh.ideal.mgw.service.local.SensitiveService;
import cn.sh.ideal.mgw.utils.MessageUtil;
import cn.sh.ideal.mgw.utils.XmlObject;
import cn.sh.ideal.mgw.utils.sender.ToolBarSender;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.RequestSessionbean;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.util.EmojiConverter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.dubboService.channel
 * @typeName CustomServiceImpl
 * <AUTHOR> Zhou
 * @Description:  
 * @date 2016年4月1日 下午2:24:24
 * @version 
 */
@Component("weiboService")
public class WeiboServiceImpl implements WeiboService {

	private static final Logger log = Logger.getLogger(WeiboServiceImpl.class);
	
	@Resource(name = "blackService")
	private BlackService blackService;
	
	@Resource(name = "sensitiveService")
	private SensitiveService sensitiveService;
	
	@Autowired
	private SysInitService initService;
	
	@Autowired
	private ChannelConfigDao channelConfigDao;
	
	@Autowired
	private MessageInfoDao messageInfoDao;
	
	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;
	String[] selfServiceStatus = { "1", "6" };
	@Autowired
	private LocalSessionService sessionService;
	
	private final String CHANNELCODE = "1020";
	
	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;

	
	@Autowired
	private IMRService imrService;
	
	@Value("#{config['outAgent']}")
	private  String outAgent;
	
	/**
	 * 调用imr拼接url
	 */
	@Value("#{config['media.imrUrl']}")
	private String imrUrl;
	/**
	 * 调用mir拼接url
	 */
	@Value("#{config['media.imrUrl']}")
	private String mirUrl;

	
	@Override
	public WeiboResponseDTO receiveWeiboMessage(WeiboInfoRequestDTO weiboInfoRequestDTO) {

		// 获取fromUser
		String fromUserName = weiboInfoRequestDTO.getSender_id();
		String toUserName = weiboInfoRequestDTO.getReceiver_id();
		
		log.info("weiboInfoRequestDTO: fromUserName=" + fromUserName + "___toUserName=" + toUserName);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");	
		log.info("weiboInfoRequestDTO: creatTime=" + sdf.format(new Date(weiboInfoRequestDTO.getCreated_at())));
		ChannelConfig channelConfig = (ChannelConfig) redisDao.readValue(Constants.KEY_CHANNEL_INFO + toUserName);
		
		if(channelConfig != null){
			RequestSessionbean requestSessionbean = new RequestSessionbean(fromUserName, toUserName, CHANNELCODE, channelConfig.getTenantCode());
			SensitiveLog sensitiveLog = new SensitiveLog(CHANNELCODE,channelConfig.getTenantCode(), fromUserName, toUserName, weiboInfoRequestDTO.getText());
			
			if (sensitiveService.filter(sensitiveLog)) {
				log.info("微博消息已经被过滤" + JSON.toJSONString(sensitiveLog));
				return null;
			}
			else{
				requestSessionbean.setSkillType(channelConfig.getRealTime());
				
				log.info("Before media2Message"+ JSON.toJSONString(requestSessionbean));
				
				JSONObject configObj = JSONObject.parseObject(channelConfig.getAccountConfig());
				weiboInfoRequestDTO.setToken(configObj.getString("token"));
				MessageInfo message = MsgConvertUtil.media2Message(CHANNELCODE, weiboInfoRequestDTO);
				log.info("After media2Message :"+ JSON.toJSONString(message));
				
				SessionInfo sessioninfo = sessionService.getSession(requestSessionbean);
				
				log.info("weibo调用SM返回的sessionInfo: "+JSON.toJSONString(sessioninfo));
				// platformMessage.setNickname(sessioninfo.getNickname());
				if (Constants.SELF_ENABLED.equals(channelConfig.getSelfType()) && (ArrayUtils.contains(selfServiceStatus, sessioninfo.getStatus()) || outAgent.equals(message.getContent()))) {
					// 自助

					Object contentObj = getJsonObj(message.getContent());
					PlatformMessage platformMessage = null;
					if (contentObj instanceof JSONObject) {

						platformMessage = JSONObject.toJavaObject((JSONObject) contentObj, PlatformMessage.class);
					} else {
						platformMessage = new PlatformMessage();
						platformMessage.setContent((String) contentObj);
					}
					platformMessage.setFollowData(message.getFollowData());
					//FIXME 网关自己的platformMessage里的msgType是对象,而IMR的这个model里msgType是String
//					platformMessage.setMsgType(PlatformMessage.MsgType.valueOf(requestMessage.getMsgType()));
					platformMessage.setMsgType(message.getMsgType());
					platformMessage.setToUser(message.getAcceptedAccount());
					platformMessage.setOpenId(message.getSendAccount());


					MessageHandleResponse messageHandle = imrService.messageHandle(new MessageHandleRequest(platformMessage));
					//FIXME
//					String resultXml = NetUtil.send(initService.getSysParam(Constants.IMR_ADDRESS).getParamValue() + imrUrl, "POST", JSONObject.toJSONString(platformMessage));
					WeiboResponseDTO weiboRes=new WeiboResponseDTO();
					weiboRes.setReceiver_id(fromUserName);
					weiboRes.setSender_id(toUserName);

					/**
					 * {
					 "result": true,
					 "receiver_id":456,
					 "sender_id":123,
					 "type": "text",
					 "data":"{
					 "text": "中文消息"
					 }"
					 }
					 */
					if(messageHandle!=null&&messageHandle.getResponseMessage()!=null){
						JSONObject requestObj = new JSONObject();
						requestObj.put("userId", message.getUserId());
						requestObj.put("acceptedAccount", message.getSendAccount());
						requestObj.put("sendAccount", message.getAcceptedAccount());
						requestObj.put("tenantCode", message.getTenantCode());
						requestObj.put("workNo", "");
						requestObj.put("messageId", "");
						requestObj.put("sessionId", sessioninfo.getSessionId());
						requestObj.put("channelCode", message);
						requestObj.put("businessType", message.getBusinessType());
						requestObj.put("appId", "");
						requestObj.put("account", new JSONArray());

						weiboRes.setResult("true");
						String msgType = messageHandle.getResponseMessage().getMsgType().toLowerCase();
						requestObj.put("msgType", msgType.toLowerCase());

						JSONObject msgObj = new JSONObject();
						String xmlString = messageHandle.getResponseMessage().toXMLString();
						XmlObject xmlObject = null;
						try {
							xmlObject = XmlObject.parse(xmlString);
						} catch (Exception e) {
							e.printStackTrace();
						}
						switch (msgType) {
							case "text":
								String txtContent = xmlObject.getChildValue("Content");
								JSONObject dataObj = new JSONObject();
								dataObj.put("text",txtContent);
								weiboRes.setType("text");
								try {
									weiboRes.setData(URLEncoder.encode(dataObj.toJSONString(),"UTF-8"));
								} catch (UnsupportedEncodingException e) {
									e.printStackTrace();
								}
								requestObj.put("content", txtContent);
								break;

							case "news":
								msgObj.put("articlesCount", xmlObject.getChildValue("ArticlesCount"));

								List<XmlObject> articles = xmlObject.getUniqueSubXmlObject("Articles").getSubXmlObjects("item");
								JSONArray itemAry = new JSONArray();
								for (XmlObject item : articles) {
									JSONObject itemObj = new JSONObject();
									itemObj.put("title", item.getChildValue("Title"));
									itemObj.put("description", item.getChildValue("Description"));
									itemObj.put("picurl", item.getChildValue("PicUrl"));
									itemObj.put("url", item.getChildValue("Url"));
									itemAry.add(itemObj);
								}

								msgObj.put("articles", itemAry);
								requestObj.put("content", msgObj);

								break;
							default:
								break;
						}
						return weiboRes;



					}




				} else {
				if (sessioninfo != null) {
					message.setNickname(sessioninfo.getNickname());
					message.setSessionId(sessioninfo.getSessionId());
					message.setTenantCode(sessioninfo.getTenantCode());
					message.setCreateTime(new Date(weiboInfoRequestDTO.getCreated_at()));

					try {
						// 封装消息体
						String content = message.getContent();
						// 正常人工进入的消息设置为1
						message.setMessageSource("1");
						if ("text".equals(message.getMsgType())) {
							message.setContent(EmojiConverter.encode(message.getContent()));
						}

						log.info("messageInfo 入库: " + JSON.toJSONString(message));
						this.messageInfoDao.insert(message);
						message.setContent(content);

						String messageString = JSON.toJSONString(message);

						redisMessage.listrPush(cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE, message);

						log.info("weibo messageInfo write in redis : " + messageString);

					} catch (Exception e) {
						log.error("存储数据异常!", e);
					}
				}
				}				
			}
		}		
		
		return null;
	}
	
	/**
	 * 判断字符串是否是jsonObject
	 * 
	 * @param jsonString
	 * @return
	 */
	public static Object getJsonObj(String jsonString) {
		if (jsonString.startsWith("{")) {
			JSONObject jsonObject = JSONObject.parseObject(jsonString);
			return jsonObject;
		} else {
			return jsonString;
		}

	}

}
