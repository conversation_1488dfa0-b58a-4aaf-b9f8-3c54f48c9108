package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.util.Date;

public class BlackLog implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.AUTO_ID
     *
     * @mbggenerated
     */
    private int autoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.TYPE
     *
     * @mbggenerated
     */
    private String type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.OPER_TIME
     *
     * @mbggenerated
     */
    private Date operTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.ACCOUNT
     *
     * @mbggenerated
     */
    private String account;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.CHANNEL_CODE
     *
     * @mbggenerated
     */
    private String channelCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.START_TIME
     *
     * @mbggenerated
     */
    private String startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.END_TIME
     *
     * @mbggenerated
     */
    private String endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.TENANT_CODE
     *
     * @mbggenerated
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.BLACK_ID
     *
     * @mbggenerated
     */
    private String blackId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.STATUS_CODE
     *
     * @mbggenerated
     */
    private Long statusCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.STATUS_TEXT
     *
     * @mbggenerated
     */
    private String statusText;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.START_DATE
     *
     * @mbggenerated
     */
    private Date startDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_BLACK_LOG.END_DATE
     *
     * @mbggenerated
     */
    private Date endDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table CMS_BLACK_LOG
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.AUTO_ID
     *
     * @return the value of CMS_BLACK_LOG.AUTO_ID
     *
     * @mbggenerated
     */
    
    
    
    public int getAutoId() {
        return autoId;
    }

    
    
    public BlackLog() {
		super();
		// TODO Auto-generated constructor stub
	}

    public BlackLog(BlackList blackList,String channelCode){
    	super();
    	this.account=blackList.getBlackUser();
    	this.blackId=String.valueOf(blackList.getAutoId());
    	this.endDate=blackList.getBlackEndDate();
    	this.startDate=blackList.getBlackBeginDate();
    	this.channelCode=channelCode;
    	this.endTime=blackList.getBlackEndTime().toString();
    	this.startTime=blackList.getBlackBeginTime().toString();
    	
    	
    	
    }
	/**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.AUTO_ID
     *
     * @param autoId the value for CMS_BLACK_LOG.AUTO_ID
     *
     * @mbggenerated
     */
    public void setAutoId(int autoId) {
        this.autoId = autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.TYPE
     *
     * @return the value of CMS_BLACK_LOG.TYPE
     *
     * @mbggenerated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.TYPE
     *
     * @param type the value for CMS_BLACK_LOG.TYPE
     *
     * @mbggenerated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.OPER_TIME
     *
     * @return the value of CMS_BLACK_LOG.OPER_TIME
     *
     * @mbggenerated
     */
    public Date getOperTime() {
        return operTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.OPER_TIME
     *
     * @param operTime the value for CMS_BLACK_LOG.OPER_TIME
     *
     * @mbggenerated
     */
    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.ACCOUNT
     *
     * @return the value of CMS_BLACK_LOG.ACCOUNT
     *
     * @mbggenerated
     */
    public String getAccount() {
        return account;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.ACCOUNT
     *
     * @param account the value for CMS_BLACK_LOG.ACCOUNT
     *
     * @mbggenerated
     */
    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.CHANNEL_CODE
     *
     * @return the value of CMS_BLACK_LOG.CHANNEL_CODE
     *
     * @mbggenerated
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.CHANNEL_CODE
     *
     * @param channelCode the value for CMS_BLACK_LOG.CHANNEL_CODE
     *
     * @mbggenerated
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.START_TIME
     *
     * @return the value of CMS_BLACK_LOG.START_TIME
     *
     * @mbggenerated
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.START_TIME
     *
     * @param startTime the value for CMS_BLACK_LOG.START_TIME
     *
     * @mbggenerated
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime == null ? null : startTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.END_TIME
     *
     * @return the value of CMS_BLACK_LOG.END_TIME
     *
     * @mbggenerated
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.END_TIME
     *
     * @param endTime the value for CMS_BLACK_LOG.END_TIME
     *
     * @mbggenerated
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime == null ? null : endTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.TENANT_CODE
     *
     * @return the value of CMS_BLACK_LOG.TENANT_CODE
     *
     * @mbggenerated
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.TENANT_CODE
     *
     * @param tenantCode the value for CMS_BLACK_LOG.TENANT_CODE
     *
     * @mbggenerated
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.BLACK_ID
     *
     * @return the value of CMS_BLACK_LOG.BLACK_ID
     *
     * @mbggenerated
     */
    public String getBlackId() {
        return blackId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.BLACK_ID
     *
     * @param blackId the value for CMS_BLACK_LOG.BLACK_ID
     *
     * @mbggenerated
     */
    public void setBlackId(String blackId) {
        this.blackId = blackId == null ? null : blackId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.STATUS_CODE
     *
     * @return the value of CMS_BLACK_LOG.STATUS_CODE
     *
     * @mbggenerated
     */
    public Long getStatusCode() {
        return statusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.STATUS_CODE
     *
     * @param statusCode the value for CMS_BLACK_LOG.STATUS_CODE
     *
     * @mbggenerated
     */
    public void setStatusCode(Long statusCode) {
        this.statusCode = statusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.STATUS_TEXT
     *
     * @return the value of CMS_BLACK_LOG.STATUS_TEXT
     *
     * @mbggenerated
     */
    public String getStatusText() {
        return statusText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.STATUS_TEXT
     *
     * @param statusText the value for CMS_BLACK_LOG.STATUS_TEXT
     *
     * @mbggenerated
     */
    public void setStatusText(String statusText) {
        this.statusText = statusText == null ? null : statusText.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.START_DATE
     *
     * @return the value of CMS_BLACK_LOG.START_DATE
     *
     * @mbggenerated
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.START_DATE
     *
     * @param startDate the value for CMS_BLACK_LOG.START_DATE
     *
     * @mbggenerated
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate ;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_BLACK_LOG.END_DATE
     *
     * @return the value of CMS_BLACK_LOG.END_DATE
     *
     * @mbggenerated
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_BLACK_LOG.END_DATE
     *
     * @param endDate the value for CMS_BLACK_LOG.END_DATE
     *
     * @mbggenerated
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate ;
    }
}