package cn.sh.ideal.mgw.model;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

public class Message implements Serializable {

	private static final long serialVersionUID = -1L;
	/** 主键 */
	private int id;

	/** 消息ID */
	private String messageId;
	/**功能条用户id*/
	private String toolbarId;

	/** 消息渠道 */
	private String messageChannel;

	/** 业务类型 */
	private String messageBusiness;

	/** 所属租户 */
	@JSONField(name="tenantId")
	private String tenantCode;

	/** 全息用户视图ID */
	private String userId;
	/** 全息用户等级 */
	private String userLevel;

	/** 发送账号 */
	private String sendAccout;

	/** 接受账号 */
	private String acceptedAccount;

	/** 创建时间 */
	private Date createTime;

	/** 会话ID */
	private String sessionId;

	/** 转发前技能组 */
	private String beforeSkillQueue;

	/** 分配技能组 */
	private String skillQueue;

	/** 转发前工号 */
	private String beforeWorkNo;

    /** 客服工号 */
    private String workNo;

	/** 1:未分配 2:已分配 */
	private String status;

	/** 备注 */
	private String remark;
	
	private String countent;

	/** 不同渠道扩展信息JSON格式 */
	private String extData;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getMessageChannel() {
		return messageChannel;
	}

	public void setMessageChannel(String messageChannel) {
		this.messageChannel = messageChannel;
	}

	public String getMessageBusiness() {
		return messageBusiness;
	}

	public void setMessageBusiness(String messageBusiness) {
		this.messageBusiness = messageBusiness;
	}


	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getSendAccout() {
		return sendAccout;
	}

	public void setSendAccout(String sendAccout) {
		this.sendAccout = sendAccout;
	}

	public String getAcceptedAccount() {
		return acceptedAccount;
	}

	public void setAcceptedAccount(String acceptedAccount) {
		this.acceptedAccount = acceptedAccount;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getExtData() {
		return extData;
	}

	public void setExtData(String extData) {
		this.extData = extData;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	

	public String getUserLevel() {
		return userLevel;
	}

	public void setUserLevel(String userLevel) {
		this.userLevel = userLevel;
	}

	public String getBeforeSkillQueue() {
		return beforeSkillQueue;
	}

	public void setBeforeSkillQueue(String beforeSkillQueue) {
		this.beforeSkillQueue = beforeSkillQueue;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getBeforeWorkNo() {
		return beforeWorkNo;
	}

	public void setBeforeWorkNo(String beforeWorkNo) {
		this.beforeWorkNo = beforeWorkNo;
	}
	


	public String getToolbarId() {
		return toolbarId;
	}

	public void setToolbarId(String toolbarId) {
		this.toolbarId = toolbarId;
	}
	
	

	public String getCountent() {
		return countent;
	}

	public void setCountent(String countent) {
		this.countent = countent;
	}

}
