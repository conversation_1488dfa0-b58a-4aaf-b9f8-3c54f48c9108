/**
 * 
 */
package cn.sh.ideal.mgw.service.local.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.Scanner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static cn.sh.ideal.mgw.base.Constants.PARAM_RESULT_CODE;
import static cn.sh.ideal.mgw.base.Constants.PARAM_RESULT_MSG;

import com.alibaba.fastjson.JSONObject;

/**
 * @project MGW
 * @Package cn.sh.ideal.mgw.localService.impl
 * @typeName BaseService
 * <AUTHOR>
 * @Description:  对应原来的BaseController
 * @date 2016年3月30日 下午4:48:34
 * @version 
 */
public class BaseService {

	private static Logger log =LoggerFactory.getLogger(BaseService.class);
	public final static String SUCCESS_CODE = "0";
	
	public final static String ERROR_CODE = "-1";
	
	public final static String SUCCESS_MSG = "success.";
	
	public String streamToString(InputStream in)
			throws IOException {
		Scanner scanner = new Scanner(in, "UTF-8");
		StringBuffer buffer = new StringBuffer();
		while(scanner.hasNextLine()) {
			buffer.append(scanner.nextLine());
		}
		scanner.close();
		return buffer.toString();
	}

	/**
	 * 返回成功JSON
	 * @return
	 */
	public JSONObject getSuccessJsonObj() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(PARAM_RESULT_CODE, SUCCESS_CODE);
		jsonObject.put(PARAM_RESULT_MSG, SUCCESS_MSG);
		return jsonObject;
	}
	
	public String getSuccessResult() {
		return getSuccessJsonObj().toJSONString();
	}
	
	public JSONObject getErrorJsonObj(String errmsg) {
		return getErrorJsonObj(ERROR_CODE, errmsg);
	}
	
	
	/**
	 * 返回失败JSON
	 * @param code
	 * @param errmsg
	 * @return
	 */
	public JSONObject getErrorJsonObj(String code, String errmsg) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(PARAM_RESULT_CODE, code);
		jsonObject.put(PARAM_RESULT_MSG, errmsg);
		return jsonObject;
	}
	
	public String getErrorResult(String code, String errmsg) {
		return getErrorJsonObj(code, errmsg).toJSONString();
	}
	
	public String getErrorResult(String errmsg) {
		return getErrorResult(ERROR_CODE, errmsg);
	}
	
	/**
	 * 判断字符串是否是jsonObject
	 * 
	 * @param jsonString
	 * @return
	 */
	public static Object getJsonObj(String jsonString) {
		if (jsonString.startsWith("{")) {
			JSONObject jsonObject = JSONObject.parseObject(jsonString);
			return jsonObject;
		} else {
			return jsonString;
		}

	}

	
	
}
