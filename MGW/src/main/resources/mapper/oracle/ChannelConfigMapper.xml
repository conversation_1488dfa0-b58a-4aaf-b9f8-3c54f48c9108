<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.ChannelConfigDao" >
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- ResultMap                                                    -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <resultMap id="ResultMap" type="cn.sh.ideal.model.ChannelConfig" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" /> 
    <result column="CHANNEL_ACCOUNT" property="channelAccount" jdbcType="VARCHAR" />
    <result column="ACCOUNT_CONFIG" property="accountConfig" jdbcType="VARCHAR" />
    <result column="CHANNEL_ENABLE" property="channelEnable" jdbcType="VARCHAR" />
    <result column="TENANT" property="tenantCode" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="SELF_TYPE" property="selfType" jdbcType="VARCHAR" />
    <result column="CHANNEL_TYPE" property="channelType" jdbcType="VARCHAR" />
    <result column="REAL_TIME" property="realTime" jdbcType="VARCHAR" /> 
  </resultMap>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Insert                                                       -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insert" parameterType="cn.sh.ideal.model.ChannelConfig" >
    INSERT INTO MGW_TENANT_CHANNEL_CONFIG (ID,CHANNEL_CODE,CHANNEL_ACCOUNT,ACCOUNT_CONFIG,CHANNEL_ENABLE,TENANT,REMARK, exp_date)
    VALUES (SEQ_MGW_TENANT_CHANNEL_CONFIG.NEXTVAL, #{channelCode,jdbcType=VARCHAR}, #{channelAccount,jdbcType=VARCHAR}, 
      #{accountConfig,jdbcType=VARCHAR}, #{channelEnable,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{expDate,jdbcType=TIMESTAMP})
  </insert>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Load                                                         -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="load" resultMap="ResultMap" parameterType="cn.sh.ideal.model.ChannelConfig" >
    SELECT  * FROM MGW_TENANT_CHANNEL_CONFIG
    WHERE ID = #{id,jdbcType=INTEGER}
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Query                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="query" resultMap="ResultMap" parameterType="cn.sh.ideal.model.ChannelConfig" >
    SELECT  * FROM MGW_TENANT_CHANNEL_CONFIG
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QueryCount                                                   -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryCount" resultType="int" parameterType="cn.sh.ideal.model.ChannelConfig" >
    SELECT  COUNT(1) FROM MGW_TENANT_CHANNEL_CONFIG
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Update                                                       -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <update id="update" parameterType="cn.sh.ideal.model.ChannelConfig" >
    UPDATE MGW_TENANT_CHANNEL_CONFIG
    <set >
      <if test="channelCode != null and channelCode != ''" >
        CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="channelAccount != null and channelAccount != ''" >
        CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR},
      </if>
      <if test="accountConfig != null and accountConfig != ''" >
        ACCOUNT_CONFIG = #{accountConfig,jdbcType=VARCHAR},
      </if>
      <if test="channelEnable != null and channelEnable != ''" >
        CHANNEL_ENABLE = #{channelEnable,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null and tenantCode != ''" >
        TENANT = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="expDate != null and expDate != ''" >
        EXP_DATE = #{expDate,jdbcType=TIMESTAMP},
      </if>
      <if test="selfType != null and selfType != ''" >
        SELF_TYPE = #{selfType,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null and channelType != ''" >
        CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="realTime != null and realTime != ''" >
        REAL_TIME = #{realTime,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Delete                                                       -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <delete id="delete" parameterType="cn.sh.ideal.model.ChannelConfig" >
    DELETE FROM MGW_TENANT_CHANNEL_CONFIG
    WHERE ID = #{id,jdbcType=INTEGER}
  </delete>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Where                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <sql id="where" >
    <where >
       <if test="channelCode != null and channelCode != ''">
        CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
      </if>
      <if test="channelAccount != null and channelAccount != ''" >
      and  CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR}
      </if>
      <if test="accountConfig != null and accountConfig != ''" >
      and  ACCOUNT_CONFIG = #{accountConfig,jdbcType=VARCHAR}
      </if>
      <if test="channelEnable != null and channelEnable != ''" >
      and  CHANNEL_ENABLE = #{channelEnable,jdbcType=VARCHAR}
      </if>
      <if test="tenantCode != null and tenantCode != ''" >
      and  TENANT = #{tenantCode,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
      and  REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="selfType != null and selfType != ''" >
      and  SELF_TYPE = #{selfType,jdbcType=VARCHAR}
      </if>
      <if test="channelType != null and channelType != ''" >
      and  CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR}
      </if>
      <if test="realTime != null and realTime != ''" >
      and  REAL_TIME = #{realTime,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
</mapper>