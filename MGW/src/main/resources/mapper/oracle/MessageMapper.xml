<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.receivemedia.dao.MessageDao">
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- ResultMap                                                    -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <resultMap id="ResultMap" type="cn.sh.ideal.mgw.model.Message" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="MESSAGE_ID" property="messageId" jdbcType="VARCHAR" />
    <result column="MESSAGE_CHANNEL" property="messageChannel" jdbcType="VARCHAR" />
    <result column="MESSAGE_BUSINESS" property="messageBusiness" jdbcType="VARCHAR" />
    <result column="TENANT_ID" property="tenantCode" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <result column="USER_LEVEL" property="userLevel" jdbcType="VARCHAR" />
    <result column="SEND_ACCOUT" property="sendAccout" jdbcType="VARCHAR" />
    <result column="ACCEPTED_ACCOUNT" property="acceptedAccount" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
    <result column="BEFORE_SKILL_QUEUE" property="beforeSkillQueue" jdbcType="VARCHAR" />
    <result column="SKILL_QUEUE" property="skillQueue" jdbcType="VARCHAR" />
    <result column="BEFORE_WORK_NO" property="beforeWorkNo" jdbcType="VARCHAR" />
    <result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="EXT_DATA" property="extData" jdbcType="VARCHAR" />
  </resultMap>  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Insert                                                       -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insert" parameterType="cn.sh.ideal.mgw.model.Message" useGeneratedKeys="true" keyColumn="ID" keyProperty="id">
  <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="id">  
			 SELECT SEQ_MGW_MESSAGE.NEXTVAL FROM DUAL
		   </selectKey>  
    INSERT INTO MGW_MESSAGE (ID, MESSAGE_CHANNEL, 
      MESSAGE_BUSINESS, TENANT_ID, USER_ID, 
      USER_LEVEL, SEND_ACCOUT, ACCEPTED_ACCOUNT, 
      CREATE_TIME, SESSION_ID, BEFORE_SKILL_QUEUE, 
      SKILL_QUEUE, BEFORE_WORK_NO, WORK_NO, 
      STATUS, REMARK, EXT_DATA
      )
    VALUES (#{id,jdbcType=INTEGER}, #{messageChannel,jdbcType=VARCHAR}, 
      #{messageBusiness,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{userLevel,jdbcType=VARCHAR}, #{sendAccout,jdbcType=VARCHAR}, #{acceptedAccount,jdbcType=VARCHAR}, 
     sysdate, #{sessionId,jdbcType=VARCHAR}, #{beforeSkillQueue,jdbcType=VARCHAR}, 
      #{skillQueue,jdbcType=VARCHAR}, #{beforeWorkNo,jdbcType=VARCHAR}, #{workNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{extData,jdbcType=LONGVARCHAR}
      )      
  </insert>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Load                                                         -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="load" resultMap="ResultMap" parameterType="cn.sh.ideal.mgw.model.Message" >
    SELECT  * FROM MGW_MESSAGE
    WHERE ID = #{id,jdbcType=INTEGER}
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Query                                                        -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="query" resultMap="ResultMap" parameterType="cn.sh.ideal.mgw.model.Message" >
    SELECT  * FROM MGW_MESSAGE
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QueryCount                                                   -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryCount" resultType="int" parameterType="cn.sh.ideal.mgw.model.Message" >
    SELECT  COUNT(1) FROM MGW_MESSAGE
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Update                                                       -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <update id="update" parameterType="cn.sh.ideal.mgw.model.Message" >
    UPDATE MGW_MESSAGE
    <set >
      <if test="messageId != null and messageId != ''" >
        MESSAGE_ID = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="messageChannel != null and messageChannel != ''" >
        MESSAGE_CHANNEL = #{messageChannel,jdbcType=VARCHAR},
      </if>
      <if test="messageBusiness != null and messageBusiness != ''" >
        MESSAGE_BUSINESS = #{messageBusiness,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null and tenantCode != ''" >
        TENANT_ID = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != ''" >
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userLevel != null and userLevel != ''" >
        USER_LEVEL = #{userLevel,jdbcType=VARCHAR},
      </if>      
      <if test="sendAccout != null and sendAccout != ''" >
        SEND_ACCOUT = #{sendAccout,jdbcType=VARCHAR},
      </if>
      <if test="acceptedAccount != null and acceptedAccount != ''" >
        ACCEPTED_ACCOUNT = #{acceptedAccount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime != ''" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionId != null and sessionId != ''" >
        SESSION_ID = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="beforeSkillQueue != null and beforeSkillQueue != ''" >
        BEFORE_SKILL_QUEUE = #{beforeSkillQueue,jdbcType=VARCHAR},
      </if>
      <if test="skillQueue != null and skillQueue != ''" >
        SKILL_QUEUE = #{skillQueue,jdbcType=VARCHAR},
      </if>
      <if test="beforeWorkNo != null and beforeWorkNo != ''" >
        BEFORE_WORK_NO = #{beforeWorkNo,jdbcType=VARCHAR},
      </if>
      <if test="workNo != null and workNo != ''" >
        WORK_NO = #{workNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        REMARK = #{remark,jdbcType=VARCHAR} 
      </if>
    </set>
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Delete                                                       -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <delete id="delete" parameterType="cn.sh.ideal.mgw.model.Message" >
    DELETE FROM MGW_MESSAGE
    WHERE ID = #{id,jdbcType=INTEGER}
  </delete>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Where                                                        -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <sql id="where" >
    <where >
      <if test="id != null and id != ''" >
         ID = #{id,jdbcType=INTEGER}
      </if>
      <if test="messageId != null and messageId != ''" >
         and MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
      </if>
      <if test="messageChannel != null and messageChannel != ''" >
         and MESSAGE_CHANNEL = #{messageChannel,jdbcType=VARCHAR}
      </if>
      <if test="messageBusiness != null and messageBusiness != ''" >
         and MESSAGE_BUSINESS = #{messageBusiness,jdbcType=VARCHAR}
      </if>
      <if test="tenantCode != null and tenantCode != ''" >
         and TENANT_ID = #{tenantCode,jdbcType=VARCHAR}
      </if>
      <if test="userId != null and userId != ''" >
         and USER_ID = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="userLevel != null and userLevel != ''" >
         and USER_LEVEL = #{userLevel,jdbcType=VARCHAR}
      </if>      
      <if test="sendAccout != null and sendAccout != ''" >
         and SEND_ACCOUT = #{sendAccout,jdbcType=VARCHAR}
      </if>
      <if test="acceptedAccount != null and acceptedAccount != ''" >
         and ACCEPTED_ACCOUNT = #{acceptedAccount,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null and createTime != ''" >
         and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="sessionId != null and sessionId != ''" >
         and SESSION_ID = #{sessionId,jdbcType=VARCHAR}
      </if>
      <if test="beforeSkillQueue != null and beforeSkillQueue != ''" >
         and BEFORE_SKILL_QUEUE = #{beforeSkillQueue,jdbcType=VARCHAR}
      </if>
      <if test="skillQueue != null and skillQueue != ''" >
         and SKILL_QUEUE = #{skillQueue,jdbcType=VARCHAR}
      </if>
      <if test="beforeWorkNo != null and beforeWorkNo != ''" >
         and BEFORE_WORK_NO = #{beforeWorkNo,jdbcType=VARCHAR}
      </if>
      <if test="workNo != null and workNo != ''" >
         and WORK_NO = #{workNo,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''" >
         and STATUS = #{status,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
         and REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="extData != null and extData != ''" >
         and EXT_DATA = #{extData,jdbcType=LONGVARCHAR}
      </if>
    </where>
  </sql>
</mapper>