<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.EmailReceiveMonitorDao">
	<resultMap id="BaseResultMap" type="cn.sh.ideal.mgw.model.EmailReceiveMonitor">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<id column="ID" property="id" jdbcType="DECIMAL" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="CHANNEL_ACCOUNT" property="channelAccount"
			jdbcType="VARCHAR" />
		<result column="CURRENT_COUNT" property="currentCount"
			jdbcType="DECIMAL" />
		
	</resultMap>



	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="BaseResultMap"
		parameterType="cn.sh.ideal.mgw.model.EmailReceiveMonitor">
		SELECT * FROM mgw_email_receive_monitor
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.EmailReceiveMonitor">
		SELECT COUNT(1) FROM mgw_email_receive_monitor
		<include refid="where" />
	</select>
	<insert id="insert" parameterType="cn.sh.ideal.mgw.model.EmailReceiveMonitor">
		
		insert into mgw_email_receive_monitor ( ID,TENANT_CODE, CHANNEL_ACCOUNT,
		CURRENT_COUNT, CREATE_TIME
		)
		values
		(SEQ_MGW_EMAIL_RECEIVE_MONITOR.NEXTVAL,
		#{tenantCode,jdbcType=VARCHAR},
		#{channelAccount,jdbcType=VARCHAR},
		#{currentCount,jdbcType=DECIMAL},
		SYSDATE
		)
	</insert>

	<update id="update" parameterType="cn.sh.ideal.mgw.model.EmailReceiveMonitor">
		
		update mgw_email_receive_monitor  
		<set>
			
			
				CURRENT_COUNT = #{currentCount,jdbcType=DECIMAL},
				CREATE_TIME = SYSDATE
				
		
		</set>
		where ID = #{id,jdbcType=DECIMAL}
	</update>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>		
			<if test="tenantCode != null and tenantCode !='' ">
				and TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
			</if>
			<if test="channelAccount != null and channelAccount !='' ">
				and CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR}
			</if>
			
		</where>
	</sql>
</mapper>