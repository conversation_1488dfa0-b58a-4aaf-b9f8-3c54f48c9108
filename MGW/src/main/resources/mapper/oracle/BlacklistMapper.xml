<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.BlacklistDao">
	<resultMap id="BaseResultMap" type="cn.sh.ideal.mgw.model.BlackList">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<id column="AUTO_ID" property="autoId" jdbcType="DECIMAL" />
		<result column="BLACK_USER" property="blackUser" jdbcType="VARCHAR" />
		<result column="BLACK_BEGIN_TIME" property="blackBeginTime"
			jdbcType="VARCHAR" />
		<result column="BLACK_END_TIME" property="blackEndTime"
			jdbcType="VARCHAR" />
		<result column="BLACK_CREATE_ID" property="blackCreateId"
			jdbcType="VARCHAR" />
		<result column="BLACK_CREATE_DATE" property="blackCreateDate"
			jdbcType="TIMESTAMP" />
		<result column="BLACK_UPDATE_ID" property="blackUpdateId"
			jdbcType="VARCHAR" />
		<result column="BLACK_UPDATE_DATE" property="blackUpdateDate"
			jdbcType="TIMESTAMP" />
		<result column="BLACK_DELETE_ID" property="blackDeleteId"
			jdbcType="VARCHAR" />
		<result column="BLACK_DELETE_DATE" property="blackDeleteDate"
			jdbcType="TIMESTAMP" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="BLACK_END_DATE" property="blackEndDate"
			jdbcType="TIMESTAMP" />
		<result column="BLACK_BEGIN_DATE" property="blackBeginDate"
			jdbcType="TIMESTAMP" />
	</resultMap>

	<select id="load" resultMap="BaseResultMap" parameterType="java.math.BigDecimal">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select
		*
		from CMS_BLACKLIST
		where AUTO_ID = #{autoId,jdbcType=DECIMAL}
	</select>
	<delete id="delete" parameterType="java.math.BigDecimal">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		delete from CMS_BLACKLIST
		where AUTO_ID = #{autoId,jdbcType=DECIMAL}
	</delete>


	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="BaseResultMap"
		parameterType="cn.sh.ideal.mgw.model.BlackList">
		SELECT * FROM CMS_BLACKLIST
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.BlackList">
		SELECT COUNT(1) FROM CMS_BLACKLIST
		<include refid="where" />
	</select>
	<insert id="insert" parameterType="cn.sh.ideal.mgw.model.BlackList">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		insert into CMS_BLACKLIST (AUTO_ID, BLACK_USER, BLACK_BEGIN_TIME,
		BLACK_END_TIME, BLACK_CREATE_ID, BLACK_CREATE_DATE,
		BLACK_UPDATE_ID,
		BLACK_UPDATE_DATE, BLACK_DELETE_ID,
		BLACK_DELETE_DATE, STATUS,
		CHANNEL_ID,
		TENANT_CODE, BLACK_END_DATE, BLACK_BEGIN_DATE
		)
		values
		(#{autoId,jdbcType=DECIMAL}, #{blackUser,jdbcType=VARCHAR},
		#{blackBeginTime,jdbcType=VARCHAR},
		#{blackEndTime,jdbcType=VARCHAR},
		#{blackCreateId,jdbcType=VARCHAR},
		#{blackCreateDate,jdbcType=TIMESTAMP},
		#{blackUpdateId,jdbcType=VARCHAR},
		#{blackUpdateDate,jdbcType=TIMESTAMP},
		#{blackDeleteId,jdbcType=VARCHAR},
		#{blackDeleteDate,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR},
		#{channelId,jdbcType=VARCHAR},
		#{tenantCode,jdbcType=VARCHAR},
		#{blackEndDate,jdbcType=TIMESTAMP},
		#{blackBeginDate,jdbcType=TIMESTAMP}
		)
	</insert>

	<update id="update" parameterType="cn.sh.ideal.mgw.model.BlackList">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		update CMS_BLACKLIST  
		<set>
			<if test="blackUser != null">
				BLACK_USER = #{blackUser,jdbcType=VARCHAR},
			</if>
			<if test="blackBeginTime != null">
				BLACK_BEGIN_TIME = #{blackBeginTime,jdbcType=VARCHAR},
			</if>
			<if test="blackEndTime != null">
				BLACK_END_TIME = #{blackEndTime,jdbcType=VARCHAR},
			</if>
			<if test="blackCreateId != null">
				BLACK_CREATE_ID = #{blackCreateId,jdbcType=VARCHAR},
			</if>
			<if test="blackCreateDate != null">
				BLACK_CREATE_DATE =
				#{blackCreateDate,jdbcType=TIMESTAMP},
			</if>
			<if test="blackUpdateId != null">
				BLACK_UPDATE_ID = #{blackUpdateId,jdbcType=VARCHAR},
			</if>
			<if test="blackUpdateDate != null">
				BLACK_UPDATE_DATE =
				#{blackUpdateDate,jdbcType=TIMESTAMP},
			</if>
			<if test="blackDeleteId != null">
				BLACK_DELETE_ID = #{blackDeleteId,jdbcType=VARCHAR},
			</if>
			<if test="blackDeleteDate != null">
				BLACK_DELETE_DATE =
				#{blackDeleteDate,jdbcType=TIMESTAMP},
			</if>
			<if test="status != null">
				STATUS = #{status,jdbcType=VARCHAR},
			</if>
			<if test="channelId != null">
				CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
			</if>
			<if test="tenantCode != null">
				TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
			</if>
			<if test="blackEndDate != null">
				BLACK_END_DATE = #{blackEndDate,jdbcType=TIMESTAMP},
			</if>
			<if test="blackBeginDate != null">
				BLACK_BEGIN_DATE = #{blackBeginDate,jdbcType=TIMESTAMP},
			</if>
		</set>
		where AUTO_ID = #{autoId,jdbcType=DECIMAL}
	</update>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
				<![CDATA[   
				BLACK_BEGIN_TIME<= to_char(sysdate,'hh24:mm:ss')  
				AND  BLACK_END_TIME>= to_char(sysdate,'hh24:mm:ss') 
				AND  TO_char(BLACK_BEGIN_DATE,'YYYY/MM/DD')<=to_char(sysdate,'YYYY/MM/DD')
				AND  TO_char(BLACK_END_DATE,'YYYY/MM/DD')>=to_char(sysdate,'YYYY/MM/DD')
				]]>
			<if test="blackUser != null and blackUser !='' ">
				and BLACK_USER = #{blackUser,jdbcType=VARCHAR}
			</if>

			<if test="blackCreateId != null and blackCreateId !='' ">
				and BLACK_CREATE_ID = #{blackCreateId,jdbcType=VARCHAR}
			</if>
			<if test="blackCreateDate != null and blackCreateDate !='' ">
				and BLACK_CREATE_DATE =
				#{blackCreateDate,jdbcType=TIMESTAMP}
			</if>
			<if test="blackUpdateId != null and blackUpdateId !='' ">
				and BLACK_UPDATE_ID = #{blackUpdateId,jdbcType=VARCHAR}
			</if>
			<if test="blackUpdateDate != null and blackUpdateDate !='' ">
				and BLACK_UPDATE_DATE =
				#{blackUpdateDate,jdbcType=TIMESTAMP}
			</if>
			<if test="blackDeleteId != null and blackDeleteId !='' ">
				and BLACK_DELETE_ID = #{blackDeleteId,jdbcType=VARCHAR}
			</if>
			<if test="blackDeleteDate != null and blackDeleteDate !='' ">
				and BLACK_DELETE_DATE =
				#{blackDeleteDate,jdbcType=TIMESTAMP}
			</if>
			<if test="status != null and status !='' ">
				and STATUS = #{status,jdbcType=VARCHAR}
			</if>
			<if test="channelId != null and channelId !='' ">
			
			and	CHANNEL_ID  = #{channelId,jdbcType=VARCHAR}
			</if>
			<if test="tenantCode != null and tenantCode !='' ">
				and TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
			</if>
			<if test="blackEndDate != null and blackEndDate !='' ">
				and BLACK_END_DATE = #{blackEndDate,jdbcType=TIMESTAMP}
			</if>
			<if test="blackBeginDate != null and blackBeginDate !='' ">
				and BLACK_BEGIN_DATE =
				#{blackBeginDate,jdbcType=TIMESTAMP}
			</if>
		</where>
	</sql>
</mapper>