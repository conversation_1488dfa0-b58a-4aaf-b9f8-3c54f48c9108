<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.SensitiveDao">
	<resultMap id="BaseResultMap" type="cn.sh.ideal.mgw.model.Sensitive">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<id column="auto_ID" property="autoId" jdbcType="INTEGER" />
		<result column="SENSITIVE_WORD" property="sensitiveWord"
			jdbcType="VARCHAR" />
		<result column="SENSITIVE_CREATE_ID" property="sensitiveCreateId"
			jdbcType="VARCHAR" />
		<result column="SENSITIVE_CREATE_DATE" property="sensitiveCreateDate"
			jdbcType="TIMESTAMP" />
		<result column="SENSITIVE_UPDATE_ID" property="sensitiveUpdateId"
			jdbcType="VARCHAR" />
		<result column="SENSITIVE_UPDATE_DATE" property="sensitiveUpdateDate"
			jdbcType="TIMESTAMP" />
		<result column="SENSITIVE_DELETE_ID" property="sensitiveDeleteId"
			jdbcType="VARCHAR" />
		<result column="SENSITIVE_DELETE_DATE" property="sensitiveDeleteDate"
			jdbcType="TIMESTAMP" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
		<result column="SENSITIVE_TYPE" property="sensitiveType"
			jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="MATCHINGTYPE" property="matchingType" jdbcType="VARCHAR" />
	</resultMap>
	<select id="load" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select
		*
		from CMS_SENSITIVE
		where auto_ID = #{autoId,jdbcType=INTEGER}
	</select>


	<delete id="delete" parameterType="java.lang.Integer">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		delete from CMS_SENSITIVE
		where auto_ID = #{autoId,jdbcType=INTEGER}
	</delete>
	<insert id="insert" parameterType="cn.sh.ideal.mgw.model.Sensitive">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		insert into CMS_SENSITIVE (auto_ID, SENSITIVE_WORD,
		SENSITIVE_CREATE_ID,
		SENSITIVE_CREATE_DATE, SENSITIVE_UPDATE_ID,
		SENSITIVE_UPDATE_DATE, SENSITIVE_DELETE_ID,
		SENSITIVE_DELETE_DATE, STATUS, CHANNEL_ID,
		SENSITIVE_TYPE, TENANT_CODE)
		values (#{autoId,jdbcType=INTEGER}, #{sensitiveWord,jdbcType=VARCHAR},
		#{sensitiveCreateId,jdbcType=VARCHAR},
		#{sensitiveCreateDate,jdbcType=TIMESTAMP},
		#{sensitiveUpdateId,jdbcType=VARCHAR},
		#{sensitiveUpdateDate,jdbcType=TIMESTAMP},
		#{sensitiveDeleteId,jdbcType=VARCHAR},
		#{sensitiveDeleteDate,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR},
		#{channelId,jdbcType=VARCHAR},
		#{sensitiveType,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR})
	</insert>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="BaseResultMap"
		parameterType="cn.sh.ideal.mgw.model.Message">
		SELECT * FROM CMS_SENSITIVE
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.Message">
		SELECT COUNT(1) FROM CMS_SENSITIVE
		<include refid="where" />
	</select>
	<update id="update" parameterType="cn.sh.ideal.mgw.model.Sensitive">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		update CMS_SENSITIVE
		<set>
			<if test="sensitiveWord != null">
				SENSITIVE_WORD = #{sensitiveWord,jdbcType=VARCHAR},
			</if>
			<if test="sensitiveCreateId != null">
				SENSITIVE_CREATE_ID = #{sensitiveCreateId,jdbcType=VARCHAR},
			</if>
			<if test="sensitiveCreateDate != null">
				SENSITIVE_CREATE_DATE = #{sensitiveCreateDate,jdbcType=TIMESTAMP},
			</if>
			<if test="sensitiveUpdateId != null">
				SENSITIVE_UPDATE_ID = #{sensitiveUpdateId,jdbcType=VARCHAR},
			</if>
			<if test="sensitiveUpdateDate != null">
				SENSITIVE_UPDATE_DATE = #{sensitiveUpdateDate,jdbcType=TIMESTAMP},
			</if>
			<if test="sensitiveDeleteId != null">
				SENSITIVE_DELETE_ID = #{sensitiveDeleteId,jdbcType=VARCHAR},
			</if>
			<if test="sensitiveDeleteDate != null">
				SENSITIVE_DELETE_DATE = #{sensitiveDeleteDate,jdbcType=TIMESTAMP},
			</if>
			<if test="status != null">
				STATUS = #{status,jdbcType=VARCHAR},
			</if>
			<if test="channelId != null">
				CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
			</if>
			<if test="sensitiveType != null">
				SENSITIVE_TYPE = #{sensitiveType,jdbcType=VARCHAR},
			</if>
			<if test="tenantCode != null">
				TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
			</if>
		</set>
		where auto_ID = #{autoId,jdbcType=INTEGER}
	</update>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>


			<if test="autoId != null and autoId !=''">
				auto_ID = #{autoId,jdbcType=INTEGER}
			</if>
			<if test="sensitiveWord != null and sensitiveWord !=''">
			and	SENSITIVE_WORD = #{sensitiveWord,jdbcType=VARCHAR}
			</if>
			<if test="sensitiveCreateId != null and sensitiveCreateId !=''">
			and	SENSITIVE_CREATE_ID = #{sensitiveCreateId,jdbcType=VARCHAR}
			</if>
			<if test="sensitiveCreateDate != null and sensitiveCreateDate !=''">
			and	SENSITIVE_CREATE_DATE = #{sensitiveCreateDate,jdbcType=TIMESTAMP}
			</if>
			<if test="sensitiveUpdateId != null and sensitiveUpdateId !=''">
			and	SENSITIVE_UPDATE_ID = #{sensitiveUpdateId,jdbcType=VARCHAR}
			</if>
			<if test="sensitiveUpdateDate != null and sensitiveUpdateDate !=''">
			and	SENSITIVE_UPDATE_DATE = #{sensitiveUpdateDate,jdbcType=TIMESTAMP}
			</if>
			<if test="sensitiveDeleteId != null and sensitiveDeleteId !=''">
			and	SENSITIVE_DELETE_ID = #{sensitiveDeleteId,jdbcType=VARCHAR}
			</if>
			<if test="sensitiveDeleteDate != null and sensitiveDeleteDate !=''">
			and	SENSITIVE_DELETE_DATE = #{sensitiveDeleteDate,jdbcType=TIMESTAMP}
			</if>
			<if test="status != null and status !=''">
			and	STATUS = #{status,jdbcType=VARCHAR}
			</if>
			<if test="channelId != null and channelId !=''">
			
			and	(CHANNEL_ID  like '%'||#{channelId}||'%' or CHANNEL_ID IS NULL)  
			</if>
			<if test="sensitiveType != null and sensitiveType !=''">
			and	SENSITIVE_TYPE = #{sensitiveType,jdbcType=VARCHAR}
			</if>
			<if test="tenantCode != null and tenantCode !=''">
			and	TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
			</if>
		</where>
	</sql>

</mapper>