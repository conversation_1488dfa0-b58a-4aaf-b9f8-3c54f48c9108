<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.ReceiveMessageLogDao">

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- ResultMap -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<resultMap id="ResultMap"
		type="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		<id column="ID" property="id" jdbcType="INTEGER" />
		<result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
		<result column="MESSAGE_ID" property="messageId" jdbcType="VARCHAR" />
		<result column="CHANNEL_MESSAGE_ID" property="channelMessageId"
			jdbcType="VARCHAR" />
		<result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
		<result column="MESSAGE_DATA" property="messageData" jdbcType="VARCHAR" />
		<result column="CHANNEL_ACCOUNT" property="channelAccount"
			jdbcType="VARCHAR" />
		<result column="OPER_TYPE" property="operType" jdbcType="VARCHAR" />	
	</resultMap>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Insert -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<insert id="insert"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		INSERT INTO MGW_RECEIVE_MESSAGE_LOG (ID, MESSAGE_ID,
		CHANNEL_MESSAGE_ID ,
		CHANNEL_ID ,
		MESSAGE_DATA,
		TENANT_ID ,
		CHANNEL_ACCOUNT,
		OPER_TYPE)
		VALUES (SEQ_MGW_RECEIVE_MESSAGE_LOG.NEXTVAL, #{messageId,jdbcType=VARCHAR},
		#{channelMessageId,jdbcType=VARCHAR}, #{channelId,jdbcType=VARCHAR},
		#{messageData,jdbcType=LONGVARCHAR},
		#{tenantId,jdbcType=VARCHAR},
		#{channelAccount,jdbcType=VARCHAR},
		#{operType,jdbcType=VARCHAR})
	</insert>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- getmessage -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="getChannelMessageId" resultType="string" parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		SELECT MAX(CHANNEL_MESSAGE_ID) FROM MGW_RECEIVE_MESSAGE_LOG
		
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Load -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="load" resultMap="ResultMap"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		SELECT * FROM MGW_RECEIVE_MESSAGE_LOG
		WHERE CHANNEL_MESSAGE_ID = TO_CHAR(#{id,jdbcType=INTEGER})
	</select>
	
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Load -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="loadByChannelMessageId" resultMap="ResultMap" 
		parameterType="string">
		SELECT * FROM MGW_RECEIVE_MESSAGE_LOG
		WHERE  CHANNEL_MESSAGE_ID= #{channelMessageId,jdbcType=VARCHAR}
	</select>
	

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="ResultMap"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		SELECT * FROM MGW_RECEIVE_MESSAGE_LOG
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		SELECT COUNT(1) FROM MGW_RECEIVE_MESSAGE_LOG
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Update -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<update id="update"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		UPDATE MGW_RECEIVE_MESSAGE_LOG
		<set>
			<if test="tenantId != null and tenantId != ''">
				TENANT_ID = #{tenantId,jdbcType=VARCHAR},
			</if>
			<if test="queueCode != null and queueCode != ''">
				QUEUE_CODE = #{queueCode,jdbcType=VARCHAR},
			</if>
			<if test="ruleCode != null and ruleCode != ''">
				RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
			</if>
			<if test="opTime != null and opTime != ''">
				OP_TIME = #{opTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null and remark != ''">
				REMARK = #{remark,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE ID = #{id,jdbcType=INTEGER}
	</update>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Delete -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<delete id="delete"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageLog">
		DELETE FROM MGW_RECEIVE_MESSAGE_LOG
		WHERE ID = #{id,jdbcType=INTEGER}
	</delete>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="id != null and id != ''">
				ID = #{id,jdbcType=INTEGER}
			</if>
			<if test="messageId != null and messageId != ''">
				and MESSAGE_ID =#{channelId,jdbcType=VARCHAR}
			</if>
			<if test="channelMessageId != null and channelMessageId != ''">
				and CHANNEL_MESSAGE_ID = #{channelMessageId,jdbcType=VARCHAR}
			</if>
			<if test="channelId != null and channelId != ''">
				and CHANNEL_ID = #{channelId,jdbcType=VARCHAR}
			</if>
			<if test="messageData != null and messageData != ''">
				and MESSAGE_DATA = #{messageData,jdbcType=LONGVARCHAR}
			</if>
			<if test="tenantId != null and tenantId != ''">
				and TENANT_ID = #{tenantId,jdbcType=VARCHAR}
			</if>
			<if test="channelAccount != null and channelAccount != ''">
				and CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR}
			</if>
			<if test="operType != null and operType != ''">
				and OPER_TYPE = #{operType,jdbcType=VARCHAR}
			</if>
		</where>
	</sql>
</mapper>