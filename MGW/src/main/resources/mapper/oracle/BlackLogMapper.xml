<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.BlackLogDao">
	<resultMap id="BaseResultMap" type="cn.sh.ideal.mgw.model.BlackLog">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<id column="AUTO_ID" property="autoId" jdbcType="INTEGER" />
		<result column="TYPE" property="type" jdbcType="VARCHAR" />
		<result column="OPER_TIME" property="operTime" jdbcType="TIMESTAMP" />
		<result column="ACCOUNT" property="account" jdbcType="VARCHAR" />
		<result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" />
		<result column="START_TIME" property="startTime" jdbcType="VARCHAR" />
		<result column="END_TIME" property="endTime" jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="BLACK_ID" property="blackId" jdbcType="VARCHAR" />
		<result column="STATUS_CODE" property="statusCode" jdbcType="DECIMAL" />
		<result column="STATUS_TEXT" property="statusText" jdbcType="VARCHAR" />
		<result column="START_DATE" property="startDate" jdbcType="TIMESTAMP" />
		<result column="END_DATE" property="endDate" jdbcType="TIMESTAMP" />
	</resultMap>

	<select id="load" resultMap="BaseResultMap" parameterType="java.lang.Long">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select
		*
		from CMS_BLACK_LOG
		where AUTO_ID = #{autoId,jdbcType=DECIMAL}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		delete from CMS_BLACK_LOG
		where AUTO_ID = #{autoId,jdbcType=DECIMAL}
	</delete>
	<insert id="insert" parameterType="cn.sh.ideal.mgw.model.BlackLog">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="autoId">  
			select seq_cms_black_log.nextval from dual
		 </selectKey>
			
		insert into CMS_BLACK_LOG (AUTO_ID, TYPE, OPER_TIME,
		ACCOUNT, CHANNEL_CODE, START_TIME,
		END_TIME, TENANT_CODE, BLACK_ID,
		STATUS_CODE, STATUS_TEXT, START_DATE,
		END_DATE)
		values (#{autoId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR},
		sysdate,
		#{account,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR}, #{startTime,jdbcType=VARCHAR},
		#{endTime,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR},
		#{blackId,jdbcType=VARCHAR},
		#{statusCode,jdbcType=DECIMAL}, #{statusText,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP},
		#{endDate,jdbcType=TIMESTAMP})
	</insert>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="BaseResultMap"
		parameterType="cn.sh.ideal.mgw.model.BlackLog">
		SELECT * FROM CMS_BLACK_LOG
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.BlackLog">
		SELECT COUNT(1) FROM CMS_BLACK_LOG
		<include refid="where" />
	</select>

	<update id="update" parameterType="cn.sh.ideal.mgw.model.BlackLog">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		update CMS_BLACK_LOG
		<set>
			<if test="type != null">
				TYPE = #{type,jdbcType=VARCHAR},
			</if>
			<if test="operTime != null">
				OPER_TIME = #{operTime,jdbcType=TIMESTAMP},
			</if>
			<if test="account != null">
				ACCOUNT = #{account,jdbcType=VARCHAR},
			</if>
			<if test="channelCode != null">
				CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
			</if>
			<if test="startTime != null">
				START_TIME = #{startTime,jdbcType=VARCHAR},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=VARCHAR},
			</if>
			<if test="tenantCode != null">
				TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
			</if>
			<if test="blackId != null">
				BLACK_ID = #{blackId,jdbcType=VARCHAR},
			</if>
			<if test="statusCode != null">
				STATUS_CODE = #{statusCode,jdbcType=DECIMAL},
			</if>
			<if test="statusText != null">
				STATUS_TEXT = #{statusText,jdbcType=VARCHAR},
			</if>
			<if test="startDate != null">
				START_DATE = #{startDate,jdbcType=TIMESTAMP},
			</if>
			<if test="endDate != null">
				END_DATE = #{endDate,jdbcType=TIMESTAMP},
			</if>
		</set>
		where AUTO_ID = #{autoId,jdbcType=DECIMAL}
	</update>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="type != null and type !=''  ">
				TYPE = #{type,jdbcType=VARCHAR}
			</if>
			<if test="operTime != null and operTime !='' ">
				and OPER_TIME = #{operTime,jdbcType=TIMESTAMP}
			</if>
			<if test="account != null and account !='' ">
				and ACCOUNT = #{account,jdbcType=VARCHAR}
			</if>
			<if test="channelCode != null and channelCode !='' ">
				and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
			</if>
			<if test="startTime != null and startTime !='' ">
				and START_TIME = #{startTime,jdbcType=VARCHAR}
			</if>
			<if test="endTime != null and endTime !='' ">
				and END_TIME = #{endTime,jdbcType=VARCHAR}
			</if>
			<if test="tenantCode != null and tenantCode !='' ">
				and TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
			</if>
			<if test="blackId != null and blackId !='' ">
				and BLACK_ID = #{blackId,jdbcType=VARCHAR}
			</if>
			<if test="statusCode != null and statusCode !='' ">
				and STATUS_CODE = #{statusCode,jdbcType=DECIMAL}
			</if>
			<if test="statusText != null and statusText !='' ">
				and STATUS_TEXT = #{statusText,jdbcType=VARCHAR}
			</if>
			<if test="startDate != null and startDate !='' ">
				and START_DATE = #{startDate,jdbcType=TIMESTAMP}
			</if>
			<if test="endDate != null and endDate !='' ">
				and END_DATE = #{endDate,jdbcType=TIMESTAMP}
			</if>
		</where>
	</sql>
</mapper>