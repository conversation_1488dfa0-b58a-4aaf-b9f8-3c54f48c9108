<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.CommonDao">

	<resultMap id="WhiteListResultMap" type="cn.sh.ideal.mgw.model.WhiteList">
		<id column="AUTO_ID" property="autoId" jdbcType="INTEGER" />
		<result column="WHITE_USER" property="whiteUser" jdbcType="VARCHAR" />
		<result column="WHITE_BEGIN_TIME" property="whiteBeginTime" jdbcType="TIMESTAMP" />
		<result column="WHITE_END_TIME" property="whiteEndTime" jdbcType="TIMESTAMP" />
		<result column="WHITE_CREATE_ID" property="whiteCreateId" jdbcType="VARCHAR" />
		<result column="WHITE_CREATE_DATE" property="whiteCreateDate" jdbcType="TIMESTAMP" />
		<result column="WHITE_UPDATE_ID" property="whiteUpdateId" jdbcType="VARCHAR" />
		<result column="WHITE_UPDATE_DATE" property="whiteUpdateDate" jdbcType="TIMESTAMP" />
		<result column="WHITE_DELETE_ID" property="whiteDeleteId" jdbcType="VARCHAR" />
		<result column="WHITE_DELETE_DATE" property="whiteDeleteDate" jdbcType="TIMESTAMP" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="TENANT_ID" property="tenantId" jdbcType="INTEGER" />
		<result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
	</resultMap>
	<resultMap id="BlackListResultMap" type="cn.sh.ideal.mgw.model.BlackList" >
    <id column="AUTO_ID" property="autoId" jdbcType="DECIMAL" />
    <result column="BLACK_USER" property="blackUser" jdbcType="VARCHAR" />
    <result column="BLACK_BEGIN_TIME" property="blackBeginTime" jdbcType="VARCHAR" />
    <result column="BLACK_END_TIME" property="blackEndTime" jdbcType="VARCHAR" />
    <result column="BLACK_CREATE_ID" property="blackCreateId" jdbcType="VARCHAR" />
    <result column="BLACK_CREATE_DATE" property="blackCreateDate" jdbcType="TIMESTAMP" />
    <result column="BLACK_UPDATE_ID" property="blackUpdateId" jdbcType="VARCHAR" />
    <result column="BLACK_UPDATE_DATE" property="blackUpdateDate" jdbcType="TIMESTAMP" />
    <result column="BLACK_DELETE_ID" property="blackDeleteId" jdbcType="VARCHAR" />
    <result column="BLACK_DELETE_DATE" property="blackDeleteDate" jdbcType="TIMESTAMP" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="BLACK_END_DATE" property="blackEndDate" jdbcType="TIMESTAMP" />
    <result column="BLACK_BEGIN_DATE" property="blackBeginDate" jdbcType="TIMESTAMP" />
  </resultMap>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- 获取新的SessionId -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="newMessageId" resultType="string">
		SELECT SEQ_MGW_MESSAGE.NEXTVAL FROM DUAL
	</select>
	<select id="getSysParam" resultType="hashmap" parameterType="string">
		SELECT * FROM CMS_SYS_PARAM t WHERE t.PARAM_CODE = #{code,jdbcType=VARCHAR}
	</select>
	
	<select id="getSysParamValue" resultType="string" parameterType="string">
		SELECT param_value FROM CMS_SYS_PARAM t WHERE t.PARAM_CODE = #{code,jdbcType=VARCHAR}
	</select>
	

<!-- 	<select id="getTenantWhiteList" parameterType="string" resultMap="WhiteListResultMap"> -->
<!-- 		SELECT -->
<!-- 			a.* -->
<!-- 		FROM CMS_WHITELIST a -->
<!-- 		WHERE a.TENANT_ID = #{tenantId,jdbcType=INTEGER} -->
<!-- 	</select> -->

	<select id="getTenantBlackList" parameterType="string" resultMap="BlackListResultMap">
		SELECT
			a.*
		FROM CMS_BLACKLIST a
		WHERE a.TENANT_ID = #{tenantId,jdbcType=INTEGER}
	</select>
	
</mapper>