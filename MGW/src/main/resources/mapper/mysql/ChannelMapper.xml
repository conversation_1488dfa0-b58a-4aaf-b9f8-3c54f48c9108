<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.ChannelDao">
	<select id="getChannelConfigByAppidAndSecret" parameterType="hashmap" resultType="cn.sh.ideal.model.ChannelConfig">
		SELECT 	ID id, 
	CHANNEL_CODE channelCode, 
	CHANNEL_ACCOUNT channelAccount, 
	ACCOUNT_CONFIG accountConfig, 
	CHANNEL_ENABLE channelEnable, 
	TENANT tenantCode, 
	REMARK remark
	FROM  MGW_TENANT_CHANNEL_CONFIG
	 WHERE  INSTR(ACCOUNT_CONFIG, #{appid,jdbcType=VARCHAR}) > 0 AND INSTR(ACCOUNT_CONFIG, #{secret,jdbcType=VARCHAR})> 0 
	 LIMIT 0, 1
	</select>
	
	<insert id="insertConfig" parameterType="cn.sh.ideal.model.ChannelConfig" >

INSERT INTO MGW_TENANT_CHANNEL_CONFIG 
	(ID, 
	CHANNEL_CODE, 
	CHANNEL_ACCOUNT, 
	ACCOUNT_CONFIG, 
	CHANNEL_ENABLE, 
	TENANT, 
	REMARK
	)
	VALUES
	(#{id,jdbcType=VARCHAR}, 
	#{channelCode,jdbcType=VARCHAR}, 
	#{channelAccount,jdbcType=VARCHAR}, 
	#{accountConfig,jdbcType=VARCHAR}, 
	#{channelEnable,jdbcType=VARCHAR}, 
	#{tenantCode,jdbcType=VARCHAR}, 
	#{remark,jdbcType=VARCHAR}
	);
		
	</insert>
	
	<update id="disableByTenantId" parameterType="string">
		update MGW_TENANT_CHANNEL_CONFIG set CHANNEL_ENABLE = '0' where tenant = #{id,jdbcType=VARCHAR}
	</update>
	
	<select id="existsFollowUser" parameterType="hashmap" resultType="boolean">
		select count(*) from IMR_FOLLOWS_INFO where open_id = #{openId,jdbcType=VARCHAR} and tenant_id = #{tenantId,jdbcType=VARCHAR} 
		and channel_id = #{channelId,jdbcType=VARCHAR} 
	</select>
	
	<select id="existsBindingUser" parameterType="hashmap" resultType="boolean">
		select count(*) from IMR_BINDING_USER where open_id = #{openId,jdbcType=VARCHAR} and tenant_id = #{tenantId,jdbcType=VARCHAR} 
		and channel_id = #{channelId,jdbcType=VARCHAR} 
	</select>
	
	
	<select id="existsAccount" parameterType="cn.sh.ideal.model.ChannelConfig" resultType="boolean">
		select count(*) from MGW_TENANT_CHANNEL_CONFIG where channel_account = #{channelAccount,jdbcType=VARCHAR} and channel_Code = #{channelCode,jdbcType=VARCHAR}
	</select>
	
	<select id="getChannelConfigByTenantId" parameterType="string" resultType="cn.sh.ideal.model.ChannelConfig">
		SELECT 	ID id, 
	CHANNEL_CODE channelCode, 
	CHANNEL_ACCOUNT channelAccount, 
	ACCOUNT_CONFIG accountConfig, 
	CHANNEL_ENABLE channelEnable, 
	TENANT tenantCdoe, 
	REMARK remark
	FROM  MGW_TENANT_CHANNEL_CONFIG
	 WHERE TENANT = #{id,jdbcType=VARCHAR}
	 LIMIT 0, 1
	</select>
	
	<select id="getChannelConfigById" parameterType="string" resultType="cn.sh.ideal.model.ChannelConfig">
		SELECT 	ID id, 
	CHANNEL_CODE channelCode, 
	CHANNEL_ACCOUNT channelAccount, 
	ACCOUNT_CONFIG accountConfig, 
	CHANNEL_ENABLE channelEnable, 
	TENANT tenantCode, 
	SELF_TYPE selfType,
	REMARK remark
	FROM  MGW_TENANT_CHANNEL_CONFIG
	 WHERE id = #{id,jdbcType=VARCHAR}
	</select>
	
	
	<select id="getChannelConfigsByTenantId" parameterType="string" resultType="cn.sh.ideal.model.ChannelConfig">
		SELECT t1. ID id,
		t1. CHANNEL_CODE channelCode,
		t2.channel_name channelName,
		t1. CHANNEL_ACCOUNT channelAccount,
		t1.ACCOUNT_CONFIG accountConfig,
		t1. CHANNEL_ENABLE channelEnable,
		t1. TENANT tenantCode,
		t1. REMARK remark
		FROM MGW_TENANT_CHANNEL_CONFIG t1
		left join mgw_channel_info t2
		on t2.channel_code = t1.channel_code
		WHERE tenant = #{tenantId, jdbcType = VARCHAR}
		and channel_enable = '1'
	</select>
	
	<select id="updateChannelConfig" parameterType="cn.sh.ideal.model.ChannelConfig">
		update MGW_TENANT_CHANNEL_CONFIG set ACCOUNT_CONFIG = #{accountConfig,jdbcType=VARCHAR} where id = #{id,jdbcType=VARCHAR}
	</select>
	
	<select id="getAccountByAppidAndSecret" parameterType="hashmap" resultType="string">
		select account from MGW_CHANNEL_ACCOUNT where app_id = #{appid,jdbcType=VARCHAR} and app_secret = #{secret,jdbcType=VARCHAR}
	</select>
	
	<select id="querySummarys" parameterType="String" resultType="cn.sh.ideal.mgw.model.Summary">
		
SELECT 	ID id, 
	TENANT_CODE tenantCode, 
	CHANNEL_CODE channelCode, 
	SKILL_QUEUE skillQueue, 
	WORK_NO workNo, 
	SESSION_ID sessionId, 
	START_TIME startTime, 
	END_TIME endTime, 
	DURATION duration, 
	CUSTOMER_ID customerId, 
	CUSTOMER_NAME customerName, 
	BUSINESS_TYPE businessType, 
	SUMMARY summary, 
	SATISFACTION satisfaction, 
	REMARK remark, (select level from CMS_CUSTOMER_INFO where customer_id = T.customer_id and status = '1' and T.customer_id is not null) as customerLevel
	FROM 
	SRV_SUMMARY T WHERE ID > #{id,jdbcType=VARCHAR}
	ORDER BY ID
	LIMIT 0, 200
		
	</select>
	
	<select id="getAccountConfigByChannelCode" parameterType="string" resultType="string">
	
		SELECT ACCOUNT_CONFIG FROM MGW_TENANT_CHANNEL_CONFIG WHERE CHANNEL_CODE = #{0}
	
	</select>
</mapper>