<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.QQDao" >
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- ResultMap                                                    -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <resultMap id="ResultMap" type="cn.sh.ideal.mgw.model.QQBean" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="SYSTEM_ID" property="systemId" jdbcType="VARCHAR" /> 
    <result column="LOGIN_USERNAME" property="loginUsername" jdbcType="VARCHAR" />
    <result column="LOGIN_PASSWORD" property="loginPassword" jdbcType="VARCHAR" />
    <result column="USER_TYPE" property="userType" jdbcType="VARCHAR" />
    <result column="STAFF_ID" property="staffId" jdbcType="VARCHAR" />
    <result column="LOGIN_FLAG" property="loginFlag" jdbcType="VARCHAR" />
  </resultMap>
  
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Query                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="query" resultMap="ResultMap" parameterType="cn.sh.ideal.mgw.model.QQBean" >
    SELECT  * FROM MGW_QQ
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QueryCount                                                   -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryCount" resultType="int" parameterType="cn.sh.ideal.mgw.model.QQBean" >
    SELECT  COUNT(1) FROM MGW_QQ
    <include refid="where" />
  </select>
  
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Where                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <sql id="where" >
    <where >
       <if test="systemId != null and systemId != ''">
        SYSTEM_ID = #{systemId,jdbcType=VARCHAR}
      </if>
      <if test="loginUsername != null and loginUsername != ''" >
      and  LOGIN_USERNAME = #{loginUsername,jdbcType=VARCHAR}
      </if>
      <if test="userType != null and userType != ''" >
      and  USER_TYPE = #{userType,jdbcType=VARCHAR}
      </if>
      <if test="staffId != null and staffId != ''" >
      and  STAFF_ID = #{staffId,jdbcType=VARCHAR}
      </if>      
    </where>
  </sql>
</mapper>