<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.QrCodeDao">
	<resultMap id="ResultMap" type="cn.sh.ideal.mgw.model.QrCode">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<id column="ID" property="id" jdbcType="INTEGER" />
		<result column="CHANNEL_ACCOUNT" property="channelAccount"
			jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="PLATFORM_USER" property="platformUser"
			jdbcType="VARCHAR" />
		<result column="ACTION_NAME" property="actionName" jdbcType="VARCHAR" />
		<result column="SCENE_ID" property="sceneId" jdbcType="VARCHAR" />
		<result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
		<result column="USAGE_TYPE" property="usageType" jdbcType="VARCHAR" />
		<result column="TICKET" property="ticket" jdbcType="VARCHAR" />
		<result column="FROM_CHANNEL_ACCOUNT" property="fromChannelAccount" jdbcType="VARCHAR" />
	</resultMap>
	<select id="load" resultMap="ResultMap" parameterType="java.lang.Long">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select
		*
		from MGW_QRCODE
		where ID = #{id,jdbcType=INTEGER}
	</select>
	<select id="getNextSceneId" resultType="string" parameterType="string">
		select IFNULL(max(SCENE_ID),0)+1 from MGW_QRCODE where
		TENANT_CODE=#{sceneId,jdbcType=VARCHAR} AND ACTION_NAME='QR_LIMIT_SCENE'

	</select>
	
	<select id="nextQrcodeVal" resultType="string" >
		select nextval('sceneId')
	</select>

	<delete id="delete" parameterType="java.lang.Long">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		delete from MGW_QRCODE
		where ID = #{id,jdbcType=INTEGER}
	</delete>
	
	<insert id="insert" parameterType="cn.sh.ideal.mgw.model.QrCode" useGeneratedKeys="true" keyProperty="id">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		insert into MGW_QRCODE ( CHANNEL_ACCOUNT, TENANT_CODE,
		PLATFORM_USER, ACTION_NAME, SCENE_ID,
		CREATE_DATE, USAGE_TYPE,TICKET,FROM_CHANNEL_ACCOUNT)
		values (#{channelAccount,jdbcType=VARCHAR},
		#{tenantCode,jdbcType=VARCHAR},
		#{platformUser,jdbcType=VARCHAR}, #{actionName,jdbcType=VARCHAR}, #{sceneId,jdbcType=VARCHAR},
		#{createDate,jdbcType=TIMESTAMP}, #{usageType,jdbcType=VARCHAR},#{ticket,jdbcType=VARCHAR}, #{fromChannelAccount,jdbcType=VARCHAR})
	</insert>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="ResultMap"
		parameterType="cn.sh.ideal.mgw.model.QrCode">
		SELECT * FROM MGW_QRCODE
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.QrCode"> 
		SELECT COUNT(1) FROM MGW_QRCODE
		<include refid="where" />
	</select>

	<update id="update" parameterType="cn.sh.ideal.mgw.model.QrCode">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		update MGW_QRCODE
		<set>
			<if test="tenantChannelId != null">
				CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR},
			</if>
			<if test="tenantCode != null">
				TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
			</if>
			<if test="platformUser != null">
				PLATFORM_USER = #{platformUser,jdbcType=VARCHAR},
			</if>
			<if test="actionName != null">
				ACTION_NAME = #{actionName,jdbcType=VARCHAR},
			</if>
			<if test="sceneId != null">
				SCENE_ID = #{sceneId,jdbcType=VARCHAR},
			</if>
			<if test="createDate != null">
				CREATE_DATE = #{createDate,jdbcType=DATE},
			</if>
			<if test="usageType != null">
				USAGE_TYPE = #{usageType,jdbcType=VARCHAR},
			</if>
			<if test="ticket != null">
				TICKET = #{ticket,jdbcType=VARCHAR},
			</if>
		</set>
		where ID = #{id,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="cn.sh.ideal.mgw.model.QrCode">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		update MGW_QRCODE
		set CHANNEL_ACCOUNT = #{tenantChannelId,jdbcType=VARCHAR},
		TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
		PLATFORM_USER = #{platformUser,jdbcType=VARCHAR},
		ACTION_NAME = #{actionName,jdbcType=VARCHAR},
		SCENE_ID = #{sceneId,jdbcType=VARCHAR},
		CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
		USAGE_TYPE = #{usageType,jdbcType=VARCHAR}
		where ID = #{id,jdbcType=INTEGER}
	</update>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="channelAccount != null and channelAccount !=''">
				CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR}
			</if>
			<if test="tenantCode != null and tenantCode !=''">
				and TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
			</if>
			<if test="platformUser != null and platformUser !=''">
				and PLATFORM_USER = #{platformUser,jdbcType=VARCHAR}
			</if>
			<if test="actionName != null and actionName !=''">
				and ACTION_NAME = #{actionName,jdbcType=VARCHAR}
			</if>
			<if test="sceneId != null and sceneId !=''">
				and SCENE_ID = #{sceneId,jdbcType=VARCHAR}
			</if>
			<if test="createDate != null and createDate !=''">
				and CREATE_DATE = #{createDate,jdbcType=DATE}
			</if>
			<if test="usageType != null and tusageType !=''">
				and USAGE_TYPE = #{usageType,jdbcType=VARCHAR}
			</if>
			<if test="ticket != null and ticket !=''">
				and TICKET = #{ticket,jdbcType=VARCHAR}
			</if>
			<if test="fromChannelAccount != null and fromChannelAccount !=''">
				FROM_CHANNEL_ACCOUNT = #{fromChannelAccount,jdbcType=VARCHAR}
			</if>
		</where>

	</sql>

</mapper>