<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.MessageInfoSendDao">

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- ResultMap -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<resultMap id="SendResultMap"
		type="cn.sh.ideal.model.MessageInfoSend">
		<id column="MSGID" property="msgId" jdbcType="INTEGER" />
			
		<result column="MSGID" property="msgId" jdbcType="VARCHAR" />
		<result column="CHANNELCODE" property="channelCode" jdbcType="VARCHAR" />
		<result column="SENDACCOUNT" property="sendAccount" jdbcType="VARCHAR" />
		<result column="ACCEPTACCOUNT" property="acceptAccount" jdbcType="VARCHAR" />
		<result column="MSGTYPE" property="msgType" jdbcType="VARCHAR" />
		<result column="TYPE" property="type" jdbcType="VARCHAR" />
		<result column="WEIBOID" property="weiboId" jdbcType="VARCHAR" />
		<result column="COMMENTID" property="commentId" jdbcType="VARCHAR" />
		<result column="REPLYID" property="replyId" jdbcType="VARCHAR" />
		<result column="CONTENT" property="content" jdbcType="VARCHAR" />
		<result column="CREATETIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="RESULT" property="result" jdbcType="VARCHAR" />
		<result column="WorkNo" property="workNo" jdbcType="VARCHAR" />
	</resultMap>
	
	<resultMap id="WeiboResultMap"
		type="cn.sh.ideal.mgw.model.WeiboMsgInfo">
		<id column="WEIBOID" property="weiboId" jdbcType="VARCHAR" />			
		<result column="CHANNELCODE" property="channelCode" jdbcType="VARCHAR" />
		<result column="SENDACCOUNT" property="sendAccount" jdbcType="VARCHAR" />
		<result column="ACCEPTACCOUNT" property="acceptedAccount" jdbcType="VARCHAR" />
		<result column="CONTENT" property="content" jdbcType="VARCHAR" />
		<result column="CREATETIME" property="createTime" jdbcType="TIMESTAMP" />
	</resultMap>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Insert -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<insert id="insert"
		parameterType="cn.sh.ideal.model.MessageInfoSend">
		insert into MGW_MESSAGE_SEND (CHANNELCODE,SENDACCOUNT,ACCEPTACCOUNT,MSGTYPE,TYPE,WEIBOID,COMMENTID,REPLYID,CONTENT,CREATETIME,STATUS,RESULT,WorkNo,TENANT_CODE,ACTIVE_TYPE)
	values
	(#{channelCode,jdbcType=VARCHAR},
	#{sendAccount,jdbcType=VARCHAR},
	#{acceptAccount,jdbcType=VARCHAR},
	#{msgType,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
	#{weiboId,jdbcType=VARCHAR}, #{commentId,jdbcType=VARCHAR},
	#{replyId,jdbcType=VARCHAR},
	#{content,jdbcType=VARCHAR},now(),
	#{status,jdbcType=VARCHAR},#{result,jdbcType=VARCHAR},
	#{workNo,jdbcType=VARCHAR},
	#{tenantCode,jdbcType=VARCHAR},
	#{activeType,jdbcType=VARCHAR}
	)
	</insert>
	
	<select id="historyInfo" resultMap="WeiboResultMap" parameterType="cn.sh.ideal.model.MessageInfoSend">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select WEIBOID,CHANNELCODE,SENDACCOUNT,ACCEPTACCOUNT,CONTENT,CREATETIME
		from MGW_MESSAGE_SEND
		where STATUS = '0' AND MSGTYPE = #{msgType,jdbcType=VARCHAR}
		<if test="type != null and type !=''">
			AND	TYPE = #{type,jdbcType=VARCHAR}
		</if>
		<if test="workNo != null and workNo !=''">
			AND	WORKNO = #{workNo,jdbcType=VARCHAR}
		</if>
		<if test="content != null and content !=''">
			AND	content like '%${content}%'
		</if>
		<if test="startTime !=null and startTime !=''">
                AND CREATETIME &gt;= #{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
                AND CREATETIME &lt;= #{endTime}
        </if>
         <if test="activeType !=null and activeType !=''">
                AND active_type = #{activeType,jdbcType=VARCHAR}
        </if>
       ORDER BY createtime DESC
	</select>
	
	<select id="historyInfoquery" resultMap="WeiboResultMap" parameterType="cn.sh.ideal.model.MessageInfoSend">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select WEIBOID,CHANNELCODE,SENDACCOUNT,ACCEPTACCOUNT,CONTENT,CREATETIME
		from MGW_MESSAGE_SEND
		where STATUS = '0' AND MSGTYPE = #{msgType,jdbcType=VARCHAR}
		<if test="type != null and type !=''">
			AND	TYPE = #{type,jdbcType=VARCHAR}
		</if>
	</select>
</mapper>