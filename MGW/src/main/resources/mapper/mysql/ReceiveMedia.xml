<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.MediaReceiveDao">

	<resultMap id="WhiteListResultMap" type="cn.sh.ideal.mgw.model.WhiteList">
		<id column="AUTO_ID" property="autoId" jdbcType="INTEGER" />
		<result column="WHITE_USER" property="whiteUser" jdbcType="VARCHAR" />
		<result column="WHITE_BEGIN_TIME" property="whiteBeginTime" jdbcType="TIMESTAMP" />
		<result column="WHITE_END_TIME" property="whiteEndTime" jdbcType="TIMESTAMP" />
		<result column="WHITE_CREATE_ID" property="whiteCreateId" jdbcType="VARCHAR" />
		<result column="WHITE_CREATE_DATE" property="whiteCreateDate" jdbcType="TIMESTAMP" />
		<result column="WHITE_UPDATE_ID" property="whiteUpdateId" jdbcType="VARCHAR" />
		<result column="WHITE_UPDATE_DATE" property="whiteUpdateDate" jdbcType="TIMESTAMP" />
		<result column="WHITE_DELETE_ID" property="whiteDeleteId" jdbcType="VARCHAR" />
		<result column="WHITE_DELETE_DATE" property="whiteDeleteDate" jdbcType="TIMESTAMP" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="TENANT_ID" property="tenantId" jdbcType="INTEGER" />
		<result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
	</resultMap>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- 获取新的SessionId -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
<!-- 	<select id="newMessageId" resultType="string"> -->
<!-- 		SELECT NEXTVAL('MESSAGE_ID') -->
<!-- 	</select> -->

<!-- 	<select id="getTenantWhiteList" parameterType="string" resultMap="WhiteListResultMap"> -->
<!-- 		SELECT -->
<!-- 			a.* -->
<!-- 		FROM CMS_WHITELIST a -->
<!-- 		WHERE a.TENANT_ID = #{tenantId,jdbcType=INTEGER} -->
<!-- 	</select> -->
	
	<!-- 获取敏感词 -->
<!--    <select id="querySenstive"  resultType="java.util.HashMap"> -->
<!-- 		 select t.sensitive_word,t.sensitive_type from t_media_sensitive t where t.status = '0' -->
<!--    </select> -->
   
   <!-- 获取黑名单 -->
<!--    <select id="queryBlack"  resultType="java.util.HashMap"> -->
<!-- 		 select t.black_user,t.black_type from CMS_BLACKLIST t where t.status = '0' and -->
<!-- 		 <![CDATA[ t.black_end_time >= DATE_FORMAT(SYSDATE(),'%H:%i:%s') AND t.black_begin_time <= DATE_FORMAT(SYSDATE(),'%H:%i:%s')   ]]> -->
<!--    </select> -->
   
   <!-- 获取垃圾用户 -->
<!--    <select id="queryRubbish"  resultType="java.util.HashMap"> -->
<!-- 		 select t.rubbish_user,t.rubbish_type from T_MEDIA_RUBBISHLIST t where t.status = '0'  -->
<!--    </select> -->
   
   <!-- 获取拦截策略 -->
<!--    <select id="queryStrategy"  resultType="java.util.HashMap"> -->
<!-- 		 select t.strategy_code,t.reply_content,t.strategy_type,t.class_path_name,t.is_auto_reply,t.file_names from T_MEDIA_INTERCEPT_STRATEGY t where t.status = '0' -->
<!-- 		    order by t.sort -->
<!--    </select> -->
   
   <!-- 获取FTP信息 -->
<!--    <select id="queryFtpInfo"  resultType="java.util.HashMap"> -->
<!-- 		 select t.code_value,t.code_desc from t_media_param t where  t.param_code = 'FTP_UPLOAD_URL' -->
<!--    </select> -->
   
    <!-- 获取系统访问 -->
<!--    <select id="querySysPathInfo"  resultType="java.util.HashMap"> -->
<!-- 		 select t.code_value,t.code_desc from t_media_param t where  t.param_code = 'SYSTEM_PATH' -->
<!--    </select> -->
   
   <!-- 获取未拦截任务自动回复内容 -->
<!--    <select id="queryReplyContent"  resultType="java.util.HashMap"> -->
<!-- 		 select t.reply_content,t.channel_type,t.is_do,t.file_names from t_media_autoreply_content t  -->
<!--    </select> -->
   
   <!-- 获取邮箱服务器 -->
<!--    <select id="queryMediaMail"  resultType="java.util.HashMap"> -->
<!-- 		 select t.mail_address,t.mail_amount,t.mail_server,t.mail_password,t.mail_filesize,t.mail_host,t.mail_port,t.mail_pop_server from t_media_mail t where t.status = '0' -->
<!--    </select> -->
   
   <!-- 获取短信网关 -->
<!--    <select id="queryMediaSms"  resultType="java.util.HashMap"> -->
<!-- 		select t.sms_address,t.sms_account,t.sms_pass,t.sms_tel from t_media_sms t where t.status = '0' -->
<!--    </select> -->
   
   <!-- 获取开关  -->
<!--    <select id="queryMediaSwitch"  parameterType="java.lang.String" resultType="java.lang.String"> -->
<!-- 		select t.switch from T_MEDIA_SWITCH t where t.xh = 'MEDIA_SWITCH' and t.channel_type =#channelType# -->
<!--    </select> -->
   
   <!-- 获取开始接收邮件日期  -->
<!--    <select id="queryMediaFlag"  parameterType="java.lang.String" resultType="java.lang.String"> -->
<!-- 		select to_char(t.begin,'YYYY-MM-DD HH24:MI:SS') from T_MEDIA_FLAG t where t.mail_address = #mailAddress# -->
<!--    </select> -->
   
   <!-- 更改开始接收邮件日期  -->
   <update id="updateMediaFlag"  parameterType="java.util.HashMap">
		update T_MEDIA_FLAG t set t.begin = to_date(#time#,'YYYY-MM-DD HH24:MI:SS') where t.mail_address = #mailAddress#
   </update>
   
  
  
  
  
  
  <!-- 插入拦截任务表 -->
<!--    <insert id="insertInterceptMedia" parameterType="mediaModel"> -->
<!--      insert into t_media_intercept_task -->
<!--                            (task_id, media_id,  -->
<!--                             media_title, media_content,  -->
<!--                             media_sender,media_receive, -->
<!--                             media_send_time, media_receive_time, -->
<!--                             channel_type,info_type,media_business_type, -->
<!--                             intercept_type) -->
<!--                     values (#taskId#, #mediaId#, -->
<!--                             #mediaTitle:VARCHAR:NULL#, #mediaContent:VARCHAR:NULL#,  -->
<!--                             #mediaSender#,#mediaReceiver#, -->
<!--                             to_date(#mediaSendTime#, 'yyyy-mm-dd HH24:mi:ss'), -->
<!--                             sysdate, #channelType#, -->
<!--                             #infoType#,#mediaBusinessType#,#interceptType#) -->
<!--   </insert> -->
  
  <!-- 插入拦截任务扩展表 -->
<!--    <insert id="insertInterceptMediaExt" parameterType="cn.sh.ideal.mgw.receivemedia.model.MediaModel"> -->
<!--      insert into t_media_intercept_ext_task -->
<!--                            (task_id, media_id, -->
<!--                             media_copyer) -->
<!--                     values (#taskId#, #mediaId#, -->
<!--                             #mediaCoper:VARCHAR:NULL#) -->
<!--   </insert> -->
  
  
  
  
  <!-- 插入附件表 -->
   <insert id="addTaskFile" parameterType="cn.sh.ideal.mgw.model.MediaModel">
     insert into MGW_EMAIL_FILE
                           (MESSAGE_ID,
                            FILE_NAME,URL)
                    values ( 
                   			#{taskId,jdbcType=VARCHAR},#{mediaFileName,jdbcType=VARCHAR},#{mediaFilePath,jdbcType=VARCHAR}
                    )
  </insert>
  
   <!-- 插入文件表 -->
   <insert id="addTaskTxt" parameterType="cn.sh.ideal.mgw.model.MediaModel">
     insert into MGW_EMAIL_TXT
                           (MESSAGE_ID,
                            TXT_NAME,URL)
                    values ( #{taskId,jdbcType=VARCHAR},#{mediaTxtName,jdbcType=VARCHAR},#{mediaTxtPath,jdbcType=VARCHAR}
                            )
  </insert>
  
  
  <!-- 插入自动回复日志 -->
<!--    <insert id="insertReplyLog" parameterType="mediaModel"> -->
<!--      insert into t_media_autoreply_log -->
<!--                            (task_id, reply_id,reply_type,reply_user_id,reply_create_date,sender,receiver) -->
<!--                     values (#taskId#, #replyContent:VARCHAR:NULL#,#channelType#, -->
<!--                             #isReply#,sysdate,#mediaReceiver#,#mediaSender#) -->
<!--   </insert> -->
  
  <!-- 插入接收错误日志信息 -->
<!--   <insert id="insertReceiveErrorLog" parameterType="java.util.HashMap"> -->
<!--     insert into t_media_receive_error_log -->
<!--                            (media_type, error_message,time,media_server, -->
<!--                             media_sender,pro_type,media_title,media_content,task_id) -->
<!--                     values (#media_type#, #error_message#,sysdate, -->
<!--                             #media_server#,#media_sender#,#pro_type#, -->
<!--                             #media_title:VARCHAR:NULL#,#media_content:VARCHAR:NULL#,#task_id#) -->
<!--   </insert> -->
  
  <!-- 获取系统时间 -->
<!--    <select id="querySysDate"  resultType="java.util.HashMap"> -->
<!-- 		 select date_format(sysdate(),'%Y-%m-%d %H:%i:%s') as currentDate,date_format(sysdate(),'%Y-%m-%d') as currentReceiveDate from dual -->
<!--    </select> -->
   
   <!-- 插入接收邮件日志信息 -->
<!--   <insert id="insertReceiveMailLog" parameterType="java.util.HashMap"> -->
<!--     insert into t_meida_mail_log -->
<!--                            (mail_server, begin_date,end_date,amount, -->
<!--                             time) -->
<!--                     values (#mail_server#, #begin_date#,#end_date#,#amount#,sysdate) -->
<!--   </insert> -->
  
   <!-- 获取信息类型 -->
  <select id="getInfoType" parameterType="cn.sh.ideal.mgw.model.MediaModel" resultType="java.lang.String">
       select t.skillid from t_meida_multiskill t where t.multi_address =#mediaReceiver# and t.channel_type=#channelType#
   </select>
   
   <!-- 获取微信易信账号 -->
  <select id="getWyInfo" parameterType="java.util.HashMap" resultType="java.lang.String">
       select t.user_name from t_media_wyconfig t where t.user_id =#userId# and t.channel_type =#channelType#
   </select>
	
</mapper>