<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.WeiboCommentsDao">
	<resultMap id="weiboResultMap"
		type="cn.sh.ideal.model.WeiboComentsSum">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="weiboId" property="id" jdbcType="VARCHAR" />
		<result column="comments" property="comments" jdbcType="INTEGER" />
		<result column="reposts" property="reposts" jdbcType="INTEGER" />
		<result column="attitudes" property="attitudes" jdbcType="INTEGER" />
		<result column="createTime" property="createTime" jdbcType="DATE" />
		<result column="updateTime" property="updateTime" jdbcType="DATE" />
	</resultMap>


	<select id="query" resultMap="weiboResultMap"
		parameterType="cn.sh.ideal.model.WeiboComentsSum">
		SELECT * FROM mgw_comments_count
		<include refid="where" />
	</select>
	
	
	<update id="update" parameterType="cn.sh.ideal.model.WeiboComentsSum">
		UPDATE mgw_comments_count
		<set>
			<if test="comments !=null and  comments !=''">
				comments = #{comments ,jdbcType=INTEGER},
			</if>
			<if test="reposts !=null and  reposts !=''">
				reposts = #{reposts ,jdbcType=INTEGER},
			</if>
			<if test="attitudes !=null and  attitudes !=''">
				attitudes = #{attitudes ,jdbcType=INTEGER},
			</if>
				updateTime =sysdate(),
		</set>
		WHERE weiboId = #{id ,jdbcType=VARCHAR}
	</update>
	
	
	<insert id="insert" parameterType="cn.sh.ideal.model.WeiboComentsSum" useGeneratedKeys="true" keyProperty="id">
		
	insert into mgw_comments_count (weiboId, comments,
	reposts, attitudes,createTime,updateTime)
	values
	(
		#{id,jdbcType=VARCHAR},
		#{comments,jdbcType=INTEGER},
		#{reposts,jdbcType=INTEGER},
		#{attitudes,jdbcType=INTEGER},
		sysdate(),
		#{updateTime,jdbcType=TIMESTAMP}
	)
	</insert>
	
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Thu Jun 05 16:38:50 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="id != null and id !=''">
				and
				weiboId = #{id ,jdbcType=VARCHAR}
			</if>
		</where>
	</sql>
</mapper>