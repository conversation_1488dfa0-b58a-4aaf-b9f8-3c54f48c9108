<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.CommonDao">

	<resultMap id="WhiteListResultMap" type="cn.sh.ideal.mgw.model.WhiteList">
		<id column="AUTO_ID" property="autoId" jdbcType="BIGINT" />
		<result column="WHITE_USER" property="whiteUser" jdbcType="VARCHAR" />
		<result column="WHITE_BEGIN_TIME" property="whiteBeginTime" jdbcType="TIMESTAMP" />
		<result column="WHITE_END_TIME" property="whiteEndTime" jdbcType="TIMESTAMP" />
		<result column="WHITE_CREATE_ID" property="whiteCreateId" jdbcType="VARCHAR" />
		<result column="WHITE_CREATE_DATE" property="whiteCreateDate" jdbcType="TIMESTAMP" />
		<result column="WHITE_UPDATE_ID" property="whiteUpdateId" jdbcType="VARCHAR" />
		<result column="WHITE_UPDATE_DATE" property="whiteUpdateDate" jdbcType="TIMESTAMP" />
		<result column="WHITE_DELETE_ID" property="whiteDeleteId" jdbcType="VARCHAR" />
		<result column="WHITE_DELETE_DATE" property="whiteDeleteDate" jdbcType="TIMESTAMP" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="TENANT_ID" property="tenantId" jdbcType="INTEGER" />
		<result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
	</resultMap>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- 获取新的SessionId -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="newMessageId" resultType="string">
		SELECT NEXTVAL('MESSAGE_ID')
	</select>
	<select id="getSysParam" resultType="hashmap" parameterType="string">
		SELECT * FROM CMS_SYS_PARAM t WHERE t.PARAM_CODE = #{code,jdbcType=VARCHAR}
	</select>

	<select id="getTenantWhiteList" parameterType="string" resultMap="WhiteListResultMap">
		SELECT
			a.*
		FROM CMS_WHITELIST a
		WHERE a.TENANT_ID = #{tenantId,jdbcType=INTEGER}
	</select>
	
</mapper>