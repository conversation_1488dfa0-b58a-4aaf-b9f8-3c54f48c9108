<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.SensitiveLogDao" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.mgw.model.SensitiveLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->  
    <result column="ID" property="id" jdbcType="INTEGER" />
    <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" />
    <result column="MESSAGE_BUSINESS" property="messageBusiness" jdbcType="VARCHAR" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="SEND_ACCOUT" property="sendAccout" jdbcType="VARCHAR" />
    <result column="ACCEPTED_ACCOUNT" property="acceptedAccount" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" /> 
    <result column="SENSITIVE_ID" property="sensitiveId" jdbcType="VARCHAR" />
    <result column="SENSITIVE_WORD" property="sensitiveWord" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="cn.sh.ideal.mgw.model.SensitiveLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into MGW_SENSITIVE_LOG ( CHANNEL_CODE, MESSAGE_BUSINESS, 
      TENANT_CODE, SEND_ACCOUT, ACCEPTED_ACCOUNT, 
      CREATE_TIME, SESSION_ID, STATUS, 
      CONTENT, SENSITIVE_ID, SENSITIVE_WORD
      )
    values (#{channelCode,jdbcType=VARCHAR}, #{messageBusiness,jdbcType=VARCHAR}, 
      #{tenantCode,jdbcType=VARCHAR}, #{sendAccout,jdbcType=VARCHAR}, #{acceptedAccount,jdbcType=VARCHAR}, 
     sysdate, #{sessionId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{sensitiveId,jdbcType=VARCHAR}, #{sensitiveWord,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.mgw.model.SensitiveLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into MGW_SENSITIVE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="channelCode != null" >
        CHANNEL_CODE,
      </if>
      <if test="messageBusiness != null" >
        MESSAGE_BUSINESS,
      </if>
      <if test="tenantCode != null" >
        TENANT_CODE,
      </if>
      <if test="sendAccout != null" >
        SEND_ACCOUT,
      </if>
      <if test="acceptedAccount != null" >
        ACCEPTED_ACCOUNT,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="sessionId != null" >
        SESSION_ID,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="sensitiveId != null" >
        SENSITIVE_ID,
      </if>
      <if test="sensitiveWord != null" >
        SENSITIVE_WORD,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="channelCode != null" >
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="messageBusiness != null" >
        #{messageBusiness,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="sendAccout != null" >
        #{sendAccout,jdbcType=VARCHAR},
      </if>
      <if test="acceptedAccount != null" >
        #{acceptedAccount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionId != null" >
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveId != null" >
        #{sensitiveId,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveWord != null" >
        #{sensitiveWord,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>