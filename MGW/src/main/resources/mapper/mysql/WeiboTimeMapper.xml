<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.WeiboTimeDao">

	<resultMap type="cn.sh.ideal.model.MessageInfoSend" id="baseMessageInfoMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="msg_id" property="msgId" jdbcType="VARCHAR" />
		<result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
		<result column="send_account" property="sendAccount" jdbcType="VARCHAR" />
		<result column="accept_account" property="acceptAccount" jdbcType="VARCHAR" />
		<result column="msg_type" property="msgType" jdbcType="VARCHAR" />
		<result column="type" property="type" jdbcType="VARCHAR" />
		<result column="weibo_id" property="weiboId" jdbcType="VARCHAR" />
		<result column="comment_id" property="commentId" jdbcType="VARCHAR" />
		<result column="reply_id" property="replyId" jdbcType="VARCHAR" />
		<result column="content" property="content" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="DATE" />
		<result column="is_time" property="isTime" jdbcType="VARCHAR" />
		<result column="date" property="date" jdbcType="VARCHAR" />
		<result column="work_no" property="workNo" jdbcType="VARCHAR" />
		<result column="active_type" property="activeType" jdbcType="VARCHAR" />
		<result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
		<result column="send_status" property="sendStatus" jdbcType="VARCHAR" />
	</resultMap>

	<insert id="insert" parameterType="cn.sh.ideal.model.MessageInfoSend">
		insert into mgw_weibo_time_send(
			msg_id,channel_code,send_account,accept_account,
			msg_type,type,weibo_id,comment_id,
			reply_id,content,create_time,is_time,date,
			work_no,active_type,tenant_code,send_status
		) value(
			#{msgId,jdbcType=VARCHAR},
			#{channelCode,jdbcType=VARCHAR},
			#{sendAccount,jdbcType=VARCHAR},
			#{acceptAccount,jdbcType=VARCHAR},
			#{msgType,jdbcType=VARCHAR},
			#{type,jdbcType=VARCHAR},
			#{weiboId,jdbcType=VARCHAR},
			#{commentId,jdbcType=VARCHAR},
			#{replyId,jdbcType=VARCHAR},
			#{content,jdbcType=VARCHAR},
			#{createTime,jdbcType=DATE},
			#{isTime,jdbcType=VARCHAR}, 
			#{date,jdbcType=VARCHAR}, 
			#{workNo,jdbcType=VARCHAR}, 
			#{activeType,jdbcType=VARCHAR}, 
			#{tenantCode,jdbcType=VARCHAR},
			"0"
		)
	</insert>
	
	<select id="queryList" parameterType="cn.sh.ideal.model.MessageInfoSend" resultMap="baseMessageInfoMap">
		select * from mgw_weibo_time_send where channel_code = #{channelCode,jdbcType=VARCHAR} 
		and send_account = #{sendAccount,jdbcType=VARCHAR} 
		and tenant_code =#{tenantCode,jdbcType=VARCHAR} 
		and send_status ='0'
	</select>
	
	<update id="updateTimeSendStatus" parameterType="cn.sh.ideal.model.MessageInfoSend">
		update mgw_weibo_time_send set send_status = '1' where id = #{id,jdbcType=VARCHAR}
	</update>
	
	<select id="getSystemTime" resultType="String">
		select now()	
	</select>
</mapper>