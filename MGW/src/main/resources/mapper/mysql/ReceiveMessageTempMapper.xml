<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mgw.dao.ReceiveMessageTempDao">

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- ResultMap -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<resultMap id="ResultMap"
		type="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		<id column="ID" property="id" jdbcType="INTEGER" />
		<result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
		<result column="MSG_TEMP_ID" property="messageTempId" jdbcType="VARCHAR" />
		<result column="CHANNEL_MSG_ID" property="channelMessageId"
			jdbcType="VARCHAR" />
		
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CHANNEL_ACCOUNT" property="channelAccount"
			jdbcType="VARCHAR" />
		<result column="OPER_TYPE" property="operType" jdbcType="VARCHAR" />	
	</resultMap>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Insert -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<insert id="insert"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		INSERT INTO MGW_RECEIVE_MESSAGE_TEMP (CHANNEL_MSG_ID,
		MSG_TEMP_ID,
		
		OPER_TYPE,
		TENANT_ID,
		CHANNEL_ACCOUNT ,
		CREATE_TIME
		)
		VALUES (#{channelMessageId,jdbcType=VARCHAR},
		#{messageTempId,jdbcType=VARCHAR}, 
		#{operType,jdbcType=VARCHAR},
		#{tenantId,jdbcType=VARCHAR},
		#{channelAccount,jdbcType=VARCHAR},now())
	</insert>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- getmessage -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="getMessageTempId" resultType="string" parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		SELECT MAX(MSG_TEMP_ID) FROM MGW_RECEIVE_MESSAGE_TEMP
		
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Load -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="load" resultMap="ResultMap"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		SELECT * FROM MGW_RECEIVE_MESSAGE_TEMP
		WHERE ID = #{id,jdbcType=INTEGER}
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="ResultMap"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		SELECT * FROM MGW_RECEIVE_MESSAGE_TEMP
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- QueryCount -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="queryCount" resultType="int"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		SELECT COUNT(1) FROM MGW_RECEIVE_MESSAGE_TEMP
		<include refid="where" />
	</select>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Update -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!--  TODO  这一句字段和表里的完全不对应 -->
	<update id="update"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		UPDATE MGW_RECEIVE_MESSAGE_TEMP
		<set>
			<if test="tenantId != null and tenantId != ''">
				TENANT_ID = #{tenantId,jdbcType=VARCHAR},
			</if>
			<if test="queueCode != null and queueCode != ''">
				QUEUE_CODE = #{queueCode,jdbcType=VARCHAR},
			</if>
			<if test="ruleCode != null and ruleCode != ''">
				RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
			</if>
			<if test="opTime != null and opTime != ''">
				OP_TIME = #{opTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null and remark != ''">
				REMARK = #{remark,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE ID = #{id,jdbcType=INTEGER}
	</update>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Delete -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<delete id="delete"
		parameterType="cn.sh.ideal.mgw.model.ReceiveMessageTemp">
		DELETE FROM MGW_RECEIVE_MESSAGE_TEMP
		WHERE ID = #{id,jdbcType=INTEGER}
	</delete>

	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="id != null and id != ''">
				ID = #{id,jdbcType=INTEGER}
			</if>
			<if test="channelMessageId != null and channelMessageId != ''">
				and CHANNEL_MSG_ID = #{channelMessageId,jdbcType=VARCHAR}
			</if>
			<if test="messageTempId != null and messageTempId != ''">
				and MSG_TEMP_ID = #{messageTempId,jdbcType=VARCHAR}
			</if>
			
			<if test="tenantId != null and tenantId != ''">
				and TENANT_ID = #{tenantId,jdbcType=VARCHAR}
			</if>
			<if test="channelAccount != null and channelAccount != ''">
				and CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR}
			</if>
			<if test="operType != null and operType != ''">
				and OPER_TYPE = #{operType,jdbcType=VARCHAR}
			</if>
		</where>
	</sql>
</mapper>