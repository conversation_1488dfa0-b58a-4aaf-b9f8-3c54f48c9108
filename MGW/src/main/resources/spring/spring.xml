<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
	<!-- 引入配置文件 -->
	<util:properties id="config" location="classpath:conf/app.properties" />
	<util:properties id="weiboConfig" location="classpath:conf/weibo.properties" />
	<util:properties id="weixinConfig" location="classpath:conf/openweixin.properties" />
	<context:property-placeholder location="classpath:conf/${env}/jdbc-${db}-${env}.properties" />
	<!-- 注解扫描包路径 -->
	<bean id="context" class="cn.sh.ideal.util.SpringContextUtil"/>
	<context:component-scan base-package="cn.sh.ideal" /> 
	<!-- 注解式事务 -->
<!-- 	<tx:annotation-driven transaction-manager="transactionManager" /> -->

	<!-- 拦截器方式配置事物 -->
	<tx:advice id="transactionAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="add*" propagation="REQUIRED" />
			<tx:method name="append*" propagation="REQUIRED" />
			<tx:method name="save*" propagation="REQUIRED" />
			<tx:method name="update*" propagation="REQUIRED" />
			<tx:method name="modify*" propagation="REQUIRED" />
			<tx:method name="edit*" propagation="REQUIRED" />
			<tx:method name="delete*" propagation="REQUIRED" />
			<tx:method name="remove*" propagation="REQUIRED" />
			<tx:method name="repair" propagation="REQUIRED" />
			<tx:method name="delAndRepair" propagation="REQUIRED" />
			<tx:method name="insert" propagation="REQUIRED" />
			<tx:method name="black*" propagation="REQUIRED" />
			
			<!-- 获取二维码时会调用nextval函数,这个函数不能在只读的连接中使用 ,默认即false-->
			<tx:method name="getQrCode" propagation="REQUIRED" read-only="false"/>

			<tx:method name="get*" propagation="REQUIRED" read-only="true" />
			<tx:method name="find*" propagation="REQUIRED" read-only="true" />
			<tx:method name="load*" propagation="REQUIRED" read-only="true" />
			<tx:method name="search*" propagation="REQUIRED" read-only="true" />
			<tx:method name="datagrid*" propagation="REQUIRED" read-only="true" />

		
		</tx:attributes>
	</tx:advice>
	<aop:config>
		<aop:pointcut id="transactionPointcut" expression="(execution(* cn.sh.ideal.mgw.service..*(..)))" />
		<aop:advisor pointcut-ref="transactionPointcut" advice-ref="transactionAdvice" />
	</aop:config>

	<!-- mybatis -->
	<import resource="spring-mybatis.xml" />
	<!-- httpclient -->
<!-- 	<import resource="spring-httpclient.xml" /> -->
<!-- 	<import resource="spring-scheduler.xml" /> -->
	
	<!-- DUBBO -->
	<import resource="mgw-provider.xml" />
	<import resource="mgw-consumer.xml" />
	
	<!-- redis -->
<!-- 	<import resource="spring-redis-sentinel.xml" /> -->
	<!-- <import resource="spring-redis.xml" /> -->
	<!-- plugin -->
	<!-- <import resource="spring-plugin.xml" /> -->
	<!-- task -->
	<import resource="spring-task.xml" />
</beans>