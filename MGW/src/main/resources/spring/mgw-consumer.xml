<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	
    <!-- 所有消费服务的默认超时设置 -->
    <dubbo:consumer timeout="${dubbo.consumer.timeout}">
        <!-- 停止服务超时设置，单位毫秒 -->
        <dubbo:parameter key="shutdown.timeout" value="${dubbo.shutdown.timeout}" />
    </dubbo:consumer>
    
     <dubbo:registry address="zookeeper://${zookeeper.address}"/>
    
    <!--生成远程服务代理,timeout=10000超时为10秒,retries=0表示不重试-->
    <dubbo:reference id="imrService" interface="cn.sh.ideal.imr.service.IMRService" check="false" version="1.0.0" retries="0"/>
    <dubbo:reference id="smService" interface="cn.sh.ideal.sm.service.SessionService" check="false" version="1.0.0" retries="0"/>
    <dubbo:reference id="transcodingService" interface="cn.sh.ideal.transcoding.service.TranscodingService" check="false" version="1.0.0" retries="0"/>
	
</beans>