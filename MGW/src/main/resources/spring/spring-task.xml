<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd">
	<task:executor id="executor" pool-size="5" />
	<task:scheduler id="scheduler" pool-size="10" />
	<task:annotation-driven executor="executor" scheduler="scheduler" />
	<!-- 邮件接收执行器线程池 -->
	<task:executor id="receiveWeiboThreadPool" pool-size="1" />
	<task:executor id="receiveThreadPool" pool-size="1" />
	<task:executor id="csSmsThreadPool" pool-size="1" />
	<!-- 微博发送执行器线程池 -->
	<task:executor id="weiboSendThreadPool" pool-size="1"/>
	<!-- 微博调用转发我的微博接口 -->
	<task:executor id="transpondWeiboThreadPool" pool-size="1" />
	<!-- QQ接收执行器线程池 -->
	<task:executor id="receiveQqThreadPool" pool-size="1" />
	<task:scheduled-tasks>
		<task:scheduled ref="receiveExecutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}" />
		<!-- <task:scheduled ref="qqReceiveExcutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}" /> -->
		<!-- <task:scheduled ref="univetroReceiveExcutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}"/> -->
		<!--  <task:scheduled ref="weiboReceiveExecutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}" />  -->
		<task:scheduled ref="weiboTimeSendExcutor" method="receive" fixed-delay="10000" /> 
		<!--  <task:scheduled ref="weiboReceiveExecutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}" />  -->
		<!-- <task:scheduled ref="weiboReceiveExecutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}" /> -->
		<!-- <task:scheduled ref="csSmsExcutor" method="resend" fixed-delay="#{config['receive.fixedDelay']}" /> -->
		<!--实时去跑调用微博转发 -->
		<!--<task:scheduled ref="WeiboTranspondExcutor" method="receive" fixed-delay="#{config['receive.fixedDelay']}" /> -->
	</task:scheduled-tasks> 
</beans>
