<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:p="http://www.springframework.org/schema/p"
  xmlns:context="http://www.springframework.org/schema/context"
  xmlns:aop="http://www.springframework.org/schema/aop"
  xmlns:tx="http://www.springframework.org/schema/tx"
  xmlns:util="http://www.springframework.org/schema/util"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context-3.2.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
    http://www.springframework.org/schema/aop
    http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
    http://www.springframework.org/schema/util 
    http://www.springframework.org/schema/util/spring-util-3.2.xsd">

    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
    destroy-method="close" >
    <property name="driverClassName">
      <value>${jdbc_driverClassName}</value>
    </property>
    <property name="url">
      <value>${jdbc_url}</value>
    </property>
    <property name="username">
      <value>${jdbc_username}</value>
    </property>
    <property name="password">
      <value>${jdbc_password}</value>
    </property>
    <!-- 连接池最大使用连接数 -->
    <property name="maxActive">
      <value>3</value>
    </property>
    <!-- 初始化连接大小 -->
    <property name="initialSize">
      <value>1</value>
    </property>
    <!-- 获取连接最大等待时间 -->
    <property name="maxWait">
      <value>60000</value>
    </property>
    <!-- 连接池最小空闲 -->
    <property name="minIdle">
      <value>3</value>
    </property>
    <!-- 自动清除无用连接 -->
    <property name="removeAbandoned">
      <value>true</value>
    </property>
    <!-- 清除无用连接的等待时间 -->
    <property name="removeAbandonedTimeout">
      <value>180</value>
    </property>
    <!-- 连接属性 -->
    <property name="connectionProperties">
      <value>clientEncoding=UTF-8</value>
    </property>
  </bean>
    
 <!-- mybatis文件配置，扫描所有mapper文件 -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean" p:dataSource-ref="dataSource" p:configLocation="classpath:spring/mybatis-config.xml"
		p:mapperLocations="classpath:mapper/${jdbc_type}/*.xml"/>

	<!-- spring与mybatis整合配置，扫描所有dao -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer" p:basePackage="cn.sh.ideal.**.dao" p:sqlSessionFactoryBeanName="sqlSessionFactory" />

	<!-- 对数据源进行事务管理 -->
	<bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager" p:dataSource-ref="dataSource" />
</beans>