########################################## wechat config #########################################

WX.MENU_DEL_URL=https://api.weixin.qq.com/cgi-bin/menu/delete?access_token=%s
WX.MENU_CREATE_URL=https://api.weixin.qq.com/cgi-bin/menu/create?access_token=%s
WX.CUSTOM_URL=https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=
WX.TEMPLATE_URL=https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=
WX.USER_URL=https://api.weixin.qq.com/cgi-bin/user/info?access_token=
WX.TOKEN_URL=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
WX.MEDIA_URL=https://api.weixin.qq.com/cgi-bin/media/upload?access_token=
WX.USERLIST_UEL=https://api.weixin.qq.com/cgi-bin/user/get
WX.SENDALL_URL=https://api.weixin.qq.com/cgi-bin/message/mass/sendall?access_token=
WX.MASS_SEND=https://api.weixin.qq.com/cgi-bin/message/mass/send?access_token=
WX.GROUPS_URL=https://api.weixin.qq.com/cgi-bin/groups/get?access_token=
WX.UPLOADVIDEO_URL=http://file.api.weixin.qq.com/cgi-bin/media/uploadvideo?access_token=
WX.UPLOADNEWS_URL=https://api.weixin.qq.com/cgi-bin/media/uploadnews?access_token=
WX.GET_MEDIA_URL=https://api.weixin.qq.com/cgi-bin/media/get?access_token=

#GET QRCODE
WX.QRCODE_URL=https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=
#GET QRCODE IMAGE
WX.QRCODE_IMG=https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=
WX.VOICE_URL=http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=ACCESS_TOKEN&media_id=MEDIA_ID



########################################## yixin config #########################################

YX.MENU_DEL_URL=https://api.yixin.im/cgi-bin/menu/delete?access_token=%s
YX.MENU_CREATE_URL=https://api.yixin.im/cgi-bin/menu/create?access_token=%s
YX.CUSTOM_URL=https://api.yixin.im/cgi-bin/message/custom/send?access_token=
#CUSTOM_URL=https://api.yixin.im/cgi-bin/message/custom/send?access_token=
YX.MESSAGE_URL=https://api.yixin.im/cgi-bin/message/send?access_token=
YX.USER_URL=https://api.yixin.im/cgi-bin/user/info?access_token=
YX.TOKEN_URL=https://api.yixin.im/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
YX.FOLLOW_URL=https://api.yixin.im/cgi-bin/follow/add
YX.VALID_URL=https://api.yixin.im/cgi-bin/user/valid
YX.USER_MOBILE_URL=https://api.yixin.im/private/user/info
YX.USER_LIST_URL=https://api.yixin.im/cgi-bin/user/follows
YX.SENDALL_URL=https://api.yixin.im/cgi-bin/message/group/send?access_token=
YX.GROUPS_URL=https://api.yixin.im/cgi-bin/groups/get?access_token=
YX.MEDIA_URL=https://api.yixin.im/cgi-bin/media/upload?access_token=


########################################### weibo config #####################################################

WEIBO.CUSTOM_URL=https://m.api.weibo.com/2/messages/reply.json
WEIBO.STATUSES_UPLOAD=https://api.weibo.com/2/statuses/update.json
WEIBO.CREATE_MENU=https://m.api.weibo.com/2/messages/menu/create.json
WEIBO.SHOW_MENU=https://m.api.weibo.com/2/messages/menu/show.json
WEIBO.DEL_MENU=https://m.api.weibo.com/2/messages/menu/delete.json
WEIBO.SEND_DALL=https://m.api.weibo.com/2/messages/sendall.json
WEIBO.USER_INFO=https://api.weibo.com/2/eps/user/info.json
##https://api.weibo.com/2/statuses/upload_url_text.json
WEIBO.STATUSES_UPDATE=
WB_PICURL=https://upload.api.weibo.com/2/mss/msget?
WB_USERINFO=https://api.weibo.com/2/eps/user/info.json?



########################################### alipay config ##########################################
ALIPAY.GATEWAY_URL=https://openapi.alipay.com/gateway.do
ALIPAY.VERIFY_URL=https://supergwpci.alipay.com/spdb28/commonuseridentity.do
ALIPAY.AMOUNTGRADE_URL=https://supergwpci.alipay.com/spdb28/commoncreditrating.do
ALIPAY.RESULTNOTICE_URL=https://supergwpci.alipay.com/spdb28/commonprocessnotify.do
##ALIPAY.ONLINECARD_URL=http://supergw.d9721.dl.alipaydev.com/spdb28/commonprocessnotify.do

########################################## enterprise account ################################################
QY.MENU_CREATE_URL=https://qyapi.weixin.qq.com/cgi-bin/menu/create?access_token=%s&agentid=%s
QY.MENU_DEL_URL=https://qyapi.weixin.qq.com/cgi-bin/menu/delete?access_token=%s&agentid=%s
QY.TOKEN_URL=https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s
QY.CUSTOM_URL=https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=
QY.MEDIA_URL=https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=


###########################################  webchat config ##############################################################
WEBCHAT.MENU_CREATE_URL=http://************:8080/zxkf_sz/servlet/ReceiverMenuServlet



###########################################  open weixin  ######################################################################
OPENWEIXIN.TOKEN_URL=https://api.weixin.qq.com/cgi-bin/component/api_component_token
OPENWEIXIN.PREAUTHCODE_URL=https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=
OPENWEIXIN.API_QUERY_AUTH_URL=https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=
OPENWEIXIN.AUTHORIZER_INFO_URL=https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=
OPENWEIXIN.AUTHORIZER_TOKEN_URL=https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=


###### weibo ###########
client_ID=1490325533
client_SERCRET=05db6959d04992a260e98e803bdf7274
redirect_URI=http://180.168.123.219/ServiceInvoker/weiboSend/tokenCallback
baseURL=https://api.weibo.com/2/
accessTokenURL=https://api.weibo.com/oauth2/access_token
authorizeURL=https://api.weibo.com/oauth2/authorize
rmURL=https\://rm.api.weibo.com/2/
