#\u6bcf\u6b21\u4eceREDIS\u6d88\u606f\u961f\u5217\u83b7\u53d6\u7684\u6d88\u606f\u6761\u6570
message.counts=10
#\u90ae\u4ef6\u63a5\u6536\u5237\u65b0\u65f6\u95f4
receive.fixedDelay=3000
#\u4efb\u52a1\u5206\u914d\u5668\u91cd\u65b0\u521d\u59cb\u5316\u6280\u80fd\u961f\u5217\u65f6\u95f4\u8bbe\u7f6ecorn\u8868\u8fbe\u5f0f
allocation.reInit=0 2 10 ? * *

#\u79df\u6237\u4fe1\u606f\u7f13\u5b58\u8fc7\u671f\u65f6\u95f4\u8bbe\u7f6e(\u5355\u4f4d\u79d2)
cache.tenant.timeout=36000
#\u8c03\u7528imr\u8def\u5f84
media.imrUrl=/message/receive.do
media.agentType=0

cms.custDisti=/customer/custDisti.method
media.mirUrl=/message/receive.do
media.sessionManager=/session/
#menu init url
menu.init_url=/menu/reinit.do

media.customerUrl=/customer/custDisti.do


outAgent=\#

threadPool.maxSize=100
#//\u8054\u5408\u7ef4\u62d3\u77ed\u4fe1\u7ebf\u7a0b\u6536\u53d6\u7b49\u5f85\u65f6\u95f4
sleepTime=10000
#\u8054\u5408\u7ef4\u62d3\u77ed\u4fe1\u6e20\u9053\u53f7
smsL=1001

#\u4eac\u4e1c\u7684token\u548caccess_token\
jd_access_token=f9b56b44-00d5-4662-83bc-80e1ea562951
jd_refresh_token=43ba33ac-7cfc-4124-b9a6-1128b3b0985e
client_id=8185A43EB6ADE898035698456953E6AB
client_secret=c901e1bce38b4b19b10df0917a548614
refresh_url=https://oauth.jd.com/oauth/token?

JD_channelCode=2000

#DUBBO
#zookeeper.address=***************:2181
zookeeper.address=home.immortalbo.win:2181
# ?backup=***********:2182,***********:2183
zookeeper.port=20080
dubbo.shutdown.timeout=10000
dubbo.consumer.timeout=15000

#RR

EC.coding.url=http://*************:19103/newAG/codeController/rrAutoCoding.method
