#每次从REDIS消息队列获取的消息条数
message.counts=10
#邮件接收刷新时间
receive.fixedDelay=3000
#任务分配器重新初始化技能队列时间设置corn表达式
allocation.reInit=0 2 10 ? * *

#租户信息缓存过期时间设置(单位秒)
cache.tenant.timeout=36000
#调用imr路径
media.imrUrl=/message/receive.do
media.agentType=0

cms.custDisti=/customer/custDisti.method
media.mirUrl=/message/receive.do
media.sessionManager=/session/
#menu init url
menu.init_url=/menu/reinit.do

media.customerUrl=/customer/custDisti.do


outAgent=\#

threadPool.maxSize=100
#//联合维拓短信线程收取等待时间
sleepTime=10000
#联合维拓短信渠道号
smsL=1001

#京东的token和access_token\
jd_access_token=f9b56b44-00d5-4662-83bc-80e1ea562951
jd_refresh_token=43ba33ac-7cfc-4124-b9a6-1128b3b0985e
client_id=8185A43EB6ADE898035698456953E6AB
client_secret=c901e1bce38b4b19b10df0917a548614
refresh_url=https://oauth.jd.com/oauth/token?

JD_channelCode=2000

#DUBBO
#zookeeper.address=***************:2181
zookeeper.address=home.immortalbo.win:2181
# ?backup=***********:2182,***********:2183
zookeeper.port=20080
dubbo.shutdown.timeout=10000
dubbo.consumer.timeout=15000

#RR

EC.coding.url=http://*************:19103/newAG/codeController/rrAutoCoding.method
