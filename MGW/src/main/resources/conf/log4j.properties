# ------------------------------------------------------------------------
#
# Logging Configuration
#
# $Id: log4j.properties,v 1.10 2005/07/27 04:25:09 
#
# ------------------------------------------------------------------------


#
# If we don't know the logging facility, put it into the
# ideal.log
#
log4j.rootLogger = info, root, stdout

#
# Application debugging
#
log4j.logger.cn.sh.ideal = info, ideal, stdout
log4j.additivity.cn.sh.ideal = false

log4j.logger.com.ibatis=DEBUG
log4j.logger.com.ibatis.common.jdbc.SimpleDataSource=DEBUG 
log4j.logger.com.ibatis.common.jdbc.ScriptRunner=DEBUG 
log4j.logger.com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate=DEBUG 
log4j.logger.java.sql.Connection=DEBUG 
log4j.logger.java.sql.Statement=DEBUG 
log4j.logger.java.sql.PreparedStatement=DEBUG 
log4j.logger.java.sql.ResultSet=DEBUG 
#
########################################################################
#
# Logfile definitions
#
########################################################################

#print out to console
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d [%-5p] [%F] %M : %L -- %m%n

#
# root.log
# 
log4j.appender.root = org.apache.log4j.DailyRollingFileAppender
log4j.appender.root.file = ../logs/MGW/root.log
log4j.appender.root.DatePattern='.'yyyy-MM-dd
log4j.appender.root.layout = org.apache.log4j.PatternLayout
log4j.appender.root.layout.conversionPattern =[%t] %d [%-5p] [%F] : %L -- %m%n
log4j.appender.root.append = false

#
# ideal.log
# 
# 
log4j.appender.ideal = org.apache.log4j.DailyRollingFileAppender
log4j.appender.ideal.file = ../logs/MGW/core.log
log4j.appender.ideal.DatePattern='.'yyyy-MM-dd
log4j.appender.ideal.layout = org.apache.log4j.PatternLayout
log4j.appender.ideal.layout.conversionPattern =[%t] %d [%-5p] [%F] : %L -- %m%n
log4j.appender.ideal.append = true
