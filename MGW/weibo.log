8697 INFO  [2018-10-18 15:21:55]  <<EMAIL>>解析开始
19745 INFO  [2018-10-18 15:22:06]  ================================================
56989 INFO  [2018-10-18 15:22:43]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">sdfsdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

56990 INFO  [2018-10-18 15:22:43]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">sdfsdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

56990 INFO  [2018-10-18 15:22:43]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">sdfsdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

56991 INFO  [2018-10-18 15:22:43]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">sdfsdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

56991 INFO  [2018-10-18 15:22:43]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">sdfsdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

56993 INFO  [2018-10-18 15:22:43]  邮件正文编码格式:gb2312
56994 INFO  [2018-10-18 15:22:43]  <html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">sdfsdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

57091 INFO  [2018-10-18 15:22:43]  <EMAIL>
57194 INFO  [2018-10-18 15:22:43]  请求的requestSessionbean{"acceptedAccount":"<EMAIL>","channelCode":"12345","sendAccount":"<EMAIL>","tenantCode":"1234"}
0    INFO  [2018-10-18 15:24:35]  <<EMAIL>>解析开始
6512 INFO  [2018-10-18 15:24:41]  ================================================
74437 INFO  [2018-10-18 15:25:49]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfadsfadf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

74438 INFO  [2018-10-18 15:25:49]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfadsfadf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

74438 INFO  [2018-10-18 15:25:49]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfadsfadf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

74439 INFO  [2018-10-18 15:25:49]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfadsfadf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

74439 INFO  [2018-10-18 15:25:49]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfadsfadf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

74441 INFO  [2018-10-18 15:25:49]  邮件正文编码格式:gb2312
74442 INFO  [2018-10-18 15:25:49]  <html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfadsfadf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

74537 INFO  [2018-10-18 15:25:49]  <EMAIL>
74649 INFO  [2018-10-18 15:25:50]  请求的requestSessionbean{"acceptedAccount":"<EMAIL>","channelCode":"12345","sendAccount":"<EMAIL>","tenantCode":"1234"}
0    INFO  [2018-10-18 15:29:09]  <<EMAIL>>解析开始
5884 INFO  [2018-10-18 15:29:15]  ================================================
111504 INFO  [2018-10-18 15:31:01]  s=========================:
111554 INFO  [2018-10-18 15:31:01]  s=========================:<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111554 INFO  [2018-10-18 15:31:01]  s=========================:<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111555 INFO  [2018-10-18 15:31:01]  s=========================:<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111556 INFO  [2018-10-18 15:31:01]  s=========================:<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111556 INFO  [2018-10-18 15:31:01]  s=========================:<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111556 INFO  [2018-10-18 15:31:01]  s=========================:<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111557 INFO  [2018-10-18 15:31:01]  邮件正文编码格式:
111557 INFO  [2018-10-18 15:31:01]  <div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div><br><br><span title="neteasefooter"><div id="netease_mail_footer"><div style="border-top:#CCC 1px solid;padding:10px 5px;font-size:16px;color:#777;line-height:22px"><a href="http://you.163.com/item/detail?id=1092001&from=web_gg_mail_jiaobiao_7
" target="_blank" style="color:#3366FF;text-decoration:none">【网易自营|30天无忧退货】爱上书写：施华洛世奇制造商星空原色水晶笔，限时仅29元&gt;&gt; &nbsp;</a>
 &nbsp; &nbsp;</div></div></span>
111643 INFO  [2018-10-18 15:31:01]  <EMAIL>
111764 INFO  [2018-10-18 15:31:01]  请求的requestSessionbean{"acceptedAccount":"<EMAIL>","channelCode":"12345","sendAccount":"<EMAIL>","tenantCode":"1234"}
2    ERROR [2018-10-18 15:32:14]  
javax.mail.MessageRemovedException
	at com.sun.mail.imap.IMAPMessage.forceCheckExpunged(IMAPMessage.java:299)
	at com.sun.mail.imap.IMAPMessage.loadBODYSTRUCTURE(IMAPMessage.java:1467)
	at com.sun.mail.imap.IMAPMessage.getContentType(IMAPMessage.java:555)
	at cn.sh.ideal.mgw.thread.ReceiveThreadTest.parseMessage(ReceiveThreadTest.java:101)
	at cn.sh.ideal.mgw.utils.EmailConnecter.main(EmailConnecter.java:213)
2    ERROR [2018-10-18 15:33:11]  
javax.mail.MessageRemovedException
	at com.sun.mail.imap.IMAPMessage.forceCheckExpunged(IMAPMessage.java:299)
	at com.sun.mail.imap.IMAPMessage.loadBODYSTRUCTURE(IMAPMessage.java:1467)
	at com.sun.mail.imap.IMAPMessage.getContentType(IMAPMessage.java:555)
	at cn.sh.ideal.mgw.thread.ReceiveThreadTest.parseMessage(ReceiveThreadTest.java:101)
	at cn.sh.ideal.mgw.utils.EmailConnecter.main(EmailConnecter.java:213)
1    INFO  [2018-10-18 15:34:12]  <<EMAIL>>解析开始
8454 INFO  [2018-10-18 15:34:21]  ================================================
0    INFO  [2018-10-18 15:39:44]  <<EMAIL>>解析开始
9921 INFO  [2018-10-18 15:39:54]  ================================================
38500 INFO  [2018-10-18 15:40:22]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38502 INFO  [2018-10-18 15:40:22]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38502 INFO  [2018-10-18 15:40:22]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38502 INFO  [2018-10-18 15:40:22]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38506 INFO  [2018-10-18 15:40:22]  邮件正文编码格式:gb2312
38507 INFO  [2018-10-18 15:40:22]  <html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">adfasdfasdf</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38605 INFO  [2018-10-18 15:40:22]  <EMAIL>
38712 INFO  [2018-10-18 15:40:22]  请求的requestSessionbean{"acceptedAccount":"<EMAIL>","channelCode":"12345","sendAccount":"<EMAIL>","tenantCode":"1234"}
1    INFO  [2018-10-18 15:59:26]  <<EMAIL>>解析开始
5442 INFO  [2018-10-18 15:59:32]  ================================================
38766 INFO  [2018-10-18 16:00:05]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">ssssssssssssssssssssss</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38767 INFO  [2018-10-18 16:00:05]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">ssssssssssssssssssssss</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38768 INFO  [2018-10-18 16:00:05]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">ssssssssssssssssssssss</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38768 INFO  [2018-10-18 16:00:05]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">ssssssssssssssssssssss</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38768 INFO  [2018-10-18 16:00:05]  s=========================:<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">ssssssssssssssssssssss</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38770 INFO  [2018-10-18 16:00:05]  邮件正文编码格式:gb2312
38771 INFO  [2018-10-18 16:00:05]  <html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
<div style="line-height:1.7;color:#000000;font-size:14px;font-family:Arial">ssssssssssssssssssssss</div>
<br>
<br>
<span title="neteasefooter">
<p>&nbsp;</p>
</span>
</body>
</html>

38870 INFO  [2018-10-18 16:00:05]  <EMAIL>
38993 INFO  [2018-10-18 16:00:05]  请求的requestSessionbean{"acceptedAccount":"<EMAIL>","channelCode":"12345","sendAccount":"<EMAIL>","tenantCode":"1234"}
0    INFO  [2018-10-18 16:03:45]  <<EMAIL>>解析开始
19   INFO  [2018-10-18 16:03:45]  ================================================
19   INFO  [2018-10-18 16:03:45]  fileName:null
691  INFO  [2018-10-18 16:03:46]  nameList:[/mailReceiveFile/2018-10-18/1/, /mailReceiveFile/2018-10-18/1/, /mailReceiveFile/2018-10-18/1/]
932  INFO  [2018-10-18 16:03:46]  邮件正文编码格式:gb2312
1014 INFO  [2018-10-18 16:03:46]  <EMAIL>
1114 INFO  [2018-10-18 16:03:46]  请求的requestSessionbean{"acceptedAccount":"<EMAIL>","channelCode":"12345","sendAccount":"<EMAIL>","tenantCode":"1234"}
