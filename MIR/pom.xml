<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<parent>
		<artifactId>multi_media</artifactId>
		<groupId>multi_media</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<description>MIR-Service服务</description>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>MIR</artifactId>

	<dependencies>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>MIR_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>MGW_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>SM_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>SI_API</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<!-- mysql驱动包 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>oracle.jdbc</groupId>-->
<!--			<artifactId>ojdbc</artifactId>-->
<!--		</dependency>-->
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>CM</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-lang3</artifactId>
		  <version>3.3.2</version>
		</dependency>
		<dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-collections4</artifactId>
		  <version>4.4</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.1</version>
		</dependency>
		<dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>druid</artifactId>
        </dependency>
		<!-- spring-plugins -->
		<dependency>
			<groupId>org.springframework.plugin</groupId>
			<artifactId>spring-plugin-core</artifactId>
			<version>1.1.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.20.0-GA</version>
		</dependency>
		<dependency>
			<groupId>org.jboss.netty</groupId>
			<artifactId>netty</artifactId>
			<version>3.2.10.Final</version>
		</dependency>
		<!-- Kafka客户端依赖 -->
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
		</dependency>
	</dependencies>

	<build>
		<finalName>MIR</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<filters>
			<filter>src/main/resources/conf/${env}/app-${env}.properties</filter>
			<filter>src/main/resources/conf/${env}/jdbc-${db}-${env}.properties</filter>
		</filters>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<transformers>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>cn.sh.ideal.mir.boot.Bootstrap</mainClass>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.handlers</resource>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.schemas</resource>
								</transformer>
							</transformers>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
			  <groupId>org.apache.maven.plugins</groupId>
			  <artifactId>maven-compiler-plugin</artifactId>
			  <configuration>
			  	<source>1.7</source>
			  	<target>1.7</target>
<!--					<compilerArguments>-->
<!--						<bootclasspath>C:/Program Files/Java/jdk1.8.0_144/jre/lib/rt.jar</bootclasspath>-->
<!--					</compilerArguments>-->
				</configuration>
			</plugin>
			<!--  <plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-jar</id>
						<phase>package</phase>
						<configuration>
							<tasks>
								<delete dir="D:/alljar/MIR.jar" />
								<copy todir="D:/alljar/">
									<fileset dir="target/">
										<include name="MIR.jar" />
									</fileset>
								</copy>
							</tasks>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<env>dev</env>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<env>prod</env>
			</properties>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env>test</env>
			</properties>
		</profile>
		<profile>
			<id>mysql</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<db>mysql</db>
			</properties>
		</profile>
		<profile>
			<id>oracle</id>
			<properties>
				<db>oracle</db>
			</properties>
		</profile>
	</profiles>

</project>