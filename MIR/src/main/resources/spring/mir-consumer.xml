<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
 	
 	<!-- 注册中心地址 -->
    <dubbo:registry address="zookeeper://${zookeeper.address}"/>
    <!-- 所有消费服务的默认超时设置 -->
    
    <dubbo:consumer check="false"  timeout="${dubbo.consumer.timeout}">
        <!-- 停止服务超时设置，单位毫秒 -->
        <dubbo:parameter  key="shutdown.timeout" value="${dubbo.shutdown.timeout}" />
    </dubbo:consumer>
    <!--生成远程服务代理,timeout=10000超时为10秒,retries=0表示不重试-->
    <!-- MGW-DUBBO服务 -->
    <dubbo:reference id="mgwMessageService" interface="cn.sh.ideal.mgw.service.MessageService" version="1.0.0" retries="0"/>
	<!-- SM-DUBBO服务 -->
	<dubbo:reference id="smSessionService" interface="cn.sh.ideal.sm.service.SessionService" version="1.0.0" retries="0"/>
	
	
</beans>