<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
	<!-- 引入配置文件 -->
	<context:property-placeholder location="classpath:conf/${env}/jdbc-${db}-${env}.properties" />
	<util:properties id="config" location="classpath:conf/${env}/app-${env}.properties" />
	
	<bean id="context" class="cn.sh.ideal.util.SpringContextUtil"/> 
	
	<!-- <context:component-scan base-package="cn.sh.ideal.interface s.controller"/> 
	<context:component-scan base-package="cn.sh.ideal.mir.service.dubbo"/>
	<context:component-scan base-package="cn.sh.ideal.as.controller"/> -->
	
	<!-- 注解扫描包路径 -->
	<context:component-scan base-package="cn.sh.ideal"/>
	<!-- 注解式事务 -->
	<tx:annotation-driven transaction-manager="transactionManager" />
	
	<!-- mybatis -->
	<import resource="spring-mybatis.xml" />
	<!-- provider -->
	<import resource="mir-provider.xml"/>
	<!-- consumer -->
	<import resource="mir-consumer.xml"/>
</beans>