message.send.message=%s为您服务,请输入您的问题
message.tip.addworkno=工号%s加入会话
message.tip.removeworkno=工号%s退出会话
message.tip.meetinvite=工号%s邀请您加入多方会话
message.tip.forceRelay=会话已被强制转发给我%s
message.sortmessage=正在排队请稍后，前面排队人数%s个
mesaage.overqueue=当前排队人数较多，请稍后重试
message.agentoffline=客户经理不在线
liuyan.url=

message.fullposition=亲，恰逢咨询高峰人工客服繁忙，您可使用自助查询功能查询您关注的问题，也可稍做等候，我们正在拼命向您赶来。
message.fullcount=3

audio.push.url=/GetUserDn


#ronglian
is.ronglian=false
sortinf.push=true
fullinf.push=true
#dubbo
#zk链接信息

zookeeper.address=home.immortalbo.win:2181
zookeeper.port=20082
dubbo.shutdown.timeout=10000
dubbo.consumer.timeout=15000

calculator.open=true

lastagent.timeout=
lastagent.sq=


prequeue.timeout=120
prequeue.opttimeout=60
prequeue.lasttimeout=30
#排队超2分钟提示语
prequeue.waitMsg=您当前等待时间已超过2分钟，人工坐席正在努力接线中，请问您是否愿意继续等待?
#未溢出排队等待语
prequeue.noovermsg=当前人工坐席繁忙，小瑞正在努力赶来为您服务~
#溢出排队等待语
prequeue.overmsg=目前人工坐席繁忙，非常抱歉给您带来不便，您可选择是否愿意继续排队等待人工服务~
#已为您加入人工服务排队中，请您稍等
prequeue.joinmsg=已为您加入人工服务排队中，请您稍等~
#继续排队提示
prequeue.continemsg=让您久等了，建议您保持在线，以免错过人工客服的接入，小瑞正在加速赶来为您服务
#取消排队提示
prequeue.cancelmsg=您已选择取消人工排队等待，如有其他问题您可以稍后再次联系我们，感谢您的理解与支持。
#30s未操作提示语
prequeue.norecvmsg=由于未收到您是否继续等待排队回复，系统将在30秒后自动退出排队，如有其他问题您可以稍后再次联系我们~感谢您的理解与支持。
#预排队队列已满提示
prequeue.busymsg=非常抱歉，当前人工坐席繁忙，请稍后重试。智能小鹿24小时为您服务，可以直接描述问题与我沟通~
#预排队三分钟提示语
prequeue.selfservicemsg=非常抱歉，当前人工坐席繁忙，请稍后重试。智能小鹿24小时为您服务，可以直接描述问题与我沟通~
#预排队超2分钟提示语
prequeue.overWaitMsg=您当前等待时间已超过2分钟，人工坐席正在努力接线中，请问您是否愿意继续等待?


# Kafka??
kafka.bootstrap.servers=10.150.103.92:9093,10.150.103.37:9093,10.150.102.12:9093
kafka.topic.agent.trace=topic-onecc-agent-trace
kafka.security.protocol=SASL_PLAINTEXT
kafka.sasl.mechanism=PLAIN
kafka.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="onecc" password="Callcenter!@#456";
kafka.producer.acks=1
kafka.producer.retries=3
kafka.producer.batch.size=16384
kafka.producer.linger.ms=1
kafka.producer.buffer.memory=33554432
kafka.producer.key.serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.producer.value.serializer=org.apache.kafka.common.serialization.StringSerializer


