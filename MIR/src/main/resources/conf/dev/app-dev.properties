message.send.message=%s\u4E3A\u60A8\u670D\u52A1,\u8BF7\u8F93\u5165\u60A8\u7684\u95EE\u9898
message.tip.addworkno=\u5DE5\u53F7%s\u52A0\u5165\u4F1A\u8BDD
message.tip.removeworkno=\u5DE5\u53F7%s\u9000\u51FA\u4F1A\u8BDD
message.tip.meetinvite=\u5DE5\u53F7%s\u9080\u8BF7\u60A8\u52A0\u5165\u591A\u65B9\u4F1A\u8BDD
message.tip.forceRelay=\u4F1A\u8BDD\u5DF2\u88AB\u5F3A\u5236\u8F6C\u53D1\u7ED9\u6211%s
message.sortmessage=\u6B63\u5728\u6392\u961F\u8BF7\u7A0D\u540E\uFF0C\u524D\u9762\u6392\u961F\u4EBA\u6570%s\u4E2A
mesaage.overqueue=\u5F53\u524D\u6392\u961F\u4EBA\u6570\u8F83\u591A\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5
message.agentoffline=\u5BA2\u6237\u7ECF\u7406\u4E0D\u5728\u7EBF
liuyan.url=

message.fullposition=\u4EB2\uFF0C\u6070\u9022\u54A8\u8BE2\u9AD8\u5CF0\u4EBA\u5DE5\u5BA2\u670D\u7E41\u5FD9\uFF0C\u60A8\u53EF\u4F7F\u7528\u81EA\u52A9\u67E5\u8BE2\u529F\u80FD\u67E5\u8BE2\u60A8\u5173\u6CE8\u7684\u95EE\u9898\uFF0C\u4E5F\u53EF\u7A0D\u505A\u7B49\u5019\uFF0C\u6211\u4EEC\u6B63\u5728\u62FC\u547D\u5411\u60A8\u8D76\u6765\u3002
message.fullcount=3

audio.push.url=/GetUserDn


#ronglian
is.ronglian=false
sortinf.push=true
fullinf.push=true
#dubbo
#zk\u94FE\u63A5\u4FE1\u606F

zookeeper.address=home.immortalbo.win:2181
zookeeper.port=20082
dubbo.shutdown.timeout=10000
dubbo.consumer.timeout=15000

calculator.open=true

lastagent.timeout=
lastagent.sq=


prequeue.timeout=120
prequeue.opttimeout=60
prequeue.lasttimeout=30
#prequeue.waitMsg=\u60a8\u5f53\u524d\u7b49\u5f85\u65f6\u95f4\u5df2\u8d85\u8fc7\u0032\u5206\u949f\uff0c\u4eba\u5de5\u5750\u5e2d\u6b63\u5728\u52aa\u529b\u63a5\u7ebf\u4e2d\uff0c\u8bf7\u95ee\u662f\u5426\u613f\u610f\u7ee7\u7eed\u7b49\u5f85\uff1f
#prequeue.noovermsg=\u975e\u5e38\u62b1\u6b49\uff0c\u5f53\u524d\u4eba\u5de5\u5750\u5e2d\u7e41\u5fd9\uff0c\u5c0f\u745e\u6b63\u5728\u52aa\u529b\u8d76\u6765\u4e3a\u60a8\u670d\u52a1\u007e
#prequeue.overmsg=\u76ee\u524d\u4eba\u5de5\u5750\u5e2d\u7e41\u5fd9\uff0c\u9884\u8ba1\u6392\u961f\u7b49\u5f85\u65f6\u95f4\u8f83\u957f\uff0c\u975e\u5e38\u62b1\u6b49\u7ed9\u60a8\u5e26\u6765\u4e0d\u4fbf\uff0c\u60a8\u53ef\u9009\u62e9\u662f\u5426\u8bed\u97f3\u7ee7\u7eed\u6392\u961f\u7b49\u5f85\u4eba\u5de5\u670d\u52a1\u007e
prequeue.waitMsg=\u6392\u961f\u8d85\u0032\u5206\u949f\u63d0\u793a\u8bed
prequeue.noovermsg=\u672a\u6ea2\u51fa\u6392\u961f\u7b49\u5f85\u8bed
prequeue.overmsg=\u6ea2\u51fa\u6392\u961f\u7b49\u5f85\u8bed
prequeue.joinmsg=\u5df2\u4e3a\u60a8\u52a0\u5165\u4eba\u5de5\u670d\u52a1\u6392\u961f\u4e2d\uff0c\u8bf7\u60a8\u7a0d\u7b49\u007e
prequeue.continemsg=\u975e\u5e38\u62b1\u6b49\uff0c\u8ba9\u60a8\u4e45\u7b49\u4e86\uff0c\u5efa\u8bae\u60a8\u4fdd\u6301\u5728\u7ebf\uff0c\u4ee5\u514d\u9519\u8fc7\u4eba\u5de5\u5ba2\u670d\u7684\u63a5\u5165\uff0c\u5c0f\u745e\u6b63\u5728\u52a0\u901f\u8d76\u6765\u4e3a\u60a8\u670d\u52a1
prequeue.cancelmsg=\u60a8\u5df2\u9009\u62e9\u53d6\u6d88\u4eba\u5de5\u6392\u961f\u7b49\u5f85\uff0c\u5982\u6709\u5176\u4ed6\u95ee\u9898\u60a8\u53ef\u4ee5\u7a0d\u540e\u518d\u6b21\u8054\u7cfb\u6211\u4eec\uff0c\u611f\u8c22\u60a8\u7684\u7406\u89e3\u4e0e\u652f\u6301\u3002
#prequeue.norecvmsg=\u7531\u4e8e\u672a\u6536\u5230\u60a8\u662f\u5426\u7ee7\u7eed\u7b49\u5f85\u6392\u961f\u56de\u590d\uff0c\u7cfb\u7edf\u5c06\u5728\u0033\u0030\u0073\u540e\u81ea\u52a8\u9000\u51fa\u6392\u961f\uff0c\u5982\u6709\u5176\u4ed6\u95ee\u9898\u60a8\u53ef\u4ee5\u7a0d\u540e\u518d\u6b21\u8054\u7cfb\u6211\u4eec\uff0c\u611f\u8c22\u60a8\u7684\u7684\u7406\u89e3\u4e0e\u652f\u6301
prequeue.norecvmsg=\u0033\u0030\u0073\u672a\u64cd\u4f5c\u63d0\u793a\u8bed
prequeue.busymsg=\u975e\u5e38\u62b1\u6b49\uff0c\u5f53\u524d\u4eba\u5de5\u5750\u5e2d\u7e41\u5fd9\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5\uff0c\u667a\u80fd\u5c0f\u9e7f24\u5c0f\u65f6\u4e3a\u60a8\u670d\u52a1\uff0c\u60a8\u53ef\u4ee5\u76f4\u63a5\u63cf\u8ff0\u95ee\u9898\u4e0e\u6211\u6c9f\u901a~
prequeue.selfservicemsg=\u5c0a\u656c\u7684\u5ba2\u6237\uff0c\u5f53\u524d\u4eba\u5de5\u670d\u52a1\u6392\u961f\u4eba\u6570\u8f83\u591a\u3002\u4e3a\u51cf\u5c11\u60a8\u7684\u7b49\u5f85\uff0c\u60a8\u53ef\u70b9\u51fb\u4e0b\u65b9\u3010\u81ea\u52a9\u53cd\u9988\u3011\u6309\u94ae\uff0c\u6309\u63d0\u793a\u586b\u5199\u95ee\u9898\uff0c\u6211\u4eec\u4f1a\u5c3d\u5feb\u4e3a\u60a8\u6838\u5b9e\u3002\u82e5\u4ecd\u9700\u4eba\u5de5\u534f\u52a9\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5\uff0c\u611f\u8c22\u60a8\u7684\u7406\u89e3\u3002

# Kafka??
kafka.bootstrap.servers=10.150.103.92:9093,10.150.103.37:9093,10.150.102.12:9093
kafka.topic.agent.trace=topic-onecc-agent-trace
kafka.security.protocol=SASL_PLAINTEXT
kafka.sasl.mechanism=PLAIN
kafka.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="onecc" password="Callcenter!@#456";
kafka.producer.acks=1
kafka.producer.retries=3
kafka.producer.batch.size=16384
kafka.producer.linger.ms=1
kafka.producer.buffer.memory=33554432
kafka.producer.key.serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.producer.value.serializer=org.apache.kafka.common.serialization.StringSerializer


