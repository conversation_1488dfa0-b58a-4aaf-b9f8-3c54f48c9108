VERSION=1.5
---------2016-03-04-----------------------
1、将spring定时器改成ScheduledExecutorService
2、修改分配规则逻辑OccupancyAllocationRule 添加最少会话数规则MinSessionAllocationRule
3、修改分配执行器逻辑，改成可配置线程并发（MGW_TENANT_SKILL_QUEUEU新增字段CONCURRENT_COUNT）
4、队列优化
  a、移除缓存队列，设置队列上线
  b、存储对象SessionData对象改为字符串（格式：tenantCode_sessionId）
  c、纠正排序、排队位置不准问题
5、新增客户提示:前方排队数提示、排队已满提示
6、优化向SI推送逻辑控制,加锁再次检查坐席状态以及会话数
7、移除cn.sh.ideal.mir.remote包（WAE对接代码）

---------2016-03-04-----------------------
dubbo架构
