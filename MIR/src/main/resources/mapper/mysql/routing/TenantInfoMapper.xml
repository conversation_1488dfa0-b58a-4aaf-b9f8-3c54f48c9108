<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.TenantInfoDao" >
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- ResultMap                                                    -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <resultMap id="ResultMap" type="cn.sh.ideal.model.TenantInfo" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="TENANT_NAME" property="tenantName" jdbcType="VARCHAR" />
    <result column="ACCOUNT" property="account" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="ENCRYPT_TYPE" property="encryptType" jdbcType="VARCHAR" />
    <result column="DECODE_KEY" property="decodeKey" jdbcType="VARCHAR" />
    <result column="CHANNEL" property="channel" jdbcType="VARCHAR" />
    <result column="SKILL_RULE" property="routingRule" jdbcType="VARCHAR" />
    <result column="TERMINAL_NUM" property="terminalNum" jdbcType="INTEGER" />
    <result column="DUE_DATE" property="dueDate" jdbcType="TIMESTAMP" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Insert                                                       -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insert" parameterType="cn.sh.ideal.model.TenantInfo" >
    INSERT INTO MGW_TENANT_INFO (TENANT_CODE, TENANT_NAME, 
      ACCOUNT, PASSWORD, ENCRYPT_TYPE, 
      DECODE_KEY, CHANNEL, SKILL_RULE, 
      TERMINAL_NUM, DUE_DATE, STATUS, 
      REMARK)
    VALUES (#{tenantCode,jdbcType=VARCHAR}, #{tenantName,jdbcType=VARCHAR}, 
      #{account,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{encryptType,jdbcType=VARCHAR}, 
      #{decodeKey,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{routingRule,jdbcType=VARCHAR}, 
      #{terminalNum,jdbcType=INTEGER}, #{dueDate,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Load                                                         -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="load" resultMap="ResultMap" parameterType="string" >
    SELECT ID,TENANT_CODE,TENANT_NAME,ACCOUNT,PASSWORD,ENCRYPT_TYPE,DECODE_KEY,
    CHANNEL,SKILL_RULE,TERMINAL_NUM,DUE_DATE,STATUS,REMARK
    FROM MGW_TENANT_INFO
    WHERE TENANT_CODE=#{tenantCode,jdbcType=VARCHAR}
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Query                                                        -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="query" resultMap="ResultMap" parameterType="cn.sh.ideal.model.TenantInfo" >
    SELECT ID,TENANT_CODE,TENANT_NAME,ACCOUNT,PASSWORD,ENCRYPT_TYPE,DECODE_KEY,
    CHANNEL,SKILL_RULE,TERMINAL_NUM,DUE_DATE,STATUS,REMARK
    FROM MGW_TENANT_INFO
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QueryCount                                                   -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryCount" resultType="int" parameterType="cn.sh.ideal.model.TenantInfo" >
    SELECT  COUNT(1) FROM MGW_TENANT_INFO
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Update                                                       -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <update id="update" parameterType="cn.sh.ideal.model.TenantInfo" >
    UPDATE MGW_TENANT_INFO
    <set >
      <if test="tenantCode != null and tenantCode != ''" >
        TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="tenantName != null and tenantName != ''" >
        TENANT_NAME = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="account != null and account != ''" >
        ACCOUNT = #{account,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password != ''" >
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="encryptType != null and encryptType != ''" >
        ENCRYPT_TYPE = #{encryptType,jdbcType=VARCHAR},
      </if>
      <if test="decodeKey != null and decodeKey != ''" >
        DECODE_KEY = #{decodeKey,jdbcType=VARCHAR},
      </if>
      <if test="channel != null and channel != ''" >
        CHANNEL = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="routingRule != null and routingRule != ''" >
        SKILL_RULE = #{routingRule,jdbcType=VARCHAR},
      </if>
      <if test="terminalNum != null and terminalNum != ''" >
        TERMINAL_NUM = #{terminalNum,jdbcType=INTEGER},
      </if>
      <if test="dueDate != null and dueDate != ''" >
        DUE_DATE = #{dueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null and status != ''" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        REMARK = #{remark,jdbcType=VARCHAR} 
      </if>
    </set>
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Delete                                                       -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <delete id="delete" parameterType="cn.sh.ideal.model.TenantInfo" >
    DELETE FROM MGW_TENANT_INFO
    WHERE ID = #{id,jdbcType=INTEGER}
  </delete>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Where                                                        -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <sql id="where" >
    <where >
      <if test="id != null and id != ''" >
         ID = #{id,jdbcType=INTEGER}
      </if>
      <if test="tenantCode != null and tenantCode != ''" >
         and TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
      </if>
      <if test="tenantName != null and tenantName != ''" >
         and TENANT_NAME = #{tenantName,jdbcType=VARCHAR}
      </if>
      <if test="account != null and account != ''" >
         and ACCOUNT = #{account,jdbcType=VARCHAR}
      </if>
      <if test="password != null and password != ''" >
         and PASSWORD = #{password,jdbcType=VARCHAR}
      </if>
      <if test="encryptType != null and encryptType != ''" >
         and ENCRYPT_TYPE = #{encryptType,jdbcType=VARCHAR}
      </if>
      <if test="decodeKey != null and decodeKey != ''" >
         and DECODE_KEY = #{decodeKey,jdbcType=VARCHAR}
      </if>
      <if test="channel != null and channel != ''" >
         and CHANNEL = #{channel,jdbcType=VARCHAR}
      </if>
      <if test="routingRule != null and routingRule != ''" >
         and SKILL_RULE = #{routingRule,jdbcType=VARCHAR}
      </if>
      <if test="terminalNum != null and terminalNum != ''" >
         and TERMINAL_NUM = #{terminalNum,jdbcType=INTEGER}
      </if>
      <if test="dueDate != null and dueDate != ''" >
         and DUE_DATE = #{dueDate,jdbcType=TIMESTAMP}
      </if>
      <if test="status != null and status != ''" >
         and STATUS = #{status,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
         and REMARK = #{remark,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>  
</mapper>