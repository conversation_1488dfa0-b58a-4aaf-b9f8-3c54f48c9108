<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.AgentMutualDao" >
  <resultMap id="agentIm" type="cn.sh.ideal.mir.model.AgentIM" >
    <result column="ID" property="id" jdbcType="DECIMAL" />
    <result column="UUID" property="uuid" jdbcType="VARCHAR" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="cn.sh.ideal.mir.model.AgentIM" useGeneratedKeys="true" keyProperty="id">
  	insert into mgw_message_private(uuid,tenant_code,work_no,create_time,type,content)
  	values(#{uuid,jdbcType=VARCHAR},
  	#{tenantCode,jdbcType=VARCHAR},#{workNo,jdbcType=VARCHAR},
  	now(),#{type,jdbcType=VARCHAR},#{content,jdbcType=VARCHAR})
  </insert>
  
  <select id="query" parameterType="cn.sh.ideal.mir.model.AgentMutual" resultMap="agentIm">
  		select A.id,A.uuid,A.tenant_code,A.create_time,A.type,A.work_no,A.content
  		from (select t.id,t.uuid,t.tenant_code,t.create_time,t.type,t.work_no,
  		t.content from mgw_message_private t
  		where t.tenant_code = #{tenantCode,jdbcType=VARCHAR}
  		<if test="lastId != null and lastId !=''">
  			and t.id &lt; #{lastId,jdbcType=DECIMAL}
  		</if>
  		<if test="uuid != null and uuid !=''">
  			and t.uuid = #{uuid,jdbcType=VARCHAR}
  		</if>
  		<if test="workNo != null and workNo !=''">
  			and t.uuid like concat('%',#{workNo,jdbcType=VARCHAR},'%')
  		</if>
  		order by t.create_time desc) A
  			<if test="count != null and count !=''">
  				limit 0,#{count,jdbcType=INTEGER}
			</if>
  </select>
</mapper>