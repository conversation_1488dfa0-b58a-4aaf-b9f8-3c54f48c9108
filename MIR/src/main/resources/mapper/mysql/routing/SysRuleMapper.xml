<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.SysRuleDao" >
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- ResultMap                                                    -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <resultMap id="ResultMap" type="cn.sh.ideal.model.SysRule" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="RULE_NAME" property="ruleName" jdbcType="VARCHAR" />
    <result column="RULE_TYPE" property="ruleType" jdbcType="VARCHAR" />
    <result column="RULE_CODE" property="ruleCode" jdbcType="VARCHAR" />
    <result column="RULE_METHOD" property="ruleMethod" jdbcType="VARCHAR" />
    <result column="IS_GLOBAL" property="isGlobal" jdbcType="VARCHAR" />
    <result column="INTERFACE_URL" property="interfaceUrl" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Insert                                                       -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insert" parameterType="cn.sh.ideal.model.SysRule" >
    INSERT INTO MGW_SYS_RULE (RULE_NAME, RULE_TYPE, 
      RULE_CODE, RULE_METHOD, IS_GLOBAL, REMARK,INTERFACE_URL)
    VALUES (#{ruleName,jdbcType=VARCHAR}, #{ruleType,jdbcType=VARCHAR}, 
      #{ruleCode,jdbcType=VARCHAR}, #{ruleMethod,jdbcType=VARCHAR}, #{isGlobal,jdbcType=VARCHAR}, 
       #{remark,jdbcType=VARCHAR},#{interfaceUrl,jdbcType=VARCHAR})
  </insert>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Load                                                         -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="load" resultMap="ResultMap" parameterType="cn.sh.ideal.model.SysRule" >
    SELECT  ID, RULE_NAME,RULE_TYPE,RULE_CODE,RULE_METHOD,IS_GLOBAL,INTERFACE_URL,REMARK
	FROM MGW_SYS_RULE
    WHERE ID = #{id,jdbcType=INTEGER}
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Query                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="query" resultMap="ResultMap" parameterType="cn.sh.ideal.model.SysRule" >
    SELECT  ID, RULE_NAME,RULE_TYPE,RULE_CODE,RULE_METHOD,IS_GLOBAL,INTERFACE_URL,REMARK
	FROM MGW_SYS_RULE
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QueryCount                                                   -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryCount" resultType="int" parameterType="cn.sh.ideal.model.SysRule" >
    SELECT  COUNT(1) FROM MGW_SYS_RULE
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Update                                                       -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <update id="update" parameterType="cn.sh.ideal.model.SysRule" >
    UPDATE MGW_SYS_RULE
    <set >
      <if test="ruleName != null and ruleName != ''" >
        RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null and ruleType != ''" >
        RULE_TYPE = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="ruleCode != null and ruleCode != ''" >
        RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleMethod != null and ruleMethod != ''" >
        RULE_METHOD = #{ruleMethod,jdbcType=VARCHAR},
      </if>
      <if test="isGlobal != null and isGlobal != ''" >
        IS_GLOBAL = #{isGlobal,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        REMARK = #{remark,jdbcType=VARCHAR} 
      </if>
      <if test="interfaceUrl != null and interfaceUrl != ''" >
        INTERFACE_URL = #{interfaceUrl,jdbcType=VARCHAR} 
      </if>
    </set>
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Delete                                                       -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <delete id="delete" parameterType="cn.sh.ideal.model.SysRule" >
    DELETE FROM MGW_SYS_RULE
    WHERE ID = #{id,jdbcType=INTEGER}
  </delete>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Where                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <sql id="where" >
    <where >
      <if test="id != null and id != ''" >
         ID = #{id,jdbcType=INTEGER}
      </if>
      <if test="ruleName != null and ruleName != ''" >
         and RULE_NAME = #{ruleName,jdbcType=VARCHAR}
      </if>
      <if test="ruleType != null and ruleType != ''" >
         and RULE_TYPE = #{ruleType,jdbcType=VARCHAR}
      </if>
      <if test="ruleCode != null and ruleCode != ''" >
         and RULE_CODE = #{ruleCode,jdbcType=VARCHAR}
      </if>
      <if test="ruleMethod != null and ruleMethod != ''" >
         and RULE_METHOD = #{ruleMethod,jdbcType=VARCHAR}
      </if>
      <if test="isGlobal != null and isGlobal != ''" >
         and IS_GLOBAL = #{isGlobal,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
         and REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="interfaceUrl != null and interfaceUrl != ''" >
        and INTERFACE_URL = #{interfaceUrl,jdbcType=VARCHAR} 
      </if>
    </where>
  </sql>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- 清空系统规则                                                                                                                                    -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <delete id="deleteAll" parameterType="cn.sh.ideal.model.SysRule" >
     DELETE FROM MGW_SYS_RULE WHERE rule_type IN('1','2','3')
  </delete>
</mapper>