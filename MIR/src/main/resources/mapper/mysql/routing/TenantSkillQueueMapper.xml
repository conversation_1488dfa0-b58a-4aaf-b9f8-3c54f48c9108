<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.TenantSkillQueueDao" >
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- ResultMap                                                    -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <resultMap id="ResultMap" type="cn.sh.ideal.model.TenantSkillQueue" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="QUEUE_NAME" property="queueName" jdbcType="VARCHAR" />
    <result column="QUEUE_CODE" property="queueCode" jdbcType="VARCHAR" />
    <result column="QUEUE_SIZE" property="queueSize" jdbcType="INTEGER" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
    <result column="ABLE_BUSINESS_TYPES" property="ableBusinessTypes" jdbcType="VARCHAR" />
    <result column="ABLE_CHANNELS" property="ableChannels" jdbcType="VARCHAR" />
    <result column="SORT_RULE" property="sortRule" jdbcType="VARCHAR" />
    <result column="ALLOCATION_RULE" property="allocationRule" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="SORT_SIZE" property="sortSize" jdbcType="INTEGER" />
    <result column="WEIGHT" property="weight" jdbcType="VARCHAR" />
    <result column="CONCURRENT_COUNT" property="concurrentCount" jdbcType="INTEGER" />
  </resultMap>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Insert                                                       -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insert" parameterType="cn.sh.ideal.model.TenantSkillQueue" >
    INSERT INTO MGW_TENANT_SKILL_QUEUE (QUEUE_NAME, QUEUE_CODE, 
      QUEUE_SIZE, TENANT_ID, ABLE_BUSINESS_TYPES, 
      ABLE_CHANNELS, SORT_RULE, ALLOCATION_RULE, 
      STATUS, REMARK,SKILL_TYPE_CODE)
    VALUES (#{queueName,jdbcType=VARCHAR}, #{queueCode,jdbcType=VARCHAR}, 
      #{queueSize,jdbcType=INTEGER}, #{tenantId,jdbcType=VARCHAR}, #{ableBusinessTypes,jdbcType=VARCHAR}, 
      #{ableChannels,jdbcType=VARCHAR}, #{sortRule,jdbcType=VARCHAR}, #{allocationRule,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},#{typeCode,jdbcType=VARCHAR})
  </insert>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Load                                                         -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="load" resultMap="ResultMap" parameterType="string" >
   SELECT  a.ID,a.QUEUE_NAME,a.QUEUE_CODE,a.QUEUE_SIZE,a.TENANT_ID,a.ABLE_BUSINESS_TYPES,a.ABLE_CHANNELS,
    a.SORT_RULE,a.ALLOCATION_RULE,a.STATUS,a.REMARK,a.SORT_SIZE
	FROM MGW_TENANT_SKILL_QUEUE a 
    WHERE a.ID = #{id,jdbcType=VARCHAR}
    and a.status = 1
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Query                                                        -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="query" resultMap="ResultMap" parameterType="cn.sh.ideal.model.TenantSkillQueue" >
    SELECT  a.ID,a.QUEUE_NAME,a.QUEUE_CODE,a.QUEUE_SIZE,a.TENANT_ID,a.ABLE_BUSINESS_TYPES,a.ABLE_CHANNELS,
    a.SORT_RULE,a.ALLOCATION_RULE,a.STATUS,a.REMARK,a.SORT_SIZE,a.CONCURRENT_COUNT
	FROM MGW_TENANT_SKILL_QUEUE a 
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QueryCount                                                   -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryCount" resultType="int" parameterType="cn.sh.ideal.model.TenantSkillQueue" >
    SELECT  COUNT(1) FROM MGW_TENANT_SKILL_QUEUE a
    <include refid="where" />
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Update                                                       -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <update id="update" parameterType="cn.sh.ideal.model.TenantSkillQueue" >
    UPDATE MGW_TENANT_SKILL_QUEUE
    <set >
      <if test="queueName != null and queueName != ''" >
        QUEUE_NAME = #{queueName,jdbcType=VARCHAR},
      </if>
      <if test="queueCode != null and queueCode != ''" >
        QUEUE_CODE = #{queueCode,jdbcType=VARCHAR},
      </if>
      <if test="queueSize != null and queueSize != ''" >
        QUEUE_SIZE = #{queueSize,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null and tenantId != ''" >
        TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="ableBusinessTypes != null and ableBusinessTypes != ''" >
        ABLE_BUSINESS_TYPES = #{ableBusinessTypes,jdbcType=VARCHAR},
      </if>
      <if test="ableChannels != null and ableChannels != ''" >
        ABLE_CHANNELS = #{ableChannels,jdbcType=VARCHAR},
      </if>
      <if test="sortRule != null and sortRule != ''" >
        SORT_RULE = #{sortRule,jdbcType=VARCHAR},
      </if>
      <if test="allocationRule != null and allocationRule != ''" >
        ALLOCATION_RULE = #{allocationRule,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        REMARK = #{remark,jdbcType=VARCHAR} 
      </if>
    </set>
    WHERE ID = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Delete                                                       -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <delete id="delete" parameterType="cn.sh.ideal.model.TenantSkillQueue" >
    DELETE FROM MGW_TENANT_SKILL_QUEUE
    WHERE ID = #{id,jdbcType=INTEGER}
  </delete>
  
  <select id="querySkillQueue" resultMap="ResultMap" parameterType="cn.sh.ideal.model.TenantSkillQueue" >
    SELECT  a.ID,a.QUEUE_NAME,a.QUEUE_CODE,a.QUEUE_SIZE,a.TENANT_ID,a.ABLE_BUSINESS_TYPES,a.ABLE_CHANNELS,
    a.SORT_RULE,a.ALLOCATION_RULE,a.STATUS,a.REMARK,a.SORT_SIZE,a.WEIGHT
	FROM MGW_TENANT_SKILL_QUEUE a 
	where 1=1
    <if test="tenantId != null and tenantId != ''" >
         and a.TENANT_ID = #{tenantId,jdbcType=VARCHAR}
    </if>
    <if test="ableChannels != null and ableChannels != ''" >
         and a.ABLE_CHANNELS like '%${ableChannels}%'
    </if>
    <if test="status != null and status != ''" >
         and a.STATUS = #{status,jdbcType=VARCHAR}
      </if>
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Where                                                        -->
  <!-- This element was generated on Tue Jun 03 14:08:05 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <sql id="where" >
    <where >
      <if test="id != null and id != ''" >
         a.ID = #{id,jdbcType=INTEGER}
      </if>
      <if test="queueName != null and queueName != ''" >
         and a.QUEUE_NAME = #{queueName,jdbcType=VARCHAR}
      </if>
      <if test="queueCode != null and queueCode != ''" >
         and a.QUEUE_CODE = #{queueCode,jdbcType=VARCHAR}
      </if>
      <if test="queueSize != null and queueSize != ''" >
         and a.QUEUE_SIZE = #{queueSize,jdbcType=INTEGER}
      </if>
      <if test="tenantId != null and tenantId != ''" >
         and a.TENANT_ID = #{tenantId,jdbcType=VARCHAR}
      </if>
      <if test="ableBusinessTypes != null and ableBusinessTypes != ''" >
         and a.ABLE_BUSINESS_TYPES = #{ableBusinessTypes,jdbcType=VARCHAR}
      </if>
      <if test="ableChannels != null and ableChannels != ''" >
         and a.ABLE_CHANNELS = #{ableChannels,jdbcType=VARCHAR}
      </if>
      <if test="sortRule != null and sortRule != ''" >
         and a.SORT_RULE = #{sortRule,jdbcType=VARCHAR}
      </if>
      <if test="allocationRule != null and allocationRule != ''" >
         and a.ALLOCATION_RULE = #{allocationRule,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''" >
         and a.STATUS = #{status,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
         and a.REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="weight != null and weight != ''" >
         and a.WEIGHT = #{weight,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
</mapper>