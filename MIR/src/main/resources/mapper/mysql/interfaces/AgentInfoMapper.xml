<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.as.dao.AgentInfoDao" >

  <resultMap id="agentInfoMap" type="cn.sh.ideal.as.model.AgentInfoBase" >
   <id property="autoId" column="AUTO_ID" jdbcType="INTEGER" />
    <result property="deptId" column="DEPT_ID" jdbcType="VARCHAR" />
    <result property="workNo" column="WORK_NO" jdbcType="VARCHAR" />
    <result property="userName" column="USER_NAME" jdbcType="VARCHAR" />
    <result property="skillQueue" column="SKILL_GROUP" jdbcType="VARCHAR" />
    <result property="tenantCode" column="TENANT_ID" jdbcType="VARCHAR" />
    <result property="tel" column="TEL" jdbcType="VARCHAR" />
    <result property="email" column="EMAIL" jdbcType="VARCHAR" />
    <result property="teamId" column="TEAM_ID" jdbcType="VARCHAR" />
    <result property="teamName" column="TEAM_NAME" jdbcType="VARCHAR" />
    <result property="agentType" column="AGENT_TYPE" jdbcType="VARCHAR" />
    <result property="exNo" column="EXT_NO" jdbcType="VARCHAR" />
    <result property="skillTypeNum" column="SKILL_TYPE_NUM" jdbcType="VARCHAR" />
    <result property="maxSessionCount" column="MAX_SESSION_COUNT" jdbcType="VARCHAR" />
    <result property="flagImage" column="FLAG_IMAGE" jdbcType="VARCHAR" />
    <result property="serviceTipWords" column="SERVICE_TIP_WORDS" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="SkillQueueWorkNoMap" type="cn.sh.ideal.as.model.SkillQueueWorkNo">
        <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="SKILL_QUEUE" property="skillQueue" jdbcType="TIMESTAMP"/>
        <result column="WORK_NO" property="workNo" jdbcType="VARCHAR"/>
    </resultMap>

  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryUserByAccount" resultMap="agentInfoMap" parameterType="cn.sh.ideal.as.req.LoginRequest">
	    SELECT T.AUTO_ID,T.DEPT_ID,T.LOGIN_NAME,T.WORK_NO,T.USER_NAME,T.SKILL_GROUP,T.TENANT_ID,
			  T.TEL,T.EMAIL,T.TEAM_ID,T.AGENT_TYPE,T.EXT_NO,
			  T.LOGIN_TIME,T.SIGNIN_TIME,T.SIGNOUT_TIME,T.WORK_STATE,T.SKILL_TYPE_NUM FROM CMS_AGENT_INFO T
		WHERE T.STATUS = 1  AND T.WORK_NO = #{userName,jdbcType=VARCHAR}
		<if test="websso!=1">
			AND T.PASSWD=#{passWord,jdbcType=VARCHAR}
		</if>
  </select>

  <select id="queryAgentInfo" resultMap="agentInfoMap"
  			parameterType="cn.sh.ideal.as.model.AgentInfoBase">
	    SELECT T.AUTO_ID,T.DEPT_ID,T.LOGIN_NAME,T.WORK_NO,T.USER_NAME,T.SKILL_GROUP,T.TENANT_ID,
		T.TEL,T.EMAIL,T.TEAM_ID,T.AUTO_BUSY,T.AUTO_ANSWER,T.AGENT_TYPE,T.EXT_NO,T.MAX_SESSION_COUNT,
		T.LOGIN_TIME,T.SIGNIN_TIME,T.SIGNOUT_TIME,T.WORK_STATE,T.SKILL_TYPE_NUM,T.FLAG_IMAGE,T.SERVICE_TIP_WORDS,
		A.TEAM_NAME
		FROM CMS_AGENT_INFO T
		LEFT JOIN CMS_TEAM A ON T.TEAM_ID = A.TEAM_ID
		WHERE T.STATUS = 1
		<if test="tenantCode != null and tenantCode != ''">
			AND T.TENANT_ID = #{tenantCode,jdbcType=VARCHAR}
		</if>
		<if test="workNo != null and workNo != ''">
			AND T.WORK_NO = #{workNo,jdbcType=VARCHAR}
		</if>
  </select>

   <select id="queryAgentIVR" resultType="cn.sh.ideal.as.model.AgentIVR">
  		select t.id,
  		t.tenantCode,
  		t.work_no workNo,
  		t.agent_id agentId,
  		t.subaccount,
  		t.subpwd subPassword,
  		t.voip,
  		t.voippwd voipPassword
  		from ivr_agent t
   </select>


   <select id="querySessionCount" parameterType="hashmap" resultType="int">
        select  count(1) from mgw_session_workno n where n.work_no=#{workNo} and n.tenant_code=#{tenantCode}
  </select>


   <select id="queryBusyTypeByCode" parameterType="String" resultType="cn.sh.ideal.as.model.BusyType">
  		 select b.business_code businessCode,b.business_name businessName ,b.id id from mgw_tenant_business_type b
  		 where b.tenant_id=#{tenantCode}
  </select>

 <select id="querySrvServiceCenter" resultType="cn.sh.ideal.as.model.SrvServiceCenter">
  		select t.id,
  		t.tenant_code tenantCode,
  		t.center_code centerCode,
  		t.center_name centerName,
  		t.location,
  		t.remark
  		from srv_service_center t
   </select>
  <select id="getSkillWorkNo" parameterType="hashmap" resultMap="SkillQueueWorkNoMap">
        SELECT a.`TENANT_ID` tenant_code,
        a.`work_no`,
        SUBSTRING_INDEX(
        SUBSTRING_INDEX(
        a.skill_group,
        ',',
        b.help_topic_id + 1
        ),
        ',',
        - 1
        ) AS skill_queue FROM cms_agent_info a
        JOIN help_topic b
        ON b.help_topic_id &lt; (
        LENGTH(a.skill_group) - LENGTH(REPLACE(a.skill_group, ',', '')) + 1
        )
        AND  a.status = '1'
    </select>

     <select id="queryTenantWorkNo" parameterType="string" resultType="string">
        select work_no from cms_agent_info where tenant_id = #{tenantCode,jdbcType=VARCHAR}
      	and status = '1' and skill_group is not null
    </select>

    <update id="updateAgentState" parameterType="map">
        update cms_agent_info set work_state = #{workState} where work_no = #{workNo}
    </update>

</mapper>
