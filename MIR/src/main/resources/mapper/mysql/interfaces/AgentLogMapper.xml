<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.as.dao.AgentLogDao" >
  
  <resultMap id="agentLogMap" type="cn.sh.ideal.as.model.AgentLog" >
    <id property="id" column="ID" jdbcType="INTEGER" />
    <result property="workNo" column="WORK_NO" jdbcType="VARCHAR" />
    <result property="skillQueue" column="SKILL_QUEUE" jdbcType="VARCHAR" />
    <result property="tenantCode" column="TENANT_CODE" jdbcType="VARCHAR" />
    <result property="beginTime" column="BEGIN_TIME" jdbcType="VARCHAR" />
    <result property="endTime" column="END_TIME" jdbcType="VARCHAR" />
    <result property="isForce" column="IS_FORCE" jdbcType="VARCHAR" />
    <result property="remark" column="REMARK" jdbcType="VARCHAR" />
  </resultMap>
  
  <insert id="insertSignLog" parameterType="cn.sh.ideal.as.model.AgentLog" useGeneratedKeys="true" keyProperty="id">
  	insert into mgw_agent_sign_log(tenant_code,skill_queue,work_no,begin_time,is_force,remark)
  	values(
  		#{tenantCode,jdbcType=VARCHAR},
  		#{skillQueue,jdbcType=VARCHAR},
  		#{workNo,jdbcType=VARCHAR},
  		str_to_date(#{beginTime,jdbcType=VARCHAR}, '%Y-%m-%d %T'),
  		#{isForce,jdbcType=VARCHAR},
  		#{remark,jdbcType=VARCHAR}
  	)
  </insert>
  
  <insert id="insertFreeLog" parameterType="cn.sh.ideal.as.model.AgentLog" useGeneratedKeys="true" keyProperty="id">
  	insert into mgw_agent_free_log(tenant_code,skill_queue,work_no,begin_time,is_force,remark)
  	values(
  		#{tenantCode,jdbcType=VARCHAR},
  		#{skillQueue,jdbcType=VARCHAR},
  		#{workNo,jdbcType=VARCHAR},
  		str_to_date(#{beginTime,jdbcType=VARCHAR}, '%Y-%m-%d %T'),
  		#{isForce,jdbcType=VARCHAR},
  		#{remark,jdbcType=VARCHAR}
  	)
  </insert>
  
   <insert id="insertBusyLog" parameterType="cn.sh.ideal.as.model.AgentLog" useGeneratedKeys="true" keyProperty="id">
  	insert into mgw_agent_busy_log(tenant_code,skill_queue,work_no,begin_time,is_force,remark)
  	values(
  		#{tenantCode,jdbcType=VARCHAR},
  		#{skillQueue,jdbcType=VARCHAR},
  		#{workNo,jdbcType=VARCHAR},
  		str_to_date(#{beginTime,jdbcType=VARCHAR}, '%Y-%m-%d %T'),
  		#{isForce,jdbcType=VARCHAR},
  		#{remark,jdbcType=VARCHAR}
  	)
  </insert>
  
   <update id="updateSignLog" parameterType="hashMap">
   		update mgw_agent_sign_log set end_time = str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %T')  where id = #{id,jdbcType=VARCHAR}
  </update>
  
   <update id="updateFreeLog" parameterType="hashMap">
   		update mgw_agent_free_log set end_time =  str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %T') where id = #{id,jdbcType=VARCHAR}
  </update>
  
   <update id="updateBusyLog" parameterType="hashMap">
   		update mgw_agent_busy_log set end_time =  str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %T') where id = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="clearSignLog" parameterType="cn.sh.ideal.as.model.AgentLog">
   		update mgw_agent_sign_log set end_time = str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %T') 
   		where id in (
   		  select a.id from(
   			select id from mgw_agent_sign_log 
   			where tenant_code = #{tenantCode,jdbcType=VARCHAR}
   			and work_no = #{workNo,jdbcType=VARCHAR}
   			and begin_time &gt;= str_to_date(concat(date_format(now(),'%Y-%m-%d'),' 00:00:00' ),'%Y-%m-%d %T')
   			and end_time is null
   			)a
   		)
  </update>
  
  <update id="clearBusyLog" parameterType="cn.sh.ideal.as.model.AgentLog">
   		update mgw_agent_busy_log set end_time = str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %T') 
   		where id in (
   		  select a.id from (
   			select id from mgw_agent_busy_log 
   			where tenant_code = #{tenantCode,jdbcType=VARCHAR}
   			and work_no = #{workNo,jdbcType=VARCHAR}
   			and begin_time &gt;= str_to_date(concat(date_format(now(),'%Y-%m-%d'),' 00:00:00' ),'%Y-%m-%d %T')
   			and end_time is null)a
   		)
  </update>
  
  <update id="clearFreeLog" parameterType="cn.sh.ideal.as.model.AgentLog">
   		update mgw_agent_free_log set end_time = str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %T') 
   		where id in (
   		  select a.id from (
   			select id from mgw_agent_free_log 
   			where tenant_code = #{tenantCode,jdbcType=VARCHAR}
   			and work_no = #{workNo,jdbcType=VARCHAR}
   			and begin_time &gt;= str_to_date(concat(date_format(now(),'%Y-%m-%d'),' 00:00:00' ),'%Y-%m-%d %T')
   			and end_time is null)a
   		)
  </update>
  
  <insert id="insertTransferLog" parameterType="cn.sh.ideal.as.model.TransferLog">
  	 INSERT INTO MGW_TRANSFER_LOG (MESSAGE_ID,BEFORE_WORK_NO,
  	 	BEFORE_SKILL_QUEUE,FORWARD_SKILL_QUEUE,FORWARD_WORK_NO,CREATE_TIME,STATUS,REMARK)
		VALUES(#{messageId,jdbcType=VARCHAR},#{beforeWorkNo,jdbcType=VARCHAR},
		#{beforeSkillQueue,jdbcType=VARCHAR},#{forwardSkillQueue,jdbcType=VARCHAR},#{forwardWorkNo,jdbcType=VARCHAR},
		now(),#{status,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR})
  </insert>
  
   <insert id="insertAgentLocation" parameterType="cn.sh.ideal.as.req.AgentCoordinateRequest">
  	  INSERT INTO SRV_AGENT_LOCATION_LOG(WORK_NO,CREATE_TIME,TENANT_CODE,X,Y,LOCATION_LABEL)
      VALUES (#{workNo,jdbcType=VARCHAR},now(),#{tenantCode,jdbcType=VARCHAR},#{X,jdbcType=VARCHAR},#{Y,jdbcType=VARCHAR},#{label,jdbcType=VARCHAR})
  </insert>
  
  
    <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QUERY  插入AGENT_LOG日志                                                                                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insertAgentLog" parameterType="cn.sh.ideal.as.model.AgentLogModel" >
    insert into CMS_AGENT_LOG (WORK_NO, OPTION_TIME,  LOGIN_TYPE, CLIENTTYPE, TENANT_CODE, SKILL_QUEUE)
    values ( #{workNo,jdbcType=VARCHAR}, now(),
      		#{loginType,jdbcType=VARCHAR}, #{clientType,jdbcType=VARCHAR},#{tenantCode,jdbcType=VARCHAR},#{skillQueue,jdbcType=VARCHAR})
  </insert>

</mapper>