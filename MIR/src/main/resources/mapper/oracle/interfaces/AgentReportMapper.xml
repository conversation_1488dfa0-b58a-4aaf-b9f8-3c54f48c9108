<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.as.dao.AgReportDao" >

  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QUERY 统计坐席服务数量及服务时长报表                                                                                                          -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryAgentList" resultType="cn.sh.ideal.as.resp.entity.AgentReportModel" parameterType="cn.sh.ideal.as.req.AgentBusiAcceptanceRequest">
		select a.work_no workNo ,a.bscount businessCount, a.tenantCode tenantCode,a.user_name userName,
			   sum((g.end_time-g.begin_time)* 86400000) businessTime from (
		select o.work_no work_no, o.tenant_id tenantCode,o.user_name, count(s.id) bscount
		from cms_agent_info o left join  mgw_session_workno s on o.work_no=s.work_no 
		group by o.work_no, o.tenant_id, o.user_name
		) a left join (select tenant_code,work_no,begin_time,end_time from mgw_agent_sign_log
      group by tenant_code,work_no,begin_time,end_time) g 
		on a.work_no=g.work_no 
		where 1=1 
		<if test="tenantCode != null and tenantCode != ''">
			 and a.tenantCode=#{tenantCode,jdbcType=VARCHAR}
		</if> 
		<if test="workNo != null and workNo != ''">
			  and g.work_no=#{workNo,jdbcType=VARCHAR}
		</if> 
		<if test="startTime != null and startTime != ''">
			<![CDATA[ and g.signin_time>to_date(#{startTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')]]>
		</if>
		<if test="endTime != null and endTime != ''">
			<![CDATA[and g.signin_time<to_date(#{endTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')]]>
		</if>
		group by a.work_no,a.bscount,a.tenantCode ,a.user_name
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QUERY 满意度统计表                                                                                                           -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryAgentSatisfactionList" resultType="cn.sh.ideal.as.resp.entity.AgentSatisfactionReportModel" parameterType="cn.sh.ideal.as.req.AgentSatisfiedRequest">
	<![CDATA[ select e.work_no workNo,o.user_name userName,sum(e.satified) totalScore,to_char(round(sum(e.satified)/(count(1)*5),2),'fm90.00') satisfactionRate,
          		count(1) totalNum,avg(e.satified) averageScore from imr_score e ,cms_agent_info o 
        where  e.work_no=o.work_no and  o.tenant_id=#{tenantCode,jdbcType=VARCHAR}
    ]]>
    	<if test="workNo != null and workNo != ''">
			<![CDATA[ and  o.work_no=#{workNo,jdbcType=VARCHAR} ]]>
		</if>
		<if test="startTime != null and startTime != ''">
			<![CDATA[ and e.getscoretime>(to_date(#{startTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')- to_date('1970-01-01','yyyy-mm-dd')) * 86400000]]>
		</if>
		<if test="endTime != null and endTime != ''">
			<![CDATA[ and e.getscoretime<(to_date(#{endTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')- to_date('1970-01-01','yyyy-mm-dd')) * 86400000]]>
		</if>
  		      group by e.work_no,o.user_name
  </select>
  
  
  
   <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QUERY 查询所有技能组名称                                                                                                      			-->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryAllSkillQueueNameList" resultType="java.util.HashMap">
		select e.id skillQueue,e.queue_name skillQueueName from mgw_tenant_skill_queue e   
  </select>
  
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QUERY 查询所有技能组类型 名称                                                                                                  			-->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <select id="queryAllSkillTypeNameList" resultType="java.util.HashMap">
	select g.skill_type_code skillTypeCode, g.skill_type_name skillTypeName from mgw_tenant_skill_type g
  </select>
  
   <select id="queryBusyTimes" parameterType="cn.sh.ideal.as.req.AgentServiceRequest" resultType="cn.sh.ideal.as.model.BusyCount">
  	 select t1.tenant_code tenantCode,
			t1.work_no workNo,
			t2.type_text busyType,
			nvl(floor(sum(end_time - begin_time) * 86400000),0) busyTime,
			count(*) busyCount
			from 
			(select tenant_code,work_no,end_time,begin_time,remark
			from MGW_AGENT_BUSY_LOG 
			where tenant_Code = #{tenantCode,jdbcType=VARCHAR} and work_no = #{workNo,jdbcType=VARCHAR} 
			and begin_time &gt;= to_date(to_char(sysdate,'yyyy-MM-dd')||' 00:00:00','yyyy-MM-dd hh24:mi:ss')
			group by tenant_code,work_no,begin_time,end_time,remark)t1
			left join srv_busy_type t2 
			on t1.remark = t2.type_id
			group by t1.tenant_code,t1.work_no,t2.type_text
  </select>
  
  <select id="queryServiceDuration" parameterType="cn.sh.ideal.as.req.AgentServiceRequest" resultType="string">
  	select nvl(floor(sum(end_time - begin_time) * 86400000),0) from (
		select end_time,begin_time,tenant_code,work_no from mgw_agent_free_log group by tenant_code,work_no,begin_time,end_time
		union all
		select end_time,begin_time,tenant_code,work_no from mgw_agent_busy_log group by tenant_code,work_no,begin_time,end_time
		) 
	where tenant_Code = #{tenantCode,jdbcType=VARCHAR} 
	and work_no = #{workNo,jdbcType=VARCHAR} 
	and begin_time &gt;= to_date(to_char(sysdate,'yyyy-MM-dd')||' 00:00:00','yyyy-MM-dd hh24:mi:ss')
  </select>
  
</mapper>