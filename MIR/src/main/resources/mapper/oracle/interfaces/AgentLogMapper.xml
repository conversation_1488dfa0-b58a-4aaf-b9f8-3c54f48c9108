<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.as.dao.AgentLogDao" >
  
  <resultMap id="agentLogMap" type="cn.sh.ideal.as.model.AgentLog" >
    <id property="id" column="ID" jdbcType="INTEGER" />
    <result property="workNo" column="WORK_NO" jdbcType="VARCHAR" />
    <result property="skillQueue" column="SKILL_QUEUE" jdbcType="VARCHAR" />
    <result property="tenantCode" column="TENANT_CODE" jdbcType="VARCHAR" />
    <result property="beginTime" column="BEGIN_TIME" jdbcType="VARCHAR" />
    <result property="endTime" column="END_TIME" jdbcType="VARCHAR" />
    <result property="isForce" column="IS_FORCE" jdbcType="VARCHAR" />
    <result property="remark" column="REMARK" jdbcType="VARCHAR" />
  </resultMap>
  
  <insert id="insertSignLog" parameterType="cn.sh.ideal.as.model.AgentLog">
  	<selectKey resultType="java.lang.String" order="BEFORE" keyProperty="id">  
			 select seq_mgw_agent_sign_log.nextval from dual
	</selectKey>
  	insert into mgw_agent_sign_log(id,tenant_code,skill_queue,work_no,begin_time,is_force,remark)
  	values(
  		#{id,jdbcType=VARCHAR},
  		#{tenantCode,jdbcType=VARCHAR},
  		#{skillQueue,jdbcType=VARCHAR},
  		#{workNo,jdbcType=VARCHAR},
  		to_date(#{beginTime,jdbcType=VARCHAR}, 'yyyy-MM-dd hh24:mi:ss'),
  		#{isForce,jdbcType=VARCHAR},
  		#{remark,jdbcType=VARCHAR}
  	)
  </insert>
  
  <insert id="insertFreeLog" parameterType="cn.sh.ideal.as.model.AgentLog">
  	<selectKey resultType="java.lang.String" order="BEFORE" keyProperty="id">  
			 select seq_mgw_agent_free_log.nextval from dual
	</selectKey>
  	insert into mgw_agent_free_log(id,tenant_code,skill_queue,work_no,begin_time,is_force,remark)
  	values(
  		#{id,jdbcType=VARCHAR},
  		#{tenantCode,jdbcType=VARCHAR},
  		#{skillQueue,jdbcType=VARCHAR},
  		#{workNo,jdbcType=VARCHAR},
  		to_date(#{beginTime,jdbcType=VARCHAR}, 'yyyy-MM-dd hh24:mi:ss'),
  		#{isForce,jdbcType=VARCHAR},
  		#{remark,jdbcType=VARCHAR}
  	)
  </insert>
  
   <insert id="insertBusyLog" parameterType="cn.sh.ideal.as.model.AgentLog">
  	<selectKey resultType="java.lang.String" order="BEFORE" keyProperty="id">  
			 select seq_mgw_agent_busy_log.nextval from dual
	</selectKey>
  	insert into mgw_agent_busy_log(id,tenant_code,skill_queue,work_no,begin_time,is_force,remark)
  	values(
  		#{id,jdbcType=VARCHAR},
  		#{tenantCode,jdbcType=VARCHAR},
  		#{skillQueue,jdbcType=VARCHAR},
  		#{workNo,jdbcType=VARCHAR},
  		to_date(#{beginTime,jdbcType=VARCHAR}, 'yyyy-MM-dd hh24:mi:ss'),
  		#{isForce,jdbcType=VARCHAR},
  		#{remark,jdbcType=VARCHAR}
  	)
  </insert>
  
   <update id="updateSignLog" parameterType="hashMap">
   		update mgw_agent_sign_log set end_time = to_date(#{endTime,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')  where id = #{id,jdbcType=VARCHAR}
  </update>
  
   <update id="updateFreeLog" parameterType="hashMap">
   		update mgw_agent_free_log set end_time =  to_date(#{endTime,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss') where id = #{id,jdbcType=VARCHAR}
  </update>
  
   <update id="updateBusyLog" parameterType="hashMap">
   		update mgw_agent_busy_log set end_time =  to_date(#{endTime,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss') where id = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="clearSignLog" parameterType="cn.sh.ideal.as.model.AgentLog">
   		update mgw_agent_sign_log t set t.end_time = to_date(#{endTime,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss') 
   		where exists (
   			select * from mgw_agent_sign_log 
   			where t.id = id
   			and tenant_code = #{tenantCode,jdbcType=VARCHAR}
   			and work_no = #{workNo,jdbcType=VARCHAR}
   			and begin_time &gt;= to_date(to_char(sysdate,'yyyy-MM-dd')||' 00:00:00','yyyy-MM-dd hh24:mi:ss')
   			and end_time is null
   		)
  </update>
  
  <update id="clearBusyLog" parameterType="cn.sh.ideal.as.model.AgentLog">
   		update mgw_agent_busy_log t set t.end_time = to_date(#{endTime,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')  
   		where exists (
   			select * from mgw_agent_busy_log 
   			where t.id = id
   			and tenant_code = #{tenantCode,jdbcType=VARCHAR}
   			and work_no = #{workNo,jdbcType=VARCHAR}
   			and begin_time &gt;= to_date(to_char(sysdate,'yyyy-MM-dd')||' 00:00:00','yyyy-MM-dd hh24:mi:ss')
   			and end_time is null
   		)
  </update>
  
  <update id="clearFreeLog" parameterType="cn.sh.ideal.as.model.AgentLog">
   		update mgw_agent_free_log t set t.end_time = to_date(#{endTime,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')  
   		where exists (
   			select * from mgw_agent_free_log 
   			where t.id = id
   			and tenant_code = #{tenantCode,jdbcType=VARCHAR}
   			and work_no = #{workNo,jdbcType=VARCHAR}
   			and begin_time &gt;= to_date(to_char(sysdate,'yyyy-MM-dd')||' 00:00:00','yyyy-MM-dd hh24:mi:ss')
   			and end_time is null
   		)
  </update>
  
  <insert id="insertTransferLog" parameterType="cn.sh.ideal.as.model.TransferLog">
  	 INSERT INTO MGW_TRANSFER_LOG (TRANSFER_ID,MESSAGE_ID,BEFORE_WORK_NO,
  	 	BEFORE_SKILL_QUEUE,FORWARD_SKILL_QUEUE,FORWARD_WORK_NO,CREATE_TIME,STATUS,REMARK)
		VALUES(SEQ_MGW_TRANSFER_LOG.NEXTVAL, #{messageId,jdbcType=VARCHAR},#{beforeWorkNo,jdbcType=VARCHAR},
		#{beforeSkillQueue,jdbcType=VARCHAR},#{forwardSkillQueue,jdbcType=VARCHAR},#{forwardWorkNo,jdbcType=VARCHAR},
		SYSDATE,#{status,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR})
  </insert>
  
   <insert id="insertAgentLocation" parameterType="hashmap">
  	  INSERT INTO SRV_AGENT_LOCATION_LOG(ID,WORK_NO,CREATE_TIME,TENANT_CODE,X,Y,LOCATION_LABEL)
      VALUES (SEQ_SRV_AGENT_LOCATION_LOG.NEXTVAL,#{workNo,jdbcType=VARCHAR},SYSDATE,#{tenantCode,jdbcType=VARCHAR},#{X,jdbcType=VARCHAR},#{Y,jdbcType=VARCHAR},#{label,jdbcType=VARCHAR})
  </insert>
  
  
    <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- QUERY  插入AGENT_LOG日志                                                                                                                        -->
  <!-- This element was generated on Mon May 26 18:35:08 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <insert id="insertAgentLog" parameterType="cn.sh.ideal.as.model.AgentLogModel" >
    insert into CMS_AGENT_LOG (AUTO_ID, WORK_NO, OPTION_TIME,  LOGIN_TYPE, CLIENTTYPE, TENANT_CODE, SKILL_QUEUE)
    values (SEQ_CMS_AGENT_LOG.NEXTVAL, #{workNo,jdbcType=VARCHAR}, sysdate,
      		#{loginType,jdbcType=VARCHAR}, #{clientType,jdbcType=VARCHAR},#{tenantCode,jdbcType=VARCHAR},#{skillQueue,jdbcType=VARCHAR})
  </insert>

</mapper>