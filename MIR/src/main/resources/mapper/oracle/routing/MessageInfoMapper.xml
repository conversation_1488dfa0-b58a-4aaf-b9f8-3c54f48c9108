<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.MessageInfoDao">
	<resultMap id="BaseResultMap"
		type="cn.sh.ideal.model.MessageInfo">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<result column="MESSAGE_ID" property="messageId" jdbcType="VARCHAR" />
		<result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType"
			jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="USER_ID" property="userId" jdbcType="VARCHAR" />
		<result column="SOURCE" property="source" jdbcType="VARCHAR" />
		<result column="NICKNAME" property="nickname" jdbcType="VARCHAR" />
		<result column="SEND_ACCOUNT" property="sendAccount" jdbcType="VARCHAR" />
		<result column="ACCEPTED_ACCOUNT" property="acceptedAccount"
			jdbcType="VARCHAR" />
		<result column="REPLY_ACCOUNT" property="replyAccount"
			jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="SEND_TIME" property="sendTime" jdbcType="TIMESTAMP" />
		<result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
		<result column="SKILL_QUEUE" property="skillQueue" jdbcType="VARCHAR" />
		<result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="MSG_TYPE" property="msgType" jdbcType="VARCHAR" />
		<result column="SKILL_TYPE" property="skillType" jdbcType="VARCHAR" />
		<result column="CONTENT" property="content" jdbcType="VARCHAR" />
		<result column="FOLLOW_DATA" property="followData" jdbcType="VARCHAR" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
	</resultMap>

	<select id="load" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		select
		MESSAGE_ID,CHANNEL_CODE,BUSINESS_TYPE,TENANT_CODE,USER_ID,SOURCE,NICKNAME,SEND_ACCOUNT,ACCEPTED_ACCOUNT,
		REPLY_ACCOUNT,CREATE_TIME,SEND_TIME,SESSION_ID,SKILL_QUEUE,WORK_NO,STATUS,MSG_TYPE,SKILL_TYPE,CONTENT,
		FOLLOW_DATA,REMARK
		where MESSAGE_ID = #{id,jdbcType=VARCHAR}
	</select>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Query -->
	<!-- This element was generated on Mon May 26 18:35:08 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<select id="query" resultMap="BaseResultMap"
		parameterType="cn.sh.ideal.model.MessageInfo">
			select
		MESSAGE_ID,CHANNEL_CODE,BUSINESS_TYPE,TENANT_CODE,USER_ID,SOURCE,NICKNAME,SEND_ACCOUNT,ACCEPTED_ACCOUNT,
		REPLY_ACCOUNT,CREATE_TIME,SEND_TIME,SESSION_ID,SKILL_QUEUE,WORK_NO,STATUS,MSG_TYPE,SKILL_TYPE,CONTENT,
		FOLLOW_DATA,REMARK
		FROM MGW_MESSAGE_INFO
		<include refid="where" />
	</select>
	<insert id="insert" parameterType="cn.sh.ideal.model.MessageInfo">
		<selectKey resultType="java.lang.String" order="BEFORE" keyProperty="messageId">  
			 SELECT SEQ_MGW_MESSAGE_INFO.NEXTVAL FROM DUAL
	    </selectKey>
		insert into MGW_MESSAGE_INFO (MESSAGE_ID, CHANNEL_CODE, BUSINESS_TYPE,
		TENANT_CODE, USER_ID, SOURCE,
		NICKNAME, SEND_ACCOUNT, ACCEPTED_ACCOUNT,
		REPLY_ACCOUNT, CREATE_TIME, SEND_TIME,
		SESSION_ID, SKILL_QUEUE,
		WORK_NO,
		STATUS, MSG_TYPE, SKILL_TYPE,CONTENT,
		FOLLOW_DATA, REMARK,MESSAGE_SOURCE,send_type)
		values
		(#{messageId,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR},
		#{businessType,jdbcType=VARCHAR},
		#{tenantCode,jdbcType=VARCHAR},
		#{userId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
		#{nickname,jdbcType=VARCHAR}, #{sendAccount,jdbcType=VARCHAR},
		#{acceptedAccount,jdbcType=VARCHAR},
		#{replyAccount,jdbcType=VARCHAR},
		#{createTime,jdbcType=TIMESTAMP}, sysdate(),
		#{sessionId,jdbcType=VARCHAR}, #{skillQueue,jdbcType=VARCHAR},
		#{workNo,jdbcType=VARCHAR},
		#{status,jdbcType=VARCHAR},
		#{msgType,jdbcType=VARCHAR}, 
		#{skillType,jdbcType=VARCHAR}, 
		#{content,jdbcType=VARCHAR},
		#{followData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
		#{messageSource,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR})
	</insert>
<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <!-- Update                                                       -->
  <!-- This element was generated on Thu Jun 05 16:38:50 CST 2014.  -->
  <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
  <update id="update" parameterType="cn.sh.ideal.model.MessageInfo" >
    UPDATE MGW_MESSAGE_INFO
    <set >
			<if test="channelCode != null and channelCode !=''">
				
				CHANNEL_CODE = #{channelCode ,jdbcType=VARCHAR},
			</if>
			<if test="businessType != null and businessType !=''">
				
				BUSINESS_TYPE = #{businessType ,jdbcType=VARCHAR},
			</if>
			<if test="tenantCode != null and tenantCode !=''">
				
				TENANT_CODE = #{tenantCode ,jdbcType=VARCHAR},
			</if>
			<if test="userId != null and userId !=''">
				
				USER_ID = #{userId ,jdbcType=VARCHAR},
			</if>
			<if test="source != null and source !=''">
				
				SOURCE = #{source ,jdbcType=VARCHAR},
			</if>
			<if test="nickname != null and nickname !=''">
				
				NICKNAME = #{nickname ,jdbcType=VARCHAR},
			</if>
			<if test="sendAccount != null and sendAccount !=''">
				
				SEND_ACCOUNT = #{sendAccount ,jdbcType=VARCHAR},
			</if>
			<if test="acceptedAccount != null and acceptedAccount !=''">
				
				ACCEPTED_ACCOUNT = #{acceptedAccount, jdbcType=VARCHAR},
			</if>
			<if test="replyAccount != null and replyAccount !=''">
				
				REPLY_ACCOUNT = #{replyAccount ,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null and createTime !=''">
				
				CREATE_TIME = #{createTime ,jdbcType=TIMESTAMP},
			</if>
			<if test="sendTime != null and sendTime !=''">
				
				SEND_TIME = #{sendTime ,jdbcType=TIMESTAMP},
			</if>
			<if test="sessionId != null and sessionId !=''">
				
				SESSION_ID = #{sessionId ,jdbcType=VARCHAR},
			</if>
			<if test="skillQueue != null and skillQueue !=''">
				
				SKILL_QUEUE = #{skillQueue ,jdbcType=VARCHAR},
			</if>
			<if test="workNo != null and workNo !=''">
				
				WORK_NO = #{workNo ,jdbcType=VARCHAR},
			</if>
			<if test="status != null and status !=''">
				
				STATUS = #{status ,jdbcType=VARCHAR},
			</if>
			<if test="msgType != null and msgType !=''">
				
				MSG_TYPE = #{msgType ,jdbcType=VARCHAR},
			</if>
			<if test="skillType != null and skillType !=''">
				
				SKILL_TYPE = #{skillType ,jdbcType=VARCHAR},
			</if>
			<if test="content != null and content !=''">
				
				CONTENT = #{content ,jdbcType=VARCHAR},
			</if>
			<if test="followData != null and followData !=''">
				
				FOLLOW_DATA = #{followData ,jdbcType=VARCHAR},
			</if>
			<if test="remark != null and remark !=''">
				
				REMARK = #{remark, jdbcType=VARCHAR},
			</if>
    </set>
    WHERE MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
  </update>
  
	<select id="getMsgsByIds" parameterType="list" resultMap="BaseResultMap">
    select * from MGW_MESSAGE_INFO where MESSAGE_ID in 
 		<foreach collection="list" item= "msgId" index ="index"
            open= "(" close =")" separator=",">
            #{msgId,jdbcType=VARCHAR}
     </foreach >
  </select>
  
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Thu Jun 05 16:38:50 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="messageId != null and messageId !=''">
				MESSAGE_ID = #{messageId ,jdbcType=VARCHAR}
			</if>
			<if test="channelCode != null and channelCode !=''">
				and
				CHANNEL_CODE = #{channelCode ,jdbcType=VARCHAR}
			</if>
			<if test="businessType != null and businessType !=''">
				and
				BUSINESS_TYPE = #{businessType ,jdbcType=VARCHAR}
			</if>
			<if test="tenantCode != null and tenantCode !=''">
				and
				TENANT_CODE = #{tenantCode ,jdbcType=VARCHAR}
			</if>
			<if test="userId != null and userId !=''">
				and
				USER_ID = #{userId ,jdbcType=VARCHAR}
			</if>
			<if test="source != null and source !=''">
				and
				SOURCE = #{source ,jdbcType=VARCHAR}
			</if>
			<if test="nickname != null and nickname !=''">
				and
				NICKNAME = #{nickname ,jdbcType=VARCHAR}
			</if>
			<if test="sendAccount != null and sendAccount !=''">
				and
				SEND_ACCOUNT = #{sendAccount ,jdbcType=VARCHAR}
			</if>
			<if test="acceptedAccount != null and acceptedAccount !=''">
				and
				ACCEPTED_ACCOUNT = #{acceptedAccount,jdbcType=VARCHAR}
			</if>
			<if test="replyAccount != null and replyAccount !=''">
				and
				REPLY_ACCOUNT = #{replyAccount ,jdbcType=VARCHAR}
			</if>
			<if test="createTime != null and createTime !=''">
				and
				CREATE_TIME = #{createTime ,jdbcType=TIMESTAMP}
			</if>
			<if test="sendTime != null and sendTime !=''">
				and
				SEND_TIME = #{sendTime ,jdbcType=TIMESTAMP}
			</if>
			<if test="sessionId != null and sessionId !=''">
				and
				SESSION_ID = #{sessionId ,jdbcType=VARCHAR}
			</if>
			<if test="skillQueue != null and skillQueue !=''">
				and
				SKILL_QUEUE = #{skillQueue ,jdbcType=VARCHAR}
			</if>
			<if test="workNo != null and workNo !=''">
				and
				WORK_NO = #{workNo ,jdbcType=VARCHAR}
			</if>
			<if test="status != null and status !=''">
				and
				STATUS = #{status ,jdbcType=VARCHAR}
			</if>
			<if test="msgType != null and msgType !=''">
				and
				MSG_TYPE = #{msgType ,jdbcType=VARCHAR}
			</if>
			
			<if test="skillType != null and skillType !=''">
				and
				SKILL_TYPE = #{skillType ,jdbcType=VARCHAR}
			</if>
			<if test="content != null and content !=''">
				and
				CONTENT = #{content ,jdbcType=VARCHAR}
			</if>
			<if test="followData != null and followData !=''">
				and
				FOLLOW_DATA = #{followData ,jdbcType=VARCHAR}
			</if>
			<if test="remark != null and remark !=''">
				and
				REMARK = #{remark ,jdbcType=VARCHAR}
			</if>

		</where>
	</sql>
	
	<select id="getCurDate" resultType="string">
	  SELECT TO_CHAR(sysdate, 'MM-DD') FROM DUAL
    </select>
</mapper>