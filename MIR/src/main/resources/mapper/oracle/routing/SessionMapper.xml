<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.SessionDao" >
  
  <select id="queryPriority"  resultType="string" parameterType="hashmap">
  select sum(priority) from
		(select priority from mgw_channel_priority where tenant_code = #{tenantCode, jdbcType = VARCHAR}
		 and skill_queue_id =  #{queueId, jdbcType = VARCHAR} and channel_code = #{channelCode, jdbcType = VARCHAR}
		union all
		select priority from mgw_busi_priority where tenant_code = #{tenantCode, jdbcType = VARCHAR}
		 and skill_queue_id =  #{queueId, jdbcType = VARCHAR} and busi_id = #{businessType, jdbcType = VARCHAR}
		)
	
  </select>
  
  <select id="getLastAgent"  resultType="cn.sh.ideal.model.SessionInfo" parameterType="hashmap">
  		select t.* from(
			select workno workNos,
			  skill_queue skillQueue
			  from mgw_session_info
			  where tenant_code = #{tenantCode, jdbcType = VARCHAR}
			  and customer_id= #{customerId, jdbcType = VARCHAR}
			  and workno is not null
			  <if test="timeout != null and timeout !=''">
				and create_time &gt;= #{timeout,jdbcType=TIMESTAMP}
			  </if>
			  <if test="skillQueue != null and skillQueue !=''">
				and skill_queue = #{skillQueue,jdbcType=VARCHAR}
			  </if>
			 order by create_time desc
		)t where rownum =1
  </select>
  
    <select id="getQueueSession"  resultType="cn.sh.ideal.mir.resp.entity.SessionQueue" parameterType="hashmap">
  		select 
                to_char(t.create_time,'yyyy-mm-dd hh24:mi:ss') queueTime,  		
		t.SESSION_ID sessionId,
		t.TENANT_CODE tenantCode,
		t.CHANNEL_CODE channelCode,
		t.SEND_ACCOUNT sendAccount,
		t.ACCEPTED_ACCOUNT acceptAccount,
		t.CUSTOMER_ID customerId,
		t.call_id callId,
		nvl(t1.REAL_NAME,t.NICK_NAME) customerName
		from mgw_session_info t 
		inner join cuv_customer_info t1
		on t.customer_id = t1.customer_id and t.status=3
		and skill_queue = #{skillQueue,jdbcType=VARCHAR}
  </select>
  
   <select id="getVoiceSession"  resultType="cn.sh.ideal.model.SessionInfo" parameterType="cn.sh.ideal.model.SessionInfo">
   select t.* from(
			select workno workNos,
			  skill_queue skillQueue
			  from mgw_session_info
			  where channel_code = '1000'
			  and status=5
			  and tenant_code = #{tenantCode, jdbcType = VARCHAR}
			  and customer_id= #{customerId, jdbcType = VARCHAR}
			 order by create_time desc
			 )t
			 where rownum=1
  </select>
  
  <select id="getExclusiveAgent"  resultType="string" parameterType="string">
	   select work_no from cms_user_owner_test where customer_id=#{customerId}
  </select>
  
  <select id="getCurrTimeStamp" resultType="string">
        SELECT (SYSDATE - TO_DATE('1970-1-1 8', 'YYYY-MM-DD HH24')) * 86400000 + TO_NUMBER(TO_CHAR(SYSTIMESTAMP(3), 'FF')) AS MILLIONS FROM DUAL
  </select>	
  
  <select id="getSessionCount" resultType="int" parameterType="map">
		select count(1) from mgw_session_info t1
		left join mgw_tenant_skill_type t2 on(t1.tenant_code = t2.tenant_id and t1.skill_type = t2.skill_type_code)
		where t1.status in('5','8') and t2.status=1 and t2.is_record = 'Y'
		and t1.TENANT_CODE = #{tenantCode} and t1.workno=#{workNo}
	</select>
</mapper>