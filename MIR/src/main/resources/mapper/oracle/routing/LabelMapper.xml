<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.LabelDao" >
	<select id="getCustomerId" parameterType="hashmap" resultType="string">
		select customer_id from cuv_customer_recognition 
		where tenant_code=#{tenantCode,jdbcType=VARCHAR}
		and channel = #{channel,jdbcType=VARCHAR}
		and account = #{account,jdbcType=VARCHAR}
	</select>
	
	<select id="getAgents" parameterType="hashmap" resultType="cn.sh.ideal.model.AgentInfo">
		select tenant_code tenantCode,work_no workNo
  from srv_agent_label
 where tenant_code=#{tenantCode,jdbcType=VARCHAR}
 and label_id in
       (select id
          from srv_label
         where id in
               (select srv_label_id
                  from cuv_label
                 where label_id in (select label_id
                                      from cuv_customer_action_label
                                     where customer_id = #{customerId,jdbcType=VARCHAR})))
         
	</select>
</mapper>