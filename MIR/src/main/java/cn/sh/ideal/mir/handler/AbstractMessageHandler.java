package cn.sh.ideal.mir.handler;

import cn.sh.ideal.mir.executor.MessageExecutor;
import cn.sh.ideal.mir.executor.TaskThreadFactory;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.RoutingService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.impl.MessageService;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.util.Constants;
import cn.sh.ideal.util.RedisLock;
import com.alibaba.fastjson.JSONObject;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class AbstractMessageHandler
		implements MessageHandler
{
	private static final Logger log = LoggerFactory.getLogger(AbstractMessageHandler.class);

	@Autowired
	protected SessionService sessionService;

	@Resource(name="routingService")
	private RoutingService routingService;

	@Autowired
	protected AllocationService allocationService;

	@Autowired
	protected MessageService messageService;
	protected MessageInfo message;

	public void run() { RedisLock lock = null;
		SessionInfo session = null;
		try {
			if (null == this.message) return; String sessionId = this.message.getSessionId();
			String tenantCode = this.message.getTenantCode();

			lock = RedisLock.getRedisLock("session".concat(sessionId));
			while (!lock.lock());
			log.info("handle message:{}", JSONObject.toJSONString(this.message));

			session = this.sessionService.get(sessionId, tenantCode);

			if (!pushIfMutual(session))
				hanlderSession(tenantCode, sessionId, session);
		}
		catch (Exception e) {
			log.error("路由异常", e);
			if (null != session)
				this.sessionService.update(session.getSessionId(), session.getTenantCode(), SessionStatus.TIMEOUT, null);
		}
		finally {
			if (lock != null) {
				lock.unlock();
			}
			MessageExecutor.msgThreadFactory.decrementAndGet();
		}
	}

	public void routing(SessionInfo session)
	{
		this.routingService.routing(session);
		updateSort(session.getSessionId(), session.getTenantCode(), session.getSkillQueue());
	}

	private boolean pushIfMutual(SessionInfo session) throws Exception
	{
		boolean isMutual = false;

		if (ArrayUtils.contains(Constants.artificialStatus, session.getStatus())) {
			isMutual = true;
			if (StringUtils.isNotEmpty(session.getWorkNos()))
			{
				this.allocationService.pushMessage(this.message, session);
			}
			else log.warn("session service work number is null, session id {}, status {}.", session
					.getSessionId(), session.getStatus());

		}

		return isMutual;
	}

	private void hanlderSession(String tenantCode, String sessionId, SessionInfo session) throws Exception {
		if (!this.allocationService.preAllocate(session, this.message)) {
			if (!ArrayUtils.contains(Constants.sortingStatus, session.getStatus()))
			{
				this.sessionService.update(sessionId, tenantCode, SessionStatus.ROUTE, null);

				if (StringUtils.isNotEmpty(this.message.getSkillQueue())) {
					session.setSkillQueue(this.message.getSkillQueue());
				}
				routing(session);
			}
		}
	}

	public void updateSort(String sessionId, String tenantCode, String skillQueue) {
		Map updateFields = new HashMap();
		updateFields.put("skillQueue", skillQueue);
		try
		{
			this.sessionService.update(sessionId, tenantCode, SessionStatus.SORT, updateFields);
		} catch (Exception e) {
			log.error(e.getMessage());
		}
	}

	public void setMessage(MessageInfo message) {
		this.message = message;
	}

	public MessageInfo getMessage()
	{
		return this.message;
	}
}
