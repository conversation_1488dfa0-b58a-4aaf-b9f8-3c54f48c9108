package cn.sh.ideal.mir.service.local.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;

import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.sm.resp.RespAddWorkNo;
import cn.sh.ideal.sm.resp.RespGetSession;
import cn.sh.ideal.sm.resp.RespGetSessionCount;
import cn.sh.ideal.sm.resp.RespGetSessionList;
import cn.sh.ideal.sm.resp.RespRemoveWorkNo;
import cn.sh.ideal.sm.resp.RespUpdateSession;
import cn.sh.ideal.util.Constants;

@Service("sessionService")
public class SessionServiceImpl implements SessionService{
	private static final Logger logger = LoggerFactory.getLogger(SessionServiceImpl.class);
	
	@Resource(name="smSessionService")
	private cn.sh.ideal.sm.service.SessionService smSessionService;
	
	
	@Override
	public SessionInfo get(String sessionId, String tenantCode) {
		SessionData sessionData =new SessionData();
		sessionData.setSessionId(sessionId);
		sessionData.setTenantCode(tenantCode);
		logger.info("get session request:{}",JSONObject.toJSONString(sessionData));
		RespGetSession respGetSession=smSessionService.getSession(sessionData);
		logger.info("get session response:{}",JSONObject.toJSONString(respGetSession));
		if(respGetSession==null) {
			throw new NullPointerException("session get failed");
		}
		
		if(!Objects.equal(respGetSession.getResultCode(), RespCode.SUCCESS.getResultCode())) {
			throw new RuntimeException(respGetSession.getResultMsg());
		}
		
		return respGetSession.getData();
		
	}


	@Override
	public void update(String sessionId, String tenantCode, SessionStatus status,
			Map<String, String> updateFields) {
		SessionData sessionData =new SessionData();
		sessionData.setSessionId(sessionId);
		sessionData.setTenantCode(tenantCode);
		sessionData.setStatus(status.toString());
		
		Map<String,Object> extData = new HashMap<String,Object>();
		
		if(updateFields != null && !updateFields.isEmpty())
		for (Map.Entry<String,String> entry : updateFields.entrySet()) {
			extData.put(entry.getKey(), entry.getValue());
		}
		sessionData.setData(extData);
		logger.info("update session request:{}",JSONObject.toJSONString(sessionData));
		RespUpdateSession respUpdateSession = smSessionService.updateSession(sessionData);
		logger.info("update session response:{}",JSONObject.toJSONString(respUpdateSession));
		if(respUpdateSession==null) {
			throw new NullPointerException("session update failed");
		}
		
		if(!Objects.equal(respUpdateSession.getResultCode(), RespCode.SUCCESS.getResultCode())) {
			throw new RuntimeException(respUpdateSession.getResultMsg());
		}
	}
	@Override
	public void updateWorkNo(String sessionId, String tenantCode,String skillQueue,String skillType,
			String workNo, String type) {
		SessionData sessionData =new SessionData();
		sessionData.setSessionId(sessionId);
		sessionData.setTenantCode(tenantCode);
		sessionData.setWorkNo(workNo);
		logger.info("update session workno request:{}",JSONObject.toJSONString(sessionData));
		if(Constants.HANDLE_ADD.equals(type)){
			RespAddWorkNo respAddWorkNo = smSessionService.addWorkNo(sessionData);
			logger.info("update session workno response:{}",JSONObject.toJSONString(respAddWorkNo));
			if(respAddWorkNo==null) {
				throw new NullPointerException("add or remove workno fail");
			}
			if(!Objects.equal(respAddWorkNo.getResultCode(), RespCode.SUCCESS.getResultCode())) {
				throw new RuntimeException(respAddWorkNo.getResultMsg());
			}
		}else{
			RespRemoveWorkNo respRemoveWorkNo = smSessionService.removeWorkNo(sessionData);
			logger.info("update session workno response:{}",JSONObject.toJSONString(respRemoveWorkNo));
			if(respRemoveWorkNo==null) {
				throw new NullPointerException("add or remove workno fail");
			}
			if(!Objects.equal(respRemoveWorkNo.getResultCode(), RespCode.SUCCESS.getResultCode())) {
				throw new RuntimeException(respRemoveWorkNo.getResultMsg());
			}
		}
	}

	@Override
	public List<SessionInfo> getSessionList(SessionData sessionData) {
		logger.info("get sessionList request:{}",JSONObject.toJSONString(sessionData));
		RespGetSessionList respGetSessionList = smSessionService.getSessionList(sessionData);
		logger.info("get sessionList response:{}",JSONObject.toJSONString(respGetSessionList));
		if(respGetSessionList==null) {
			throw new NullPointerException("sessionlist get failed");
		}
		
		if(!Objects.equal(respGetSessionList.getResultCode(), RespCode.SUCCESS.getResultCode())) {
			throw new RuntimeException(respGetSessionList.getResultMsg());
		}
		return respGetSessionList.getData();
	}

	@Override
	public int getSessionCount(SessionData sessionData) {
		logger.info("get sessionCount request:{}",JSONObject.toJSONString(sessionData));
		RespGetSessionCount respGetSessionCount = smSessionService.getSessionCount(sessionData);
		logger.info("get sessionCount response:{}",JSONObject.toJSONString(respGetSessionCount));
		if(respGetSessionCount==null) {
			throw new NullPointerException("sessioncount get failed");
		}
		if(!Objects.equal(respGetSessionCount.getResultCode(), RespCode.SUCCESS.getResultCode())) {
			throw new RuntimeException(respGetSessionCount.getResultMsg());
		}
	    return respGetSessionCount.getData();
		
	}
	

}
