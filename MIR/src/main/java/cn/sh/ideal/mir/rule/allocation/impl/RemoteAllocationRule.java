package cn.sh.ideal.mir.rule.allocation.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.model.SysRule;

/**
 * 第三方分配规则
 * 
 * <AUTHOR>
 *
 */
@Component("remoteAllocationRule")
public class RemoteAllocationRule extends AbstractAllocationRule implements AllocationRule{
	private static final Logger logger = LoggerFactory.getLogger(RemoteAllocationRule.class);
	
	@Override
public List<Map<Object,Object>> excute(String tenantCode, String queueId,SysRule rule){
		
		List<Map<Object,Object>> freeUsers = new ArrayList<>();
		try {
			/*String strategy = initService.getSysParam(WAEAdapter.WAE_STRATEGY).getParamValue();
			
			AgentInfo agent = waeAdapter.matchRequestAsync(tenantCode, queueId,Strategy.valueOf(strategy), 1);
			if(null != agent){
				freeUsers.add(agent);
			}else{
				logger.info("技能组:{}上没有空闲的坐席!", queueId);
			}*/
		} catch (Exception e) {
			logger.error("执行技能组分配规则异常!", e);
		}
		
		return freeUsers;
	}
}
