package cn.sh.ideal.mir.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.base.Objects;

import cn.sh.ideal.as.req.AgentSingleRequest;
import cn.sh.ideal.as.resp.AgentSingleResponse;
import cn.sh.ideal.as.service.AgentService;
import cn.sh.ideal.mir.dao.LabelDao;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.model.AgentInfo;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.model.SessionInfo;

/**
 * 浦发消息处理器-根据标签
 * <AUTHOR>
 *
 */
@Component("spdcMessageHandler")
@Scope("prototype")
public class SpdcMessageHandler extends AbstractMessageHandler{
	@Autowired
	private LabelDao labelDao;

	@Autowired
	private AgentService as;

	@Override
	public void routing(SessionInfo session) {
		try{
			Map<String,String> params = new HashMap<String,String>();
			params.put("tenantCode", session.getTenantCode());
			params.put("account", session.getSendAccount());
			params.put("channel", session.getChannelCode());
			//客户识别
			List<String> customerIds = labelDao.getCustomerId(params);

			if(CollectionUtils.isNotEmpty(customerIds)){
				AgentInfo user = getFixAgents(session, customerIds, params);
				if(null != user){
					int result = messageService.allocateMessage(session, session.getTenantCode(), user.getSkillQueue(), user.getWorkNo());
					if(0 == result) return;
				}
			}

			throw new RuntimeException("can't find the matched agent label for the customer");
		}catch(Exception e){
			super.routing(session);
		}
	}

	private AgentInfo getFixAgents(SessionInfo session,List<String> customerIds,Map<String,String> params){
		String customerId = customerIds.get(0);
		params.put("customerId", customerId);

		List<AgentInfo> agents  = labelDao.getAgents(params);
		for(AgentInfo info : agents){
			AgentSingleRequest request =new AgentSingleRequest();
			request.setTenantCode(session.getTenantCode());
			request.setWorkNo(info.getWorkNo());
			AgentSingleResponse response = as.agentInfo(request);
			if(Objects.equal(response.getResultCode(), RespCode.SUCCESS.getResultCode())){
				AgentInfo agent = response.getData();
				if(AgentStatus.FREE.getCode().equals(agent.getStatus())
						&& agent.getCurrSessionCount() < agent.getMaxSessionCount()){
					return agent;
				}
			}
		}
		return null;
	}

}
