package cn.sh.ideal.mir.rule.route.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.rule.allocation.impl.AbstractAllocationRule;
import cn.sh.ideal.mir.rule.route.RoutingRule;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.model.TenantSkillQueue;



/**
 * 按渠道路由规则实现.
 *
 * <AUTHOR>
 * @date 2014年5月28日
 */

@Component("channelBusinessRoutingRule")
public class ChannelBusinessRoutingRule extends AbstractAllocationRule implements RoutingRule {
	private static final Logger log = LoggerFactory.getLogger(ChannelBusinessRoutingRule.class);

	@Resource(name = "queueService")
	private SkillQueueService queueService;
	
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	@Override
	public TenantSkillQueue excute(SessionInfo session, List<TenantSkillQueue> skillQueues, SysRule sysRule)  {
		log.info("调用技能组规则{} 匹配会话{} ",sysRule.getRuleName(),session.getSessionId());
		// 消息渠道
		final String channel = session.getChannelCode();
		final String business = session.getBusinessType();
		
		TenantSkillQueue skillQueue = null;

		try{
			List<TenantSkillQueue> rightQueues = getRightQueue(skillQueues,channel, business);
			
			// 返回符合技能队列中排队数最少的队列
			if (!rightQueues.isEmpty()) {
				Collections.sort(rightQueues, new Comparator<TenantSkillQueue>() {
				
					@Override
					public int compare(TenantSkillQueue queue1, TenantSkillQueue queue2) {
						return queueService.getAllQueue(String.valueOf(queue1.getId())).size() -  
								queueService.getAllQueue(String.valueOf(queue2.getId())).size();
					}
				});

				// 优先分给排序在前面并且上面有签入坐席的技能组
				for(TenantSkillQueue queue : rightQueues){
					if(!CollectionUtils.isEmpty(this.getFreeUserList(queue.getTenantId(), String.valueOf(queue.getId())))){
						skillQueue = queue;
						break;
					}
				}

				if(null == skillQueue){
					skillQueue = rightQueues.get(0);
				}
			}
		}catch(Exception e){
			log.error("执行技能组规则异常:",e);
		}
		return skillQueue;
	}

	private List<TenantSkillQueue> getRightQueue(final List<TenantSkillQueue> skillQueues,final String channel,final String business){
		// 获取能处理消息渠道的技能队列
		List<TenantSkillQueue> rightQueues = new ArrayList<TenantSkillQueue>();
		
		if(rightQueues.isEmpty()){
			CollectionUtils.select(skillQueues, new Predicate() {
				@Override
				public boolean evaluate(Object object) {
	
					TenantSkillQueue queue = (TenantSkillQueue) object;
	
					if(!"vip".equals(queue.getRemark()) && !StringUtils.isEmpty(channel) && !StringUtils.isEmpty(business)){
						return StringUtils.trimToEmpty(queue.getAbleChannels()).contains(channel) 
								&& StringUtils.trimToEmpty(queue.getAbleBusinessTypes()).contains(business);
					}
	
					return false;
	
				}
	
			}, rightQueues);
		}
		if(rightQueues.isEmpty()){
			CollectionUtils.select(skillQueues, new Predicate() {
				@Override
				public boolean evaluate(Object object) {

					TenantSkillQueue queue = (TenantSkillQueue) object;

					if(!StringUtils.isEmpty(channel) ){
						return !"vip".equals(queue.getRemark()) && StringUtils.trimToEmpty(queue.getAbleChannels()).contains(channel) ;

					}

					return false;

				}

			}, rightQueues);
		}

		if(rightQueues.isEmpty()){
			CollectionUtils.select(skillQueues, new Predicate() {
				@Override
				public boolean evaluate(Object object) {

					TenantSkillQueue queue = (TenantSkillQueue) object;

					if(!StringUtils.isEmpty(business) ){
						return !"vip".equals(queue.getRemark()) && StringUtils.trimToEmpty(queue.getAbleBusinessTypes()).contains(business);

					}

					return false;

				}

			}, rightQueues);
		}
		
		List<TenantSkillQueue> tempQueues = new ArrayList<TenantSkillQueue>();
		if(rightQueues.size() > 1){
			for(TenantSkillQueue sq : rightQueues){
				long size = redisDao.setSize(AgentUtils.getAgentSkiilRelationKey(String.valueOf(sq.getId())));
				if(size > 0) tempQueues.add(sq);
			}
		}

		return CollectionUtils.isNotEmpty(tempQueues) ? tempQueues : rightQueues;
	}

}
