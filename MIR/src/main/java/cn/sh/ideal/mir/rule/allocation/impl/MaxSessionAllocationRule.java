package cn.sh.ideal.mir.rule.allocation.impl;

import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.SysRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 最大会话数分配规则
 * 
 * <AUTHOR>
 * @date 2020-06-18
 */
@Component("maxSessionAllocationRule")
public class MaxSessionAllocationRule extends AbstractAllocationRule implements AllocationRule{
	/**
	 * Logger for this class
	 */
	private static final Logger logger = LoggerFactory.getLogger(MaxSessionAllocationRule.class);
	
	@Override
	public List<Map<Object,Object>> excute(final String tenantCode,String queueId,SysRule rule){
		List<Map<Object,Object>> freeUsers = null;
		try {
			freeUsers = getFreeUserList(tenantCode, queueId);
			if (!freeUsers.isEmpty()) {
				Collections.sort(freeUsers, new Comparator<Map<Object,Object>>() {
					@Override
					public int compare(Map<Object,Object> map1, Map<Object,Object> map2) {
						Integer currSessionCount1 = (Integer)map1.get(Constant.PARAM_CURRSESSION_COUNT);
						Integer currSessionCount2 = (Integer)map2.get(Constant.PARAM_CURRSESSION_COUNT);
						return currSessionCount2 - currSessionCount1;
					}
				});
			} else {
				logger.info("技能组:{}上没有空闲的坐席!", queueId);
			}
		} catch (Exception e) {
			logger.error("执行技能组分配规则异常!", e);
		}
		
		return freeUsers;
	}
}
