//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.sh.ideal.mir.service.dubbo;

import cn.sh.ideal.as.req.AgentSingleRequest;
import cn.sh.ideal.as.resp.AgentSingleResponse;
import cn.sh.ideal.as.service.AgentService;
import cn.sh.ideal.mgw.model.response.BaseResponseDTO;
import cn.sh.ideal.mgw.service.MessageService;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.executor.QueueupCalculateExecutor;
import cn.sh.ideal.mir.model.AgentIM;
import cn.sh.ideal.mir.model.AgentMutual;
import cn.sh.ideal.mir.model.CmsPost;
import cn.sh.ideal.mir.req.*;
import cn.sh.ideal.mir.resp.AddRemoveWorkNoResponse;
import cn.sh.ideal.mir.resp.GetQueueCountResponse;
import cn.sh.ideal.mir.resp.GetQueueSkillDetailResponse;
import cn.sh.ideal.mir.resp.GetQueueSkillInfoResponse;
import cn.sh.ideal.mir.resp.GetSessionQueuePositionResponse;
import cn.sh.ideal.mir.resp.MultiConferenceTipResponse;
import cn.sh.ideal.mir.resp.NoticePushResponse;
import cn.sh.ideal.mir.resp.PushTipResponse;
import cn.sh.ideal.mir.resp.QueryPrivateMsgResponse;
import cn.sh.ideal.mir.resp.QueueSessionResponse;
import cn.sh.ideal.mir.resp.RemoveQueueSessionResponse;
import cn.sh.ideal.mir.resp.SeatMessageInfoResponse;
import cn.sh.ideal.mir.resp.SeatPrivateResponse;
import cn.sh.ideal.mir.resp.TakeOverTaskResponse;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.resp.entity.PrivateMsgModel;
import cn.sh.ideal.mir.resp.entity.QueueupCalculator;
import cn.sh.ideal.mir.resp.entity.SessionQueue;
import cn.sh.ideal.mir.service.AllocateService;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.util.MessageUtil;
import cn.sh.ideal.mir.util.MessageUtil.TipType;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionType;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.si.req.StipRequest;
import cn.sh.ideal.util.Constants;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("allocateServerImpl")
public class AllocateServiceImpl implements AllocateService {
	private static final Logger log = LoggerFactory.getLogger(AllocateServiceImpl.class);
	@Autowired
	private SessionService sessionService;
	@Autowired
	private AllocationService allocationService;
	@Autowired
	private SkillQueueService queueService;
	@Autowired
	private AgentService agentService;
	@Autowired
	private MessageService mgwMessageService;
	@Autowired
	private QueueupCalculateExecutor qce;
	@Autowired
	private SessionDao sessionDao;
	@Value("#{config['message.tip.addworkno']}")
	private String tipMsgAddwn;
	@Value("#{config['message.tip.removeworkno']}")
	private String tipMsgRmwn;
	@Value("#{config['message.tip.meetinvite']}")
	private String tipMsgInvite;

	public AllocateServiceImpl() {
	}

	public SeatMessageInfoResponse mediaSend(MessageInfo request) {
		SeatMessageInfoResponse response = new SeatMessageInfoResponse();

		try {
			log.info("Request AllocateController.mediaSend[" + JSONObject.toJSONString(request) + "]");
			SessionInfo session = this.sessionService.get(request.getSessionId(), request.getTenantCode());
			if (session.getWorkNos().contains(request.getWorkNo())) {
				MessageUtil.assembleMessage(session, request);
				log.info("mgw nsend request:{}", JSONObject.toJSONString(request));
				BaseResponseDTO responseDTO = this.mgwMessageService.newSend(request);
				log.info("mgw nsend response:{}", JSONObject.toJSONString(responseDTO));
				if (!Objects.equal(responseDTO.getResultCode(), RespCode.SUCCESS.getResultCode())) {
					response.setResultCode(RespCode.CALL_MGW_ERROR.getResultCode());
					response.setResultMsg(RespCode.CALL_MGW_ERROR.getResultMsg());
					return response;
				} else {
					if (!"email".equals(request.getMsgType()) && session.getWorkNos().contains(",")) {
						String fromWorkno = request.getWorkNo();
						String workNos = "";
						String[] var10;
						int var9 = (var10 = session.getWorkNos().split(",")).length;

						for(int var8 = 0; var8 < var9; ++var8) {
							String wn = var10[var8];
							if (!fromWorkno.equals(wn)) {
								workNos = workNos + wn + ",";
							}
						}

						workNos = !StringUtils.isEmpty(workNos) ? workNos.substring(0, workNos.lastIndexOf(",")) : workNos;
						request.setAcceptedAccount(session.getAcceptedAccount());
						request.setSendAccount(session.getSendAccount());
						request.setWorkNo(workNos);
						request.setSource("3");
						request.setSessionType(SessionType.MULTI.toString());
						this.allocationService.pushMeeting(request, fromWorkno, workNos.split(","));
					}

					response.setResultCode(RespCode.SUCCESS.getResultCode());
					response.setResultMsg(RespCode.SUCCESS.getResultMsg());
					return response;
				}
			} else {
				response.setResultCode(RespCode.WORKNO_NOT_EXITS_SESSION.getResultCode());
				response.setResultMsg(RespCode.WORKNO_NOT_EXITS_SESSION.getResultMsg());
				return response;
			}
		} catch (Exception var11) {
			log.error("坐席消息发送异常:{}", var11.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var11.getMessage());
			return response;
		}
	}

	public AddRemoveWorkNoResponse mediaWorkNo(AddRemoveWorkNoRequest request) {
		AddRemoveWorkNoResponse response = new AddRemoveWorkNoResponse();

		try {
			request.check();
			SessionInfo session = this.sessionService.get(request.getSessionId(), request.getTenantCode());
			this.sessionService.updateWorkNo(request.getSessionId(), request.getTenantCode(), request.getSkillQueue(), session.getSkillType(), request.getWorkNo(), request.getType());
			String tip = "add".equals(request.getType()) ? this.tipMsgAddwn : this.tipMsgRmwn;
			this.allocationService.pushTip(session.getWorkNos().split(","), request.getTenantCode(), request.getSessionId(), TipType.SESSION_TIP.toString(), String.format(tip, request.getWorkNo()), request.getExtData());
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var5) {
			log.error("添加(移除)工号异常:{}", var5.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var5.getMessage());
			return response;
		}
	}

	public MultiConferenceTipResponse inviteMeet(MultiConferenceTipRequest request) {
		MultiConferenceTipResponse response = new MultiConferenceTipResponse();

		try {
			request.check();
			List<String> wns = new ArrayList();
			String[] var7;
			int var6 = (var7 = request.getTarget().split(",")).length;

			for(int var5 = 0; var5 < var6; ++var5) {
				String wn = var7[var5];

				try {
					AgentSingleRequest agentSingleRequest = new AgentSingleRequest();
					agentSingleRequest.setTenantCode(request.getTenantCode());
					agentSingleRequest.setSkillQueue("*");
					agentSingleRequest.setWorkNo(request.getWorkNo());
					AgentSingleResponse info = this.agentService.agentInfo(agentSingleRequest);
					if (!Objects.equal(info.getResultCode(), RespCode.SUCCESS.getResultCode()) || !Objects.equal(AgentStatus.FREE.getCode(), info.getData().getStatus())) {
						throw new Exception(info.getResultMsg());
					}

					wns.add(wn);
				} catch (Exception var10) {
					log.error(var10.getMessage());
				}
			}

			this.allocationService.pushTip((String[])wns.toArray(new String[0]), request.getTenantCode(), request.getSessionId(), TipType.IM_MEETING.toString(), String.format(this.tipMsgInvite, request.getWorkNo()), request.getExtData());
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var11) {
			log.error("发送会议邀请异常:", var11.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var11.getMessage());
			return response;
		}
	}

	public SeatPrivateResponse sendPrivate(SeatPrivateRequest request) {
		SeatPrivateResponse response = new SeatPrivateResponse();

		try {
			request.check();
			AgentIM agentIm = new AgentIM();
			BeanUtils.copyProperties(request, agentIm);
			this.allocationService.pushPrivateMsg(agentIm);
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var4) {
			log.error("发送私聊信息异常:", var4.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public GetQueueSkillDetailResponse getQueueInfo(GetQueueSkillDetailRequest request) {
		GetQueueSkillDetailResponse response = new GetQueueSkillDetailResponse();

		try {
			request.check();
			List<SkillQueue> skillQueue = new ArrayList();
			if (StringUtils.isEmpty(request.getQueueId())) {
				skillQueue = this.queueService.getSkillQueues(request.getTenantId());
			} else {
				((List)skillQueue).add(this.queueService.getSkillQueue(request.getTenantId(), request.getQueueId()));
			}

			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			response.setData((List)skillQueue);
			return response;
		} catch (Exception var4) {
			log.error("获取技能组信息异常:", var4.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public GetQueueSkillInfoResponse getSkillQueue(GetQueueSkillInfoRequest request) {
		GetQueueSkillInfoResponse response = new GetQueueSkillInfoResponse();

		try {
			request.check();
			List<TenantSkillQueue> tenantSkillQueue = new ArrayList();
			if (StringUtils.isNotEmpty(request.getQueueIds())) {
				String[] var7;
				int var6 = (var7 = request.getQueueIds().split(",")).length;

				for(int var5 = 0; var5 < var6; ++var5) {
					String queueId = var7[var5];
					SkillQueue sq = this.queueService.getSkillInfo(request.getTenantCode(), queueId);
					if (sq != null) {
						tenantSkillQueue.add(sq.getTenantSkillQueue());
					}
				}
			} else {
				Iterator var11 = this.queueService.getSkillInfos(request.getTenantCode()).iterator();

				while(var11.hasNext()) {
					SkillQueue tsq = (SkillQueue)var11.next();
					tenantSkillQueue.add(tsq.getTenantSkillQueue());
				}
			}

			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			response.setData(tenantSkillQueue);
			return response;
		} catch (Exception var9) {
			log.error("获取技能组基本信息异常:", var9);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var9.getMessage());
			return response;
		}
	}

	public TakeOverTaskResponse takeOverTask(TakeOverTaskRequest request) {
		TakeOverTaskResponse response = new TakeOverTaskResponse();

		try {
			request.check();
			SessionInfo session = this.sessionService.get(request.getSessionId(), request.getTenantCode());
			if (!ArrayUtils.contains(Constants.artificialStatus, session.getStatus())) {
				response.setResultCode(RespCode.SESSION_STATUS_ERROR.getResultCode());
				response.setResultMsg(RespCode.SESSION_STATUS_ERROR.getResultMsg());
				return response;
			} else {
				this.allocationService.takeOverTask(request.getTenantCode(), request.getSkillQueue(), request.getTargetWn(), request.getSessionId());
				response.setResultCode(RespCode.SUCCESS.getResultCode());
				response.setResultMsg(RespCode.SUCCESS.getResultMsg());
				return response;
			}
		} catch (Exception var4) {
			log.error("任务接管异常:", var4);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public NoticePushResponse pushPost(NoticePushRequest request) {
		NoticePushResponse response = new NoticePushResponse();

		try {
			log.info("Request AllocateController.pushPost[" + request.toString() + "]");
			request.check();
			CmsPost post = new CmsPost();
			BeanUtils.copyProperties(request, post);
			this.allocationService.pushPost(post);
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var4) {
			log.error("公告推送异常异常:", var4);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public GetSessionQueuePositionResponse rank(GetSessionQueuePositionRequest request) {
		GetSessionQueuePositionResponse response = new GetSessionQueuePositionResponse();

		try {
			if(StringUtils.isEmpty(request.getQueueId()) && StringUtils.isNotEmpty(request.getSessionId())){
				SessionInfo session = sessionService.get(request.getSessionId(),request.getTenantCode());
				request.setQueueId(session.getSkillQueue());
			}
			this.queueService.getQueueLocation(request, response);
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var4) {
			log.error("获取会话排队位置异常:", var4);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public PushTipResponse pushTip(PushTipRequest request) {
		PushTipResponse response = new PushTipResponse();

		try {
			request.check();
			StipRequest tip = new StipRequest();
			tip.setContent(request.getContent());
			tip.setExtData(request.getExtData());
			tip.setSession(request.getSession());
			tip.setSessionId(request.getSessionId());
			tip.setTenantCode(request.getTenantCode());
			tip.setType(request.getType());
			tip.setWorkNo(request.getWorkNo());
			tip.setChannelCode(request.getChannelCode());
			this.allocationService.pushTip(tip, tip.getWorkNo().split(","));
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var4) {
			log.error("向坐席推送提示异常:", var4);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public QueryPrivateMsgResponse queryPriMsgs(QueryPrivateMsgRequest request) {
		QueryPrivateMsgResponse response = new QueryPrivateMsgResponse();

		try {
			request.check();
			AgentMutual agentMutual = new AgentMutual();
			agentMutual.setLastId(request.getLastId());
			agentMutual.setTenantCode(request.getTenantCode());
			agentMutual.setUuid(request.getUuid());
			agentMutual.setWorkNo(request.getWorkNo());
			agentMutual.setCount(request.getCount());
			List<AgentIM> agentIMList = this.allocationService.queryPrivateMsgs(agentMutual);
			if (agentIMList != null && !agentIMList.isEmpty()) {
				List<PrivateMsgModel> privateMsgModels = new ArrayList();
				Iterator var7 = agentIMList.iterator();

				while(var7.hasNext()) {
					AgentIM agentIM = (AgentIM)var7.next();
					PrivateMsgModel privateMsgModel = new PrivateMsgModel();
					BeanUtils.copyProperties(agentIM, privateMsgModel);
					privateMsgModels.add(privateMsgModel);
				}

				response.setData(privateMsgModels);
			}

			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var9) {
			log.error("私聊信息查询异常:", var9);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var9.getMessage());
			return response;
		}
	}

	public RemoveQueueSessionResponse removeSession(RemoveQueueSessionRequest request) {
		RemoveQueueSessionResponse response = new RemoveQueueSessionResponse();

		try {
			request.check();
			this.queueService.poll(request.getTenantCode(), request.getQueueId(), request.getSessionId());
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var4) {
			log.error("移除队列中的会话异常:", var4);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public GetQueueCountResponse getQueueCount(GetQueueCountRequest request) {
		GetQueueCountResponse response = new GetQueueCountResponse();

		try {
			log.debug("Request GetQueueCountRequest [" + request.toString() + "]");
			Set tempSet = null;
			if (request.getSkillQueue() != null) {
				tempSet = new HashSet(Arrays.asList(request.getSkillQueue().split(",")));
			}

			this.queueService.getQueueCount(request.getTenantCode(), tempSet, response);
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} catch (Exception var4) {
			log.error("获取队列排队数异常:", var4);
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(var4.getMessage());
			return response;
		}
	}

	public QueueSessionResponse getQueueSessions(QueueSessionRequest request) {
		try {
			request.check();
			String[] queueIds = StringUtils.isEmpty(request.getQueueId()) ? null : request.getQueueId().split(",");
			List<QueueupCalculator> queueupList = new ArrayList();
			String[] var7 = queueIds;
			int var6 = queueIds.length;

			for(int var5 = 0; var5 < var6; ++var5) {
				String queueId = var7[var5];
				QueueupCalculator qc = new QueueupCalculator();
				SkillQueue queueInfo = this.queueService.getSkillInfo((String)null, queueId);
				qc.setQueueId(queueId);
				qc.setQueueName(queueInfo.getTenantSkillQueue().getQueueName());
				Map<String, Object> params = new HashMap();
				params.put("skillQueue", queueId);
				List<SessionQueue> sessionQueues = this.sessionDao.getQueueSession(params);
				qc.setSessions(sessionQueues);
				queueupList.add(qc);
			}

			return new QueueSessionResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), queueupList);
		} catch (Exception var12) {
			log.error("获取队列会话列表异常", var12);
			return new QueueSessionResponse(RespCode.FAIL.getResultCode(), var12.getMessage(), (List)null);
		}
	}

	public BaseResponse pullSession(PullSessionRequest request) {
		try {
			request.check();
			this.qce.pullSession(request.getTenantCode(), request.getQueueId(), request.getWorkNo(), request.getSessionId(), request.getForward());
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		} catch (Exception var3) {
			log.error("拉取会话异常", var3);
			return new BaseResponse(RespCode.FAIL.getResultCode(), var3.getMessage());
		}
	}

	@Override
	public BaseResponse preQueueOpt(PreQueueOptRequest request) {
		try{
			request.check();
			SessionInfo session = sessionService.get(request.getSessionId(),request.getTenantCode());
			if (ArrayUtils.contains(Constants.sortingStatus, session.getStatus())) {
				queueService.preQueueOpt(session,request);
				return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
			}else{
				throw new IllegalArgumentException("会话已关闭或已分配到人工");
			}
		}catch(Exception e){
			log.error("prequeue异常",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}
}
