package cn.sh.ideal.mir.service.local.impl;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.service.MessageService;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.dao.SysRuleDao;
import cn.sh.ideal.mir.dao.TenantSkillQueueDao;
import cn.sh.ideal.mir.executor.QueueupCalculateExecutor;
import cn.sh.ideal.mir.req.GetSessionQueuePositionRequest;
import cn.sh.ideal.mir.req.PreQueueOptRequest;
import cn.sh.ideal.mir.resp.GetQueueCountResponse;
import cn.sh.ideal.mir.resp.GetSessionQueuePositionResponse;
import cn.sh.ideal.mir.rule.allocation.impl.AbstractAllocationRule;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.service.local.SortService;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.*;
import cn.sh.ideal.util.RedisLock;
import cn.sh.ideal.util.ReferenceConfigUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("queueService")
public class SkillQueueServiceImpl extends AbstractAllocationRule
		implements SkillQueueService
{
	protected static final Logger logger = LoggerFactory.getLogger(SkillQueueServiceImpl.class);

	@Autowired
	protected AllocationService allocationService;

	@Autowired
	protected SortService sortService;

	@Autowired
	private QueueupCalculateExecutor qce;

	@Autowired
	protected RedisDao<String, Serializable> redisDao;

	@Autowired
	protected RedisDao<String, String> redisSession;

	@Autowired
	protected RedisDao<String, SkillQueue> redisSq;

	@Autowired
	private MessageService mgwMessageService;
	@Autowired
	private SessionService sessionService;

	@Autowired
	private SysRuleDao ruleDao;
	@Autowired
	private SessionDao sessionDao;

	@Autowired
	private TenantSkillQueueDao tsq;
	@Value("#{config['pushMGWurl']}")
	private String sendMessageUrl = "";

	@Value("#{config['message.sortmessage']}")
	private String sortMsg = "";

	@Value("#{config['mesaage.overqueue']}")
	private String overqueue = "";

	@Value("#{config['sortinf.push']}")
	private String sortinfpPush = "true";

	@Value("#{config['sortinf.push.url']}")
	private String sortinfPushUrl = "";
	@Value("#{config['prequeue.timeout']}")
	private Integer preTimeout;
	@Value("#{config['prequeue.opttimeout']}")
	private Integer optTimeout;
	@Value("#{config['prequeue.noovermsg']}")
	private String noovermsg = "";
	@Value("#{config['prequeue.overmsg']}")
	private String overmsg = "";
	@Value("#{config['prequeue.joinmsg']}")
	private String joinmsg = "";
	@Value("#{config['prequeue.continemsg']}")
	private String continemsg = "";
	@Value("#{config['prequeue.cancelmsg']}")
	private String cancelmsg = "";
	@Value("#{config['prequeue.nummsg']}")
	private String nummsg = "";
	@Value("#{config['prequeue.busymsg']}")
	private String busymsg = "";

	@Autowired
	SysInitService sysInitService;
	private Set<String> tenantCodes = new HashSet();
	private static final String QUEUE_IDS = "QUEUEIDS:";

	public void init()
	{
		this.initService.init(true, true, false, false, true);
		initSysRule();
		pipelineQueue();
		loadDubboProperties();
	}

	public void deleteSkillQueue(String tenantId, String queueId) {
		delQueueId(tenantId, queueId);
		deleteSkillInfo(tenantId, queueId);
		deleteQueue(queueId);
		this.qce.delete(tenantId, queueId);
	}

	public void editSkillInfo(SkillQueue queue)
	{
		String key = getBaseQueueKey(queue.getTenantCode(), queue.getId());
		RedisLock lock = RedisLock.getRedisLock(key);
		try {
			while (!lock.lock());
			this.redisDao.saveValue(key, queue);
			this.redisSession.setAdd("QUEUEIDS:".concat(queue.getTenantCode()), new String[] { queue.getId() });
		} catch (Exception e) {
			logger.error("存储队列基本信息异常:{}", e.getMessage());
		} finally {
			lock.unlock();
		}
	}

	public void deleteSkillInfo(String tenantId, String queueId)
	{
		try {
			this.redisDao.deleteValue(getBaseQueueKey(tenantId, queueId));
		} catch (Exception e) {
			logger.error("删除技能组异常:{}", e.getMessage());
		}
	}

	public void deleteQueue(String queueId)
	{
		try {
			this.redisDao.deleteValue(getAlloQueueKey(queueId));
		} catch (Exception e) {
			logger.error("删除技能组队列异常:{}", e.getMessage());
		}
	}

	public SkillQueue getSkillQueue(String tenantId, String queueId)
	{
		try {
			SkillQueue skillQueue = getSkillInfo(tenantId, queueId);
			skillQueue.setAllocateQueue(getAllQueue(queueId));
			return skillQueue;
		} catch (Exception e) {
			logger.error("获取队列" + queueId + "异常:{}", e.getMessage());
		}
		return null;
	}

	public SkillQueue getSkillInfo(String tenantId, String queueId)
	{
		String skey = getBaseQueueKey(tenantId, queueId);

		return (SkillQueue)this.redisDao.readValue(skey);
	}

	public List<String> getAllQueue(String queueId)
	{
		return new ArrayList(this.redisSession.zsetRangeAll(getAlloQueueKey(queueId)));
	}

	public Set<String> getQueueIds(String tenantId)
	{
		Set queueIds = new HashSet();
		if (StringUtils.isEmpty(tenantId)) {
			for (String key : this.tenantCodes)
				queueIds.addAll(this.redisSession.setMembers("QUEUEIDS:".concat(key)));
		}
		else {
			queueIds = this.redisSession.setMembers("QUEUEIDS:".concat(tenantId));
		}

		return queueIds;
	}

	public void push(String tenantId, String queueId, SessionInfo session, boolean isFirst)
	{
		String allocateKey = getAlloQueueKey(queueId);
		String value = session.getTenantCode().concat("_").concat(session.getSessionId());
		SkillQueue skillInfo = getSkillInfo(session.getTenantCode(), queueId);

		if (skillInfo == null) {
			throw new NullPointerException("不存在技能组:" + queueId);
		}

		// 获取预排队数量限制
		SysParam sysParam = sysInitService.getSysParam("MIR_DEFAULT_QUEUE_SIZE");
		int preQueueSize = Integer.parseInt(sysParam.getParamValue());

		double priority = this.sortService.getPriority(session, skillInfo.getSortRule());

		if(isFirst){
			long rank = queueSize(queueId);

			// 判断当前排队人数是否超过预排队数量限制
			if (rank > preQueueSize) {
				// 超过预排队数量限制，关闭会话并提示
				pushTip2Customer(session, busymsg);

				// 在Redis中标记，便于PreQueueHandler处理
				redisDao.saveValue(PreQueueHandler.PREQUEUE + value + "_over_limit", System.currentTimeMillis());

				// 关闭当前会话
				sessionService.update(session.getSessionId(), session.getTenantCode(), SessionStatus.USER_CLOSE, null);
				return; // 直接返回，不再执行后续排队逻辑
			}

			String msg = nummsg  + "#queue#" + rank + "#size#" + skillInfo.getQueueSize();
			pushTip2Customer(session,msg);
			if (skillInfo.getQueueSize() <= queueSize(queueId)) {
				pushTip2Customer(session,overmsg);
				redisDao.saveValue(PreQueueHandler.PREQUEUE + value + "_noanswer_over",System.currentTimeMillis() + (optTimeout * 1000));
			}else{
				if(getFreeUserList(session.getTenantCode(), session.getSkillQueue()).isEmpty() || rank > 0){
					pushTip2Customer(session,noovermsg);
				}
				redisDao.saveValue(PreQueueHandler.PREQUEUE + value + "_common",System.currentTimeMillis() + (preTimeout * 1000));
			}

		}
		this.redisSession.zsetAdd(allocateKey, value, priority);

		//pushSortMessage(session, isFirst, false);
	}

	public void pushSortMessage(SessionInfo session, boolean isFirst, boolean isSort) {
		/*if (("true".equals(this.sortinfpPush)) && (isFirst)) {
			long location = getRank(session.getTenantCode(), session.getSkillQueue(), session.getSessionId()) + 1L;
			if ((location > 1L) || ((location == 1L) && (
					(isSort) || (getFreeUserList(session.getTenantCode(), session.getSkillQueue()).isEmpty()))))
			pushTip2Customer(session, String.format(this.sortMsg, location),null);
		}*/
	}

	public void pushTip2Customer(SessionInfo session, String message) {
		try {
			String isReply = "Y";
			if (!StringUtils.isEmpty(session.getSkillType())) {
				SkillType skillType = this.initService.getSkillType(session.getTenantCode(), session.getSkillType());
				isReply = skillType.getIsReplay();
			}
			if ("Y".equals(isReply)){
				MessageInfo info = new MessageInfo();
				info.setAcceptedAccount(session.getSendAccount());
				info.setSendAccount(session.getAcceptedAccount());
				info.setMsgType("text");
				info.setContent(message);
				info.setSessionId(session.getSessionId());
				info.setTenantCode(session.getTenantCode());
				info.setChannelCode(session.getChannelCode());
				info.setMessageSource("4");
				logger.info("push tip to customer:{}", JSONObject.toJSONString(info));
				this.mgwMessageService.newSend(info);
			}
		}
		catch (Exception e) {
			logger.error("给客户推送提示信息异常", e);
		}
	}

	@Override
	public void preQueueOpt(SessionInfo sessionInfo,PreQueueOptRequest request) {
		String value = request.getTenantCode().concat("_").concat(request.getSessionId());
		for(String key: redisDao.getKeysByPattern(PreQueueHandler.PREQUEUE + value + "*")){
			redisDao.deleteValue(key);
		}
		if("1".equals(request.getOptType())){
			if("20".equals(request.getSendType())){
				pushTip2Customer(sessionInfo,joinmsg);
				redisDao.saveValue(PreQueueHandler.PREQUEUE + value + "_common",System.currentTimeMillis() + (preTimeout * 1000));
			}else{
				pushTip2Customer(sessionInfo,continemsg);
				// 添加3分钟监控，如果3分钟内没有进入队列则推送自助反馈提示
				redisDao.saveValue(PreQueueHandler.PREQUEUE + value + "_continue_wait", System.currentTimeMillis() + (3 * 60 * 1000));
			}
		}else{
			SkillQueue skillInfo = getSkillInfo(sessionInfo.getTenantCode(), sessionInfo.getSkillQueue());
			long rank = getRank(sessionInfo.getTenantCode(),sessionInfo.getSkillQueue(),request.getSessionId());
			if(rank >= skillInfo.getQueueSize()){
				sessionDao.delSessionStatusLog(request.getSessionId());
			}

			pushTip2Customer(sessionInfo,cancelmsg);
			poll(request.getTenantCode(),sessionInfo.getSkillQueue(),request.getSessionId());
			sessionService.update(request.getSessionId(), request.getTenantCode(), SessionStatus.USER_CLOSE, null);
		}

	}

	public String poll(String tenantId, String queueId)
	{
		Set<String> queue = null;

		String allocateKey = getAlloQueueKey(queueId);

		RedisLock lock = RedisLock.getRedisLock(allocateKey);
		try {
			while (!lock.lock());
			queue = this.redisSession.zsetRange(allocateKey, 1L);

			if (CollectionUtils.isNotEmpty(queue)) {
				for (String sessionId : queue) {
					redisSession.zsetRemove(allocateKey, sessionId);
					return sessionId;
				}
			}
		}
		catch (Exception e) {
			logger.error("poll session 异常:{}", e.getMessage());
		} finally {
			lock.unlock(); } lock.unlock();

		return null;
	}

	public void poll(String tenantCode, String queueId, String sessionId)
	{
		try {
			this.redisSession.zsetRemove(getAlloQueueKey(queueId),  tenantCode.concat("_").concat(sessionId) );
		} catch (Exception e) {
			logger.error("poll session 异常:{}", e.getMessage());
		}
	}

	public List<SkillQueue> getSkillQueues(String tenantId)
	{
		return getSkillInfos(tenantId);
	}

	public void pipelineQueue() {
		logger.info("初始化技能组信息……");
		Map<String, Set<String>> queueIdsMap = new HashMap();
		Set<String> keys = this.redisDao.getKeysByPattern(getBaseQueueKey("*", "*"));
		TenantSkillQueue model = new TenantSkillQueue();
		model.setStatus("1");
		List<TenantSkillQueue> tsqs = this.tsq.query(model);

		Map<String, SkillQueue> queues = new HashMap();
		Map<Object, Object> rules = this.redisDao.mapGet("SYS_RULE");

		int defaultQueueSize =
				Integer.parseInt(this.initService.getSysParam("MIR_DEFAULT_QUEUE_SIZE").getParamValue());
		for (TenantSkillQueue tsq : tsqs)
		{
			String queueId = String.valueOf(tsq.getId());
			putQueueId(queueIdsMap, tsq.getTenantId(), queueId);
			SysRule alloateRule = StringUtils.isEmpty(tsq.getAllocationRule()) ? Constant.defaultAllocationRule :
					(SysRule)rules.get(tsq.getAllocationRule());
			SysRule sortRule = StringUtils.isEmpty(tsq.getSortRule()) ? Constant.defaultSortRule :
					(SysRule)rules.get(tsq.getSortRule());
			int queueSize = tsq.getQueueSize() != null ? tsq.getQueueSize().intValue() : defaultQueueSize;
			int concurrentCount = tsq.getConcurrentCount() == null ? 1 : tsq.getConcurrentCount().intValue();
			String baseKey = getBaseQueueKey(tsq.getTenantId(), queueId);

			queues.put(baseKey,
					new SkillQueue(queueId, tsq.getTenantId(), tsq, alloateRule, sortRule, queueSize, concurrentCount));

			keys.remove(baseKey);
		}
		this.redisSq.pipelineSet(queues);

		this.redisDao.pipelineDel(keys);
		for (String tenantCode : queueIdsMap.keySet())
		{
			this.tenantCodes.add(tenantCode);
			String queueIdsKey = "QUEUEIDS:".concat(tenantCode);

			setAndClearForSet(queueIdsKey, (Collection)queueIdsMap.get(tenantCode));
		}
	}

	private void putQueueId(Map<String, Set<String>> queueIdsMap, String tenantCode, String queueId) {
		Set queueIds = (Set)queueIdsMap.get(tenantCode);
		if (queueIds == null) {
			queueIds = new HashSet();
			queueIds.add(queueId);
			queueIdsMap.put(tenantCode, queueIds);
		} else {
			queueIds.add(queueId);
		}
	}

	public boolean isEmpty(String queueId)
	{
		long size = this.redisSession.zsetSize(getAlloQueueKey(queueId));
		return size == 0L;
	}

	public long queueSize(String queueId)
	{
		return this.redisSession.zsetSize(getAlloQueueKey(queueId));
	}

	public List<SkillQueue> getSkillInfos(String tenantId)
	{
		Set keys = new HashSet();
		for (String queueId : getQueueIds(tenantId)) {
			keys.add(getBaseQueueKey(tenantId, queueId));
		}

		return this.redisSq.pipelineGet(keys);
	}

	protected String getBaseQueueKey(String tenantCode, String queueId) {
		return "QUEUE_" + queueId + ":BASE";
	}

	protected String getAlloQueueKey(String queueId) {
		return "QUEUE_" + queueId + ":ALLOCATE";
	}

	public void getQueueLocation(GetSessionQueuePositionRequest request, GetSessionQueuePositionResponse response)
	{
		Map data = new HashMap();
		data.put("total", Long.valueOf(queueSize(request.getQueueId())));
		if (StringUtils.isEmpty(request.getSessionId())) {
			return;
		}

		SkillQueue skillInfo = getSkillInfo(request.getTenantCode(), request.getQueueId());
		data.put("size",skillInfo.getQueueSize());
		data.put("rank", Long.valueOf(getRank(request.getTenantCode(), request.getQueueId(), request.getSessionId())));

		response.setData(data);
	}

	public void getQueueCount(String tenantCode, Set<String> queueIds, GetQueueCountResponse response)
	{
		Map counter = new HashMap();
		if ((StringUtils.isNotEmpty(tenantCode)) && (CollectionUtils.isEmpty(queueIds))) {
			queueIds = getQueueIds(tenantCode);
		}
		int total = 0;
		for (String queueId : queueIds) {
			long count = queueSize(queueId);
			counter.put(queueId, Long.valueOf(count));
			total = (int)(total + count);
		}
		response.setData(counter);
		response.setTotal(total);
	}

	public long getRank(String tenantCode, String queueId, String sessionId) {
		try {
			return this.redisSession.rank(getAlloQueueKey(queueId), tenantCode.concat("_").concat(sessionId));
		} catch (Exception e) {
			logger.warn("队列中不存在会话:{}", sessionId);
		}
		return -1L;
	}

	private void loadDubboProperties()
	{
		try {
			Properties p = new Properties();
			p.load(SkillQueueService.class.getClassLoader().getResourceAsStream("conf/dubbo.properties"));

			ReferenceConfigUtil.setProperties(p);
		} catch (Exception e) {
			logger.error("初始化dubbo配置异常", e);
		}
	}

	public void initSysRule() {
		logger.info("初始化系统规则……");
		try {
			Map ruleMap = new HashMap();
			for (SysRule rule : this.ruleDao.query(new SysRule())) {
				ruleMap.put(rule.getId(), rule);
			}

			this.redisDao.mapPutAll("SYS_RULE", ruleMap);
		} catch (Exception e) {
			logger.error("初始化系统规则异常", e);
		}
	}

	private synchronized void delQueueId(String tenantCode, String queueId) {
		this.redisDao.setRemove("QUEUEIDS:".concat(tenantCode), queueId);
	}

	public Set<String> getTenantCodes()
	{
		return this.tenantCodes;
	}

	public void setAndClearForSet(String key, Collection<String> newSet)
	{
		Set oldSet = this.redisSession.setMembers(key);
		this.redisSession.setAdd(key, (String[])newSet.toArray(new String[0]));
		Set diffSet = new HashSet();

		diffSet.addAll(oldSet);
		diffSet.removeAll(newSet);

		if (diffSet.isEmpty()) return;
		String[] delArr = (String[])diffSet.toArray(new String[0]);

		this.redisSession.setRemove(key, delArr);
	}

	public int getQueueWeight(String skillQueue)
	{
		String weight = this.redisDao.readValue(skillQueue + "queueWeight").toString();
		if (StringUtils.isEmpty(weight))
			return 0;
		return Integer.parseInt(weight);
	}

	public List<TenantSkillQueue> getQueueList(TenantSkillQueue tenantSkillQueue) {
		return this.tsq.querySkillQueue(tenantSkillQueue);
	}
}
