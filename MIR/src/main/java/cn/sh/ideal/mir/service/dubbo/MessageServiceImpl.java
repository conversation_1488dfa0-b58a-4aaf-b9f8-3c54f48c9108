package cn.sh.ideal.mir.service.dubbo;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.req.MessageDirectRequest;
import cn.sh.ideal.mir.req.MessageReceiveRequest;
import cn.sh.ideal.mir.resp.MessageDirectResponse;
import cn.sh.ideal.mir.resp.MessageReceiveResponse;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.service.MessageService;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.util.Constants;

@Service("messageServerImpl")
public class MessageServiceImpl implements MessageService{
	
	private static final Logger log = LoggerFactory.getLogger(MessageServiceImpl.class);
	
	@Autowired
	private SessionService sessionService;

	@Autowired
	private AllocationService allocationService;
	
	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;
	
	@Override
	public MessageReceiveResponse receive(MessageReceiveRequest request) {
		MessageReceiveResponse response=new MessageReceiveResponse();
		try {
			request.check();
			log.info("Request MessageController.receive[" + request.toString() + "]");

			MessageInfo message = request.getMessageInfo();
			redisMessage.listrPush(Constants.WAITING_REQUEST_MESSAGE, message);
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		}
//		catch (RequestException e) {
//			log.error("接收消息参数校验错误：", e.getMessage());
//			response.setResultCode(RespCode.INVALID_ARGUMENTS.getResultCode());
//			response.setResultMsg(e.getMessage());
//			return response;
//		} 
		catch (Exception e) {
			log.error("接受消息异常:{}", e.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(e.getMessage());
			return response;
		}
	}

	@Override
	public MessageDirectResponse direct(MessageDirectRequest request) {
		MessageDirectResponse response =new MessageDirectResponse();
		try {
			request.check();
			log.info("Request MessageController.direct[" + request.toString() + "]");
			SessionInfo session = sessionService.get(request.getSessionId(), request.getTenantCode());
			if(ArrayUtils.contains(Constants.artificialStatus, session.getStatus())){
				response.setResultCode(RespCode.SESSION_STATUS_ERROR.getResultCode());
				response.setResultMsg(RespCode.SESSION_STATUS_ERROR.getResultMsg());
				return response;
			}
			allocationService.directPushMessage(session, request.getWorkNo());
			response.setResultCode(RespCode.SUCCESS.getResultCode());
			response.setResultMsg(RespCode.SUCCESS.getResultMsg());
			return response;
		} 
//		catch (RequestException e) {
//			log.error("直接分配消息参数校验错误：", e.getMessage());
//			response.setResultCode(RespCode.INVALID_ARGUMENTS.getResultCode());
//			response.setResultMsg(e.getMessage());
//			return response;
//		}
		catch (Exception e) {
			log.error("直接分配消息异常:{}", e.getMessage());
			response.setResultCode(RespCode.FAIL.getResultCode());
			response.setResultMsg(e.getMessage());
			return response;
		}
	}

}
