package cn.sh.ideal.mir.executor;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
/**
 * 消息处理线程工厂
 * <AUTHOR>
 *
 */
public class MessageThreadFactory implements ThreadFactory{
	private static AtomicInteger atomicInteger = new AtomicInteger(0);
	
	@Override
	public Thread newThread(Runnable r) {
		return new Thread(r);
	}
	
	public static int incrementAndGet(){
		return atomicInteger.incrementAndGet();  
	}
	
	public static int decrementAndGet(){
		return atomicInteger.decrementAndGet();
	}
	
	public static int getCurrentThreadCount(){
		return atomicInteger.get();
	}
	
}
