package cn.sh.ideal.mir.executor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.handler.MessageHandlerFactory;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.util.Constants;

@Component("messageExecutor")
public class MessageExecutor {
	private static final Logger logger = LoggerFactory.getLogger(MessageExecutor.class);

	@Autowired
	private RedisDao<String, MessageInfo> redisMessage;
	
	@Autowired
	private SysInitService initService;
	
	private final static String THREAD_NAME_MESSAGE = "message";
	private final static String CONCURRENT_COUNT = "CONCURRENT_COUNT";
	
	
	private ExecutorService msgThreadPool;
	
	public static TaskThreadFactory msgThreadFactory;
	
	
	private int poolSize = 100;
	
	@PostConstruct
	public void init(){
		//初始化线程池
		try{
			poolSize = Integer.parseInt(initService.getSysParam(CONCURRENT_COUNT).getParamValue());
		}catch(Exception e){
			poolSize = 100;
		}
		msgThreadFactory = ThreadFactorys.newThreadFactory(THREAD_NAME_MESSAGE);
		msgThreadPool = Executors.newFixedThreadPool(poolSize,msgThreadFactory);
		
		Executors.newScheduledThreadPool(1).scheduleAtFixedRate(new Runnable() {
			@Override
			public void run() {
				message();
			}
		}, 0, 1500, TimeUnit.MILLISECONDS);
	}
	
	public void message(){
		while(true){
			try{
				int currNum = msgThreadFactory.getCurrentThreadCount();
				if(currNum >= poolSize || redisMessage.listSize(Constants.WAITING_REQUEST_MESSAGE) == 0) break;
				
				MessageInfo message = redisMessage.listlPop(Constants.WAITING_REQUEST_MESSAGE);
				if(message == null) break;
				SysRule rule = initService.getTenantInfo(message.getTenantCode()).getRouteImpl();
				String routingRule = null == rule ? Constants.DEFAULT_ROUTING_RULE : rule.getRuleCode();
				msgThreadPool.execute(MessageHandlerFactory.create(message,routingRule));
			}catch(Exception e){
				logger.error("消息执行器异常",e);
			}
		}
	}
}
