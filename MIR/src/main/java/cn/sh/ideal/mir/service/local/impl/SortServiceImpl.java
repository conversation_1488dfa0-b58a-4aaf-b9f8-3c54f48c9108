package cn.sh.ideal.mir.service.local.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.service.local.SortService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.util.NetUtil;


/**
 * 排队Service实现
 * <AUTHOR>
 *
 */
@Service("sortService")
public class SortServiceImpl implements SortService{
	private static final Logger logger = LoggerFactory.getLogger(SortServiceImpl.class);

	@Autowired
	private SessionDao sessionDao;

	@Autowired
	private RedisDao<String, Integer> redisDao;

	private final static String PRIORITY = "priority:";

	public double getPriority(SessionInfo session,SysRule rule){
		int priority = 0;
		/*try{
			if(rule.getRuleMethod().equals("1")){
				priority = this.getPriorityRemote(session, rule);
			}else{
				priority = this.getPriorityLocal(session);
			}
		}catch(Exception e){
			logger.error("查询权值出错!",e);
		}

		String str = "0." + sessionDao.getCurrTimeStamp();

		return -(priority - Double.parseDouble(str));*/
		return priority;
	}

	@Override
	public void clearPrority(String tenantCode) {
		String pattern = PRIORITY + tenantCode;
		if("*".equals(tenantCode)){
			redisDao.pipelineDel(redisDao.getKeysByPattern(pattern));
		}else{
			redisDao.deleteValue(pattern);
		}
	}


	/*
	 * 本地方法计算权值
	 */
	private int getPriorityLocal(SessionInfo session){
		String busiKey = StringUtils.isEmpty(session.getBusinessType()) ? "" : "bi+".concat(session.getBusinessType());
		String key = "ch_".concat(session.getChannelCode()).concat(busiKey);
		Integer priority = (Integer)redisDao.mapGetValue(PRIORITY.concat(session.getTenantCode()),key);

		if(null == priority){
			Map<String, String> map  = new HashMap<String,String>();
			map.put("tenantCode", session.getTenantCode()); // 拿到 所属租户
			map.put("businessType", session.getBusinessType()); //拿到 业务类型
			map.put("channelCode", session.getChannelCode());// 拿到 会话渠道
			map.put("queueId", session.getSkillQueue()); //获取技能组ID

			String priStr = sessionDao.queryPriority(map);

			priority = StringUtils.isEmpty(priStr) ? 0 : Integer.parseInt(priStr);

			redisDao.mapPut(PRIORITY.concat(session.getTenantCode()), key, priority);
		}

		return	priority.intValue();
	}

	/*
	 * 第三方接口计算权值
	 */
	private int getPriorityRemote(SessionInfo session,SysRule rule){
		String priority = null;

		JSONObject param = new JSONObject();
		param.put("sessionId",session.getSessionId());
		param.put("sendAccount",session.getSendAccount());
		param.put("acceptedAccount",session.getAcceptedAccount());
		param.put("channelCode",session.getChannelCode());
		param.put("tenantCode",session.getTenantCode());
		param.put("businessType",session.getBusinessType());
		param.put("skillQueue",session.getSkillQueue());

		String result = NetUtil.send(rule.getInterfaceUrl(), NetUtil.POST, param.toJSONString(), "application/json");
		if(!StringUtils.isEmpty(result)){
			JSONObject object = JSONObject.parseObject(result);
			if("0".equals(object.getString("resultCode"))){
				priority =  object.getJSONObject("data").getString("priority");
			}
		}

		return	StringUtils.isEmpty(priority) ? 0 : Integer.parseInt(priority);
	}
}
