package cn.sh.ideal.mir.executor;

import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.service.local.SortService;
import cn.sh.ideal.mir.service.local.impl.MessageService;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("allocationExecutor")
public class AllocationExecutor
{
	private static final Logger logger = LoggerFactory.getLogger(AllocationExecutor.class);

	@Autowired
	private SessionService sessionService;

	@Autowired
	private AllocationTask allocationTask;

	@Autowired
	private SkillQueueService queueService;

	@Autowired
	private SortService sortService;

	public void checkMissed() { logger.info("start check the missed task……");
		try
		{
			SessionData session = new SessionData();

			session.setTenantCode("0");
			session.setStatus("3");

			List<SessionInfo> sessions = this.sessionService.getSessionList(session);
			if (CollectionUtils.isNotEmpty(sessions)) {
				for (SessionInfo sess : sessions) {
					if (!ArrayUtils.contains(MessageService.voiceChannels, sess.getChannelCode())) {
						this.queueService.push(sess.getTenantCode(), sess.getSkillQueue(), sess, false);
					}
				}
			}
		}
		catch (Exception e)
		{
			logger.error("检查丢失任务异常:{}", e.getMessage());
		} }

	@PostConstruct
	public void allocation() {
		int maxPoolSize = 5;
		for (int i = 1; i <= maxPoolSize; i++) {
			logger.info("start allocate task " + i);
			Executors.newScheduledThreadPool(1).scheduleAtFixedRate(this.allocationTask, 0L, 1L, TimeUnit.SECONDS);
		}

		new Thread(new Runnable()
		{
			public void run()
			{
				AllocationExecutor.this.checkMissed();
			}
		}).start();
		this.sortService.clearPrority("*");
	}
}
