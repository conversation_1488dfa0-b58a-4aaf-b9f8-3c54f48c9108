package cn.sh.ideal.mir.rule.allocation.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.model.SysRule;

/**
 * 系统默认分配规则
 * 
 * <AUTHOR>
 * @date 2014年12月10日
 */
@Component("defaultAllocationRule")
public class DefautAllocationRule extends AbstractAllocationRule implements AllocationRule{
	/**
	 * Logger for this class
	 */
	private static final Logger logger = LoggerFactory.getLogger(DefautAllocationRule.class);

	@Override
	public List<Map<Object,Object>> excute(String tenantCode,String queueId,SysRule rule){
		List<Map<Object,Object>> freeUsers = new ArrayList<>();
		
		List<Map<Object,Object>> result = new ArrayList<>();
		try {
			freeUsers = getFreeUserList(tenantCode, queueId);
			
			int count = freeUsers.size();
			
			
			//取数  
	        Random rand = new Random(System.currentTimeMillis());  
	        for(int i = 0;i<count; i++){  
	            int num = rand.nextInt(count - i);  
	            
	            result.add(freeUsers.get(num)); 
	            
	            freeUsers.remove(num);  
	        }  
			
			if (result.isEmpty())
				logger.info("技能组:{}上没有空闲的坐席!", queueId);
		} catch (Exception e) {
			logger.error("执行技能组分配规则异常!", e);
		}
		
		return result;
	}
}
