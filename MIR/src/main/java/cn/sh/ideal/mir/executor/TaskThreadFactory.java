package cn.sh.ideal.mir.executor;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
/**
 * 任务处理线程工厂
 * <AUTHOR>
 *
 */
public class TaskThreadFactory implements ThreadFactory{
	private String name;
	
	private AtomicInteger atomicInteger = new AtomicInteger(0);
	
	private AtomicBoolean flag = new AtomicBoolean(true);
	
	public TaskThreadFactory(String name){
		this.name = name;
	}
	@Override
	public Thread newThread(Runnable r) {
		return new Thread(r,name);
	}
	
	public int incrementAndGet(){
		return atomicInteger.incrementAndGet();  
	}
	
	public int decrementAndGet(){
		return atomicInteger.decrementAndGet();
	}
	
	public int getCurrentThreadCount(){
		return atomicInteger.get();
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public boolean getFlag() {
		return flag.get();
	}
	public void setFlag(boolean flag) {
		this.flag = new AtomicBoolean(flag);
	}
	
}
