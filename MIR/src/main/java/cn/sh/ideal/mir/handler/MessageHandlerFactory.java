package cn.sh.ideal.mir.handler;

import cn.sh.ideal.mir.executor.MessageExecutor;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.util.SpringContextUtil;

/**
 * 消息处理器
 * <AUTHOR>
 *
 */
public class MessageHandlerFactory{
	public static MessageHandler create(MessageInfo message,String routingRule){
		MessageHandler handler = (MessageHandler)SpringContextUtil.getBean(routingRule);
		handler.setMessage(message);
		MessageExecutor.msgThreadFactory.incrementAndGet();
		return handler;
	}
}
