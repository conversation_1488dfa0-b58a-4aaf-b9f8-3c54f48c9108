package cn.sh.ideal.mir.rule.comparator;

import java.util.Comparator;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.util.DateUtils;

public class MostIdle implements Comparator<Map<Object,Object>>{
	@Override
	public int compare(Map<Object,Object> map1, Map<Object,Object> map2) {
		int result = 0;
		String timeStr1 = (String)map1.get(Constant.PARAM_LAST_SESSIONTIME);
		String timeStr2 = (String)map2.get(Constant.PARAM_LAST_SESSIONTIME);
		
		long time1 = StringUtils.isEmpty(timeStr1) ? 0 : 
				DateUtils.str2Date(timeStr1, "yyyy-MM-dd HH:mm:ss").getTime();
		long time2 = StringUtils.isEmpty(timeStr2) ? 0 : 
			DateUtils.str2Date(timeStr2, "yyyy-MM-dd HH:mm:ss").getTime();
		long cha = time1 - time2;
	
		if(cha > 0) result = 1;
		else if(cha < 0 ) result = -1;
	
		return result;
	}

}