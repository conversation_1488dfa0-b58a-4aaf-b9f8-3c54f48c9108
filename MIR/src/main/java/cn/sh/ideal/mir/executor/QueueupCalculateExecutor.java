package cn.sh.ideal.mir.executor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.resp.entity.QueueupCalculator;
import cn.sh.ideal.mir.resp.entity.SessionQueue;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.service.local.impl.MessageService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.util.Constants;
import cn.sh.ideal.util.RedisLock;
@Component("queueupCalculateExecutor")
public class QueueupCalculateExecutor {
	private static final String QUEUE_CALCULATOR_KEY = "calculator:";
	private static final String QUEUE_CALCULATOR_MAP_KEY = "calculatormap:";
	@Autowired
	private SkillQueueService queueService;
	@Autowired
	private SessionDao sessionDao;
	@Autowired
	private SessionService sessionService;
	@Autowired
	private MessageService messageService;

	@Autowired
	private RedisTemplate<String,Serializable> rt;

	@Value("#{config['calculator.open']}")
	private String isOpen;

	private ExecutorService executor = Executors.newCachedThreadPool();

	private static Map<String,BoundHashOperations<String,String,QueueupCalculator>> bhos= new ConcurrentHashMap<>();

	@PostConstruct
	public void execute(){
		if("true".equals(isOpen)){
			Calculator calculator = new Calculator();
			ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);  
			executor.scheduleAtFixedRate(calculator, 0, 1500, TimeUnit.MILLISECONDS);
		}
	}

	class Calculator implements Runnable{
		@Override
		public void run() {
			for(final String queueId : queueService.getQueueIds(null)){
				RedisLock lock = RedisLock.getRedisLock(QUEUE_CALCULATOR_KEY + queueId);
				try{
					final SkillQueue queueInfo = queueService.getSkillInfo(null,queueId);
					if(null == queueInfo) continue;
					final BoundHashOperations<String, String, QueueupCalculator> ops = getBoundHashOperations(queueInfo.getTenantCode());

					if (null == queueInfo || !lock.lock(1000, 5)){
						continue;
					}

					executor.execute(new Runnable(){
						@Override
						public void run() {
							calculate(queueInfo,ops);
						}

					});
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					lock.unlock();
				}
			}
		}

		private void calculate(SkillQueue queueInfo,BoundHashOperations<String, String, QueueupCalculator> ops){
			try{
				String queueId = queueInfo.getId();

				QueueupCalculator qc = new QueueupCalculator();
				qc.setQueueId(queueId);
				qc.setQueueName(queueInfo.getTenantSkillQueue().getQueueName());

				List<SessionQueue> sortedQueues = new ArrayList<>();
				if(!queueService.isEmpty(queueId)){
					Map<String,Object> params = new HashMap<>();
					params.put(Constants.PARAM_SKILL_QUEUE, queueId);

					List<SessionQueue> sessionQueues = sessionDao.getQueueSession(params);
					Map<String,SessionQueue> sqMaps = new HashMap<>();

					for(SessionQueue sq : sessionQueues){
						sqMaps.put(sq.getSessionId(), sq);
					}


					List<String> tasks = queueService.getAllQueue(queueId);

					for(String task : tasks){
						String sessionId = task.split("_")[1];
						SessionQueue session = sqMaps.get(sessionId);
						if(null == session) continue;

						sortedQueues.add(session);
					}
				}

				qc.setSessions(sortedQueues);

				ops.put(queueId,qc);
			}catch(Exception e){
				e.printStackTrace();
			}
		}
	}

	public List<QueueupCalculator> getQueueupCalculator(String tenantCode,String[] queueIds){
		BoundHashOperations<String, String, QueueupCalculator> ops = getBoundHashOperations(tenantCode);

		List<QueueupCalculator> qcs = new ArrayList<>();
		if(null != queueIds){
			for(String queueId : queueIds){
				QueueupCalculator qc = ops.get(queueId);
				if(null != qc){
					qcs.add(qc);
				}
			}
		}else{
			Map<String,QueueupCalculator> map = ops.entries();
			for(String key : map.keySet()){
				qcs.add(map.get(key));
			}
		}

		return qcs;
	}

	public void pullSession(String tenantCode,String queueId,String workNo,String sessionId,String forward){
		SessionInfo session = null;
		try{
			session = sessionService.get(sessionId, tenantCode);
		}catch(Exception e){
			throw new RuntimeException("未找到该会话或者会话已经关闭");
		}
		if(SessionStatus.SORT.getCode().equals(session.getStatus())){
			String setKey = Constants.SKILLQUEUE + queueId + Constants.ALLOCATE_QUEUE;
			if(0 == rt.opsForZSet().remove(setKey, tenantCode.concat("_").concat(sessionId))){
				throw new RuntimeException("该会话正在分配中");
			}else{
				if(0 != messageService.allocateMessage(session, tenantCode, StringUtils.defaultIfEmpty(forward, queueId), workNo,true,true)){
					queueService.push(tenantCode,queueId,session,false);
					throw new RuntimeException("会话推送失败");
				}
			}
		}else{
			throw new RuntimeException("该会话已经分配");
		}
	}


	private BoundHashOperations<String, String, QueueupCalculator> getBoundHashOperations(String tenantCode){
		String key = QUEUE_CALCULATOR_MAP_KEY + tenantCode;
		BoundHashOperations<String, String, QueueupCalculator> bho = bhos.get(key);

		if(null == bho){
			bho = rt.boundHashOps(key);
			bhos.put(key, bho);
		}

		return bho;
	}
	
	public void delete(String tenantCode,String queueId){
		getBoundHashOperations(tenantCode).delete(queueId);
	}
}
