package cn.sh.ideal.mir.dao;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import cn.sh.ideal.mir.resp.entity.SessionQueue;
import cn.sh.ideal.model.SessionInfo;


public interface SessionDao extends GenericDao<SessionInfo, Serializable> {
	public String queryPriority(Map<String, String> paramMap);

	public SessionInfo getLastAgent(Map<String, Object> paramMap);

	public List<SessionQueue> getQueueSession(Map<String, Object> paramMap);

	public SessionInfo getVoiceSession(SessionInfo paramSessionInfo);

	public String getExclusiveAgent(String paramString);

	public int getSessionCount(Map<String, String> paramMap);
	public void delSessionStatusLog(String sessionId);
}
