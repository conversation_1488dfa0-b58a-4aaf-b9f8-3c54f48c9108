package cn.sh.ideal.mir.handler;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.req.GetQueueCountRequest;
import cn.sh.ideal.mir.req.GetQueueSkillInfoRequest;
import cn.sh.ideal.mir.resp.GetQueueCountResponse;
import cn.sh.ideal.mir.resp.GetQueueSkillInfoResponse;
import cn.sh.ideal.mir.service.AllocateService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.util.DateUtils;

/**
 * last agent消息处理器
 * <AUTHOR>
 *
 */
@Component("skillWeightMessageHandler")
@Scope("prototype")
public class SkillWeightMessageHandler extends AbstractMessageHandler{
	private static final Logger log = LoggerFactory.getLogger(SkillWeightMessageHandler.class);
	@Autowired
	private SessionDao sessionDao;
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	@Autowired
	private AllocateService allocateServerImpl;
	@Autowired
	private SkillQueueService queueService;
		
	@Override
	public void routing(SessionInfo session) {
		try{
			log.info("###############skillWeightMessageHandler_session:{}",JSONObject.toJSON(session));
			String tenantCode = session.getTenantCode();
			String channelCode = session.getChannelCode();
			for(int level = 0; level < 4; level++){
				List<TenantSkillQueue> list = getQueueUp(tenantCode,level,channelCode); //获取各权重下的技能组
				String resultSkillId = getSkillQueue(list);
				if(StringUtils.isNoneEmpty(resultSkillId)){
					session.setSkillQueue(resultSkillId);
					break;
				}
			}
			
		}catch(Exception e){
			log.error("last agent 异常",e);
		}
		log.info("###############skillWeightMessageHandler_return_session:{}",JSONObject.toJSON(session));
		super.routing(session);
	}
	
	private String getSkillQueue(List<TenantSkillQueue> list){
		for(TenantSkillQueue tenantSkillQueue : list){
			boolean tempSingleFlag = checkQueueSize(tenantSkillQueue.getId().toString(),tenantSkillQueue.getTenantId());
			if(!tempSingleFlag){	//如果没满赋予新的技能组,返回技能组id
				return tenantSkillQueue.getId().toString();
			}
		}
		return "";
	}
	
	/**
	 * 判断技能组队列是否已满
	 * @param skillQueue
	 * @param tenantCode
	 * @return
	 */
	private boolean checkQueueSize(String skillQueue,String tenantCode){
		GetQueueSkillInfoRequest gqsiRequest = new GetQueueSkillInfoRequest();
		gqsiRequest.setQueueIds(skillQueue);
		gqsiRequest.setTenantCode(tenantCode);
		//获取缓存中租户技能组
		GetQueueSkillInfoResponse response = allocateServerImpl.getSkillQueue(gqsiRequest);
		log.info("#############skillWeightMessageHandler_getSkillQueue_response:{}",JSONObject.toJSON(response));
		List<TenantSkillQueue> tempList = response.getData();
		if(tempList != null && tempList.size() > 0){
			TenantSkillQueue redisSkillQueue = tempList.get(0);
			GetQueueCountRequest gqcRequest = new GetQueueCountRequest();
			gqcRequest.setSkillQueue(redisSkillQueue.getId().toString());
			gqcRequest.setTenantCode(tenantCode);
			GetQueueCountResponse gqcResponse = allocateServerImpl.getQueueCount(gqcRequest);
			log.info("#############skillWeightMessageHandler_getQueueCount_response:{}",JSONObject.toJSON(gqcResponse));
			Map<String,Long> tempMap = gqcResponse.getData();
			//缓存中的技能组队列已满
			if(redisSkillQueue.getQueueSize().intValue() >= tempMap.get(skillQueue).intValue()){
				return true;
			}
		}
		return false;
	}
	
	private List<TenantSkillQueue> getQueueUp(String tenantCode,int weight,String channelCode){
		TenantSkillQueue tenantSkillQueueQuery = new TenantSkillQueue();
		tenantSkillQueueQuery.setWeight(String.valueOf(weight));
		tenantSkillQueueQuery.setTenantId(tenantCode);
		tenantSkillQueueQuery.setAbleChannels(channelCode);
		tenantSkillQueueQuery.setStatus("1");
		List<TenantSkillQueue> list = queueService.getQueueList(tenantSkillQueueQuery);		
		return list;
	}
}
