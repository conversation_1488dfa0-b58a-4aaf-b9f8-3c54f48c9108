package cn.sh.ideal.mir.model;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
/**
 * 坐席私聊消息
 * <AUTHOR>
 *
 */
public class AgentIM implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private int id;

	private String uuid;
	
	private String tenantCode;
	
	private String name;//可以是工号 名称 登录名
	
	private String workNo;//发送消息的工号
	
	private String accept;//接收消息的工号,这里只能是同一租户下的工号
	
	@JSONField (format="yyyy-MM-dd HH:mm:ss")  
	private Date createTime;
	
	private String type;//text face image attach
	
	private String content;//text url face_code
	
	private Object agent;
	
	public Object getAgent() {
		return agent;
	}
	public void setAgent(Object agent) {
		this.agent = agent;
	}
	/**
	 * 最小化实例
	 */
	public AgentIM() {
		super();
	}
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}
	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getAccept() {
		return accept;
	}

	public void setAccept(String accept) {
		this.accept = accept;
	}
	
	
}
