package cn.sh.ideal.mir.service.local.impl;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.listener.MessageListenerHandler;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.util.Constants;
import cn.sh.ideal.util.RedisLock;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
public class PreQueueHandler {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	public final static String PREQUEUE = "prequeue_" ;
	@Autowired
	private SkillQueueService skillQueueService;
	@Autowired
	private SessionService sessionService;

	@Autowired
	private RedisDao<String, Serializable> redisDao;
	@Autowired
	private SessionDao sessionDao;

	@Value("#{config['prequeue.waitMsg']}")
	private String waitMsg;

	@Value("#{config['prequeue.norecvmsg']}")
	private String norecvmsg = "";
	@Value("#{config['prequeue.overnorecvmsg']}")
	private String overnorecvmsg = "";
	@Value("#{config['prequeue.quitmsg']}")
	private String quitmsg = "";
	@Value("#{config['prequeue.lasttimeout']}")
	private Integer lastTimeout;
	@Value("#{config['prequeue.timeout']}")
	private Integer preTimeout;
	@Value("#{config['prequeue.opttimeout']}")
	private Integer optTimeout;
	@Value("#{config['prequeue.selfservicemsg']}")
	private String selfServiceMsg = "";

	@PostConstruct
	public void init() {
		Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(new Runnable() {
			@Override
			public void run() {
				RedisLock lock = RedisLock.getRedisLock(PREQUEUE + "check");
				try {
					if (lock.lock()) {
						Long currStamp = System.currentTimeMillis();
						for(String key : redisDao.getKeysByPattern(PREQUEUE + "*")) {
							Long expireStamp = (Long) redisDao.readValue(key);
							if (currStamp < expireStamp) continue;

							redisDao.deleteValue(key);
							handleTimeout(currStamp,key);
						}
					}else{
						lock = null;
					}
				}catch (Exception e){
					logger.error("prequeue-check 处理异常", e);
				}finally {
					if (lock != null) {
						lock.unlock();
					}
				}
			}
		},0,5,TimeUnit.SECONDS);
	}

	public void handleTimeout(Long currStamp,String key) {
		try {
			logger.info("prequeue-key : {} 超时", key);
			String[] arr = key.split("_");
			SessionInfo session = this.sessionService.get(arr[2], arr[1]);
			if (ArrayUtils.contains(Constants.sortingStatus, session.getStatus())) {
				//1分钟未选择
				if("noanswer".equals(arr[3])){
					skillQueueService.pushTip2Customer(session, arr.length == 5 ? overnorecvmsg : norecvmsg);
					String value = arr[1].concat("_").concat(arr[2]) + "_last";
					redisDao.saveValue(PREQUEUE + value,currStamp + (lastTimeout*1000));
				}else if("last".equals(arr[3])){ //1分钟未选择后又过30s
					SkillQueue skillInfo = skillQueueService.getSkillInfo(session.getTenantCode(), session.getSkillQueue());
					long rank = skillQueueService.getRank(session.getTenantCode(),session.getSkillQueue(),session.getSessionId());
					if(rank >= skillInfo.getQueueSize()){
						sessionDao.delSessionStatusLog(session.getSessionId());
					}

					skillQueueService.pushTip2Customer(session, quitmsg);
					skillQueueService.poll(session.getTenantCode(),session.getSkillQueue(),session.getSessionId());
					this.sessionService.update(session.getSessionId(), session.getTenantCode(), SessionStatus.TIMEOUT, null);
				}else if("over".equals(arr[3]) && "limit".equals(arr[4])){ //超过预排队数量限制
					// 超过预排队数量限制的处理，删除会话状态日志
					logger.info("会话 {} 因超过预排队数量限制已被关闭", session.getSessionId());
					sessionDao.delSessionStatusLog(session.getSessionId());
					// 会话状态已经在push方法中更新为USER_CLOSE，这里不需要额外处理
				}else if("continue".equals(arr[3]) && "wait".equals(arr[4])){ //3分钟继续等待超时
					// 3分钟继续等待超时，推送自助反馈提示并删除记录
					logger.info("会话 {} 3分钟继续等待超时，推送自助反馈提示", session.getSessionId());
					skillQueueService.pushTip2Customer(session, selfServiceMsg);

					// 删除会话状态日志
					SkillQueue skillInfo = skillQueueService.getSkillInfo(session.getTenantCode(), session.getSkillQueue());
					long rank = skillQueueService.getRank(session.getTenantCode(),session.getSkillQueue(),session.getSessionId());
					if(rank >= skillInfo.getQueueSize()){
						sessionDao.delSessionStatusLog(session.getSessionId());
					}

					// 从队列中移除会话
					skillQueueService.poll(session.getTenantCode(),session.getSkillQueue(),session.getSessionId());
					// 更新会话状态为超时
					this.sessionService.update(session.getSessionId(), session.getTenantCode(), SessionStatus.TIMEOUT, null);
				}else{  //2分钟排队超时
					skillQueueService.pushTip2Customer(session, waitMsg);
					String value = arr[1].concat("_").concat(arr[2]) + "_noanswer";
					redisDao.saveValue(PREQUEUE + value ,currStamp + (optTimeout*1000));
				}
			}
		} catch (Exception e) {
			logger.error("prequeue-key : {} 处理异常", key);
		}
	}
}
