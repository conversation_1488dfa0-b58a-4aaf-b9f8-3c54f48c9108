package cn.sh.ideal.mir.model.tbl;

import java.io.Serializable;
import java.util.Date;

/**
 * 坐席消息交互Tbl
 * <AUTHOR>
 *
 */
public class AgentMutualTbl implements Serializable{
	
	  /**
	 * 
	 */
	private static final long serialVersionUID = -332364120362777874L;

	/** 主键 */
    private String id;

    /** 消息ID */
    private String messageId;
    
    /** 租户ID */
    private String tenantId;

    /** 会话ID */
    private String sessionId;
    
    /**  消息发送人 */
    private String fromUser;
    
    /**  消息接受人 */
    private String toUser;
    
    /** 不同消息类型扩展信息JSON格式 */
    private String extData;
    
    /** 创建时间 */
    private Date createTime;
    
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

	public String getFromUser() {
		return fromUser;
	}

	public void setFromUser(String fromUser) {
		this.fromUser = fromUser;
	}

	public String getToUser() {
		return toUser;
	}

	public void setToUser(String toUser) {
		this.toUser = toUser;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
