/**
 *
 */
package cn.sh.ideal.mir.service.local.impl;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.ConfigUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;

import cn.sh.ideal.as.model.TransferLog;
import cn.sh.ideal.as.req.AgentInfoRequest;
import cn.sh.ideal.as.req.AgentSingleRequest;
import cn.sh.ideal.as.req.TransferRequest;
import cn.sh.ideal.as.resp.AgentInfoResponse;
import cn.sh.ideal.as.resp.AgentSingleResponse;
import cn.sh.ideal.as.service.AgentService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.init.ReferenceService;
import cn.sh.ideal.mir.dao.AgentMutualDao;
import cn.sh.ideal.mir.dao.SysRuleDao;
import cn.sh.ideal.mir.model.AgentIM;
import cn.sh.ideal.mir.model.AgentMutual;
import cn.sh.ideal.mir.model.CmsPost;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.mir.util.MessageUtil.TipType;
import cn.sh.ideal.model.AgentInfo;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.model.SessionType;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.si.req.AgentSmRequest;
import cn.sh.ideal.si.req.StipRequest;
import cn.sh.ideal.util.Constants;
import cn.sh.ideal.util.EmojiConverter;
import cn.sh.ideal.util.SpringContextUtil;

/**
 * 消息分配Service实现.
 *
 * <AUTHOR>
 * @date 2014年12月10日
 */
@Service("allocationService")
public class AllocationServiceImpl implements AllocationService {
	private static final Logger logger = LoggerFactory.getLogger(AllocationServiceImpl.class);

	@Autowired
	private  AgentMutualDao amd;

	@Autowired
	private  SysRuleDao ruleDao;

	@Autowired
	private RedisDao<String, Serializable> redisDao;

	@Autowired
	private SessionService sessionService;

	@Autowired
	private SkillQueueService queueService;

	@Autowired
	private AgentService agentService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private ReferenceService<cn.sh.ideal.si.service.MessageService> referenceService;

	@Value("#{config['message.tip.forceRelay']}")
	private String forceRelayTip = "";

	public static String[] voiceMsgTypes = {"rcvoice","ocxVoice","onlineVideo"};

	@Override
	public void transferMessage(SessionInfo session,TransferRequest request,TransferLog log)  {
		Map<String,String> params = new HashMap<String,String>();

		String tenantCode = session.getTenantCode();
		String beforeSkillQueue = session.getSkillQueue();
		String beforeWorkNo = session.getWorkNos();
		String forward = request.getForward();
		if(request.getType().equals(AgentUtils.TRANSFER_USER)){
			String status = "Y".equals(request.getIgnoreStatus()) ? null : AgentStatus.FREE.getCode();
			transferUser(tenantCode,beforeSkillQueue,forward,session.getChannelCode(),status,params);
		}else{
			if("Y".equals(request.getIgnoreStatus())){
				transferSkillQueueForEmail(tenantCode, forward, beforeWorkNo, params);
			}else{
				transferSkillQueue(tenantCode, forward, beforeWorkNo, params);
			}
		}

		updateSessionTransfer(tenantCode, session.getSessionId(), params, log);
		//推送提示消息
		AgentSingleRequest agentSingleRequest=new AgentSingleRequest();
		agentSingleRequest.setTenantCode(tenantCode);
		agentSingleRequest.setSkillQueue(beforeSkillQueue);
		agentSingleRequest.setWorkNo(beforeWorkNo);
		AgentSingleResponse info = agentService.agentInfo(agentSingleRequest);

		String workNo = params.get(Constants.PARAM_WORK_NO);
		this.pushTransferTip(tenantCode, workNo, session.getSessionId(), TipType.RELAY_SESSION.toString(), request.getRemark(), info.getData(),session);
		//想原坐席推动转发提示
		if("Y".equals(request.getIsForce())){
			this.pushTip(session.getWorkNos().split(","),tenantCode,session.getSessionId(),TipType.FORCE_RELAY.toString(),String.format(forceRelayTip,workNo),null);
		}

		messageService.updateSessiontimeAndOccupany(tenantCode,params.get(Constants.PARAM_SKILL_QUEUE),params.get(Constants.PARAM_WORK_NO));
	}

	private void transferUser(String tenantCode,String beforeSq,String forward,String channel,String status,Map<String,String> params){
		String queueId = getSuitableQueue(tenantCode, forward, channel, status,beforeSq);
		if(StringUtils.isEmpty(queueId)){
			throw new MultiMediaException("1020");
		}
		params.put(Constants.PARAM_SKILL_QUEUE, queueId);
		params.put(Constants.PARAM_WORK_NO, forward);
	}

	private void transferSkillQueueForEmail(String tenantCode,String forward,final String beforeWorkNo,Map<String,String> params){
		AgentInfoRequest request =new AgentInfoRequest();
		request.setTenantCode(tenantCode);
		request.setSkillQueue(forward);
		AgentInfoResponse response = agentService.agentInfo(request);
		if(Objects.equal(response.getResultCode(),RespCode.SUCCESS.getResultCode())){
			List<Map<Object,Object>> freeUsers = response.getData();
			CollectionUtils.filter(freeUsers, new Predicate() {
				@SuppressWarnings("unchecked")
				@Override
				public boolean evaluate(Object arg0) {
					if(arg0 == null) return false;
					Map<Object,Object> info = (Map<Object,Object>)arg0;
					String workNo = (String)info.get(Constants.PARAM_WORK_NO);
					Integer maxSessionCount = (Integer)info.get(Constant.PARAM_MAXSESSION_COUNT);
					Integer currSesssionCount = (Integer)info.get(Constant.PARAM_CURRSESSION_COUNT);
					return !workNo.equals(beforeWorkNo) && maxSessionCount > currSesssionCount;
				}
			});

			if (!freeUsers.isEmpty()) {
				Collections.sort(freeUsers, new Comparator<Map<Object,Object>>() {
					@Override
					public int compare(Map<Object,Object> o1, Map<Object,Object> o2) {
						return (Integer)o1.get(Constant.PARAM_CURRSESSION_COUNT) - (Integer)o2.get(Constant.PARAM_CURRSESSION_COUNT);
					}
				});

				params.put(Constants.PARAM_WORK_NO, (String)(freeUsers.get(0).get(Constants.PARAM_WORK_NO)));
				params.put(Constants.PARAM_SKILL_QUEUE, forward);
			}else{
				throw new MultiMediaException("1011");

			}
		}else{
			throw new MultiMediaException("1011");
		}

	}
	@SuppressWarnings("unchecked")
	private void transferSkillQueue(String tenantCode,String forward,final String beforeWorkNo,Map<String,String> params){
		SkillQueue skillQueue = queueService.getSkillQueue(tenantCode,forward);
		AllocationRule allocationRule = (AllocationRule) SpringContextUtil.getBean(skillQueue.getAllocateRule().getRuleCode());
		List<Map<Object,Object>> freeUsers = allocationRule.excute(tenantCode,skillQueue.getId(),skillQueue.getAllocateRule());
		CollectionUtils.filter(freeUsers, new Predicate() {
			@Override
			public boolean evaluate(Object object) {
				Map<Object,Object> user = (Map<Object,Object>)object;
				String workNo = (String)user.get(Constants.PARAM_WORK_NO);
				return !beforeWorkNo.equals(workNo);
			}
		});
		if(CollectionUtils.isEmpty(freeUsers)){
			throw new MultiMediaException("1011");
		}
		params.put(Constants.PARAM_WORK_NO, (String)(freeUsers.get(0).get(Constants.PARAM_WORK_NO)));
		params.put(Constants.PARAM_SKILL_QUEUE, forward);
	}

	private void pushTransferTip(String tenantCode, String workNo,String sessionId,String tipType, String content,AgentInfo agent,SessionInfo session){
		StipRequest tip = new StipRequest();
		tip.setTenantCode(tenantCode);
		tip.setSessionId(sessionId);
		tip.setType(tipType);
		tip.setContent(content);
		tip.setExtData(agent);
		tip.setWorkNo(workNo);
		tip.setSession(session);

		tipPush(tip, tenantCode, workNo);
	}

	private void updateSessionTransfer(String tenantCode,String sessionId,Map<String,String> params,TransferLog log){
		String queueId = params.get(Constants.PARAM_SKILL_QUEUE);
		String workNo = params.get(Constants.PARAM_WORK_NO);

		String agentKey = AgentUtils.getAgentBaseKey(tenantCode, workNo);
		Integer currCount = (Integer)redisDao.mapGetValue(agentKey, Constant.PARAM_CURRSESSION_COUNT);
		Integer maxCount = (Integer)redisDao.mapGetValue(agentKey, Constant.PARAM_MAXSESSION_COUNT);

		if(currCount >= maxCount){
			throw new MultiMediaException("1020");
		}

		log.setForwardSkillQueue(queueId);
		log.setForwardWorkNo(workNo);
		params.put(Constants.PARAM_SKILL_QUEUE, queueId);
		params.put(Constants.PARAM_WORK_NO, workNo);
		params.put(Constant.PARAM_SESSION_TYPE, SessionType.FORWORD.toString());

		sessionService.update(sessionId, tenantCode, SessionStatus.FORWORD, params);
	}


	@Override
	public void pushTip(String[] workNos, String tenantCode, String sessionId,
						String tipType, String content,Object extData) {
		StipRequest tip = new StipRequest();
		tip.setTenantCode(tenantCode);
		tip.setSessionId(sessionId);
		tip.setType(tipType);
		tip.setContent(content);
		tip.setExtData(extData);

		for(String wn : workNos){
			tip.setWorkNo(wn);
			tipPush(tip, tenantCode, wn);
		}

	}

	@Override
	public void pushTip(StipRequest tip,String[] workNos){
		for(String wn : workNos){
			tip.setWorkNo(wn);
			tipPush(tip, tip.getTenantCode(), wn);
		}
	}

	private void tipPush(StipRequest request,String tenantCode, String workNo){
		try{
			cn.sh.ideal.si.service.MessageService messageService= referenceService.getSiService(tenantCode, workNo,
					ConfigUtils.getProperty("dubbo.si.version"), cn.sh.ideal.si.service.MessageService.class);
			logger.info("push tip to si request:{}",JSONObject.toJSONString(request));

			messageService.stip(request);

		}catch(Exception e){
			logger.error("向坐席推送提示信息失败:{}",e.getMessage());
		}
	}

	@Override
	public void pushMessage(SessionInfo session,List<Map<Object,Object>> users) throws Exception {
		for(Map<Object,Object> user : users){
			String workNo = (String)user.get(Constants.PARAM_WORK_NO);
			int result = messageService.allocateMessage(session,session.getTenantCode(),session.getSkillQueue(),workNo);
			if(result == 0)return;
		}

		throw new MultiMediaException("1013");

	}

	@Override
	public void pushMessage(MessageInfo message,SessionInfo session) throws Exception {
		//检查是否为语音消息
		if(ArrayUtils.contains(voiceMsgTypes,message.getMsgType())){
			queueService.push(message.getTenantCode(),session.getSkillQueue(), session,false);
		}else{
			for(String wn : session.getWorkNos().split(",")){
				try{
					message.setWorkNo(wn);
					this.pushMessage(message);
					//更新消息
				}catch(Exception e){
					logger.error("向坐席{}推送消息异常:{}",wn,e.getMessage());
				}
			}
		}
	}


	public void pushMeeting(MessageInfo message,String fromWorkno,String[] worknos)  throws Exception{
		for(String wn : worknos){
			try{
				message.setWorkNo(wn);
				this.pushMessage(message);
			}catch(Exception e){
				logger.error("向坐席{}推送消息异常:{}",wn,e.getMessage());
			}
		}

	}

	private void pushMessage(MessageInfo message) throws Exception{
		List<MessageInfo> messages = new ArrayList<MessageInfo>();
		messages.add(message);
		cn.sh.ideal.si.service.MessageService messageService= referenceService.getSiService(message.getTenantCode(),
				message.getWorkNo(),ConfigUtils.getProperty("dubbo.si.version"), cn.sh.ideal.si.service.MessageService.class);
		messageService.pushMessageInfos(messages);
	}


	public void pushPrivateMsg(AgentIM agentIm) throws Exception{
		String content = agentIm.getContent();
		AgentSingleRequest agentSingleRequest=new AgentSingleRequest();
		agentSingleRequest.setTenantCode(agentIm.getTenantCode());
		agentSingleRequest.setSkillQueue("*");
		agentSingleRequest.setWorkNo(agentIm.getWorkNo());
		AgentSingleResponse agentSingleResponse=agentService.agentInfo(agentSingleRequest);
		agentIm.setAgent(agentSingleResponse.getData());
		if("text".equals(agentIm.getType())){
			agentIm.setContent(EmojiConverter.encode(content));
		}
		amd.insert(agentIm);
		agentIm.setContent(content);
		for(String wn : agentIm.getUuid().split(",")){
			if(wn.equals(agentIm.getWorkNo())) continue;
			agentIm.setAccept(wn);

			try{
				cn.sh.ideal.si.service.MessageService messageService= referenceService.getSiService(agentIm.getTenantCode(),
						wn,ConfigUtils.getProperty("dubbo.si.version"), cn.sh.ideal.si.service.MessageService.class);
				AgentSmRequest agentSmRequest=new AgentSmRequest();
				agentSmRequest.setAccept(agentIm.getAccept());
				agentSmRequest.setAgent(agentIm.getAgent());
				agentSmRequest.setId(agentIm.getId());
				agentSmRequest.setUuid(agentIm.getUuid());
				agentSmRequest.setTenantCode(agentIm.getTenantCode());;
				agentSmRequest.setName(agentIm.getName());
				agentSmRequest.setWorkNo(agentIm.getWorkNo());
				agentSmRequest.setAccept(agentIm.getAccept());
				agentSmRequest.setCreateTime(agentIm.getCreateTime());
				agentSmRequest.setType(agentIm.getType());
				agentSmRequest.setContent(agentIm.getContent());
				messageService.agentSM(agentSmRequest);
			}catch(Exception e){
				logger.warn("push private message to {} failed:{}",wn,e.getMessage());
			}
		}

	}

	@Override
	public void directPushMessage(SessionInfo session,String workNo) throws Exception {
		//查询坐席技能组
		String queueId = getSuitableQueue(session.getTenantCode(), workNo, session.getChannelCode(),
				AgentStatus.FREE.getCode(), null);

		if(StringUtils.isEmpty(queueId)){
			throw new MultiMediaException("1010");
		}

		if(messageService.allocateMessage(session,session.getTenantCode(),queueId,workNo) != 0){
			throw new MultiMediaException("1013");
		}

		//如果该会话存在队列中，移除掉
		if(!StringUtils.isEmpty(session.getSkillQueue())){
			queueService.poll(session.getTenantCode(),session.getSkillQueue(), session.getSessionId());
		}
	}

	@Override
	public boolean preAllocate(SessionInfo session,MessageInfo message) throws Exception {
		if("Y".equals(message.getForce()))
			return this.forceAllocate(session, message);

		String queueId = StringUtils.defaultIfEmpty(message.getSkillQueue(), session.getSkillQueue());

		String workNo = StringUtils.defaultIfEmpty(message.getWorkNo(), session.getWorkNos());

		if(StringUtils.isNotEmpty(queueId)){
			session.setSkillQueue(queueId);
			session.setWorkNos(workNo);
		}else{
			if(StringUtils.isNotEmpty(workNo)){
				getSuitableQueue(session,workNo);
			}
		}

		return false;
	}

	private void getSuitableQueue(SessionInfo session,String workNo){
		String key = AgentUtils.getAgentBaseKey(session.getTenantCode(), workNo);
		if(!redisDao.exist(key)){
			logger.warn("未找到工号{}匹配的坐席信息",workNo);
		}else{
			String skills = (String)redisDao.mapGetValue(key, Constants.PARAM_SKILL_QUEUE);

			if(StringUtils.isEmpty(skills)) return;

			for(String skillQueue : skills.split(",")){
				String key1 = AgentUtils.getAgentSkillKey(session.getTenantCode(), skillQueue, workNo);
				String channelArr = (String)redisDao.mapGetValue(key1, Constants.PARAM_CHANNEL_ARR);

				if(channelArr.contains(session.getChannelCode())){
					session.setWorkNos(workNo);
					session.setSkillQueue(skillQueue);
					return;
				}
			}
		}

		session.setWorkNos(null);
	}

	private String getSuitableQueue(String tenantCode,String workNo,String channel,String status,String oqueueId){
		String suitQueue1 = null;
		String suitQueue2 = null;

		String key = AgentUtils.getAgentBaseKey(tenantCode, workNo);
		if(!redisDao.exist(key)){
			logger.warn("未找到工号{}匹配的坐席信息",workNo);
		}else{
			Map<Object,Object> baseMap = redisDao.mapGet(key);
			String sta = (String)baseMap.get(Constants.PARAM_STATUS);
			String skills = (String)baseMap.get(Constants.PARAM_SKILL_QUEUE);
			Integer maxSessionCount = (Integer)baseMap.get(Constant.PARAM_MAXSESSION_COUNT);
			Integer currSessionCount = (Integer)baseMap.get(Constant.PARAM_CURRSESSION_COUNT);
			if(maxSessionCount <= currSessionCount) return null;
			if(StringUtils.isNotEmpty(skills)){
				for(String skillQueue : skills.split(",")){
					String key1 = AgentUtils.getAgentSkillKey(tenantCode, skillQueue, workNo);
					Map<Object,Object> map = redisDao.mapGet(key1);

					String queueId = (String)map.get(Constants.PARAM_SKILL_QUEUE);
					String channelArr = (String)map.get(Constants.PARAM_CHANNEL_ARR);
					//预分配
					if((status != null && status.equals(sta)) || status == null ){
						if(null != oqueueId && queueId.equals(oqueueId)){
							suitQueue1 = queueId;break;
						}

						if(StringUtils.contains(channelArr, channel)){
							suitQueue2 = queueId;
						}
					}
				}
			}
		}

		return null != suitQueue1 ? suitQueue1 : suitQueue2;
	}



	/**
	 * 强制分配
	 *
	 * @param session
	 * @param message
	 * @return
	 */
	private boolean forceAllocate(SessionInfo session,MessageInfo message){
		if(!session.getStatus().equals(SessionStatus.MANUAL.getCode())){
			Map<String,String> params = new HashMap<String,String>();
			params.put(Constants.PARAM_WORK_NO, message.getWorkNo());
			if(message.getWorkNo().split(",").length == 1){
				String queueId = getSuitableQueue(session.getTenantCode(), message.getWorkNo(), session.getChannelCode(), null,null);
				params.put(Constants.PARAM_SKILL_QUEUE, queueId);
			}
			//更新会话状态
			try{
				sessionService.update(message.getSessionId(), message.getTenantCode(), SessionStatus.MANUAL, params);
			}catch(Exception e){
				logger.error("更新会话状态异常",e);
			}
		}

		String [] workNos = message.getWorkNo().split(",");
		for(String workNo : workNos){
			message.setWorkNo(workNo);
			String key = AgentUtils.getAgentBaseKey(session.getTenantCode(), workNo);
			if(!redisDao.exist(key)){
				logger.warn("强制分配时未找到工号{}匹配的坐席信息",message.getWorkNo());
				continue;
			}
			//boolean isFree = AgentStatus.FREE.getCode().equals((String)redisDao.mapGetValue(key,Constants.PARAM_STATUS));

			try{
				this.pushMessage(message);
			}catch(Exception e){
				logger.error("强制分配消息异常:工号{} {}",workNo,e.getMessage());
			}

		}

		return true;
	}


	@Override
	public void takeOverTask(String tenantCode,String skillQueue,String targetWn,String sessionId){
		Map<String,String> params = new HashMap<String,String>();
		params.put(Constants.PARAM_WORK_NO, targetWn);
		params.put(Constants.PARAM_SKILL_QUEUE, skillQueue);

		sessionService.update(sessionId, tenantCode, SessionStatus.MANUAL, params);
		try {
			this.pushTip(targetWn.split(","),tenantCode, sessionId, TipType.TAKE_OVER.toString(), null,null);
			String[] workNo = {targetWn};
			this.pushTip(workNo,tenantCode,sessionId,TipType.RELAY_SESSION.toString(),null,null);
		} catch (Exception e) {
			logger.error("推送接管提示异常:{}",e.getMessage());
		}
	}

	@Override
	public void pushPost(CmsPost post) {
		Set<String> keys = redisDao.getKeysByPattern(AgentUtils.getAgentBaseKey(post.getTenantId(), "*"));
		for(String key : keys){
			if(!redisDao.exist(key))continue;
			String tenantCode = (String)redisDao.mapGetValue(key, "tenantCode");
			String workNO = (String)redisDao.mapGetValue(key, "workNo");
			try{
				//				TODO 修改公告推送地址
				//				messageService.callPush(post,initService.getServiceInvokerAddress(tenantCode,workNO,mediapcmUrl),Constants.CONTENT_TYPE_JSON);
			}catch(Exception e){
				logger.error("公告消息推送错误:tenantCode="+tenantCode+",workNO="+workNO, e.getMessage());
			}
		}
	}

	@Override
	public SysRule getRuleById(String ruleId) {
		return ruleDao.load(ruleId);
	}

	@Override
	public SysRule getRuleByCode(String tenantCode,String ruleCode) {
		SysRule rule = new SysRule();
		rule.setRuleCode(ruleCode);

		return ruleDao.query(rule).get(0);
	}

	@Override
	public List<AgentIM> queryPrivateMsgs(AgentMutual agentMutual){
		List<AgentIM> ims = amd.query(agentMutual);
		for(AgentIM im : ims){
			if("text".equals(im.getType())){
				im.setContent(EmojiConverter.decode(im.getContent()));
			}
		}
		return ims;
	}
}
