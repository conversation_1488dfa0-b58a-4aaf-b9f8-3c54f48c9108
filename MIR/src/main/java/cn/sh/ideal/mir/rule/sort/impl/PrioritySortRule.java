package cn.sh.ideal.mir.rule.sort.impl;

import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.rule.sort.SortRule;
import cn.sh.ideal.model.SessionInfo;

/**
 * 权值大小排序规则 
 * <AUTHOR>
 *
 */
@Component("prioritySortRule")
public class PrioritySortRule implements SortRule{

	@Override
	public int compare(SessionInfo session1, SessionInfo session2) {
		 
		return session2.getPriority() - session1.getPriority();
	}


}
