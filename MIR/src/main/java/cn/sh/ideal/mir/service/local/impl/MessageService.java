//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.sh.ideal.mir.service.local.impl;

import cn.sh.ideal.as.model.AgentStatusDTO;
import cn.sh.ideal.as.service.local.AvayaService;
import cn.sh.ideal.as.service.local.impl.AgentStatusService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.init.ReferenceService;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.dao.MessageInfoDao;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.util.MessageUtil;
import cn.sh.ideal.model.AgentInfo;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.model.SkillType;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.util.DateUtils;
import cn.sh.ideal.util.EmojiConverter;
import cn.sh.ideal.util.NetUtil;
import cn.sh.ideal.util.RedisLock;
import com.alibaba.dubbo.common.utils.ConfigUtils;
import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("messageService")
public class MessageService {
	private static final Logger logger = LoggerFactory.getLogger(MessageService.class);
	@Autowired
	private MessageInfoDao messageDao;
	@Autowired
	private SysInitService initService;
	@Autowired
	private SessionService sessionService;
	@Autowired
	private cn.sh.ideal.mgw.service.MessageService mgwMessageService;
	@Autowired
	private AvayaService avayaService;
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	@Autowired
	private AgentStatusService ass;
	@Autowired
	private ReferenceService<cn.sh.ideal.si.service.MessageService> referenceService;
	@Value("#{config['audio.push.url']}")
	private String pushAudioUrl = "";
	public static String[] voiceChannels = new String[]{"1000", "1017", "1018"};

	public MessageService() {
	}

	public int allocateMessage(SessionInfo session, String tenantCode, String queueId, String workNo) {
		return this.allocateMessage(session, tenantCode, queueId, workNo, false, false);
	}

	public int allocateMessage(SessionInfo session, String tenantCode, String queueId, String workNo, boolean ignoreBusy, boolean isPull) {
		List<MessageInfo> messages = this.getMessages(session.getMessageIds(), session.getSessionId());
		if (CollectionUtils.isEmpty(messages)) {
			logger.warn("查询不到会话相关的消息");
			return 0;
		} else {
			this.assembleMessage(messages, session, workNo, queueId);
			String agentkey = AgentUtils.getAgentBaseKey(tenantCode, workNo);
			RedisLock lock = RedisLock.getRedisLock(agentkey + "allocate");

			try {
				String isReply = "Y";
				if (StringUtils.isNotEmpty(session.getSkillType())) {
					try {
						SkillType skillType = this.initService.getSkillType(session.getTenantCode(), session.getSkillType());
						isReply = skillType.getIsReplay();
					} catch (Exception var17) {
						this.sessionService.update(session.getSessionId(), tenantCode, SessionStatus.TIMEOUT, (Map)null);
						byte var12 = 0;
						return var12;
					}
				}

				if (lock.lock()) {
					if (!this.isFree(tenantCode, workNo, ignoreBusy, isPull)) {
						throw new MultiMediaException("1020");
					} else {
						this.doPush(session, messages, tenantCode, queueId, workNo);
						this.avayaService.updateAgentMultiState(workNo, "inwork", session.getChannelCode(), session.getSendAccount());
						this.updateSession(session, tenantCode, queueId, workNo);
						this.updateSessiontimeAndOccupany(tenantCode, queueId, workNo);
						if ("Y".equals(isReply)) {
							this.sendTheFirstMessage(session, queueId, workNo);
						}

						byte var21 = 0;
						return var21;
					}
				} else {
					lock = null;
					throw new MultiMediaException("1020");
				}
			} catch (Exception var18) {
				logger.error("t111",var18);
				byte var11 = -2;
				return var11;
			} finally {
				if (lock != null) {
					lock.unlock();
				}

			}
		}
	}

	private List<MessageInfo> getMessages(List<String> messageIds, String sessionId) {
		MessageInfo msg = new MessageInfo();
		msg.setSessionId(sessionId);
		List<MessageInfo> messages = this.messageDao.query(msg);
		return messages;
	}

	private void assembleMessage(List<MessageInfo> messages, SessionInfo session, String workNo, String queueId) {
		Iterator var6 = messages.iterator();

		while(var6.hasNext()) {
			MessageInfo msg = (MessageInfo)var6.next();
			msg.setSessionId(session.getSessionId());
			msg.setWorkNo(workNo);
			msg.setSkillQueue(queueId);
			msg.setNickname(session.getNickname());
			if ("text".equals(msg.getMsgType()) && msg.getContent() != null) {
				String content = EmojiConverter.decode(msg.getContent());
				msg.setContent(content);
			}
		}

	}

	private void doPush(SessionInfo session, List<MessageInfo> messages, String tenantCode, String queueId, String workNo) throws Exception {
		String agentkey = AgentUtils.getAgentBaseKey(tenantCode, workNo);
		String allocatWrongTimes = (String)this.redisDao.mapGetValue(agentkey, "wrongTimes");
		if (allocatWrongTimes == null || "".equals(allocatWrongTimes)) {
			allocatWrongTimes = "0";
		}

		if (!ArrayUtils.contains(voiceChannels, session.getChannelCode())) {
			cn.sh.ideal.si.service.MessageService messageService = (cn.sh.ideal.si.service.MessageService)this.referenceService.getSiService(tenantCode, workNo, ConfigUtils.getProperty("dubbo.si.version"), cn.sh.ideal.si.service.MessageService.class);
			logger.info("push messages to si request:{}", JSONObject.toJSONString(messages));
			BaseResponse baseResponse = messageService.pushMessageInfos(messages);
			logger.info("push messages to si response:{}", JSONObject.toJSONString(baseResponse));
			if (!Objects.equals(baseResponse.getResultCode(), RespCode.SUCCESS.getResultCode())) {
				int wrongTimes = Integer.parseInt(allocatWrongTimes);
				++wrongTimes;
				if (wrongTimes > 500) {
					this.redisDao.mapPut(agentkey, "wrongTimes", "0");
					this.ass.getSuccSqs(new AgentStatusDTO(tenantCode, workNo, AgentStatus.LOGIN, "Y", (String)null, "signout", (String)null));
					this.ass.updateAgentInfo(new AgentStatusDTO(tenantCode, workNo, AgentStatus.OFFLINE, (String)null, (String)null, (String)null, (String)null));
				} else {
					this.redisDao.mapPut(agentkey, "wrongTimes", String.valueOf(wrongTimes));
				}

				throw new RuntimeException(baseResponse.getResultMsg());
			}

			this.redisDao.mapPut(agentkey, "wrongTimes", "0");
		}

	}

	public void pushVoiceAg(AgentInfo info, SessionInfo session, List<MessageInfo> messages) throws Exception {
		RedisLock lock = RedisLock.getRedisLock("agentvoice:" + info.getTenantCode() + info.getWorkNo());
		logger.info("lock tenantCode:{} workNo:{}", info.getTenantCode(), info.getWorkNo());
		if (lock.lock(500L, 20)) {
			if (!this.isFreeofIvr(info.getTenantCode(), info.getWorkNo())) {
				throw new RuntimeException("该坐席占线中,请耐心等待！");
			} else {
				if ("1000".equals(session.getChannelCode())) {
					String pushUrl = this.initService.assemblyUrl("VOICE_AG_ADDRESS", this.pushAudioUrl);
					Object voiceAgObject = MessageUtil.getVoiceJsonParam(info.getTenantCode(), session.getSessionId(), info.getWorkNo(), session.getSendAccount(), info.getExNo());
					this.callPush(voiceAgObject, pushUrl, "application/json;charset=UTF-8");
				} else {
					cn.sh.ideal.si.service.MessageService messageService = (cn.sh.ideal.si.service.MessageService)this.referenceService.getSiService(info.getTenantCode(), info.getWorkNo(), ConfigUtils.getProperty("dubbo.si.version"), cn.sh.ideal.si.service.MessageService.class);
					logger.info("push messages to si request:{}", JSONObject.toJSONString(messages));
					BaseResponse baseResponse = messageService.pushMessageInfos(messages);
					logger.info("push messages to si response:{}", JSONObject.toJSONString(baseResponse));
					if (!Objects.equals(baseResponse.getResultCode(), RespCode.SUCCESS.getResultCode())) {
						throw new RuntimeException(baseResponse.getResultMsg());
					}
				}

			}
		} else {
			throw new RuntimeException("该坐席占线中,请耐心等待！");
		}
	}

	public boolean isFreeofIvr(String tenantCode, String workNo) {
		return true;
	}

	public boolean isFree(String tenantCode, String workNo, boolean ignoreBusy, boolean isPull) {
		Map<Object, Object> map = this.redisDao.mapGet(AgentUtils.getAgentBaseKey(tenantCode, workNo));
		int status = Integer.parseInt((String)map.get("status"));
		boolean isFree = ignoreBusy ? status >= 3 : status == 3;
		if (isFree) {
			Integer currSessionCount = (Integer)map.get("currSessionCount");
			Integer maxSessionCount = (Integer)map.get("maxSessionCount");
			int cha = currSessionCount - maxSessionCount;
			if (cha < 0) {
				return true;
			}

			if (isPull) {
				int pulloverCount = 5;

				try {
					String pulloverCountStr = this.initService.getSysParam("PULLOVER_COUNT").getParamValue();
					pulloverCount = Integer.parseInt(pulloverCountStr);
				} catch (Exception var13) {
					logger.warn("the sys param PULLOVER_COUNT not set");
				}

				if (pulloverCount > cha) {
					return true;
				}

				return false;
			}
		}

		return false;
	}

	private void updateSession(SessionInfo session, String tenantCode, String queueId, String workNo) {
		Map<String, String> params = new HashMap();
		params.put("workNo", workNo);
		params.put("skillQueue", queueId);

		try {
			this.sessionService.update(session.getSessionId(), tenantCode, SessionStatus.MANUAL, params);
		} catch (Exception var7) {
			logger.error("更新会话状态异常:", var7.getMessage());
		}

	}

	public void updateSessiontimeAndOccupany(String tenantCode, String queueId, String workNo) {
		try {
			String sessionTime = DateUtils.date2Str(new Date(), "yyyy-MM-dd HH:mm:ss");
			this.redisDao.mapPut(AgentUtils.getAgentBaseKey(tenantCode, workNo), "lastSessionTime", sessionTime);
		} catch (Exception var5) {
			logger.error("更新坐席最新会话时间:{}", var5);
		}

	}

	public void sendTheFirstMessage(SessionInfo session, String queueId, String workNo) {
		String channelCode = StringUtils.trimToEmpty(session.getChannelCode());

		try {
			if (!"1002".equals(channelCode) || !"1005".equals(channelCode) || !"1000".equals(channelCode)) {
				String agentKey = AgentUtils.getAgentBaseKey(session.getTenantCode(), workNo);
				String serviceTip = (String)this.redisDao.mapGetValue(agentKey, "serviceTipWords");
				String userName = (String)this.redisDao.mapGetValue(agentKey, "userName");
				String workName = StringUtils.isNotEmpty(userName) ? userName : workNo;
				String serviceTipbak = String.format(this.initService.getSysParam("MIR_AUTO_REPLY_MSG").getParamValue(), workName);
				if (StringUtils.isNotEmpty(serviceTip)) {
					try {
						JSONObject sessionListJson = JSONObject.parseObject(serviceTip);
						logger.info(session.getBusinessType());

						try {
							if (StringUtils.isNotEmpty(session.getBusinessType())) {
								if (StringUtils.isNotEmpty(sessionListJson.getString(session.getBusinessType()))) {
									serviceTip = sessionListJson.getString(session.getBusinessType());
								} else {
									serviceTip = sessionListJson.getString("normal");
								}
							} else {
								serviceTip = sessionListJson.getString("normal");
							}
						} catch (Exception var12) {
							serviceTip = sessionListJson.getString("normal");
						}

						logger.info(serviceTip);
						serviceTip = (String)StringUtils.defaultIfEmpty(serviceTip, serviceTipbak);
					} catch (Exception var13) {
						serviceTip = serviceTipbak;
						logger.error("获取坐席欢迎语异常", var13);
					}
				} else {
					serviceTip = serviceTipbak;
				}

				logger.info(serviceTip);
				MessageInfo message = this.formatMessage(session.getAcceptedAccount(), session.getSendAccount(), serviceTip, session.getChannelCode(), session.getTenantCode(), session.getSessionId(), workNo);
				logger.info("push first message to customer:{}", JSONObject.toJSONString(message));
				this.mgwMessageService.newSend(message);
			}
		} catch (Exception var14) {
			logger.error("自动回复消息出错:{}", var14.getMessage());
		}

	}

	public void callPush(Object message, String pushUrl, String contentType) throws Exception {
		String result = NetUtil.send(pushUrl, "POST", JSONObject.toJSONString(message), contentType);
		JSONObject json = JSONObject.parseObject(result);
		if (!json.getString("resultCode").equals("0")) {
			throw new RuntimeException(json.getString("resultMsg"));
		}
	}

	private MessageInfo formatMessage(String fromuser, String touser, String msg, String channel, String tenantCode, String sessionId, String workNo) {
		MessageInfo message = new MessageInfo();
		message.setChannelCode(channel);
		message.setSendAccount(fromuser);
		message.setAcceptedAccount(touser);
		message.setTenantCode(tenantCode);
		message.setSessionId(sessionId);
		message.setSource("2");
		message.setWorkNo(workNo);
		message.setMsgType("text");
		message.setContent(msg);
		message.setMessageSource("4");
		return message;
	}
}
