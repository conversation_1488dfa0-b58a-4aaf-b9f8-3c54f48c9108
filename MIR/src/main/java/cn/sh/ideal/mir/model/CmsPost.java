package cn.sh.ideal.mir.model;

import java.io.Serializable;

public class CmsPost implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.AUTO_ID
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String autoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.SENDER
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String sender;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.TITLE
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String title;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.CONTENT
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.BEGIN_TIME
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String beginTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.END_TIME
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.FILE_PATH
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String filePath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.IS_SEND
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String isSend;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.TENANT_ID
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_POST.STATUS
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    private String status;
    
    private String receiver;
    

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.AUTO_ID
     *
     * @return the value of CMS_POST.AUTO_ID
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getAutoId() {
        return autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.AUTO_ID
     *
     * @param autoId the value for CMS_POST.AUTO_ID
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setAutoId(String autoId) {
        this.autoId = autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.SENDER
     *
     * @return the value of CMS_POST.SENDER
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getSender() {
        return sender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.SENDER
     *
     * @param sender the value for CMS_POST.SENDER
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setSender(String sender) {
        this.sender = sender;
    }


    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.TITLE
     *
     * @return the value of CMS_POST.TITLE
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.TITLE
     *
     * @param title the value for CMS_POST.TITLE
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.CONTENT
     *
     * @return the value of CMS_POST.CONTENT
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.CONTENT
     *
     * @param content the value for CMS_POST.CONTENT
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setContent(String content) {
        this.content = content;
    }



    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.FILE_PATH
     *
     * @return the value of CMS_POST.FILE_PATH
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.FILE_PATH
     *
     * @param filePath the value for CMS_POST.FILE_PATH
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.IS_SEND
     *
     * @return the value of CMS_POST.IS_SEND
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getIsSend() {
        return isSend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.IS_SEND
     *
     * @param isSend the value for CMS_POST.IS_SEND
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setIsSend(String isSend) {
        this.isSend = isSend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.TENANT_ID
     *
     * @return the value of CMS_POST.TENANT_ID
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.TENANT_ID
     *
     * @param tenantId the value for CMS_POST.TENANT_ID
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_POST.STATUS
     *
     * @return the value of CMS_POST.STATUS
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_POST.STATUS
     *
     * @param status the value for CMS_POST.STATUS
     *
     * @mbggenerated Thu Jun 04 14:20:57 CST 2015
     */
    public void setStatus(String status) {
        this.status = status;
    }

	public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

}