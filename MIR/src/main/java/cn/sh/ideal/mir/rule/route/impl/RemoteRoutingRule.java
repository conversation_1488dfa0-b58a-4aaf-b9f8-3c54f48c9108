package cn.sh.ideal.mir.rule.route.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.mir.rule.route.RoutingRule;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.util.NetUtil;


/**
 * 第三方技能组规则实现
 *
 * <AUTHOR>
 * @date 2014年5月20日
 */
@Component("remoteRoutingRule")
public class RemoteRoutingRule implements RoutingRule {
	private static final Logger log = LoggerFactory.getLogger(RemoteRoutingRule.class);
	
	@Override
	public TenantSkillQueue excute(SessionInfo session, List<TenantSkillQueue> queues,SysRule rule) {
		try{
			JSONObject params = new JSONObject();
			params.put("session", session);
			params.put("skillQueues", queues);

			//调用第三方技能组规则
			JSONObject result = JSONObject.parseObject(NetUtil.send(rule.getInterfaceUrl(), NetUtil.POST, 
					JSONObject.toJSONString(params)));

			if("0".equals(result.getString("resultCode"))){
				return JSONObject.parseObject(result.getString("data"), TenantSkillQueue.class);
			}

		}catch(Exception e){
			log.error("执行第三方技能组规则异常",e);
		}
		return null;
	}
}
