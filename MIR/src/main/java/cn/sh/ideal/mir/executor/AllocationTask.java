package cn.sh.ideal.mir.executor;

import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.mir.dao.TenantSkillQueueDao;
import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.service.local.impl.MessageService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.util.RedisLock;
import cn.sh.ideal.util.SpringContextUtil;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component("allocationTask")
public class AllocationTask
		implements Runnable
{
	private static final Logger logger = LoggerFactory.getLogger(AllocationTask.class);

	@Autowired
	private AllocationService allocationService;

	@Autowired
	private MessageService messageService;

	@Autowired
	protected RedisTemplate<String, Serializable> redisTemplate;

	@Autowired
	private SkillQueueService queueService;

	@Autowired
	private SessionService sessionService;

	@Autowired
	private TenantSkillQueueDao skillQueueDao;
	private static ConcurrentHashMap<String, BoundHashOperations<String, String, String>> bhos = new ConcurrentHashMap();

	public void run() {
		try {
			for (String queueId : this.skillQueueDao.querySkillQueueWithWeight()) {
				if (this.queueService.isEmpty(queueId)) continue;
				SkillQueue queueInfo = this.queueService.getSkillInfo(null, queueId);
				if (null == queueInfo) continue;
				RedisLock lock = RedisLock.getRedisLock("skill-allocate-" + queueId);
				try{
					//logger.info("lock pre");
					if(lock.lock()){
						//logger.info("lock inner");
						logger.info("队列:{} 任务数:{}", queueId,queueService.queueSize(queueId));

						final AllocationRule allocationRule = (AllocationRule) SpringContextUtil.getBean(queueInfo.getAllocateRule().getRuleCode());

						List<Map<Object,Object>> freeUsers = allocationRule.excute(queueInfo.getTenantCode(),queueId,queueInfo.getAllocateRule());

						if(queueService.isEmpty(queueId) || freeUsers.isEmpty()) continue;

						allocate(allocationRule,queueInfo,freeUsers);
					}else{
						lock = null;
						//logger.info("lock null");
					}
				}catch (Exception e){
					logger.error("队列:{} 分配异常", queueId,e);
				}finally {
					if(lock != null){
						lock.unlock();
						//logger.info("lock un");
					}
				}
			}
		} catch (Exception e)
		{
			logger.error("分配器执行异常", e);
		}
	}

	private void allocate(AllocationRule allocationRule, SkillQueue queueInfo, List<Map<Object, Object>> freeUsers)
	{
		String queueId = queueInfo.getId();
		while ((!this.queueService.isEmpty(queueId)) && (!freeUsers.isEmpty()))
			try {
				String session = this.queueService.poll(queueInfo.getTenantCode(), queueId);
				if (null == session) break;
				String[] params = session.split("_");
				String result = pushMessage(params[0], params[1], queueId, freeUsers);
				logger.info("allocation session:{} result is:{}", params[1], result);

				if ("sucess".equals(result))
					freeUsers = allocationRule.excute(queueInfo.getTenantCode(), queueId, queueInfo.getAllocateRule());
				else
					break;
			}
			catch (Exception e) {
				logger.error("会话分配异常", e);
			}
	}

	private String pushMessage(String tenantCode, String sessionId, String queueId, List<Map<Object, Object>> users)
	{
		String result = "fail";
		SessionInfo session = null;
		try {
			session = this.sessionService.get(sessionId, tenantCode);
			if (Integer.parseInt(session.getStatus()) == 3)
			{
				if (0 != preAllocate(users, session, queueId)) {
					this.allocationService.pushMessage(session, users);
				}
			}
			else if (Integer.parseInt(session.getStatus()) == 2) {
				this.queueService.push(tenantCode, queueId, session, false);
			}
			result = "sucess";
		} catch (Exception e) {
			logger.error(e.getMessage());
			if (((e instanceof NullPointerException)) || ((e instanceof MultiMediaException))) {
				result = "fail";
				this.queueService.push(tenantCode, queueId, session, false);
			}
		}
		return result;
	}

	private BoundHashOperations<String, String, String> getBoundHashOperations(String tenantCode) {
		String key = "PREALLOCATE:" + tenantCode;
		BoundHashOperations bho = (BoundHashOperations)bhos.get(key);

		if (null == bho) {
			bho = this.redisTemplate.boundHashOps(key);
			bhos.put(key, bho);
		}

		return bho;
	}

	private int preAllocate(List<Map<Object,Object>> users,SessionInfo session,String queueId){
		BoundHashOperations<String, String, String> bho = getBoundHashOperations(session.getTenantCode());
		final String workNo = bho.get(session.getSessionId());
		int presult = -1;
		if(StringUtils.isNotEmpty(workNo)){
			boolean exist = CollectionUtils.exists(users, new Predicate() {
				@Override
				public boolean evaluate(Object object) {
					Map<Object,Object> info = (Map<Object,Object>)object;
					String wn = (String)info.get("workNo");
					return wn.equals(workNo);
				}
			});

			if(exist){
				presult = messageService.allocateMessage(session, session.getTenantCode(), queueId, workNo);
				bho.delete(session.getSessionId());
			}
		}

		return presult;
	}
}
