package cn.sh.ideal.mir.handler;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.service.local.RoutingService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;

/**
 * WAE消息处理器
 * <AUTHOR>
 *
 */
@Component("waeMessageHandler")
@Scope("prototype")
public class WaeMessageHandler extends AbstractMessageHandler{
	@Resource(name="waeRoutingService")
	private RoutingService rs;

	@Override
	public void routing(SessionInfo session) {
		try{
			rs.routing(session);
		}catch(Exception e){
			sessionService.update(session.getSessionId(), session.getTenantCode(), SessionStatus.TIMEOUT, null);
			//throw e;
		}
	}
	
}
