package cn.sh.ideal.mir.executor;

import cn.sh.ideal.dao.RedisDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
public class RedisLockCheck {
     private static final Logger logger = LoggerFactory.getLogger(RedisLockCheck.class);
    @Autowired
    protected RedisDao<String, String> redisDao;

    public void execute(){
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try{
                   for(String lockKey : redisDao.getKeysByPattern("lock*ALLOCATE")){
                       Long locktime = Long.parseLong(redisDao.readValue(lockKey));
                       if(System.currentTimeMillis() - locktime > 60000){
                           logger.warn("监测到死锁[{}]",lockKey);
                           redisDao.deleteValue(lockKey);
                       }
                   }
                }catch (Exception e){
                    logger.error("同步会话异常",e);
                }

            }
        },0,15, TimeUnit.SECONDS);
    }
}
