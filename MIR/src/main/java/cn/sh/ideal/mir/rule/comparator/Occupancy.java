package cn.sh.ideal.mir.rule.comparator;

import java.util.Comparator;
import java.util.Map;

import cn.sh.ideal.mir.util.Constant;

public class Occupancy implements Comparator<Map<Object,Object>>{

	@Override
	public int compare(Map<Object,Object> o1, Map<Object,Object> o2) {
		int result = 0;
		Integer maxSessionCount1 = (Integer)o1.get(Constant.PARAM_MAXSESSION_COUNT);
		Integer maxSessionCount2 = (Integer)o2.get(Constant.PARAM_MAXSESSION_COUNT);
		
		Integer currSessionCount1 = (Integer)o1.get(Constant.PARAM_CURRSESSION_COUNT);
		Integer currSessionCount2 = (Integer)o2.get(Constant.PARAM_CURRSESSION_COUNT);
		
		double occupancy1 = currSessionCount1/(maxSessionCount1 + 0.0);
		double occupancy2 = currSessionCount2/(maxSessionCount2 + 0.0);
		
		double cha = occupancy1 - occupancy2;
		
		if(cha > 0 ) result = 1;
		else if(cha < 0) result = -1;
	
		return result;
	}

}
