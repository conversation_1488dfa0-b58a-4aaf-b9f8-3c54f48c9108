package cn.sh.ideal.mir.service.local;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import cn.sh.ideal.mir.req.GetSessionQueuePositionRequest;
import cn.sh.ideal.mir.req.PreQueueOptRequest;
import cn.sh.ideal.mir.resp.GetQueueCountResponse;
import cn.sh.ideal.mir.resp.GetSessionQueuePositionResponse;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.TenantSkillQueue;

public interface SkillQueueService {

	/**
	 * 编辑技能组基本信息
	 * @param queue
	 */
	public void editSkillInfo(SkillQueue queue);


	/**
	 * 删除技能组
	 *
	 * @param tenantId
	 * @param queueId
	 */
	public void deleteSkillQueue(String tenantId,String queueId);

	/**
	 * 删除技能组基本信息
	 *
	 * @param queueId
	 */
	public void deleteSkillInfo(String tenantId,String queueId);

	/**
	 * 删除队列
	 * @param queueId
	 */
	public void deleteQueue(String queueId);

	/**
	 * 查询技能组队列
	 *
	 * @param tenantId
	 * @param queueId
	 * @return
	 */
	public SkillQueue getSkillQueue(String tenantId,String queueId);

	/**
	 * 查询技能组队列
	 *
	 * @param queueId
	 * @return
	 */
	public List<SkillQueue> getSkillInfos(String tenantId);

	/**
	 * 获取租户下的技能组ID集合
	 *
	 * @param tenantId
	 * @return
	 */
	public Set<String> getQueueIds(String tenantId);

	/**
	 * 往技能组队列添加会话
	 *
	 * @param queueId
	 * @param sessionInfo
	 */
	public void push(String tenantId,String queueId,SessionInfo session,boolean isFirst);

	/**
	 * 移除技能组队列会话
	 *
	 * @param queueId
	 * @return
	 */
	public String poll(String tenantId,String queueId);

	/**
	 * 移除技能组队列指定会话
	 *
	 * @param queueId
	 * @param sessionId
	 * @return
	 */
	public void poll(String tenantCode,String queueId,String sessionId);

	/**
	 * 获取租户下所有技能组队列
	 *
	 * @param tenantId
	 * @return
	 */
	public List<SkillQueue> getSkillQueues(String tenantId);

	/**
	 * 批量插入队列
	 *
	 */
	public void pipelineQueue();

	/**
	 * 判定当前技能队列是否为空
	 * @param queueId
	 * @return
	 */
	public boolean isEmpty(String queueId);


	public long queueSize(String queueId);

	/**
	 * 获取技能组基本信息
	 *
	 * @param tenantId
	 * @param queueId
	 * @return
	 */
	public SkillQueue getSkillInfo(String tenantId,String queueId);

	/**
	 * 查询队列（分配队列+缓存队列）
	 *
	 * @param queueId
	 * @return
	 */
	public List<String> getAllQueue(String queueId);

	/**
	 * 获取会话排队位置
	 * @return
	 */
	public void  getQueueLocation(GetSessionQueuePositionRequest request,GetSessionQueuePositionResponse response);

	/**
	 * 获取队列排队数
	 * @param tenantCode
	 * @param queueIds
	 * @return
	 */
	public void getQueueCount(String tenantCode,Set<String> queueIds,GetQueueCountResponse response);

	public void init();

	public void pushSortMessage(SessionInfo session,boolean isFirst,boolean isSort);

	public Set<String> getTenantCodes();

	public void setAndClearForSet(String key,Collection<String> newSet);

	/**
	 * 获取权重
	 * @param skillQueue
	 * @return
	 */
	public int getQueueWeight(String skillQueue);

	/**
	 * 获取队列
	 * @param tenantSkillQueue
	 * @return
	 */
	public List<TenantSkillQueue> getQueueList(TenantSkillQueue tenantSkillQueue);

	public void pushTip2Customer(SessionInfo session, String message);
	public void preQueueOpt(SessionInfo sessionInfo,PreQueueOptRequest request);
	public long getRank(String tenantCode, String queueId, String sessionId);
}
