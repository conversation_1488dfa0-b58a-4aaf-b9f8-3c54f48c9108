package cn.sh.ideal.mir.handler;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.util.Constants;
import cn.sh.ideal.util.NetUtil;

/**
 * 第三方消息处理器
 * <AUTHOR>
 *
 */
@Component("remoteMessageHandler")
@Scope("prototype")
public class RemoteMessageHandler extends AbstractMessageHandler{
	private static final Logger log = LoggerFactory.getLogger(RemoteMessageHandler.class);
	@Autowired
	private SysInitService initService;
	@Override
	public void routing(SessionInfo session) {
		try{
			SysRule rule = initService.getTenantInfo(session.getTenantCode()).getRouteImpl();
			if(StringUtils.isEmpty(rule.getInterfaceUrl())){
				throw new RuntimeException("can't find the romote route url");
			}
			
			JSONObject result = JSONObject.parseObject(NetUtil.send(rule.getInterfaceUrl(), NetUtil.POST, 
					JSONObject.toJSONString(session)));
			
			if("0".equals(result.getString("resultCode"))){
				JSONObject data = result.getJSONObject(Constants.PARAM_DATA);
				String skillQueue = data.getString(Constants.PARAM_SKILL_QUEUE);
				String workNo = data.getString(Constants.PARAM_WORK_NO);
				
				if(StringUtils.isEmpty(skillQueue)){
					throw new RuntimeException("can't find the skillQueue");
				}
				
				session.setSkillQueue(skillQueue);
				
				if(StringUtils.isNotEmpty(workNo)){
					int res = messageService.allocateMessage(session, session.getTenantCode(), skillQueue, workNo);
					if(0 == res) return;
				}
				
			}
			
			throw new RuntimeException("can't find the suitables workno");
		}catch(Exception e){
			log.error("remote route execute error ",e);
			super.routing(session);
		}
	}
	
}
