package cn.sh.ideal.mir.handler;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.util.DateUtils;

/**
 * last agent消息处理器
 * <AUTHOR>
 *
 */
@Component("lastAgentMessageHandler")
@Scope("prototype")
public class LastAgentMessageHandler extends AbstractMessageHandler{
	private static final Logger log = LoggerFactory.getLogger(LastAgentMessageHandler.class);
	@Autowired
	private SessionDao sessionDao;
	
	@Value("#{config['lastagent.timeout']}")
	private String timeout = "";

	@Value("#{config['lastagent.sq']}")
	private String sq = "";

	
	
	@Override
	public void routing(SessionInfo session) {
		try{
			timeout = timeout.trim();
			sq = sq.trim();
			String skillQueue = session.getSkillQueue();
			log.info("last agent route ablequeue:{} currqueue:{} timeout:{}",sq,skillQueue,timeout);
			if(StringUtils.isNotEmpty(session.getCustomerId())){
				boolean isSq = StringUtils.isEmpty(skillQueue) || 
						StringUtils.isEmpty(sq.trim()) ||  ArrayUtils.contains(sq.trim().split(","), skillQueue);

				if(isSq){
					Map<String,Object> params = new HashMap<String,Object>();
					params.put("tenantCode", session.getTenantCode());
					params.put("customerId", session.getCustomerId());
					params.put("skillQueue", skillQueue);

					if(StringUtils.isNotEmpty(timeout.trim())){
						params.put("timeout", DateUtils.getIntervalDate(new Date(),Calendar.HOUR_OF_DAY,-(Integer.parseInt(timeout.trim()))));
					}

					SessionInfo lSession = sessionDao.getLastAgent(params);
					if(null == lSession){
						log.warn("can't find lastagent");
					}else{
						String workNo = lSession.getWorkNos().split(",")[0];
						session.setSkillQueue(lSession.getSkillQueue());
						session.setWorkNos(workNo);
						log.info("last agent workNo:{} skillQueue:{}",workNo,skillQueue);
					}
				}
			}
			
		}catch(Exception e){
			log.error("last agent 异常",e);
		}
		super.routing(session);
	}
}
