package cn.sh.ideal.mir.rule.route;

import java.util.List;

import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.model.TenantSkillQueue;


public interface RoutingRule{

	/**
	 * 路由规则实现方法
	 * 
	 * @param message
	 *            需要路由的消息
	 * @param skillQueues
	 *            当前租户可用的技能队列
	 * @return 路由到的队列号没有则返回null
	 * @throws Exception
	 */
	public TenantSkillQueue excute(SessionInfo session, List<TenantSkillQueue> queues,SysRule rule);

}
