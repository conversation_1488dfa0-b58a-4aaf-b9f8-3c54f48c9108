package cn.sh.ideal.mir.service.local.impl;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.dao.SysRuleDao;
import cn.sh.ideal.mir.rule.route.RoutingRule;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.RoutingService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.TenantInfo;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.util.SpringContextUtil;

/**
 * 息路由Service实现.
 *
 * <AUTHOR>
 * @date 2014年5月27日
 */
@Service("routingService")
public class RoutingServiceImpl implements RoutingService{

	//private static final Logger logger = LoggerFactory.getLogger(RoutingServiceImpl.class);

	@Autowired
	protected RedisDao<String, Serializable> redisDao;

	@Autowired
	protected AllocationService allocationService;
	
	@Autowired
	protected SysInitService initService;
	
	@Autowired
	protected SessionService sessionService;
	
	@Autowired
	protected SysRuleDao ruleDao;
	
	@Autowired
	private SkillQueueService queueService;
	
	/**
	 * 路由
	 */
	public boolean routing(SessionInfo session) {
		TenantSkillQueue skillQueue = findSkillQueue(session);

		session.setSkillQueue(String.valueOf(skillQueue.getId()));
		
		if(StringUtils.isNoneEmpty(session.getWorkNos())){
			redisDao.mapPut(Constant.PREALLOCATE + session.getTenantCode(), session.getSessionId(), session.getWorkNos());
		}

		queueService.push(session.getTenantCode(),String.valueOf(skillQueue.getId()), session,true);
		
		return true;
	}
	
	
	private TenantSkillQueue findSkillQueue(SessionInfo session) {
		TenantSkillQueue skillQueue = null;
		TenantInfo tenantInfo = initService.getTenantInfo(session.getTenantCode());
		//指定技能组队列
		if(StringUtils.isNotEmpty(session.getSkillQueue())) {
			SkillQueue sq = queueService.getSkillInfo(session.getTenantCode(), session.getSkillQueue());
			
			if(sq == null) {
				throw new MultiMediaException("1017");
			}
			skillQueue = sq.getTenantSkillQueue();
		} else {
			//未指定技能组队列
			List<TenantSkillQueue> skillQueues = new ArrayList<TenantSkillQueue>();
			
			List<SkillQueue> queues = queueService.getSkillInfos(session.getTenantCode());
			
			for (SkillQueue queue : queues) {
				skillQueues.add(queue.getTenantSkillQueue());
			}
			
			if(skillQueues.isEmpty()) {
				throw new MultiMediaException("1017");
			}
			
			RoutingRule  routingRule = (RoutingRule)SpringContextUtil.getBean(tenantInfo.getSkillImpl().getRuleCode());
			skillQueue = routingRule.excute(session, skillQueues, tenantInfo.getSkillImpl());
			
			if(null == skillQueue){
				throw new MultiMediaException("1017");
			}
			
		}
		
		return skillQueue;
	}
}
	
