package cn.sh.ideal.mir.model;

import java.io.Serializable;

import cn.sh.ideal.model.SessionInfo;

public class AgentTip implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String tenantCode;
	private String workNo;
	private String type;
	private String sessionId;
	private String content;
	private Object extData;
	private SessionInfo session;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Object getExtData() {
		return extData;
	}
	public void setExtData(Object extData) {
		this.extData = extData;
	}
	public SessionInfo getSession() {
		return session;
	}
	public void setSession(SessionInfo session) {
		this.session = session;
	}
}
