package cn.sh.ideal.mir.model;

import cn.sh.ideal.mir.model.tbl.AgentMutualTbl;

/**
 * 坐席消息交互Model
 * 
 * <AUTHOR>
 * @date 2014-12-10
 *
 */
public class AgentMutual extends AgentMutualTbl{

	/**
	 * 
	 */
	private static final long serialVersionUID = 7758160371458306841L;
    
	private String tenantCode;
	
	private String uuid;
	
	private String lastId;
	
	private String workNo;
	
	private int count;

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getLastId() {
		return lastId;
	}

	public void setLastId(String lastId) {
		this.lastId = lastId;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}
	
}
