package cn.sh.ideal.mir.rule.allocation.impl;

import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.SysRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 熟练度分配规则
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
@Component("proficiencyAllocationRule")
public class ProficiencyAllocationRule extends AbstractAllocationRule implements AllocationRule{

	private static final Logger logger = LoggerFactory.getLogger(ProficiencyAllocationRule.class);
	
	@Override
	public List<Map<Object,Object>> excute(final String tenantCode,String queueId,SysRule rule){
		List<Map<Object,Object>> freeUsers = null;
		try {
			freeUsers = getFreeUserList(tenantCode, queueId);
			if (!freeUsers.isEmpty()) {
				Collections.sort(freeUsers, new Comparator<Map<Object,Object>>() {
					@Override
					public int compare(Map<Object,Object> map1, Map<Object,Object> map2) {
						String p1 = (String)map1.get("exNo");
						String p2 = (String)map2.get("exNo");
						return Integer.parseInt(p2) - Integer.parseInt(p1);
					}
				});
			} else {
				logger.info("技能组:{}上没有空闲的坐席!", queueId);
			}
		} catch (Exception e) {
			logger.error("执行技能组分配规则异常!", e);
		}
		
		return freeUsers;
	}
}
