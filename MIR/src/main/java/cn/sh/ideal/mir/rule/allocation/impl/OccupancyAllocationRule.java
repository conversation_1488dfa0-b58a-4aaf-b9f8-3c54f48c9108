package cn.sh.ideal.mir.rule.allocation.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.sh.ideal.mir.rule.allocation.AllocationRule;
import cn.sh.ideal.mir.rule.comparator.Occupancy;
import cn.sh.ideal.model.SysRule;

/**
 * 最小占用率
 * 
 * <AUTHOR>
 * @date 2015-07-29
 */
@Component("occupancyAllocationRule")
public class OccupancyAllocationRule extends AbstractAllocationRule implements AllocationRule{
	/**
	 * Logger for this class
	 */
	private static final Logger logger = LoggerFactory.getLogger(OccupancyAllocationRule.class);
	
	@Override
	public List<Map<Object,Object>> excute(String tenantCode,String queueId,SysRule rule){
		List<Map<Object,Object>> freeUsers = null;
		try {
			freeUsers = getFreeUserList(tenantCode, queueId);
			if (!freeUsers.isEmpty()) {
				Collections.sort(freeUsers, new Occupancy());
			} else {
				logger.info("技能组:{}上没有空闲的坐席!", queueId);
			}
		} catch (Exception e) {
			logger.error("执行技能组分配规则异常!", e);
		}
		
		return freeUsers;
	}
}
