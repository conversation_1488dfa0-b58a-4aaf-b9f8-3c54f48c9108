package cn.sh.ideal.mir.service.local;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.as.model.TransferLog;
import cn.sh.ideal.as.req.TransferRequest;
import cn.sh.ideal.mir.model.AgentIM;
import cn.sh.ideal.mir.model.AgentMutual;
import cn.sh.ideal.mir.model.CmsPost;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.si.req.StipRequest;

/**
 * 消息分配Service
 * 
 * <AUTHOR>
 * @date 2014年5月27日
 */
public interface AllocationService {
	public static final String SESSION_NUM = "SESSION_NUM:";


	/**
	 * 
	 * @param session
	 * @param tenantCode
	 * @param beforeWorkNo
	 * @param beforeSkillQueue
	 * @param forward
	 * @param type
	 * @param isForce
	 * @param remark
	 * @param log
	 */
	public void transferMessage(SessionInfo session,TransferRequest request,TransferLog log) ;

	/**
	 * 向坐席推送消息任务
	 * 
	 * @param sessionId
	 * @param workNo
	 * @throws Exception
	 */
	public void pushMessage(SessionInfo session,List<Map<Object,Object>> users) throws Exception;
	
	/**
	 * 向坐席推送消息（客户）
	 * 
	 * @param message
	 * @param session
	 * @throws Exception
	 */
	public void pushMessage(MessageInfo message,SessionInfo session)  throws Exception;
	
	/**
	 * 向坐席推送消息（预分配）
	 * 
	 * @param session
	 * @param message
	 * @throws Exception
	 */
	public boolean preAllocate(SessionInfo session ,MessageInfo message)  throws Exception;
	
	/**
	 * 直接向坐席推送消息
	 * 
	 * @param tenantCode
	 * @param sessionId
	 * @param workNo
	 * @throws Exception
	 */
	public void directPushMessage(SessionInfo session,String workNo) throws Exception;
	
	/**
	 * 向坐席推送消息（多方会议）
	 * 
	 * @param message
	 * @param fromWorkno
	 * @param worknos
	 * @throws Exception
	 */
	public void pushMeeting(MessageInfo message,String fromWorkno,String[] worknos)  throws Exception;
	
	/**
	 * 向坐席推送提示消息
	 * 
	 * @param workNos
	 * @param tenantId
	 * @param sessionId
	 * @param tipType
	 * @param content
	 * @throws Exception
	 */
	public void pushTip(String[] workNos,String tenantId,String sessionId,String tipType,String content,Object extData);
	/**
	 * 提示推送接口
	 * @param tip
	 * @param workNos
	 */
	public void pushTip(StipRequest tip,String[] workNos);
	
	/**
	 * 向坐席推送消息（私聊）
	 * 
	 * @param agentIm
	 * @throws Exception
	 */
	public void pushPrivateMsg(AgentIM agentIm) throws Exception;

	/**
	 * 添加会议发送消息
	 * @param meet
	 */
	//public void insertMeetSend(AgentMutual meet);
	
	/**
	 * 根据Id获取规则
	 * 
	 * @param ruleId
	 * @return
	 */
	public SysRule getRuleById(String ruleId);
	
	/**
	 * 根据规则code获取规则
	 * 
	 * @param tennantCode
	 * @param ruleCode
	 * @return
	 */
	public SysRule getRuleByCode(String tennantCode,String ruleCode);
	
	/**
	 * 任务接管
	 * 
	 * @param tenantCode
	 * @param skillQueue
	 * @param targetWn
	 * @param sessionId
	 */
	public void takeOverTask(String tenantCode,String skillQueue,String targetWn,String sessionId);
	
	/**
	 * 公告推送
	 * 
	 * @param post
	 */
	public void pushPost(CmsPost post);
	
	/**
	 * 查询私聊信息
	 * @param params
	 */
	public List<AgentIM> queryPrivateMsgs(AgentMutual agentMutual);
	
}
