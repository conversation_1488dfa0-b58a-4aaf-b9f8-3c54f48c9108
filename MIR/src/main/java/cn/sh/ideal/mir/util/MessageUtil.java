package cn.sh.ideal.mir.util;

import static cn.sh.ideal.mir.util.Constant.PARAM_BEGIN_DATE;
import static cn.sh.ideal.mir.util.Constant.PARAM_END_DATE;
import static cn.sh.ideal.mir.util.Constant.PARAM_USER_DN;
import static cn.sh.ideal.mir.util.Constant.PARAM_WORK_DN;
import static cn.sh.ideal.util.Constants.*;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;

/**
 * 消息处理工具类
 * 
 * <AUTHOR>
 * @date 2014-12-10
 *
 */
public class MessageUtil {
	//提示类型
	public enum TipType{
		RELAY_SESSION,//转发
		SESSION_TIP, //会话提示
		IM_MEETING, //会议邀请
		TAKE_OVER, //任务接管
		FORCE_RELAY,//强制转发
		FORCE_SIGNIN,
		FORCE_SIGNOUT,
		FORCE_BUSY,
		FORCE_FREE,
		FORCE_LOGIN
	}
	
	public static JSONObject getJsonParam(String tenantId,String skillQueue,String workNo,String skillType,String type){
		JSONObject json = new JSONObject();
		
		json.put(PARAM_TENANT_CODE, tenantId);
		json.put(PARAM_SKILL_QUEUE, skillQueue);
		json.put(PARAM_WORK_NO, workNo);
		json.put(PARAM_TYPE, type);
		json.put(Constant.PARAM_SKILL_TYPE, skillType);
		
		return json;
		
	}
	
	/**
	 * 语音坐席回调jsonparam
	 * 
	 * @param UserDn
	 * @param workDn
	 * @return
	 */
	public static JSONObject getVoiceJsonParam(String tenantCode,String sessionId,String workNo, String userDn,String workDn){
		JSONObject json = new JSONObject();
		
		json.put(PARAM_TENANT_CODE, tenantCode);
		json.put(PARAM_SESSION_ID, sessionId);
		json.put(PARAM_WORK_NO, workNo);
		json.put(PARAM_USER_DN, userDn);
		json.put(PARAM_WORK_DN, workDn);
		
		return json;
	}
	
	
	/**
	 * 转发到技能组的json param
	 * @param tenantId
	 * @param addSkillQueue
	 * @param addWorkNo
	 * @param decreaseSkillQueue
	 * @param decreaseWorkNo
	 * @param type
	 * @return
	 */
	public static JSONObject getJsonParam(String tenantId,String addSkillQueue,String addWorkNo,
			String decreaseSkillQueue,String decreaseWorkNo,String skillType,String type){
		JSONObject json = new JSONObject();

		json.put(PARAM_TENANT_CODE, tenantId);
		json.put(PARAM_ADD_SKILLQUEUE, addSkillQueue);
		json.put(PARAM_ADD_WORKNO, addWorkNo);
		json.put(PARAM_DECR_SKILLQUEUE, decreaseSkillQueue);
		json.put(PARAM_DECR_WORKNO, decreaseWorkNo);
		json.put(PARAM_SKILL_TYPE, skillType);
		json.put(PARAM_TYPE, type);

		return json;
	}
	
	public static JSONObject getSessionCountParam(String tenantCode,String workNo,String beginDate,String endDate){
		JSONObject json = new JSONObject();

		json.put(PARAM_TENANT_CODE, tenantCode);
		json.put(PARAM_WORK_NO, workNo);
		json.put(PARAM_BEGIN_DATE, beginDate);
		json.put(PARAM_END_DATE, endDate);
		
		return json;
	}
	
	/**
	 * 根据会话拼装消息
	 * 
	 * @param session
	 * @param message
	 */
	public static void assembleMessage(SessionInfo session,MessageInfo messageInfo){
		if(StringUtils.isEmpty(messageInfo.getChannelCode()))
				messageInfo.setChannelCode(session.getChannelCode());
		
		if(StringUtils.isEmpty(messageInfo.getAcceptedAccount()))
			messageInfo.setAcceptedAccount(session.getSendAccount());
		
		if(StringUtils.isEmpty(messageInfo.getSendAccount()))
			messageInfo.setSendAccount(session.getAcceptedAccount());
		
		if(StringUtils.isEmpty(messageInfo.getWorkNo()))
			messageInfo.setWorkNo(session.getWorkNos());
		
		messageInfo.setTenantCode(session.getTenantCode());
		
		messageInfo.setBusinessType(session.getBusinessType());
		messageInfo.setSkillQueue(session.getSkillQueue());
		messageInfo.setSkillType(session.getSkillType());
		messageInfo.setSource("2");
		messageInfo.setMessageSource("2");
	}


	
	/**
	 * 生成消息id
	 * @return
	 */
	public static synchronized String genMessageId(){
		SimpleDateFormat sFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		return sFormat.format(new Date());
	}
	
	public static void main(String[] args) throws Exception{
		JSONObject object = new JSONObject();
		object.put("a", 123);
		
		Object content = "a";
		
		System.out.println(content.toString());
	}

}
