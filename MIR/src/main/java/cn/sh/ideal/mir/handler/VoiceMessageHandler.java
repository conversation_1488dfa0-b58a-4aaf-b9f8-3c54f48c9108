package cn.sh.ideal.mir.handler;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.service.local.impl.MessageService;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.util.Constants;
import cn.sh.ideal.util.DateUtils;

/**
 * 语音多媒体混排消息处理器
 * <AUTHOR>
 *
 */
@Component("voiceMessageHandler")
@Scope("prototype")
public class VoiceMessageHandler extends AbstractMessageHandler{
	private static final Logger log = LoggerFactory.getLogger(VoiceMessageHandler.class);

	@Autowired
	private SessionDao sessionDao;
	@Autowired
	private MessageService messageService;
	@Autowired
	private RedisDao<String,String> redisDao;

	@Value("#{config['lastagent.timeout']}")
	private String timeout = "";

	@Value("#{config['lastagent.sq']}")
	private String sq = "";
	
	@Value("#{config['message.agentoffline']}")
	private String offline = "";
	
	
	@Value("#{config['liuyan.url']}")
	private String liuyanUrl = "";
	
	@Autowired
	private cn.sh.ideal.mgw.service.MessageService mgwMessageService;
	
	@Override
	public void routing(SessionInfo session){
		try{
			if(StringUtils.isNotEmpty(session.getCustomerId())){
				if(StringUtils.isNotEmpty(session.getWorkNos())){
					int result = messageService.allocateMessage(session, session.getTenantCode(), session.getSkillQueue(),session.getWorkNos());
					if(0 == result) return;
				}
				
				SessionInfo vsession = sessionDao.getVoiceSession(session);
				if(null != vsession) {
					String skillQueue = getSuitableQueue(session, vsession.getWorkNos());
					int result = messageService.allocateMessage(session, session.getTenantCode(), skillQueue == null ? vsession.getSkillQueue() : skillQueue,vsession.getWorkNos());
					if(0 == result) return;
				}

				String eworkNo = sessionDao.getExclusiveAgent(session.getCustomerId());
				if(StringUtils.isNotEmpty(eworkNo)) {
					//session.setSkillQueue(getSuitableQueue(session, eworkNo));
					//session.setWorkNos(eworkNo);
					String skillQueue = getSuitableQueue(session,eworkNo);
					int result = messageService.allocateMessage(session, session.getTenantCode(),skillQueue,eworkNo);
					if(0 != result) {
						sendMgwMessage(session);
						sessionService.update(session.getSessionId(), session.getTenantCode(), SessionStatus.USER_CLOSE, null);
					}
					return;
				}
				
				getLastAgent(session);
			}
		}catch(Exception e){
			log.error("last agent 异常",e);
		}
		
		super.routing(session);
	}


	private void getLastAgent(SessionInfo session) {
		timeout = timeout.trim();
		sq = sq.trim();
		String skillQueue = session.getSkillQueue();
		log.info("last agent route ablequeue:{} currqueue:{} timeout:{}",sq,skillQueue,timeout);
		if(StringUtils.isNotEmpty(session.getCustomerId())){
			boolean isSq = StringUtils.isEmpty(skillQueue) || 
					StringUtils.isEmpty(sq.trim()) ||  ArrayUtils.contains(sq.trim().split(","), skillQueue);

			if(isSq){
				Map<String,Object> params = new HashMap<String,Object>();
				params.put("tenantCode", session.getTenantCode());
				params.put("customerId", session.getCustomerId());
				params.put("skillQueue", skillQueue);

				if(StringUtils.isNotEmpty(timeout.trim())){
					params.put("timeout", DateUtils.getIntervalDate(new Date(),Calendar.HOUR_OF_DAY,-(Integer.parseInt(timeout.trim()))));
				}

				SessionInfo lSession = sessionDao.getLastAgent(params);
				if(null == lSession){
					log.warn("can't find lastagent");
				}else{
					String workNo = lSession.getWorkNos().split(",")[0];
					session.setSkillQueue(lSession.getSkillQueue());
					session.setWorkNos(workNo);
					log.info("last agent workNo:{} skillQueue:{}",workNo,skillQueue);
				}
			}
		}


	}

	private String getSuitableQueue(SessionInfo session, String workNo){
		//if(StringUtils.isNotEmpty(session.getSkillQueue())) return session.getSkillQueue();
		String skillQueue = null;
		String pattern = Constants.TENANTCODE + session.getTenantCode() + Constants.SKILLQUEUE + "*" + Constants.AGENT + workNo;
		Set<String> keys = redisDao.getKeysByPattern(pattern);

		if (CollectionUtils.isEmpty(keys))
			log.warn("未找到工号{}匹配的坐席信息", workNo);
		else {
			
			for (String key : keys) {
				String queueId = (String)this.redisDao.mapGetValue(key, "skillQueue");
				String channelArr = (String)this.redisDao.mapGetValue(key, "channelArr");
				if (StringUtils.isNotEmpty(channelArr) && channelArr.contains(session.getChannelCode())) {
					skillQueue = queueId;
					if(queueId.equals(session.getSkillQueue())) break;
				}
			}
		}

		return skillQueue;
	}
	
	
	private void sendMgwMessage(SessionInfo session) {
		MessageInfo info = new MessageInfo();
		info.setAcceptedAccount(session.getSendAccount());
		info.setSendAccount(session.getAcceptedAccount());
		info.setMsgType("text");
		String url = "<a href='" +String.format(liuyanUrl, session.getSendAccount(),session.getNickname()) + "'>请留言</a>";
		info.setContent(String.format(offline, url));
		info.setTenantCode(session.getTenantCode());
		info.setChannelCode(session.getChannelCode());
		info.setMessageSource("4");
		log.info("push tip to customer:{}",JSONObject.toJSONString(info));
		mgwMessageService.newSend(info);
	}
}
