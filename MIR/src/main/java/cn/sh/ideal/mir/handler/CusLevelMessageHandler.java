package cn.sh.ideal.mir.handler;

import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.util.NetUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.jboss.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;


/**
 * 客户等级消息处理器
 * <AUTHOR>
 *
 */
@Component("cusLevelMessageHandler")
@Scope("prototype")
public class CusLevelMessageHandler extends AbstractMessageHandler{
	private static final Logger log = LoggerFactory.getLogger(CusLevelMessageHandler.class);
	@Value("#{config['cuslevel.sq']}")
	private String sq = "{}";

	@Value("#{config['cuslevel.url']}")
	private String url = "";

	private JSONObject sqJson;

	@Autowired
	private SessionDao sessionDao;

	@PostConstruct
	public void init(){
		sqJson = JSONObject.parseObject(sq);
	}

	@Override
	public void routing(SessionInfo session) {
		try{
			boolean flag = false;
			if("5009".equals(session.getChannelCode())){
				JSONObject params = new JSONObject();
				String result = NetUtil.send(String.format(url,session.getCallId()),"POST",params.toJSONString(),"application/json");
				JSONObject robj = JSONObject.parseObject(result);
				if("0".equals(robj.getString("respCode"))){
					String level = robj.getJSONObject("data").getString("primaryTierName");
					if(StringUtils.isNotEmpty(level) && sqJson.containsKey(level)){
						log.info("重点客户：{},sq{}",session.getCallId(),sqJson.getString(level));
						session.setSkillQueue(sqJson.getString(level));
						flag = true;
					}
				}
			}

			if(!flag){
				Map<String,Object> params = new HashMap<String,Object>();
				params.put("tenantCode", session.getTenantCode());
				params.put("customerId", session.getCustomerId());
				params.put("skillQueue", session.getSkillQueue());


				SessionInfo lSession = sessionDao.getLastAgent(params);
				if(null == lSession){
					log.warn("can't find lastagent");
				}else{
					String workNo = lSession.getWorkNos().split(",")[0];
					session.setSkillQueue(lSession.getSkillQueue());
					session.setWorkNos(workNo);
					log.info("last agent workNo:{} skillQueue:{}",workNo,lSession.getSkillQueue());
				}
			}

		}catch(Exception e){
			log.error("cusLevelMessageHandler 异常",e);
		}
		super.routing(session);
	}
}
