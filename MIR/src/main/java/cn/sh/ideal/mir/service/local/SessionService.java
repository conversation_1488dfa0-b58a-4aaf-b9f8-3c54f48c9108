package cn.sh.ideal.mir.service.local;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;

public interface SessionService {
	//public SessionInfo get(String sendAccount, String acceptedAccount, String channel, String tenantCode);
	
	/**
	 * 获取会话信息
	 * @param sessionId
	 * @param tenantCode
	 * @return
	 */
	public SessionInfo get(String sessionId, String tenantCode);
	
	public void update(String sessionId, String tenantCode, SessionStatus status, Map<String, String> updateFields);
	
	
	/**
	 * 更新会话工号
	 * @param sessionId 会话ID
	 * @param tenantCode 租户号
	 * @param skillQueue 技能组队列
	 * @param skillType 技能组类型
	 * @param workNo 工号
	 * @param type 类型
	 */
	public void updateWorkNo(String sessionId,String tenantCode,String skillQueue,String skillType,String workNo,String type);
	
	public List<SessionInfo> getSessionList(SessionData sessionData);
	
	public int getSessionCount(SessionData sessionData);
}
