package cn.sh.ideal.mir.util;

import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.util.Constants;

/**
 * MIR常量类
 * 
 * <AUTHOR>
 *
 */
public class Constant {
	public final static String PARAM_SESSION_COUNT = "sessionCount";
	public final static String PARAM_BUSYTYPE_COUNT = "busyCount";
	public final static String PARAM_SERVICE_TIME = "serviceTime";
	public final static String PARAM_USER_DN = "UserDn";
	public final static String PARAM_WORK_DN = "WorkDn";
	public final static String PARAM_BEGIN_DATE = "beginDate";
	public final static String PARAM_END_DATE = "endDate";
	public final static String PARAM_PORT = "port";
	public final static String PARAM_SKILL_TYPE = "skillType";
	public final static String PARAM_CURRSESSION_COUNT = "currSessionCount";
	public final static String PARAM_MAXSESSION_COUNT = "maxSessionCount";
	public final static String PARAM_STATUS_DESC = "statusDesc";
	public final static String PARAM_LOCATION = "location";
	public final static String PARAM_LAST_SESSIONTIME = "lastSessionTime";
	public final static String PARAM_SESSION_TYPE = "sessionType";
	
	public final static String PARAM_PULLOVER_COUNT = "PULLOVER_COUNT";
	//语音坐席地址
	public final static String SYS_PARAM_VOICE_AG_ADDRESS = "VOICE_AG_ADDRESS";
	
	public final static String SESSION_LOCK = "session";
	
	public final static String ROUTE_RULE = "ROUTE_RULE";
	
	public final static String COUNTER = "COUNTER";
	
	public final static String IVR_AGENT = "IVR_AGENT:";
	
	public final static String SRV_SERVICE_CENTER = "SRV_SERVICE_CENTER";
	
	public final static String PREALLOCATE = "PREALLOCATE:";
	
	public final static String SYS_RULE = "SYS_RULE";
	
	public final static String RELATION = "RELATION:";
	
	public final static SysRule defaultSortRule;
	
	public final static SysRule defaultAllocationRule;
	
	public final static String FREE_AGENTS  = "FREE_AGENTS";
	
	static {
		defaultSortRule = new SysRule();
		defaultSortRule.setRuleCode(Constants.DEFAULT_SORT_RULE);
		defaultSortRule.setRuleType("2");
		defaultSortRule.setRuleMethod("2");
		defaultAllocationRule = new SysRule();
		defaultAllocationRule.setRuleCode(Constants.DEFAULT_ALLOCATE_RULE);
		defaultAllocationRule.setRuleType("3");
		defaultAllocationRule.setRuleMethod("2");
	}
	
	
}
