package cn.sh.ideal.mir.executor;

import java.util.HashMap;
import java.util.Map;

/**
 * 线程工厂的工厂
 * <AUTHOR>
 *
 */
public class ThreadFactorys {
	private static Map<String,TaskThreadFactory> factorys = new HashMap<String,TaskThreadFactory>();
	
	public static TaskThreadFactory getThreadFactory(String name){
		TaskThreadFactory factory = factorys.get(name);
		
		return null == factory ? newThreadFactory(name) : factory;
	}
	
	public static TaskThreadFactory newThreadFactory(String name){
		TaskThreadFactory factory = new TaskThreadFactory(name);
		factorys.put(name, factory);
		return factory;
	}
	
	public static void removeThreadFactory(String name){
		factorys.remove(name);
	}
}
