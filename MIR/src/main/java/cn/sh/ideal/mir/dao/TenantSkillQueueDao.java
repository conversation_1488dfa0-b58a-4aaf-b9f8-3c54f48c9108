/**
 * 
 */
package cn.sh.ideal.mir.dao;

import java.io.Serializable;
import java.util.List;

import cn.sh.ideal.model.TenantSkillQueue;


/**
 * 租户技能队列Dao
 * <AUTHOR>
 * @date 2014年5月27日
 */
public interface TenantSkillQueueDao extends GenericDao<TenantSkillQueue, Serializable> {

	public List<TenantSkillQueue> querySkillQueue(TenantSkillQueue tenantSkillQueue);
	List<String> querySkillQueueWithWeight();
}
