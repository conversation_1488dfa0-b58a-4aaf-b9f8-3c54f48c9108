package cn.sh.ideal.mir.rule.allocation.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.as.service.local.AvayaService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.as.util.AvayaUtil;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.util.Constants;

/**
 * 排序规则抽象类
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("unchecked")
public class AbstractAllocationRule {
	@Autowired
	protected SysInitService initService;
	
	@Autowired
	protected AvayaService avayaService;
	
	@Autowired
	protected RedisDao<String,String> redisdao;
	
	private static String [] OUT_CALL_STATES= {"ResourceNotReady","ResourceWrapUp","ResourceEnable","ResourceDisable","ResourceAux","ResourceInChannel"};
	
	/**
	 * 获取租户下已签入空闲坐席
	 * 
	 * @param tenantCode
	 * @param skillQueue
	 * @return
	 */
	public List<Map<Object,Object>> getFreeUserList(final String tenantCode,String skillQueue){
			List<Map<Object,Object>> freeUsers = new ArrayList<>();
			Set<String> keys = redisdao.setMembers(AgentUtils.getAgentSkiilRelationKey(skillQueue));
			if(CollectionUtils.isEmpty(keys)) return freeUsers;
			
			Set<String> agentKeys = new HashSet<>();
			
			for(String key : keys){
				agentKeys.add(AgentUtils.getAgentBaseKey(tenantCode, key));
			}
			
			freeUsers = redisdao.pipelineHmget(agentKeys);
			
			CollectionUtils.filter(freeUsers, new Predicate() {
				
				@Override
				public boolean evaluate(Object object) {
					if(null == object) return false;;
					Map<Object,Object> map = (Map<Object,Object>)object;
					String workNo = (String)map.get(Constants.PARAM_WORK_NO);
					String status = (String)map.get(Constants.PARAM_STATUS);
					
					if(!AgentStatus.FREE.getCode().equals(status)) return false;
					
					Integer maxSessionCount = (Integer)map.get(Constant.PARAM_MAXSESSION_COUNT);
					
					Integer currSessionCount = (Integer)map.get(Constant.PARAM_CURRSESSION_COUNT);
					
					return currSessionCount < maxSessionCount
							&& validateAvaya(workNo);
				}

			});
			
			return freeUsers;
	}
	
	/*public int getCurrSessionCount(Map<Object,Object> map,String tenantCode) {
		int currSessionCount = 0;
		String workNo = (String)map.get(Constants.PARAM_WORK_NO);
		String[] skills = ((String)map.get(Constants.PARAM_SKILL_QUEUE)).split(",");
		
		for(String queueId : skills) {
			currSessionCount += (Integer)redisdao.mapGetValue(AgentUtils.getAgentSkillKey(tenantCode, queueId, workNo),Constant.PARAM_CURRSESSION_COUNT);
		}
		
		return currSessionCount;
	}*/
	
	private boolean validateAvaya(String workNo){
		boolean flag = true;
		try{
			String isAvaya = initService.getSysParam(AvayaUtil.AVAYA_ISSEND).getParamValue();
			if(StringUtils.isNotEmpty(isAvaya) && "true".equals(isAvaya)){
				String result = avayaService.getAgentState(workNo);
				
				if(StringUtils.isEmpty(result)) return true;
				
				JSONObject json = JSONObject.parseObject(result);
				String state = json.getString("state");
				
				return ArrayUtils.contains(OUT_CALL_STATES, state);
			}}catch(Exception e){
				flag = true;
			}

		return flag;
	}

}
