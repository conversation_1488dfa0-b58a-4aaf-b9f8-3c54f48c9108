/**
 * Project Name:SM Maven Webapp
 * File Name:CmsSysParamServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2015年2月3日下午2:14:53
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.mir.service.local.impl;

import java.io.Serializable;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.service.local.SessionTipService;
import cn.sh.ideal.model.SessionTip;


@Service("sessionTipService")
public class SessionTipServiceImpl implements SessionTipService {
    
    @Resource(name = "redisDao")
    private RedisDao<String, Serializable> redisDao;

    public static String TIP_ ="TIP_";

    @Override
    public SessionTip get(String tenantCode,String skillQueue,String acceptedAccount) {
        return (SessionTip) redisDao.readValue(TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
    }
}
