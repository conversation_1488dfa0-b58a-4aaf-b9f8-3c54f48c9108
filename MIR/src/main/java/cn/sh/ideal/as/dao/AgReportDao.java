package cn.sh.ideal.as.dao;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.as.req.AgentBusiAcceptanceRequest;
import cn.sh.ideal.as.req.AgentSatisfiedRequest;
import cn.sh.ideal.as.req.AgentServiceRequest;

/**
 * 坐席报表
 * <AUTHOR>
 * @since v1.1.8
 *
 */
public interface AgReportDao<T> {
	/**
	 * 统计坐席服务数量及服务时长报表
	 * @param t
	 * @return
	 */
	List<T> queryAgentList(AgentBusiAcceptanceRequest request);
	
	/**
	 * 统计坐席满意度报表
	 * @param t
	 * @return
	 */
	List<T> queryAgentSatisfactionList(AgentSatisfiedRequest request);
	
	
	/**
	 * 查询所有技能组名称
	 * @param 
	 * @return
	 */
	List<Map<String, Object>> queryAllSkillQueueNameList();
	
	
	/**
	 * 查询所有技能组类型名称
	 * @param 
	 * @return
	 */
	List<Map<String, Object>> queryAllSkillTypeNameList();
	
	/**
	 * 查询示忙类型次数
	 * 
	 * @param map
	 * @return
	 */
	List<Map<String,Object>> queryBusyTimes(AgentServiceRequest request);
	
	/**
	 * 查询服务时长
	 * @param map
	 * @return
	 */
	String queryServiceDuration(AgentServiceRequest request);
}
