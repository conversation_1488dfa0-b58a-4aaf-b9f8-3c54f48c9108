package cn.sh.ideal.as.service.dubbo;

import cn.sh.ideal.as.dao.AgentInfoDao;
import cn.sh.ideal.as.dao.AgentLogDao;
import cn.sh.ideal.as.filter.AgentInfoFilter;
import cn.sh.ideal.as.filter.AgentInfoTask;
import cn.sh.ideal.as.model.AgentInfoBase;
import cn.sh.ideal.as.model.AgentSrvp;
import cn.sh.ideal.as.model.AgentStatusDTO;
import cn.sh.ideal.as.model.BusyType;
import cn.sh.ideal.as.model.Score;
import cn.sh.ideal.as.model.TransferLog;
import cn.sh.ideal.as.req.AgentCoordinateRequest;
import cn.sh.ideal.as.req.AgentInfoRequest;
import cn.sh.ideal.as.req.AgentSingleRequest;
import cn.sh.ideal.as.req.BusinessTypeRequest;
import cn.sh.ideal.as.req.BusyRequest;
import cn.sh.ideal.as.req.FreeRequest;
import cn.sh.ideal.as.req.IvrStatusRequest;
import cn.sh.ideal.as.req.LoginRequest;
import cn.sh.ideal.as.req.LogoutRequest;
import cn.sh.ideal.as.req.SigninRequest;
import cn.sh.ideal.as.req.SignoutRequest;
import cn.sh.ideal.as.req.SrvpRequest;
import cn.sh.ideal.as.req.TransferRequest;
import cn.sh.ideal.as.resp.AgentInfoResponse;
import cn.sh.ideal.as.resp.AgentSingleResponse;
import cn.sh.ideal.as.resp.BusinessTypeResponse;
import cn.sh.ideal.as.resp.BusyResponse;
import cn.sh.ideal.as.resp.FreeResponse;
import cn.sh.ideal.as.resp.LoginResponse;
import cn.sh.ideal.as.resp.SigninResponse;
import cn.sh.ideal.as.resp.SignoutResponse;
import cn.sh.ideal.as.resp.SrvpResponse;
import cn.sh.ideal.as.resp.TransferModel;
import cn.sh.ideal.as.service.AgentService;
import cn.sh.ideal.as.service.local.AgInitService;
import cn.sh.ideal.as.service.local.impl.AgentStatusService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.init.ReferenceService;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.util.MessageUtil;
import cn.sh.ideal.mir.util.MessageUtil.TipType;
import cn.sh.ideal.model.AgentInfo;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionType;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.si.service.MessageService;
import cn.sh.ideal.util.NetUtil;
import cn.sh.ideal.util.RedisLock;
import com.alibaba.dubbo.common.utils.ConfigUtils;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.Future;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("agentService")
public class AgentServiceImpl
		implements AgentService
{
	private static Logger logger = LoggerFactory.getLogger(AgentServiceImpl.class);
	@Autowired
	private SysInitService sysService;
	@Autowired
	private AllocationService allocateService;
	@Autowired
	private AgentStatusService ass;
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	@Autowired
	private RedisDao<String, String> redisStr;
	@Autowired
	private AgentInfoDao agentDao;
	@Autowired
	private AgentLogDao logDao;
	@Autowired
	private SkillQueueService queueService;
	@Autowired
	private AgInitService agInitService;
	@Autowired
	private SessionService sessionService;
	@Autowired
	private ReferenceService<MessageService> rs;

	public LoginResponse login(LoginRequest request)
	{
		try
		{
			request.check();
			AgentInfoBase info = this.agentDao.queryUserByAccount(request);
			if (null == info) {
				throw new MultiMediaException("1000");
			}
			Map<Object, Object> map = checkDuplicateLogin(info.getTenantCode(), info.getWorkNo(), request.getClientSessionId(), request
					.getIsForce());
			if (null == map)
			{
				AgentStatusDTO agentStatusDTOTemp = new AgentStatusDTO(info.getTenantCode(), info.getWorkNo(), AgentInfo.AgentStatus.LOGIN, request.getIsForce(), request.getClientType(), "login", request.getClientSessionId());
				agentStatusDTOTemp.setServiceCenter(request.getServiceCenter());
				map = this.ass.updateAgentInfo(agentStatusDTOTemp);
			}
			String serverIP = RpcContext.getContext().getRemoteHost();
			updateIps(info.getTenantCode(), info.getWorkNo(), "add", serverIP + ":" + request.getPort());
			List<Map<Object, Object>> infos = new ArrayList();
			infos.add(map);
			return new LoginResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), infos);
		}
		catch (Exception e)
		{
			logger.error("登陆异常", e);
			return new LoginResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public SigninResponse signIn(SigninRequest request)
	{
		try
		{
			request.check();

			return new SigninResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), this.ass
					.getSuccSqs(new AgentStatusDTO(request
							.getTenantCode(), request.getWorkNo(), AgentInfo.AgentStatus.SIGNIN, request.getIsForce(), null, null, null)));
		}
		catch (Exception e)
		{
			logger.error("签入异常", e);
			return new SigninResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public FreeResponse setFree(FreeRequest request)
	{
		try
		{
			request.check();

			return new FreeResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), this.ass
					.getSuccSqs(new AgentStatusDTO(request
							.getTenantCode(), request.getWorkNo(), AgentInfo.AgentStatus.FREE, request.getIsForce(), null, null, null)));
		}
		catch (Exception e)
		{
			logger.error("示闲异常", e);
			return new FreeResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public BusyResponse setBusy(BusyRequest request)
	{
		try
		{
			request.check();

			return new BusyResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), this.ass
					.getSuccSqs(new AgentStatusDTO(request
							.getTenantCode(), request.getWorkNo(), AgentInfo.AgentStatus.BUSY, request.getIsForce(), null, request.getBusyType(), null)));
		}
		catch (Exception e)
		{
			logger.error("示忙异常", e);
			return new BusyResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public SignoutResponse signOut(SignoutRequest request)
	{
		try
		{
			request.check();
			if (!"Y".equals(request.getIsForce())) {
				checkSessions(request.getTenantCode(), request.getWorkNo());
			}
			return new SignoutResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), this.ass
					.getSuccSqs(new AgentStatusDTO(request
							.getTenantCode(), request.getWorkNo(), AgentInfo.AgentStatus.LOGIN, request.getIsForce(), null, "signout", null)));
		}
		catch (Exception e)
		{
			logger.error("签出异常", e);
			return new SignoutResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public BaseResponse logout(LogoutRequest request)
	{
		try
		{
			request.check();
			this.ass.updateAgentInfo(new AgentStatusDTO(request
					.getTenantCode(), request.getWorkNo(), AgentInfo.AgentStatus.OFFLINE, null, null, null, null));
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}
		catch (Exception e)
		{
			logger.error("登出异常", e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	public AgentInfoResponse agentInfo(AgentInfoRequest request)
	{
		try
		{
			request.check();

			List<String> agnetKeys = new ArrayList<>(getPatternKeys(request));
			List<Map<Object,Object>> agents = redisDao.pipelineHmget(new HashSet<>(agnetKeys));
			AgentInfoFilter filter = new AgentInfoFilter(request);
			CollectionUtils.filter(agents, filter);

			return new AgentInfoResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), agents, filter
					.getCounter());
		}
		catch (Exception e)
		{
			logger.error("查询坐席实时信息异常", e);
			return new AgentInfoResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null, null);
		}

	}

	public AgentSingleResponse agentInfo(AgentSingleRequest request)
	{
		try
		{
			request.check();
			Map<Object, Object> map = this.redisDao.mapGet(AgentUtils.getAgentBaseKey(request.getTenantCode(), request.getWorkNo()));
			if (map.isEmpty()) {
				throw new MultiMediaException("1002", String.format("找不到坐席信息:租户%s 工号%s", new Object[] { request.getTenantCode(), request.getWorkNo() }));
			}
			AgentInfo info = new AgentInfo();
			try
			{
				BeanUtils.copyProperties(info, map);
			}
			catch (Exception e)
			{
				logger.error("坐席信息格式转换异常:{}", e.getMessage());
			}
			return new AgentSingleResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), info);
		}
		catch (Exception e)
		{
			logger.error("查询坐席信息异常", e);
			return new AgentSingleResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	private BaseResponse transferTask(TransferRequest request)
	{
		try
		{
			request.check();
			SessionInfo session = this.sessionService.get(request.getSessionId(), request.getTenantCode());
			if (SessionType.MULTI.getName().equals(session.getSessionType())) {
				throw new MultiMediaException("1015");
			}
			String beforeSq = session.getSkillQueue();
			String beforeWn = session.getWorkNos();
			if (!this.redisDao.exist(AgentUtils.getAgentSkillKey(request.getTenantCode(), beforeSq, beforeWn))) {
				throw new MultiMediaException("1016");
			}
			TransferLog log = new TransferLog(request.getTenantCode(), beforeSq, beforeWn, null, null, request.getType(), request.getRemark());
			this.allocateService.transferMessage(session, request, log);

			this.logDao.insertTransferLog(log);

			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}
		catch (Exception e)
		{
			logger.error("转发异常", e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	public BaseResponse uploadLocation(AgentCoordinateRequest request)
	{
		try
		{
			request.check();

			JSONObject location = new JSONObject();
			location.put("X", request.getX());
			location.put("Y", request.getY());
			location.put("label", request.getLabel());


			String key = AgentUtils.getAgentBaseKey(request.getTenantCode(), request.getWorkNo());
			if (!this.redisDao.exist(key)) {
				throw new MultiMediaException("1002");
			}
			this.redisDao.mapPut(key, "location", location);

			this.logDao.insertAgentLocation(request);

			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}
		catch (Exception e)
		{
			logger.error("坐标上传异常", e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	public BaseResponse scanLogin(cn.sh.ideal.as.req.ScanLoginRequest request)
	{
		try
		{
			request.check();

			request.setClientType(StringUtils.isEmpty(request.getClientType()) ? "0" : request.getClientType());
			if ("1".equals(request.getType())) {
				if ((StringUtils.isEmpty(request.getVerifyCode())) ||
						(!request.getVerifyCode().equals(this.redisDao.readValue("LVCODE@@" + request.getWorkNo() + request.getTenantCode())))) {
					return new BaseResponse(RespCode.FAIL.getResultCode(), "验证码验证失败");
				}
			}
			String key = AgentUtils.getAgentBaseKey(request.getTenantCode(), request.getWorkNo());
			if (!this.redisDao.exist(key)) {
				throw new MultiMediaException("1002");
			}
			this.redisDao.mapPut(key, "clientType", request.getClientType());


			updateIps(request.getTenantCode(), request.getWorkNo(), "add",
					StringUtils.isEmpty(request.getWebsocketIPNew()) ? AgentUtils.visitIp(null, null) : request.getWebsocketIPNew());

			cn.sh.ideal.si.req.ScanLoginRequest slr = new cn.sh.ideal.si.req.ScanLoginRequest();
			BeanUtils.copyProperties(slr, request);

			MessageService service = (MessageService)this.rs.getSiService(request.getTenantCode(), request.getWorkNo(),
					ConfigUtils.getProperty("dubbo.si.version"), MessageService.class);

			service.scanLogin(slr);

			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}
		catch (Exception e)
		{
			logger.error("扫码登录异常", e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	public SrvpResponse srvp(SrvpRequest request)
	{
		try
		{
			request.check();
			Map<String, Object> rsMap = new HashMap();

			AgentInfoRequest agentDto = new AgentInfoRequest();
			agentDto.setTenantCode(request.getTenantCode());
			agentDto.setWorkNo(request.getWorkNo());
			agentDto.setMerge("1");

			AgentInfoResponse resp = agentInfo(agentDto);
			List<Map<Object, Object>> agentList = resp.getData();
			if (CollectionUtils.isEmpty(agentList)) {
				throw new RuntimeException("没有坐席在线");
			}
			List<AgentSrvp> rsList = new ArrayList();
			for (Map<Object, Object> ag : agentList)
			{
				String tenantCode = (String)ag.get("tenantCode");
				String skillQueue = (String)ag.get("skillQueue");
				String workNo = (String)ag.get("workNo");
				AgentSrvp srvp = new AgentSrvp();
				srvp.setTenantCode(tenantCode);
				srvp.setWorkNo(workNo);
				srvp.setBusiness((String)ag.get("businessArr"));
				srvp.setChannel((String)ag.get("channelArr"));
				srvp.setAgentName((String)ag.get("userName"));
				srvp.setStatus((String)ag.get("status"));
				long personNum = 0L;
				for (String s : skillQueue.split(","))
				{
					long total = this.queueService.queueSize(s);
					personNum += total;
				}
				srvp.setPersonNum(personNum);


				Object param = new HashMap();
				((HashMap)param).put("workNo", workNo);
				((HashMap)param).put("tenantCode", tenantCode);

				srvp.setSentiment(this.agentDao.querySessionCount((Map)param));


				JSONObject jsParam = new JSONObject();
				jsParam.put("workNo", workNo);
				jsParam.put("tenantCode", tenantCode);

				SysParam satisfy = this.sysService.getSysParam("CMS_SATISFY");
				String cmsSatisfy = satisfy.getParamValue();


				String res = NetUtil.send(cmsSatisfy, "POST", jsParam.toJSONString());
				JSONObject reObj = JSONObject.parseObject(res);
				if ("0".equals(reObj.get("resultCode")))
				{
					JSONArray jsonArray = (JSONArray)reObj.get("data");
					List<Score> list = JSON.parseArray(jsonArray.toJSONString(), Score.class);
					Integer sum = Integer.valueOf(0);
					String avgScore = "0";
					for (Score c : list) {
						sum = Integer.valueOf(sum.intValue() + c.getSatified());
					}
					if (list.size() > 0) {
						avgScore = new BigDecimal(sum.intValue()).divide(new BigDecimal(list.size()), 2, 4).toString();
					}
					srvp.setSatisfy(avgScore);
				}
				SysParam sys = this.sysService.getSysParam("CMS_COMMENTS");
				String cmsComments = sys.getParamValue();
				logger.error("查询评论接口调用地址:" + cmsComments);
				String comm = NetUtil.send(cmsComments, "POST", jsParam.toJSONString().replace("workNo", "agentWorkNo"));
				if (StringUtils.isEmpty(comm))
				{
					srvp.setComments(0L);
				}
				else
				{
					JSONObject comment = JSONObject.parseObject(comm);
					JSONObject data = (JSONObject)comment.get("data");
					Object total = data.get("total");
					long commNum = Long.parseLong(total.toString());
					srvp.setComments(commNum);
				}
				rsList.add(srvp);
			}
			rsMap.put("dataList", rsList);
			rsMap.put("counter", resp.getCounter());

			return new SrvpResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), rsMap);
		}
		catch (Exception e)
		{
			logger.error("查询坐席服务信息异常", e);
			return new SrvpResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public BusinessTypeResponse getBusinessType(BusinessTypeRequest request)
	{
		try
		{
			request.check();

			Map<String, Object> rsMap = new HashMap();

			List<BusyType> list = this.agentDao.queryBusyTypeByCode(request.getTenantCode());
			rsMap.put("list", list);
			return new BusinessTypeResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(), rsMap);
		}
		catch (Exception e)
		{
			logger.error("查询业务类型异常", e);
			return new BusinessTypeResponse(RespCode.FAIL.getResultCode(), e.getMessage(), null);
		}
	}

	public BaseResponse updateVoiceAgentStatus(IvrStatusRequest request)
	{
		try
		{
			request.check();

			String key = "IVR_AGENT:" + request.getAgentId();
			this.redisDao.mapPut(key, "status", request.getStatus());
			if ("1".equals(request.getStatus())) {
				RedisLock.getRedisLock("agentvoice:" + request.getAgentId()).unlock();
			}
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}
		catch (Exception e)
		{
			logger.error("更细坐席语音状态异常", e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	private Map<Object, Object> checkDuplicateLogin(String tenantCode, String workNo, String clientId, String isForce)
	{
		boolean check = true;
		String key = AgentUtils.getAgentBaseKey(tenantCode, workNo);
		if (!this.redisDao.exist(key)) {
			logger.info("workNo : {}信息不存在重新加载", workNo);
			AgentInfoBase info = new AgentInfoBase();
			info.setTenantCode(tenantCode);
			info.setWorkNo(workNo);

			agInitService.initAgentInfo(info, true);

			//再次检查
			if (!this.redisDao.exist(key)) {
				throw new RuntimeException("坐席信息不存在");
			}
		}


		Map<Object, Object> map = this.redisDao.mapGet(key);

		String cId = (String)map.get("clientSessionId");
		String status = (String)map.get("status");

		logger.info("clientSessionId : {},status:{}", cId, status);
		if ((StringUtils.isNotEmpty(status)) && (!AgentInfo.AgentStatus.OFFLINE.getCode().equals(status)))
		{
			if ((!"Y".equals(isForce)) && ((StringUtils.isEmpty(cId)) || ((StringUtils.isNotEmpty(cId)) && (!cId.equals(clientId))))) {
				throw new MultiMediaException("1001");
			}
			if (((StringUtils.isNotEmpty(cId)) && (cId.equals(clientId))) ||
					("Y".equals(isForce))) {
				if ("Y".equals(isForce)) {
					this.allocateService.pushTip(workNo.split(","), tenantCode, "", MessageUtil.TipType.FORCE_LOGIN.name(), null, null);
				} else {
					check = false;
				}
			}
		}
		return check ? null : map;
	}

	private void updateIps(String tenantCode, String workNo, String optType, String ip)
	{
		logger.info("坐席{}地址{}", workNo, ip);

		String key = "AGENTIPS";
		String mapKey = tenantCode.concat(workNo);
		try
		{
			if ("add".equals(optType)) {
				this.redisDao.mapPut(key, mapKey, ip);
			} else {
				this.redisDao.mapRemove(key, new Object[] { mapKey });
			}
		}
		catch (Exception e)
		{
			logger.error("更新坐席注册ip信息异常", e);
		}
	}

	private void checkSessions(String tenantCode, String workNo)
	{
		Map<Object, Object> map = this.redisDao.mapGet(AgentUtils.getAgentBaseKey(tenantCode, workNo));
		if ((null != map) && (!map.isEmpty()))
		{
			Integer currSessionCount = (Integer)map.get("currSessionCount");
			if (currSessionCount.intValue() > 0) {
				throw new MultiMediaException("1005");
			}
		}
	}

	private Set<String> getPatternKeys(AgentInfoRequest agentDto)
	{
		Set<String> baseKeys = new HashSet<String>();
		for(String tenantCode : agentDto.getTenantCode().split("\\|")){
			if(StringUtils.isNotEmpty(agentDto.getWorkNo())){
				for(String workNo : agentDto.getWorkNo().split("\\|")){
					String bkey = AgentUtils.getAgentBaseKey(tenantCode, workNo);
					if(!redisDao.exist(bkey)) continue;
					baseKeys.add(bkey);
				}
			}else if(StringUtils.isNotEmpty(agentDto.getSkillQueue())){
				for(String skillQueue : agentDto.getSkillQueue().split("\\|")){
					for(String workNo : redisStr.setMembers(AgentUtils.getAgentSkiilRelationKey(skillQueue))){
						baseKeys.add(AgentUtils.getAgentBaseKey(tenantCode, workNo));
					}
				}
			}else{
				for(String workNo : redisStr.setMembers(AgentUtils.getAgentTenantRelationKey(tenantCode))){
					baseKeys.add(AgentUtils.getAgentBaseKey(tenantCode, workNo));
				}
			}
		}
		return baseKeys;
	}

	public BaseResponse transferTaskUser(TransferRequest request)
	{
		request.setType("1");
		return transferTask(request);
	}

	public BaseResponse transferTaskSkillQueue(TransferRequest request)
	{
		request.setType("2");
		return transferTask(request);
	}

	public BaseResponse batchTransferTaskUser(List<TransferRequest> request)
	{
		logger.info("批量转发,入参 : " + JSON.toJSONString(request));

		String successSessionId = "";
		try
		{
			for (TransferRequest transferRequest : request)
			{
				transferRequest.setType("1");
				BaseResponse transferTask = transferTask(transferRequest);

				logger.info("调用单次转发,返回值  : " + JSON.toJSONString(transferTask));
				if ((transferTask == null) || (transferTask.getResultCode().equals(RespCode.FAIL.getResultCode()))) {
					successSessionId = successSessionId + transferRequest.getSessionId() + ",";
				} else if ((transferTask == null) || (!transferTask.getResultCode().equals(RespCode.SUCCESS.getResultCode()))) {}
			}
			successSessionId = successSessionId.endsWith(",") ? successSessionId.substring(0, successSessionId.length() - 1) : successSessionId;
			if (StringUtils.isNotEmpty(successSessionId)) {
				logger.info("未成功的sessionId是 " + successSessionId);
			}
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), successSessionId);
		}
		catch (Exception e)
		{
			logger.error("批量转发异常");
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	public BaseResponse batchTransferTaskSkillQueue(List<TransferRequest> request)
	{
		logger.info("批量转发至技能组,入参 : " + JSON.toJSONString(request));

		String successSessionId = "";
		int successCount = 0;
		boolean flag = true;
		JSONObject mapResult = new JSONObject();
		List<String> sessionIdListSus = new ArrayList();
		try
		{
			for (TransferRequest transferRequest : request)
			{
				transferRequest.setType("2");
				BaseResponse transferTask = transferTask(transferRequest);

				logger.info("调用单次转发至技能组,返回值  : " + JSON.toJSONString(transferTask));
				try
				{
					if ("0".equals(transferTask.getResultCode())) {
						sessionIdListSus.add(transferRequest.getSessionId());
					} else {
						flag = false;
					}
					mapResult.put(transferRequest.getSessionId(), transferTask.getResultMsg());
				}
				catch (Exception e)
				{
					logger.error("", e);
				}
				if ((transferTask == null) || (transferTask.getResultCode().equals(RespCode.FAIL.getResultCode()))) {
					successSessionId = successSessionId + transferRequest.getSessionId() + ",";
				} else if ((transferTask != null) && (transferTask.getResultCode().equals(RespCode.SUCCESS.getResultCode()))) {
					successCount++;
				}
			}
			successSessionId = successSessionId.endsWith(",") ? successSessionId.substring(0, successSessionId.length() - 1) : successSessionId;
			if (StringUtils.isNotEmpty(successSessionId)) {
				logger.info("未成功的sessionId是 " + successSessionId);
			}
			return new TransferModel(flag ? RespCode.SUCCESS.getResultCode() : RespCode.FAIL.getResultCode(), mapResult.toString(), sessionIdListSus);
		}
		catch (Exception e)
		{
			logger.error("批量转发技能组异常");
			return new TransferModel(RespCode.FAIL.getResultCode(), e.getMessage(), sessionIdListSus);
		}
	}
}
