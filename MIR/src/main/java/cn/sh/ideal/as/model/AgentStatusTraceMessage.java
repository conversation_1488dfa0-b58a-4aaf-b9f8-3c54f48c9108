package cn.sh.ideal.as.model;

import java.io.Serializable;

/**
 * 坐席状态跟踪消息实体
 * 用于发送到Kafka的坐席状态变更消息
 * 格式：{工号，原始状态，开始时间，结束时间}
 */
public class AgentStatusTraceMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 坐席工号
     */
    private String workNo;

    /**
     * 原始状态
     */
    private String originalStatus;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
    
    public AgentStatusTraceMessage() {
    }

    public AgentStatusTraceMessage(String workNo, String originalStatus, String startTime, String endTime) {
        this.workNo = workNo;
        this.originalStatus = originalStatus;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getOriginalStatus() {
        return originalStatus;
    }

    public void setOriginalStatus(String originalStatus) {
        this.originalStatus = originalStatus;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    @Override
    public String toString() {
        return "AgentStatusTraceMessage{" +
                "workNo='" + workNo + '\'' +
                ", originalStatus='" + originalStatus + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }
}
