package cn.sh.ideal.as.model;

public class Score {

	private int id;
	private String sessionId;
	private String workNo;
	private int satified;
	private String getScoreTime;
	private String getRequestTime;
	private String suggest;
	private String tenantCode;
	private String status;
	
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public int getSatified() {
		return satified;
	}
	public void setSatified(int satified) {
		this.satified = satified;
	}
	public String getGetScoreTime() {
		return getScoreTime;
	}
	public void setGetScoreTime(String getScoreTime) {
		this.getScoreTime = getScoreTime;
	}
	
	public String getGetRequestTime() {
		return getRequestTime;
	}
	public void setGetRequestTime(String getRequestTime) {
		this.getRequestTime = getRequestTime;
	}
	public String getSuggest() {
		return suggest;
	}
	public void setSuggest(String suggest) {
		this.suggest = suggest;
	}
	
}
