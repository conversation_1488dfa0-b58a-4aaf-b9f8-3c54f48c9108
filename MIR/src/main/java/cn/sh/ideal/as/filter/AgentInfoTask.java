package cn.sh.ideal.as.filter;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveTask;

import org.apache.commons.beanutils.BeanUtils;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.model.AgentInfo;

public class AgentInfoTask extends RecursiveTask<List<Map<Object,Object>>>{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public static final int total = 10;
	private List<String> agentKeys;
	private String[] skillQueues;
	private RedisDao<String, Serializable> redisDao;
	private boolean merge;
	private boolean resolve;

	public AgentInfoTask(List<String> agentKeys,String[] skillQueues,boolean merge,boolean resolve,RedisDao<String, Serializable> redisDao){
		this.agentKeys = agentKeys;
		this.merge = merge;
		this.skillQueues = skillQueues;
		this.redisDao = redisDao;
		this.resolve = resolve;
	}
	
	@Override
	protected List<Map<Object,Object>> compute() {
		List<Map<Object,Object>> list=new ArrayList<>();
		List<AgentInfoTask> tasks = new ArrayList<>();
		int size = agentKeys.size();
		int batchCount = size / total;
		if(!resolve && batchCount >= 1){
			int batchNum = size % total == 0 ? total : total + 1;
			for(int i = 0;i < batchNum;i++ ){
				int from = i * batchCount;
				int to = (i+1) == batchNum ? size : (i+1) * batchCount;
				
				AgentInfoTask task = new AgentInfoTask(agentKeys.subList(from, to),skillQueues,merge,true,redisDao);
				tasks.add(task);
				task.fork();
			}
		}else{
			return merge(agentKeys, merge);
		}
		
		for(AgentInfoTask task : tasks){
			list.addAll(task.join());
		}
		
		return list;
	}
	
	private List<Map<Object,Object>> merge(List<String> keys,boolean merge){
		//List<Map<Object,Object>> agents = new ArrayList<Map<Object,Object>>();
		List<Map<Object,Object>> infos = redisDao.pipelineHmget(new HashSet<>(keys));
		/*for(Map<Object,Object> map : infos){
			if(null == map || map.isEmpty()) continue;
			//判断是否合并技能组
			String tenantCode = (String)map.get(Constants.PARAM_TENANT_CODE);
			String workNo = (String)map.get(Constants.PARAM_WORK_NO);
			String skills = (String)map.get(Constants.PARAM_SKILL_QUEUE);
			if(StringUtils.isEmpty(skills)) continue;
			
			Set<String> skillSet = new HashSet<>();
			for(String skillQueue : StringUtils.split(skills,",")){
				if(null == skillQueues || (null != skillQueue && ArrayUtils.contains(skillQueues, skillQueue)))
					skillSet.add(AgentUtils.getAgentSkillKey(tenantCode, skillQueue, workNo));
			}
			List<Map<Object,Object>> skillInfos = redisDao.pipelineHmget(skillSet);
			int totalCurrCount = 0;
			for(Map<Object,Object> smap : skillInfos){
				if(null == smap) continue;
				if(!merge){
					totalCurrCount += (Integer)smap.get(Constant.PARAM_CURRSESSION_COUNT);
				}else{
					map.putAll(smap);
					agents.add(new HashMap<>(map));
				}
			}
			
			if(merge){
				map.put(Constant.PARAM_CURRSESSION_COUNT, totalCurrCount);
				agents.add(map);
			}
		}	*/
		
		return infos;
	}
	
	public static void main(String[] args) throws IllegalAccessException, InvocationTargetException {
		Map<Object,Object> map = new HashMap<Object,Object>();
		AgentInfo info = new AgentInfo();
		info.setWorkNo("1003");
		info.setCurrSessionCount(3);
		info.setOccupancy(1.2f);
		BeanUtils.copyProperties(map,info);
		
		System.out.println(map.toString());
	}
	
}
