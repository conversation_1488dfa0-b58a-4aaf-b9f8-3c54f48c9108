//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.sh.ideal.as.service.local.impl;

import cn.sh.ideal.as.dao.AgentLogDao;
import cn.sh.ideal.as.model.AgentLog;
import cn.sh.ideal.as.model.AgentLogModel;
import cn.sh.ideal.as.model.AgentStatusDTO;
import cn.sh.ideal.as.model.AgentStatusTraceMessage;
import cn.sh.ideal.as.model.SrvServiceCenter;
import cn.sh.ideal.as.service.kafka.KafkaProducerService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.mir.service.local.AllocationService;
import cn.sh.ideal.mir.util.MessageUtil;
import cn.sh.ideal.mir.util.MessageUtil.TipType;
import cn.sh.ideal.model.AgentInfo;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.util.RedisLock;
import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("agentStatusService")
public class AgentStatusService {
    private static final Logger logger = LoggerFactory.getLogger(AgentStatusService.class);

    private static final String AGENT_STATUS_LOCK = "agent_atomic_status";
    @Autowired
    private RedisDao<String, Serializable> redisDao;
    @Autowired
    private AgentLogDao logDao;
    @Autowired
    private AllocationService allocateService;
    @Autowired
    private KafkaProducerService kafkaProducerService;

    public Map<Object, Object> updateAgentInfo(AgentStatusDTO asd) {
        RedisLock lock = RedisLock.getRedisLock("agent_atomic_status" + asd.getTenantCode() + asd.getWorkNo());
        RedisLock lock1 = RedisLock.getRedisLock("agent_init");

        Map var14;
        try {
            AgentInfo.AgentStatus status = asd.getStatus();
            Map<Object, Object> agent = null;

            while(!this.redisDao.exist(lock1.getKey()) && !lock.lock(1000L, 5)) {
            }

            String key = AgentUtils.getAgentBaseKey(asd.getTenantCode(), asd.getWorkNo());
            if (!this.redisDao.exist(key)) {
                throw new RuntimeException("不存在坐席信息");
            }

            agent = this.redisDao.mapGet(key);
            String currentTime = AgentUtils.getCurrentTime();
            Map<Object, Object> param = new HashMap();
            this.checkAgentInfo(asd, agent, currentTime, param);
            if (!StringUtils.isEmpty(asd.getClientType())) {
                param.put("clientType", asd.getClientType());
            }

            if (AgentStatus.LOGIN.getCode().equals(asd.getStatus().getCode()) && "login".equals(asd.getRemark())) {
                param.put("clientSessionId", asd.getClientId());
                if (StringUtils.isNotEmpty(asd.getServiceCenter())) {
                    SrvServiceCenter center = (SrvServiceCenter)this.redisDao.mapGetValue("SRV_SERVICE_CENTER", asd.getServiceCenter() + asd.getTenantCode());
                    if (null != center) {
                        param.put("serviceCenter", asd.getServiceCenter());
                        param.put("location", JSONObject.parse(center.getLocation()));
                    }
                }
            }

            param.put("status", status.getCode());
            param.put("statusDesc", status.name());
            param.put("isForce", asd.getIsForce());
            param.put("optTime", currentTime);
            this.redisDao.mapPutAll(key, param);
            this.pushForceTip(asd, status);
            var14 = this.redisDao.mapGet(key);
        } finally {
            lock.unlock();
        }

        return var14;
    }

    public String getSuccSqs(AgentStatusDTO asd) {
        Map<Object, Object> info = this.updateAgentInfo(asd);
        return (String)info.get("skillQueue");
    }

    private void pushForceTip(AgentStatusDTO asd, AgentInfo.AgentStatus status) {
        if ("Y".equals(asd.getIsForce())) {
            MessageUtil.TipType type = null;
            if (AgentStatus.LOGIN == status && "signout".equals(asd.getRemark())) {
                type = TipType.FORCE_SIGNOUT;
            } else if (AgentStatus.SIGNIN == status) {
                type = TipType.FORCE_SIGNIN;
            } else if (AgentStatus.FREE == status) {
                type = TipType.FORCE_FREE;
            } else if (AgentStatus.BUSY == status) {
                type = TipType.FORCE_BUSY;
            }

            if (null != type) {
                this.allocateService.pushTip(asd.getWorkNo().split(","), asd.getTenantCode(), "", type.name(), (String)null, (Object)null);
            }
        }

    }

    private void checkAgentInfo(AgentStatusDTO asd, Map<Object, Object> info, String currentTime, Map<Object, Object> param) {
        AgentLog log = new AgentLog();
        String skillQueue = (String)info.get("skillQueue");
        String queueId = StringUtils.isEmpty(skillQueue) ? "0" : skillQueue.split(",")[0];
        log.setTenantCode(asd.getTenantCode());
        log.setSkillQueue(queueId);
        log.setWorkNo(asd.getWorkNo());
        log.setIsForce(asd.getIsForce());
        HashMap<String, Object> map = new HashMap();
        map.put("id", info.get("signId"));
        map.put("endTime", currentTime);
        AgentInfo.AgentStatus ostatus = AgentStatus.valueOf((String)info.get("statusDesc"));
        switch (asd.getStatus()) {
            case OFFLINE:
                param.put("loginTime", (Object)null);
                param.put("signId", (Object)null);
                param.put("signTime", (Object)null);
                param.put("bfId", (Object)null);
                param.put("clientSessionId", (Object)null);
                param.put("busyType", (Object)null);
                this.logDao.insertAgentLog(new AgentLogModel(asd.getWorkNo(), "0", asd.getTenantCode(), (String)info.get("clientType"), queueId));
                this.clearAgentLog(true, true, true, log, currentTime);
                break;
            case LOGIN:
                if ("login".equals(asd.getRemark())) {
                    if (!"Y".equals(asd.getIsForce()) && ostatus != AgentStatus.OFFLINE) {
                        throw new MultiMediaException("1003");
                    }

                    param.put("loginTime", currentTime);
                    this.logDao.insertAgentLog(new AgentLogModel(asd.getWorkNo(), "1", asd.getTenantCode(), (String)info.get("clientType"), queueId));
                } else {
                    if (ostatus == AgentStatus.OFFLINE) {
                        throw new MultiMediaException("1003");
                    }

                    if (ostatus == AgentStatus.LOGIN) {
                        break;
                    }

                    this.logDao.updateSignLog(map);
                    param.put("signId", (Object)null);
                    param.put("busyType", (Object)null);
                    map.put("id", info.get("bfId"));
                    if (null != info.get("bfId")) {
                        if (info.get("status").equals(AgentStatus.BUSY.getCode())) {
                            this.logDao.updateBusyLog(map);
                        } else {
                            this.logDao.updateFreeLog(map);
                        }

                        param.put("bfId", (Object)null);
                    }
                }

                this.clearAgentLog(true, true, true, log, currentTime);
                break;
            case SIGNIN:
                if (ostatus == AgentStatus.OFFLINE) {
                    throw new MultiMediaException("1003");
                }

                if (ostatus != AgentStatus.SIGNIN) {
                    param.put("signTime", currentTime);
                    log.setBeginTime(currentTime);
                    this.logDao.insertSignLog(log);
                    param.put("signId", log.getId());
                    this.clearAgentLog(false, true, true, log, currentTime);
                }
                break;
            case FREE:
                if (ostatus == AgentStatus.BUSY) {
                    map.put("id", info.get("bfId"));
                    this.logDao.updateBusyLog(map);
                    param.put("bfId", (Object)null);
                } else {
                    if (ostatus == AgentStatus.FREE) {
                        break;
                    }

                    if (ostatus == AgentStatus.LOGIN || ostatus == AgentStatus.OFFLINE) {
                        throw new MultiMediaException("1003");
                    }
                }

                param.put("busyType", (Object)null);
                log.setBeginTime(currentTime);
                this.logDao.insertFreeLog(log);
                param.put("bfId", log.getId());
                this.clearAgentLog(false, false, true, log, currentTime);

                // 发送Kafka消息 - FREE状态变更成功
                if (ostatus == AgentStatus.BUSY) {
                    // 从BUSY变为FREE，发送BUSY状态结束消息
                    // 获取BUSY状态的开始时间，可能在optTime字段中
                    String startTime = (String) info.get("optTime");
                    this.sendAgentStatusTraceMessage(asd, ostatus, startTime, currentTime);
                }
                break;
            case BUSY:
                if (ostatus == AgentStatus.FREE) {
                    map.put("id", info.get("bfId"));
                    this.logDao.updateFreeLog(map);
                    param.put("bfId", (Object)null);
                } else {
                    if (ostatus == AgentStatus.BUSY) {
                        return;
                    }

                    if (ostatus == AgentStatus.LOGIN || ostatus == AgentStatus.OFFLINE) {
                        throw new MultiMediaException("1003");
                    }
                }

                log.setRemark(asd.getRemark());
                log.setBeginTime(currentTime);
                this.logDao.insertBusyLog(log);
                param.put("bfId", log.getId());
                param.put("busyType", asd.getRemark());
                this.clearAgentLog(false, true, false, log, currentTime);

                // 发送Kafka消息 - BUSY状态变更成功
                if (ostatus == AgentStatus.FREE) {
                    // 从FREE变为BUSY，发送FREE状态结束消息
                    // 获取FREE状态的开始时间，可能在optTime字段中
                    String startTime = (String) info.get("optTime");
                    this.sendAgentStatusTraceMessage(asd, ostatus, startTime, currentTime);
                }
        }

    }

    /**
     * 发送坐席状态跟踪消息到Kafka
     * 格式：{工号，原始状态，开始时间，结束时间}
     *
     * @param asd 坐席状态DTO
     * @param originalStatus 原状态
     * @param startTime 原状态开始时间
     * @param endTime 原状态结束时间（当前状态变更时间）
     */
    private void sendAgentStatusTraceMessage(AgentStatusDTO asd, AgentInfo.AgentStatus originalStatus,
                                           String startTime, String endTime) {
        try {
            AgentStatusTraceMessage message = new AgentStatusTraceMessage(
                asd.getWorkNo(),
                originalStatus.name(),
                startTime,  // 从info中获取的开始时间
                endTime  // 结束时间就是当前状态变更时间
            );

            // 异步发送，不阻塞主流程
            kafkaProducerService.sendAgentStatusTrace(message);
        } catch (Exception e) {
            // Kafka消息发送失败不应影响主业务流程，只记录日志
            System.err.println("Failed to send agent status trace message: " + e.getMessage());
        }
    }

    private void clearAgentLog(boolean csl, boolean cfl, boolean cbl, AgentLog log, String endTime) {
        log.setEndTime(endTime);
        if (csl) {
            this.logDao.clearSignLog(log);
        }

        if (cfl) {
            this.logDao.clearFreeLog(log);
        }

        if (cbl) {
            this.logDao.clearBusyLog(log);
        }

    }
}
