package cn.sh.ideal.as.model;

import cn.sh.ideal.model.AgentInfo.AgentStatus;

public class AgentStatusDTO {
	private String tenantCode;
	private String workNo;
	private AgentStatus status;
	private String isForce;
	private String clientType;
	private String remark;
	private String clientId;
	private String serviceCenter;

	public AgentStatusDTO(){}
	
	public AgentStatusDTO(String tenantCode,String workNo, AgentStatus status,
			String isForce, String clientType, String remark, String clientId) {
		super();
		this.tenantCode = tenantCode;
		this.workNo = workNo;
		this.status = status;
		this.isForce = isForce;
		this.clientType = clientType;
		this.remark = remark;
		this.clientId = clientId;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public AgentStatus getStatus() {
		return status;
	}
	public void setStatus(AgentStatus status) {
		this.status = status;
	}
	public String getIsForce() {
		return isForce;
	}
	public void setIsForce(String isForce) {
		this.isForce = isForce;
	}
	public String getClientType() {
		return clientType;
	}
	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getServiceCenter() {
		return serviceCenter;
	}

	public void setServiceCenter(String serviceCenter) {
		this.serviceCenter = serviceCenter;
	}
}
