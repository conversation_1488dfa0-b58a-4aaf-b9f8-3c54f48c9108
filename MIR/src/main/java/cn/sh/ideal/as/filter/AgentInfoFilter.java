package cn.sh.ideal.as.filter;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import cn.sh.ideal.as.req.AgentInfoRequest;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.util.Constants;

/**
 * 坐席信息过滤器
 * 
 * <AUTHOR>
 * 2015-04-22
 *
 * @since 1.1.8
 */
public class AgentInfoFilter implements Predicate{
	//过滤参数对象
	private AgentInfoRequest agentDto;
	//状态计数器
	private Map<String,Integer> counter;

	AgentInfoFilter(){}

	public AgentInfoFilter(AgentInfoRequest dto){
		this.agentDto = dto;
		counter  = new HashMap<String,Integer>();
		counter.put(AgentStatus.OFFLINE.name(), 0);
		counter.put(AgentStatus.LOGIN.name(), 0);
		counter.put(AgentStatus.SIGNIN.name(), 0);
		counter.put(AgentStatus.FREE.name(), 0);
		counter.put(AgentStatus.BUSY.name(), 0);
	}
	@SuppressWarnings("unchecked")
	@Override
	public boolean evaluate(Object arg0) {
		if(arg0 == null) return false;
		boolean flag = true;

		Map<Object,Object> info = (Map<Object,Object>)arg0;
		String tenantCode = (String)info.get("tenantCode");
		String status = (String)info.get("status");
		String workNo = (String)info.get("workNo");
		String skillQueue = (String)info.get("skillQueue");

		if(!tenantCode.equals(agentDto.getTenantCode())){
			return false;
		}

		if(StringUtils.isNotEmpty(agentDto.getWorkNo()) && !workNo.equals(agentDto.getWorkNo())){
			return false;
		}

		if(StringUtils.isNotEmpty(agentDto.getSkillQueue()) && !skillQueue.equals(agentDto.getSkillQueue())){
			return false;
		}

		if(StringUtils.isNotEmpty(agentDto.getStatus()) && !ArrayUtils.contains(agentDto.getStatus().split("\\|"),status)){
			return false;
		}

		//反射遍历属性并对比匹配
		/*Field[] fields = agentDto.getClass().getDeclaredFields();
		for(Field field : fields){
			String name = field.getName();
			if(name.equals("serialVersionUID") || name.equals("merge")) continue;
			try {
				String methodName = "get".concat(name.substring(0, 1).toUpperCase()).concat(name.substring(1));
				Method dtoMethod = agentDto.getClass().getMethod(methodName);

				String dtoValue = (String) dtoMethod.invoke(agentDto);
				String infoValue = (String) info.get(name);//infoMethod.invoke(info);

				if(StringUtils.isNotEmpty(dtoValue)){
					if(name.equals(Constants.PARAM_WORK_NO) || name.equals(Constants.PARAM_STATUS) || name.equals(Constants.PARAM_TENANT_CODE)){
						flag = ArrayUtils.contains(dtoValue.split("\\|"),infoValue);
					}else if(name.equals(Constants.PARAM_SKILL_QUEUE) || name.equals(Constants.PARAM_CHANNEL_ARR) || name.equals(Constants.PARAM_BUSINESS_ARR)){
						for(String channel : dtoValue.split("\\|")){
							if(ArrayUtils.contains(infoValue.split(","),channel)){
								flag = true;break;
							}else{
								flag = false;
							}
						}
					}else{
						flag = dtoValue.equals(infoValue);
					}
				}else{
					flag = true;
				}

				if(flag) continue;else break;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}*/

		String desc = (String)info.get(Constant.PARAM_STATUS_DESC);
		int count = counter.get(desc);
		counter.put(desc,  ++ count);

		//统计数量
		/*if(flag){

		}*/

		return flag;
	}
	public Map<String,Integer> getCounter() {
		return counter;
	}
}
