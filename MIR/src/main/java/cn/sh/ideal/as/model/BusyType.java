package cn.sh.ideal.as.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class BusyType implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String businessCode;
	private String businessName;
	private BigDecimal id;
	
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public String getBusinessName() {
		return businessName;
	}
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}
	public BigDecimal getId() {
		return id;
	}
	public void setId(BigDecimal id) {
		this.id = id;
	}
}
