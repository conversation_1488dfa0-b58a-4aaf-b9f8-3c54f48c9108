package cn.sh.ideal.as.dao;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.as.model.AgentIVR;
import cn.sh.ideal.as.model.AgentInfoBase;
import cn.sh.ideal.as.model.BusyType;
import cn.sh.ideal.as.model.SkillQueueWorkNo;
import cn.sh.ideal.as.model.SrvServiceCenter;
import cn.sh.ideal.as.req.LoginRequest;
/**
 * 坐席信息Dao
 *
 * <AUTHOR>
 * @since v1.1.8
 *
 */
public interface AgentInfoDao {

	/**
	 * 获取坐席的详细信息
	 *
	 * @param agent
	 * @return
	 */
	public List<AgentInfoBase> queryAgentInfo(AgentInfoBase base);

	/**
	 * 根据账号密码查询坐席信息
	 *
	 * @param params
	 * @return
	 */
	public AgentInfoBase queryUserByAccount(LoginRequest params);

	/**
	 * 人气度查询
	 * @param params
	 */
	public Integer querySessionCount(Map<String,String> params);


	/**业务类型查询*/

	public List<BusyType> queryBusyTypeByCode(String tenantCode);

	public List<AgentIVR> queryAgentIVR();

	/**
	 * 获取服务中心的详细信息
	 *
	 * @param agent
	 * @return
	 */
	public List<SrvServiceCenter> querySrvServiceCenter();

	public List<String> queryTenantWorkNo(String tenantCode);

	public List<SkillQueueWorkNo> getSkillWorkNo();

	public void updateAgentState(Map<String,String> params);
}
