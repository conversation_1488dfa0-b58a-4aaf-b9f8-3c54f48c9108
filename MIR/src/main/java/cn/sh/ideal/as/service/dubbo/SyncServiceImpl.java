package cn.sh.ideal.as.service.dubbo;

import java.util.HashSet;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.as.model.AgentInfoBase;
import cn.sh.ideal.as.req.ResetAgentRequest;
import cn.sh.ideal.as.req.SyncAgentRequest;
import cn.sh.ideal.as.req.SyncSkillQueueRequest;
import cn.sh.ideal.as.req.SyncSkillTypeRequest;
import cn.sh.ideal.as.service.SyncService;
import cn.sh.ideal.as.service.local.AgInitService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.dao.TenantSkillQueueDao;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.service.local.SortService;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.util.Constants;
/**
 * 坐席同步service
 * <AUTHOR>
 *
 */
@Service("syncService")
public class SyncServiceImpl implements SyncService{
	private static Logger logger = LoggerFactory.getLogger(SyncServiceImpl.class);

	@Autowired
	private AgInitService agInitService;
	
	@Autowired
	private SkillQueueService queueService;
	
	@Autowired
	private SysInitService initService;
	
	@Autowired
	private SortService sortService;
	
	@Autowired
	private RedisDao<String, String> redisDao;
	
	@Autowired
	private TenantSkillQueueDao tsDao;

	@Override
	public BaseResponse syncAgentInfo(SyncAgentRequest request) {
		try{
			request.check();
			
			if(Constants.HANDLE_DEL.equals(request.getType())){
				this.delAgentInfo(request.getTenantCode(),request.getWorkNo());
			}else{
				this.editAgentInfo(request.getTenantCode(),request.getWorkNo());
			}
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("同步坐席信息",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	@Override
	public BaseResponse syncSkillQueue(SyncSkillQueueRequest request) {
		try{
			request.check();
			
			TenantSkillQueue tsq = tsDao.load(request.getSkillQueue());
			if(tsq == null){
				throw new MultiMediaException("1017");
			}
			
			SkillQueue sq = initService.skillQueue(tsq);
			if(Constants.HANDLE_DEL.equals(request.getType())){
				delSkillQueue(request.getTenantCode(),request.getSkillQueue());
			}else{
				queueService.editSkillInfo(sq);
			}
			sortService.clearPrority(request.getTenantCode());
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("同步技能组信息异常",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	@Override
	public BaseResponse syncSkillType(SyncSkillTypeRequest request) {
		try{
			request.check();
			
			initService.initSkillType();
			
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("同步技能组类型信息异常",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	@Override
	public BaseResponse resetAgent(ResetAgentRequest request) {
		try{
			request.check();
			
			AgentInfoBase info = new AgentInfoBase();
			info.setTenantCode(request.getTenantCode());
			info.setWorkNo(request.getWorkNo());
			
			agInitService.initAgentInfo(info,true);
			
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("重置坐席信息异常",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	@Override
	public BaseResponse syncSysParam() {
		try{
			initService.initSysParam();
			
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("同步系统参数异常",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}

	@Override
	public BaseResponse reload() {
		try{
			agInitService.init();
			
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("reload异常",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}
	
	private void editAgentInfo(String tenantCode,String workNo){
		AgentInfoBase info = new AgentInfoBase();
		info.setTenantCode(tenantCode);
		info.setWorkNo(workNo);
		
		agInitService.initAgentInfo(info, false);
	}
	
	private void delAgentInfo(String tenantCode,String workNo){
		String key = AgentUtils.getAgentBaseKey(tenantCode, workNo);
		if(redisDao.exist(key)){
			String status = (String)redisDao.mapGetValue(key, Constants.PARAM_STATUS);
			if(status != null && !status.equals(AgentStatus.OFFLINE.getCode())){
				throw new MultiMediaException("1007");
			}
			Set<String> keys = new HashSet<>();
			keys.add(key);
			String skills = (String)redisDao.mapGetValue(key, Constants.PARAM_SKILL_QUEUE);
			
			if(StringUtils.isNotEmpty(skills)){
				for(String skillQueue : skills.split(",")){
					keys.add(AgentUtils.getAgentSkillKey(tenantCode, skillQueue, workNo));
				}
			}
			
			redisDao.pipelineDel(keys);
			redisDao.setRemove(AgentUtils.getAgentTenantRelationKey(tenantCode), workNo);
		}
	}
	
	
	private void delSkillQueue(String tenantCode,String queueId){
		queueService.deleteSkillQueue(tenantCode,queueId);
		Set<String> keys = new HashSet<>();
		String queueKey = AgentUtils.getAgentSkiilRelationKey(queueId);
		keys.add(queueKey);
		
		Set<String> workNos = redisDao.setMembers(queueKey);
		if(null != workNos){
			for(String workNo : workNos)
			keys.add(AgentUtils.getAgentSkillKey(tenantCode, queueId, workNo));
		}
		
		redisDao.pipelineDel(keys);
	}

}
