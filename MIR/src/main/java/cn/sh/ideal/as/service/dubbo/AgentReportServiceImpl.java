package cn.sh.ideal.as.service.dubbo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.as.dao.AgReportDao;
import cn.sh.ideal.as.req.AgentBusiAcceptanceRequest;
import cn.sh.ideal.as.req.AgentInfoRequest;
import cn.sh.ideal.as.req.AgentSatisfiedRequest;
import cn.sh.ideal.as.req.AgentServiceRequest;
import cn.sh.ideal.as.resp.AgentBusiAcceptanceResponse;
import cn.sh.ideal.as.resp.AgentInfoResponse;
import cn.sh.ideal.as.resp.AgentSatisfiedResponse;
import cn.sh.ideal.as.resp.AgentServiceResponse;
import cn.sh.ideal.as.resp.entity.AgentSatisfactionReportModel;
import cn.sh.ideal.as.service.AgentReportService;
import cn.sh.ideal.as.service.AgentService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.mir.resp.code.RespCode;
import cn.sh.ideal.mir.service.local.SessionService;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.SessionData;
/**
 * 坐席数据报表相关服务
 * <AUTHOR>
 *
 */
@Service("agentReportService")
public class AgentReportServiceImpl implements AgentReportService{
	private static Logger logger = LoggerFactory.getLogger(AgentReportServiceImpl.class);
	
	@Autowired
	private AgentService agentService;
	
	@Autowired
	private SessionService sessionService;
	
	@SuppressWarnings("rawtypes")
	@Autowired
	private AgReportDao agReportDao;

	@Override
	public AgentInfoResponse agentStateReport(AgentInfoRequest request) {
		request.setSkillQueue("0".equals(request.getSkillQueue()) ? null : request.getSkillQueue());
		request.setChannelArr("0".equals(request.getChannelArr()) ? null : request.getChannelArr());
		request.setWorkNo("0".equals(request.getWorkNo()) ? null : request.getWorkNo());
		return agentService.agentInfo(request);
	}

	@Override
	public AgentBusiAcceptanceResponse agentBusinessAcceptanceReport(AgentBusiAcceptanceRequest request) {
		/*
		try{
			request.check();
			
			List<AgentReportModel> agentList=(List<AgentReportModel>)agReportDao.queryAgentList(request);
			AgentInfoRequest agentDTO=new AgentInfoRequest();
			agentDTO.setTenantCode(request.getTenantCode());
			AgentInfoResponse infos = agentService.agentInfo(agentDTO);
			if(RespCode.SUCCESS.getResultCode().equals(infos.getResultCode())){
				List<AgentInfo> list=infos.getData();
				for(AgentReportModel agent:agentList){
					for(AgentInfo info:list){
						if(agent.getWorkNo().equals(info.getWorkNo())){
							agent.setState(info.getStatus());
						}
					}
				}
			}
			return new AgentBusiAcceptanceResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(),agentList);
		}catch(Exception e){
			logger.error("统计坐席服务数量及服务时长异常:",e);
			return new AgentBusiAcceptanceResponse(RespCode.FAIL.getResultCode(), e.getMessage(),null);
		}
		*/
		
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public AgentSatisfiedResponse agentSatisfiedReport(AgentSatisfiedRequest request) {
		try{
			request.check();
			
			List<AgentSatisfactionReportModel> agentList=(List<AgentSatisfactionReportModel>)agReportDao.queryAgentSatisfactionList(request);
			return new AgentSatisfiedResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(),agentList);
		}catch(Exception e){
			logger.error("统计坐满意度异常:",e);
			return new AgentSatisfiedResponse(RespCode.FAIL.getResultCode(), e.getMessage(),null);
		}
	}

	@Override
	public AgentServiceResponse agentServiceReport(AgentServiceRequest request) {
		try{
			request.check();
			
			SessionData data = new SessionData();
			data.setTenantCode(request.getTenantCode());
			data.setWorkNo(request.getWorkNo());
			data.setBeginDate(AgentUtils.getCurrentTime("yyyy-MM-dd").concat(" 00:00"));
			data.setEndDate(AgentUtils.getCurrentTime("yyyy-MM-dd HH:mm"));
			
			int count = sessionService.getSessionCount(data);
			Map<String,Object> result = new HashMap<String,Object>();
			
			result.put(Constant.PARAM_SERVICE_TIME, agReportDao.queryServiceDuration(request));
			result.put(Constant.PARAM_SESSION_COUNT, count);
			result.put(Constant.PARAM_BUSYTYPE_COUNT, agReportDao.queryBusyTimes(request));
			
			return new AgentServiceResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg(),result);
		}catch(Exception e){
			logger.error("查询坐席服务信息异常:",e);
			return new AgentServiceResponse(RespCode.FAIL.getResultCode(), e.getMessage(),null);
		}
	}

}
