package cn.sh.ideal.as.model;

import java.io.Serializable;
/**
 * 容联云坐席账号配置
 * <AUTHOR>
 *
 */
public class AgentIVR implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private int id;
	private String tenantCode;
	private String workNo;
	private String agentId;
	private String subAccount;
	private String subPassword;
	private String voip;
	private String voipPassword;
	private String status;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getAgentId() {
		return agentId;
	}
	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	public String getSubAccount() {
		return subAccount;
	}
	public void setSubAccount(String subAccount) {
		this.subAccount = subAccount;
	}
	public String getSubPassword() {
		return subPassword;
	}
	public void setSubPassword(String subPassword) {
		this.subPassword = subPassword;
	}
	public String getVoip() {
		return voip;
	}
	public void setVoip(String voip) {
		this.voip = voip;
	}
	public String getVoipPassword() {
		return voipPassword;
	}
	public void setVoipPassword(String voipPassword) {
		this.voipPassword = voipPassword;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}
