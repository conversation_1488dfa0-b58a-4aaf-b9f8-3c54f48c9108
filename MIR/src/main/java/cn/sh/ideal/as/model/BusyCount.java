package cn.sh.ideal.as.model;

import java.io.Serializable;

public class BusyCount implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String tenantCode;
	private String workNo;
	private String busyType;
	private String busyTime;
	private String busyCount;
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getBusyType() {
		return busyType;
	}
	public void setBusyType(String busyType) {
		this.busyType = busyType;
	}
	public String getBusyCount() {
		return busyCount;
	}
	public void setBusyCount(String busyCount) {
		this.busyCount = busyCount;
	}
	public String getBusyTime() {
		return busyTime;
	}
	public void setBusyTime(String busyTime) {
		this.busyTime = busyTime;
	}
}
