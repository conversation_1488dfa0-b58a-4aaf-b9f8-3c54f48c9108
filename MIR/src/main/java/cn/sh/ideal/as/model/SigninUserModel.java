package cn.sh.ideal.as.model;

import java.io.Serializable;

/**
 * 签入对象,签入对象的信息
 * 
 * <AUTHOR>
 * @version 1.13,12/10/14
 * 
 */
public class SigninUserModel implements Serializable,Cloneable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	// 签入id
	private String signinId;

	// 登录名字
	private String workNo;

	// 姓名
	private String workName;
	
	private String tel;

	// 签入时间
	private String signinTime;
	
	// 最新活动时间  yyyy-mm-dd HH:mm:ss
	private String localTime;

	// 签出时间
	private String signoutTime;
	
	//示忙示闲时间
	private String busyTime;

	// 技能组id
	private String skillQueue;
	
	private String agentType;
	
	//技能组名字
	private String skillQueueName;

	// 状态示忙、示闲 1示忙，2示闲
	private String status;
	
	//示闲状态  2示闲 4强制示闲
	private String freeState;
	
	//示忙状态 1示忙3强制示忙
	private String busyState;
	
	//签入状态 1签入，2签出
	private String signinState;

	// 签入类型,签入或强制签入
	private String signinType;
	
	//签出类型，签出或者强制签出
	private String signOutType;

	// 渠道
	private String channelArr;

	// 最大会话数
	private String userMaxSessionCount;

	// 当前会话数
	private int localSessionCount;
	
	//租户ID
	private String tenantId;
	
	//示忙原因
	private String busyTypeId;
	
	//avaya状态信息
	private String avayaState;
	
	//技能组分类
	private String skillTypeCode;
	//技能组分类集合
	private String skillTypeNum;
	//技能组分类名称
	private String skillTypeName;
	private Object location;
	
	private String clientType;
	
	public String getSigninId() {
		return signinId;
	}

	public void setSigninId(String signinId) {
		this.signinId = signinId;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getWorkName() {
		return workName;
	}

	public void setWorkName(String workName) {
		this.workName = workName;
	}

	public String getSigninTime() {
		return signinTime;
	}

	public void setSigninTime(String signinTime) {
		this.signinTime = signinTime;
	}

	public String getSignoutTime() {
		return signoutTime;
	}

	public void setSignoutTime(String signoutTime) {
		this.signoutTime = signoutTime;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}
	

	public String getSkillQueueName() {
		return skillQueueName;
	}

	public void setSkillQueueName(String skillQueueName) {
		this.skillQueueName = skillQueueName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getSigninType() {
		return signinType;
	}

	public void setSigninType(String signinType) {
		this.signinType = signinType;
	}

	public String getChannelArr() {
		return channelArr;
	}

	public void setChannelArr(String channelArr) {
		this.channelArr = channelArr;
	}

	public int getLocalSessionCount() {
		return localSessionCount;
	}

	public void setLocalSessionCount(int localSessionCount) {
		this.localSessionCount = localSessionCount;
	}
	
	public String getSignOutType() {
		return signOutType;
	}

	public void setSignOutType(String signOutType) {
		this.signOutType = signOutType;
	}

	public String getSigninState() {
		return signinState;
	}

	public void setSigninState(String signinState) {
		this.signinState = signinState;
	}
	
	public String getFreeState() {
		return freeState;
	}

	public void setFreeState(String freeState) {
		this.freeState = freeState;
	}

	public String getBusyState() {
		return busyState;
	}

	public void setBusyState(String busyState) {
		this.busyState = busyState;
	}
	
	public String getBusyTime() {
		return busyTime;
	}

	public void setBusyTime(String busyTime) {
		this.busyTime = busyTime;
	}
	
	public String getBusyTypeId() {
		return busyTypeId;
	}

	public void setBusyTypeId(String busyTypeId) {
		this.busyTypeId = busyTypeId;
	}

	public String getLocalTime() {
		return localTime;
	}

	public void setLocalTime(String localTime) {
		this.localTime = localTime;
	}
	
	public String getAvayaState() {
		return avayaState;
	}

	public void setAvayaState(String avayaState) {
		this.avayaState = avayaState;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((workNo == null) ? 0 : workNo.hashCode());
		return result;
	}
	
	

	
	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SigninUserModel other = (SigninUserModel) obj;
		if (workNo == null) {
			if (other.workNo != null)
				return false;
		} else if (!workNo.equals(other.workNo))
			return false;
		return true;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		SigninUserModel p_sum = null;
		p_sum = (SigninUserModel) super.clone();
		return p_sum;
	}

	public String getSkillTypeCode() {
		return skillTypeCode;
	}

	public void setSkillTypeCode(String skillTypeCode) {
		this.skillTypeCode = skillTypeCode;
	}

	public String getUserMaxSessionCount() {
		return userMaxSessionCount;
	}

	public void setUserMaxSessionCount(String userMaxSessionCount) {
		this.userMaxSessionCount = userMaxSessionCount;
	}

	public String getSkillTypeNum() {
		return skillTypeNum;
	}

	public void setSkillTypeNum(String skillTypeNum) {
		this.skillTypeNum = skillTypeNum;
	}

	public String getSkillTypeName() {
		return skillTypeName;
	}

	public void setSkillTypeName(String skillTypeName) {
		this.skillTypeName = skillTypeName;
	}

	public Object getLocation() {
		return location;
	}

	public void setLocation(Object location) {
		this.location = location;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getAgentType() {
		return agentType;
	}

	public void setAgentType(String agentType) {
		this.agentType = agentType;
	}

	public String getClientType() {
		return clientType;
	}

	public void setClientType(String clientType) {
		this.clientType = clientType;
	}

	/*public String getX() {
		return x;
	}

	public void setX(String x) {
		this.x = x;
	}

	public String getY() {
		return y;
	}

	public void setY(String y) {
		this.y = y;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}*/


}
