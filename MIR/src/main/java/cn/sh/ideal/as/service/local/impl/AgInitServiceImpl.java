package cn.sh.ideal.as.service.local.impl;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.as.dao.AgentInfoDao;
import cn.sh.ideal.as.model.AgentInfoBase;
import cn.sh.ideal.as.model.AgentInfoSkill;
import cn.sh.ideal.as.model.SkillQueueWorkNo;
import cn.sh.ideal.as.model.SrvServiceCenter;
import cn.sh.ideal.as.service.local.AgInitService;
import cn.sh.ideal.as.util.AgentUtils;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.mir.dao.SessionDao;
import cn.sh.ideal.mir.service.local.SkillQueueService;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.util.RedisLock;
/**
 * 坐席信息初始化Service
 * 
 * <AUTHOR>
 * 2015-04-22
 * 
 * @since 1.1.8
 */
@Service("agInitService")
public class AgInitServiceImpl implements AgInitService{
	private static final Logger logger = LoggerFactory.getLogger(AgInitServiceImpl.class);

	public static final String AGENT_INIT = "agent_init";

	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	@Autowired
	private AgentInfoDao agentDao;
	
	
	@Autowired
	private SessionDao sessionDao;
	
	@Autowired
	private SkillQueueService queueService;
	
	@PostConstruct
	public void init(){
		logger.info("系统初始化开始……");
		queueService.init();
		AgentInfoBase base = new AgentInfoBase();
		initAgentInfo(base,false);
		
		Set<String> relationKeys = getRelationKey();
		
		initTenantWorkNo(relationKeys);
		initQueueWorkNo(relationKeys);
		
		redisDao.pipelineDel(relationKeys);
		logger.info("系统初始化完成");
	}

	@Override
	public void initAgentInfo(AgentInfoBase base,final boolean reset) {
		logger.info("初始化坐席信息……");
		RedisLock lock = RedisLock.getRedisLock(AgInitServiceImpl.AGENT_INIT);
		ExecutorService executor = null;
		try{
			List<AgentInfoBase> agents = agentDao.queryAgentInfo(base);
			final Set<String> keys = getAgentKey(base);
			
			while(!lock.lock());
			final CountDownLatch latch = new CountDownLatch(agents.size());

			executor = Executors.newFixedThreadPool(10);

			for(final AgentInfoBase agent : agents){
				executor.execute(new Runnable() {
					public void run() {
						initAgent(agent, reset, keys, latch);
					}
				});
			}
			
			latch.await();
			if(CollectionUtils.isNotEmpty(keys)){
				redisDao.pipelineDel(keys);
			}
			
		}catch(Exception e){
			logger.error("初始化坐席信息异常!",e);
		}finally{
			lock.unlock();
			if(null != executor){
				executor.shutdown();
			}
		}
	}
	
	private void initAgent(AgentInfoBase base,boolean reset,final Set<String> keys,CountDownLatch latch){
		try{
			Map<String,Map<Object,Object>> nmaps = new HashMap<>();
			if(StringUtils.isEmpty(base.getSkillQueue())) return;
			
			//JSONObject skillNum = StringUtils.isEmpty(base.getSkillTypeNum()) ? new JSONObject() :
					//JSONObject.parseObject(base.getSkillTypeNum());
			
			Set<String> queueIdSet = new HashSet<>();
			Set<String> queueNameSet = new HashSet<>();
			Set<String> channelSet = new HashSet<>();
			Set<String> businessSet = new HashSet<>();
			//int totolMaxSessionCount = 0;
			//处理多技能组问题
			for(String queueId : base.getSkillQueue().split(",")){
				SkillQueue sq =  queueService.getSkillInfo(base.getTenantCode(),queueId);
				if(null == sq) continue;
				TenantSkillQueue tsq = sq.getTenantSkillQueue();
				AgentInfoSkill ais = new AgentInfoSkill();
				ais.setSkillQueue(queueId);
				queueIdSet.add(queueId);
				queueNameSet.add(tsq.getQueueName());
				if(StringUtils.isNotEmpty(tsq.getAbleChannels())){
					ais.setChannelArr(tsq.getAbleChannels());
					channelSet.addAll(Arrays.asList(StringUtils.split(tsq.getAbleChannels(),",")));
				}
				if(StringUtils.isNotEmpty(tsq.getAbleBusinessTypes())){
					ais.setChannelArr(tsq.getAbleBusinessTypes());
					businessSet.addAll(Arrays.asList(StringUtils.split(tsq.getAbleBusinessTypes(),",")));
				}
				
				//int maxSessionCount = skillNum.containsKey(queueId) ? skillNum.getInteger(queueId) : 0;
				//totolMaxSessionCount += maxSessionCount;
				ais.setTenantCode(base.getTenantCode());
				ais.setWorkNo(base.getWorkNo());
				ais.setSkillQueueName(tsq.getQueueName());
				//ais.setMaxSessionCount(maxSessionCount);
				//ais.setCurrSessionCount(0);
				
				String skillKey = AgentUtils.getAgentSkillKey(base.getTenantCode(),queueId,base.getWorkNo());
				keys.remove(skillKey);
				nmaps.put(skillKey,AgentUtils.objectToMap(ais));
				//nmaps.put(skillKey,AgentUtils.objectToMap(reset ? ais : this.syncAgentInfoSkill(ais, skillKey)));
			}
			
			base.setSkillQueue(StringUtils.join(queueIdSet, ","));
			base.setSkillQueueName(StringUtils.join(queueNameSet, ","));
			base.setChannelArr(StringUtils.join(channelSet, ","));
			base.setBusinessArr(StringUtils.join(businessSet, ","));
			//base.setMaxSessionCount(totolMaxSessionCount);
//			base.setStatus(AgentStatus.OFFLINE.getCode());
//			base.setStatusDesc(AgentStatus.OFFLINE.name());
			
			String baseKey = AgentUtils.getAgentBaseKey(base.getTenantCode(), base.getWorkNo());
			keys.remove(baseKey);
			Map<Object,Object> data = AgentUtils.objectToMap(base);
			if(!redisDao.exist(baseKey)){
				data.put("status",AgentStatus.OFFLINE.getCode());
				data.put("statusDesc",AgentStatus.OFFLINE.name());
				data.put("currSessionCount",0);
			}
			if(reset){
				Map<String,String> params = new HashMap<>();
				params.put("tenantCode",base.getTenantCode());
				params.put("workNo",base.getWorkNo());
				data.put("currSessionCount",sessionDao.getSessionCount(params));
			}
			nmaps.put(baseKey, data);
			//nmaps.put(baseKey, AgentUtils.objectToMap(reset ? base : this.syncAgentInfoBase(base, baseKey)));
			
			redisDao.pipelineHmset(nmaps);
		}catch(Exception e){
			logger.error("初始化坐席信息异常!",e);
		}finally{
			latch.countDown();
		}
	}
	
	private AgentInfoBase syncAgentInfoBase(AgentInfoBase base,String key){
		Map<Object,Object> map = redisDao.mapGet(key);
		/*if(!map.isEmpty() && null != map.get("status")){
			base.setStatus((String)map.get("status"));
			base.setStatusDesc((String)map.get("statusDesc"));
			base.setLastSessionTime((String)map.get("lastSessionTime"));
			base.setLoginTime((String)map.get("loginTime"));
			base.setSignTime((String)map.get("signTime"));
			base.setOptTime((String)map.get("optTime"));
			base.setIsForce( (String)map.get("isForce"));
			base.setSignId((String)map.get("signId"));
			base.setBfId((String)map.get("bfId"));
			base.setBusyType((String)map.get("busyType"));
			base.setCurrSessionCount((Integer)map.get("currSessionCount"));
		}*/
		
		return base;
	}
	
	/*private AgentInfoSkill syncAgentInfoSkill(AgentInfoSkill skill,String key){
		Map<Object,Object> map = redisDao.mapGet(key);
		if(!map.isEmpty()){
			skill.setCurrSessionCount((Integer)map.get("currSessionCount"));
			skill.setLastSessionTime((String)map.get("lastSessionTime"));
		}
		
		return skill;
	}*/
	
	private Set<String> getAgentKey(AgentInfoBase base){
		Set<String> keys = new HashSet<>();
		String tenantCode = StringUtils.defaultIfEmpty(base.getTenantCode(), "*");
		String skillQueue = StringUtils.defaultIfEmpty(base.getSkillQueue(), "*");
		String workNo = StringUtils.defaultIfEmpty(base.getWorkNo(), "*");
		if("*".equals(workNo)){
			keys.addAll(redisDao.getKeysByPattern(AgentUtils.getAgentBaseKey(tenantCode,workNo)));
		}else{
			keys.add(AgentUtils.getAgentBaseKey(tenantCode,workNo));
		}
		
		keys.addAll(redisDao.getKeysByPattern(AgentUtils.getAgentSkillKey(tenantCode, skillQueue,workNo)));
		
		return keys;
	}
	
	private Set<String> getRelationKey(){
		Set<String> keys = new HashSet<>();
		
		keys.addAll(redisDao.getKeysByPattern(AgentUtils.getAgentTenantRelationKey("*")));
		keys.addAll(redisDao.getKeysByPattern(AgentUtils.getAgentSkiilRelationKey("*")));
		
		return keys;
	}
	
	
	private void initTenantWorkNo(Set<String> relationKeys){
		logger.info("初始化租户坐席关联信息……");
		try{
			for(String tenantCode : queueService.getTenantCodes()){
				String key = AgentUtils.getAgentTenantRelationKey(tenantCode);
				
				List<String> workNos = agentDao.queryTenantWorkNo(tenantCode);
				if(CollectionUtils.isNotEmpty(workNos)){
					queueService.setAndClearForSet(key, workNos);
				}
				
				relationKeys.remove(key);
			}
		}catch(Exception e){
			logger.error("初始化租户坐席关联信息异常",e);
		}
	}
	
	private void initQueueWorkNo(Set<String> relationKeys){
		logger.info("初始化技能组坐席关联信息……");
		try{
			ConcurrentMap<String,Set<String>> queueWorkNoMap = new ConcurrentHashMap<>();
			for(SkillQueueWorkNo swn : agentDao.getSkillWorkNo()){
				Set<String> set = queueWorkNoMap.get(swn.getSkillQueue());
				if(null == set){
					set = new HashSet<String>();
					queueWorkNoMap.put(swn.getSkillQueue(), set);
				}
				set.add(swn.getWorkNo());
			}
			
			for(String skillQueue : queueWorkNoMap.keySet()){
				String key = AgentUtils.getAgentSkiilRelationKey(skillQueue);
				queueService.setAndClearForSet(key,
						queueWorkNoMap.get(skillQueue));
				
				relationKeys.remove(key);
			}
		}catch(Exception e){
			logger.error("初始化技能组坐席关联信息异常",e);
		}
	}

	public void initsrvServiceCenter(){
		logger.info("初始化服务中心信息……");
		try{
			List<SrvServiceCenter> srvServiceCenteres = agentDao.querySrvServiceCenter();
			String key = Constant.SRV_SERVICE_CENTER;
			for(SrvServiceCenter center : srvServiceCenteres){
				redisDao.mapPut(key, center.getCenterCode() + center.getTenantCode(), center);
			}
		}catch(Exception e){
			logger.error("初始化服务中心信息异常",e);
		}
	}

	
	
	public static void main(String[] args) {
		String str = "8,9,";
		Set<String> channelSet = new HashSet<>();
		
		channelSet.addAll(Arrays.asList(StringUtils.split(str,",")));
		
		System.out.println(channelSet);
	}
}
