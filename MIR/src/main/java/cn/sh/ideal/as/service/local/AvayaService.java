package cn.sh.ideal.as.service.local;


/**
 * 
 * avaya接口信息
 * <AUTHOR>
 * @date 2014年11月14日
 *
 */
public interface AvayaService {

	/**
	 * 设置坐席可处理多媒体会话接口
	 * @param agentId    坐席工号
	 * @param agentState  表示暂停notready
	 * @param code  01表示聊天、02微信、03weibo、04 mail
	 * @return
	 */
	public String setAgentStateForMultimedia(String agentId,String agentState,String code);
	
	
	/**
	 * 获取坐席实时话务状态
	 * @return 
	 * 	[{"extno":"1101","agentid":"8003","state":"Aux Work",”ani”:””,"dnis":""}]
	 * 	<p>
	 * 		返回结果信息：
	 * 			<li>extno:分机号</li>
	 * 			<li>agentid:坐席工号</li>
	 * 			<li>state:状态</li>
	 * 			<li>ani:主叫</li>
	 * 			<li>dani:被叫</li>
	 * </p>
	 */
	public String getAgentState();
	
	
	public String getAgentState(String workNo);
	
	public void updateAgentState(String workNo,String newState,String extNo);
	public void updateAgentMultiState(String workNo,String newState,String channelCode,String customerAccount);
}
