package cn.sh.ideal.as.model;

import java.io.Serializable;

/**
 * 任务转发封装对象
 * 
 * <AUTHOR>
 * @version 1.1.3,12/18/14
 */
public class TransferLog implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//消息ID
	private String messageId;
	
	//转发人工号
	private String beforeWorkNo;
	
	//转发人技能组
	private String beforeSkillQueue;
	
	//转到人的技能组
	private String forwardSkillQueue;
	
	//转到人工号
	private String forwardWorkNo;
	
	//创建时间
	private String createTime;
	
	//状态 (0，转发到技能组，1转发到人,2自动转发)
	private String status; 
	
	//转发备注
	private String remark;
	
	public TransferLog(){}
	public TransferLog(String messageId,String bsq,String bwn,String fsq,String fwn,String status,String remark){
		this.messageId = messageId;
		this.beforeSkillQueue = bsq;
		this.beforeWorkNo = bwn;
		this.forwardSkillQueue = fsq;
		this.forwardWorkNo = fwn;
		this.status = status;
		this.remark = remark;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getBeforeWorkNo() {
		return beforeWorkNo;
	}

	public void setBeforeWorkNo(String beforeWorkNo) {
		this.beforeWorkNo = beforeWorkNo;
	}

	public String getBeforeSkillQueue() {
		return beforeSkillQueue;
	}

	public void setBeforeSkillQueue(String beforeSkillQueue) {
		this.beforeSkillQueue = beforeSkillQueue;
	}

	public String getForwardSkillQueue() {
		return forwardSkillQueue;
	}

	public void setForwardSkillQueue(String forwardSkillQueue) {
		this.forwardSkillQueue = forwardSkillQueue;
	}

	public String getForwardWorkNo() {
		return forwardWorkNo;
	}

	public void setForwardWorkNo(String forwardWorkNo) {
		this.forwardWorkNo = forwardWorkNo;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}
