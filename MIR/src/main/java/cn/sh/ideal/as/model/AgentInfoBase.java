package cn.sh.ideal.as.model;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

public class AgentInfoBase implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String autoId;
	
	/**组织架构ID*/
	private String deptId;
	
	/**工号*/
	private String workNo;
	
	/**用户名*/
	private String userName;
	
	/**租户*/
	private String tenantCode;
	
	/**电话*/
	private String tel;
	
	/**邮件*/
	private String email;
	/**班组*/
	private String teamId;
	
	private String teamName;
	
	/**坐席类型*/
	private String agentType;
	
	/**分机号*/
	private String exNo;
	
	/**技能组*/
	private String skillQueue;
	
	/**技能组名称*/
	private String skillQueueName;
	
	/**可接入渠道*/
	private String channelArr;
	
	private String businessArr;

	/**最大会话数*/
	private int maxSessionCount;
	@JSONField(serialize=false)
	private String skillTypeNum;

	private String flagImage;
	

	@JSONField(serialize=false)  
	private String serviceTipWords;
	
	public String getAutoId() {
		return autoId;
	}
	public void setAutoId(String autoId) {
		this.autoId = autoId;
	}
	public String getDeptId() {
		return deptId;
	}
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getTel() {
		return tel;
	}
	public void setTel(String tel) {
		this.tel = tel;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getTeamId() {
		return teamId;
	}
	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}
	public String getTeamName() {
		return teamName;
	}
	public void setTeamName(String teamName) {
		this.teamName = teamName;
	}
	public String getAgentType() {
		return agentType;
	}
	public void setAgentType(String agentType) {
		this.agentType = agentType;
	}
	public String getExNo() {
		return exNo;
	}
	public void setExNo(String exNo) {
		this.exNo = exNo;
	}
	public String getSkillQueue() {
		return skillQueue;
	}
	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}
	public String getSkillQueueName() {
		return skillQueueName;
	}
	public void setSkillQueueName(String skillQueueName) {
		this.skillQueueName = skillQueueName;
	}

	public String getChannelArr() {
		return channelArr;
	}
	public void setChannelArr(String channelArr) {
		this.channelArr = channelArr;
	}
	public String getBusinessArr() {
		return businessArr;
	}
	public void setBusinessArr(String businessArr) {
		this.businessArr = businessArr;
	}
	public int getMaxSessionCount() {
		return maxSessionCount;
	}
	public void setMaxSessionCount(int maxSessionCount) {
		this.maxSessionCount = maxSessionCount;
	}

	public String getFlagImage() {
		return flagImage;
	}
	public void setFlagImage(String flagImage) {
		this.flagImage = flagImage;
	}

	public String getServiceTipWords() {
		return serviceTipWords;
	}
	public void setServiceTipWords(String serviceTipWords) {
		this.serviceTipWords = serviceTipWords;
	}

	public String getSkillTypeNum() {
		return skillTypeNum;
	}

	public void setSkillTypeNum(String skillTypeNum) {
		this.skillTypeNum = skillTypeNum;
	}
}
