package cn.sh.ideal.as.util;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

import cn.sh.ideal.exception.MultiMediaException;
import cn.sh.ideal.mir.util.Constant;
import cn.sh.ideal.model.AgentInfo.AgentStatus;
import cn.sh.ideal.util.Constants;

public class AgentUtils {
	//private static final Logger logger = LoggerFactory.getLogger(AgentUtils.class);
	/*
	 * 会话数同步类型
	 */
	public final static String SESSION_COUNT_INCR = "scincr";
	public final static String SESSION_COUNT_DECR = "scdecr";
	public final static String SESSION_COUNT_BOTH = "scboth";
	
	/*
	 * 转发类型
	 */
	public final static String TRANSFER_SKILLQUEUE = "0";
	public final static String TRANSFER_USER = "1";
	public final static String TRANSFER_AUTO = "2";
	
	/**登录日志类型 1：登录*/
	public final static String AGENT_LOGIN_TYPE = "1";
	/**登录日志类型 0：登出 */
	public final static String AGENT_LOGOUT_TYPE = "0";
	
	public final static String[] mutualStatus = {AgentStatus.FREE.getCode(),AgentStatus.BUSY.getCode()};
	
	/**
	 * 插入日志时间
	 */
	public static String getCurrentTime(){
		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
	}
	
	public static String getCurrentTime(String format){
		return new SimpleDateFormat(format).format(new Date());
	}
	
	/**
	 * 拼装AgentInfo redis key
	 * 
	 * @param tenantCode
	 * @param queueId
	 * @param workNo
	 * @return
	 */
	/*public static String getAgentKey(String tenantCode,String queueId,String workNo){
		return Constants.TENANTCODE + tenantCode + Constants.SKILLQUEUE + queueId + Constants.AGENT + workNo;
	}*/
	
	/**
	 * 验证参数合法性
	 * 
	 * @param params
	 */
	public static void validateParam(String ... params){
		for(String param : params){
			if(StringUtils.isEmpty(param))
				throw new MultiMediaException("0001");
		}
	}
	
	/**
	 * 获取访问人员ip信息
	 * @param request
	 * @return
	 */
	public static String visitIp(HttpServletRequest request,String port){
	 /*  Enumeration headerNames = request.getHeaderNames();
	    logger.info("获取http header");
	    while (headerNames.hasMoreElements()) {
	        String key = (String) headerNames.nextElement();
	        String value = request.getHeader(key);
	        logger.info("header key=" + key +", value=" + value);
	    }
		*/
		 String ip = request.getHeader("x-forwarded-for");
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			 ip = request.getHeader("Proxy-Client-IP");
		 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			 ip = request.getHeader("WL-Proxy-Client-IP");
		 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			 ip = request.getRemoteAddr();
		 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			 ip = request.getHeader("http_client_ip");
		 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			 ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		 }
		 // 如果是多级代理，那么取第一个ip为客户ip
		 if (ip != null && ip.indexOf(",") != -1) {
			 ip = ip.substring(ip.lastIndexOf(",") + 1, ip.length()).trim();
		 }
		 return ip.concat(":").concat(port);
	}
	
	public static String getAgentBaseKey(String tenantCode,String workNo){
		return Constants.TENANTCODE + tenantCode + Constants.AGENT + workNo + Constants.QUEUE_BASE;
	}
	
	public static String getAgentSkillKey(String tenantCode,String queueId,String workNo){
		return Constants.TENANTCODE + tenantCode + Constants.SKILLQUEUE + queueId + Constants.AGENT + workNo;
	}
	
	public static String getAgentTenantRelationKey(String tenantCode){
		return Constant.RELATION + Constants.TENANTCODE + tenantCode;
	}
	
	public static String getAgentSkiilRelationKey(String skillQueue){
		return Constant.RELATION + Constants.SKILLQUEUE + skillQueue;
	}
	
	 public static Map<Object, Object> objectToMap(Object obj) throws Exception {    
	        if(obj == null)  
	            return null;      
	   
	        Map<Object, Object> map = new HashMap<Object, Object>();   
	   
	        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());    
	        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();    
	        for (PropertyDescriptor property : propertyDescriptors) {    
	            String key = property.getName();    
	            if (key.compareToIgnoreCase("class") == 0) {   
	                continue;  
	            }  
	            Method getter = property.getReadMethod();  
	            Object value = getter!=null ? getter.invoke(obj) : null;  
	            map.put(key, value);  
	        }    
	   
	        return map;  
	    }    
}
