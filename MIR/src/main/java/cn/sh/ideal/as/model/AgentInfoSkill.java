package cn.sh.ideal.as.model;

import java.io.Serializable;

public class AgentInfoSkill implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**技能组*/
	private String skillQueue;
	
	private String tenantCode;
	
	private String workNo;
	
	/**技能组名称*/
	private String skillQueueName;
	
	/**最大会话数*/
	//private int maxSessionCount;
	
	//private String lastSessionTime;
	
	/**当前会话数*/
	//private int currSessionCount;
	
	/**可接入渠道*/
	private String channelArr;
	
	private String businessArr;

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getSkillQueueName() {
		return skillQueueName;
	}

	public void setSkillQueueName(String skillQueueName) {
		this.skillQueueName = skillQueueName;
	}

/*	public int getMaxSessionCount() {
		return maxSessionCount;
	}

	public void setMaxSessionCount(int maxSessionCount) {
		this.maxSessionCount = maxSessionCount;
	}

	public String getLastSessionTime() {
		return lastSessionTime;
	}

	public void setLastSessionTime(String lastSessionTime) {
		this.lastSessionTime = lastSessionTime;
	}

	public int getCurrSessionCount() {
		return currSessionCount;
	}

	public void setCurrSessionCount(int currSessionCount) {
		this.currSessionCount = currSessionCount;
	}*/

	public String getChannelArr() {
		return channelArr;
	}

	public void setChannelArr(String channelArr) {
		this.channelArr = channelArr;
	}

	public String getBusinessArr() {
		return businessArr;
	}

	public void setBusinessArr(String businessArr) {
		this.businessArr = businessArr;
	}

	/**
	 * @return the workNo
	 */
	public String getWorkNo() {
		return workNo;
	}

	/**
	 * @param workNo the workNo to set
	 */
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

}
