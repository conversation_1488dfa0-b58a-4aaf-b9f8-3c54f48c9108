package cn.sh.ideal.as.model;

import java.io.Serializable;

public class AgentLogModel implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
   public AgentLogModel(String workNo,String loginType, String tenantCode,String clientType,String skillQueue){
    	this.workNo=workNo;
    	this.loginType=loginType;
    	this.tenantCode=tenantCode;
    	this.skillQueue=skillQueue;
    	if(clientType==null||"".equals(clientType)){
    		this.clientType="0";
    	}else{
    		this.clientType=clientType;
    	}
    }
	   
    private String autoId;

    /**工号*/
    private String workNo;

    /**操作时间*/
    private String optionTime;

    /**登录类型*/
    private String loginType;
   
    private String tenantCode;
    
    /**
     * 客户端类型：
     * 0-普通，
     * 10-移动客户端(iPhone)，
     * 10-移动客户端(iPad)，
     * 20-移动客户端(Android Mobile)，
     * 21-移动客户端(Android Pad)
     * */
    private String clientType;
    
    /**技能组*/
    private String skillQueue;

    public String getAutoId() {
        return autoId;
    }

    public void setAutoId(String autoId) {
        this.autoId = autoId;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo == null ? null : workNo.trim();
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType == null ? null : loginType.trim();
    }

	public String getOptionTime() {
		return optionTime;
	}

	public void setOptionTime(String optionTime) {
		this.optionTime = optionTime;
	}

	public String getClientType() {
		return clientType;
	}

	public void setClientType(String clientType) {
		this.clientType = clientType;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}
    
    
}