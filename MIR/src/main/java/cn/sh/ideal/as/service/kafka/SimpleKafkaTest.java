package cn.sh.ideal.as.service.kafka;

import cn.sh.ideal.as.model.AgentStatusTraceMessage;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.Properties;
import java.util.concurrent.Future;

/**
 * 简单的Kafka测试类，用于验证Kafka连接和消息发送功能
 * 不依赖Spring框架，可以独立运行
 */
public class SimpleKafkaTest {
    
    private static final String BOOTSTRAP_SERVERS = "10.150.103.92:9093,10.150.103.37:9093,10.150.102.12:9093";
    private static final String TOPIC = "topic-onecc-agent-trace";
    private static final String SECURITY_PROTOCOL = "SASL_PLAINTEXT";
    private static final String SASL_MECHANISM = "PLAIN";
    private static final String SASL_JAAS_CONFIG = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"onecc\" password=\"Callcenter!@#456\";";
    
    public static void main(String[] args) {
        SimpleKafkaTest test = new SimpleKafkaTest();
        test.testKafkaConnection();
    }
    
    public void testKafkaConnection() {
        KafkaProducer<String, String> producer = null;
        
        try {
            // 配置Kafka生产者
            Properties props = new Properties();
            props.put("bootstrap.servers", BOOTSTRAP_SERVERS);
            props.put("security.protocol", SECURITY_PROTOCOL);
            props.put("sasl.mechanism", SASL_MECHANISM);
            props.put("sasl.jaas.config", SASL_JAAS_CONFIG);
            props.put("acks", "1");
            props.put("retries", 3);
            props.put("batch.size", 16384);
            props.put("linger.ms", 1);
            props.put("buffer.memory", 33554432);
            props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            
            producer = new KafkaProducer<String, String>(props);
            
            // 创建测试消息
            AgentStatusTraceMessage message = new AgentStatusTraceMessage(
                "test001",
                "BUSY",
                "2025-01-07 16:25:00",  // 开始时间
                "2025-01-07 16:30:00"   // 结束时间
            );

            // 简单的JSON序列化（不使用fastjson）
            String messageJson = toSimpleJson(message);
            String key = message.getWorkNo();
            
            ProducerRecord<String, String> record = new ProducerRecord<String, String>(
                TOPIC, key, messageJson);
            
            System.out.println("Sending message to Kafka...");
            System.out.println("Topic: " + TOPIC);
            System.out.println("Key: " + key);
            System.out.println("Message: " + messageJson);
            
            // 同步发送消息
            Future<RecordMetadata> future = producer.send(record);
            RecordMetadata metadata = future.get(); // 等待发送完成
            
            System.out.println("Message sent successfully!");
            System.out.println("Topic: " + metadata.topic());
            System.out.println("Partition: " + metadata.partition());
            System.out.println("Offset: " + metadata.offset());
            
        } catch (Exception e) {
            System.err.println("Error sending message to Kafka: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (producer != null) {
                producer.close();
            }
        }
    }
    
    /**
     * 简单的JSON序列化方法，不依赖外部库
     */
    private String toSimpleJson(AgentStatusTraceMessage message) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"workNo\":\"").append(message.getWorkNo()).append("\",");
        json.append("\"originalStatus\":\"").append(message.getOriginalStatus()).append("\",");
        json.append("\"startTime\":\"").append(message.getStartTime()).append("\",");
        json.append("\"endTime\":\"").append(message.getEndTime()).append("\"");
        json.append("}");
        return json.toString();
    }
}
