package cn.sh.ideal.as.dao;

import java.util.HashMap;

import cn.sh.ideal.as.model.AgentLog;
import cn.sh.ideal.as.model.AgentLogModel;
import cn.sh.ideal.as.model.TransferLog;
import cn.sh.ideal.as.req.AgentCoordinateRequest;
/**
 * 坐席日志Dao
 * 
 * <AUTHOR>
 * @since v1.1.8
 *
 */
public interface AgentLogDao {
	

	public void insertAgentLog(AgentLogModel log);
	
	/**
	 * 插入签入日志
	 * 
	 * @param log
	 */
	public void insertSignLog(AgentLog log);
	
	/**
	 * 插入示闲日志
	 * 
	 * @param log
	 */
	public void insertFreeLog(AgentLog log);
	
	/**
	 * 插入示忙日志
	 * 
	 * @param log
	 */
	public void insertBusyLog(AgentLog log);
	
	/**
	 * 更新签入日志
	 * 
	 * @param log
	 */
	public void updateSignLog(HashMap<String,Object>  map);
	
	/**
	 * 更新示闲日志
	 * 
	 * @param log
	 */
	public void updateFreeLog(HashMap<String,Object> map);
	
	/**
	 * 更新示忙日志
	 * 
	 * @param log
	 */
	public void updateBusyLog(HashMap<String,Object>  map);
	
	/**
	 * 清理签入日志（更新没有结束时间的日志）
	 * 
	 * @param log
	 */
	public void clearSignLog(AgentLog log);
	
	/**
	 * 清理示闲日志（更新没有结束时间的日志）
	 * 
	 * @param log
	 */
	public void clearFreeLog(AgentLog log);
	
	/**
	 * 清理示忙日志（更新没有结束时间的日志）
	 * 
	 * @param log
	 */
	public void clearBusyLog(AgentLog log);
	
	/**
	 * 插入转发日志
	 * 
	 * @param log
	 */
	public void insertTransferLog(TransferLog log);
	
	/**
	 * 插入坐席坐标日志
	 * 
	 * @param params
	 */
	public void insertAgentLocation(AgentCoordinateRequest params);


}
