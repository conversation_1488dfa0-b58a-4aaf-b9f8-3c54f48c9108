package cn.sh.ideal.as.model;

import java.io.Serializable;

public class AgentReportModel implements Serializable {
	
	private static final long serialVersionUID = 1L;

    private String workNo;//工号

    private String businessCount;//业务数量
    
    private long businessTime;//业务时长

    private String state;//坐席状态
    
    private String tenantCode;//租户代码 
    
    private String userName; //坐席名称
    

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getBusinessCount() {
		return businessCount;
	}

	public void setBusinessCount(String businessCount) {
		this.businessCount = businessCount;
	}

	public long getBusinessTime() {
		return businessTime;
	}

	public void setBusinessTime(long businessTime) {
		this.businessTime = businessTime;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}


    
    
}