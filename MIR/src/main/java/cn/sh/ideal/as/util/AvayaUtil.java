package cn.sh.ideal.as.util;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;



/**
 * 公共整合部分
 * <AUTHOR>
 * @date 2014年11月6日
 */
public class AvayaUtil {
	
	
		/**
		 * 日志文件
		 */
		private static final Logger logger = LoggerFactory.getLogger(AvayaUtil.class);
		//ayava 访问接口
	
		/**avaya地址信息*/
		public final static String AVAYA_URL = "AVAYA_URL";
		
		/**avaya是否调用avaya信息*/
		public final static String AVAYA_ISSEND = "AVAYA_ISSEND";
		
		/**储存avaya详细状态信息*/
		public final static Map<String,String> AVAYA_STATUS = new HashMap<String, String>();
		
		/**获取数据库中avaya状态，默认为false不发送*/
		public static String AVAYA_STATE = "false";
		
		/**
		 * 请求分配坐席接口
		 * @param： Id  唯一标识
		 * @param： Mediatype 多媒体类型｛weibo,weixin,chat,sms,mail｝、、
		 * @param： Businesstype 业务类型
		 * @param: Customlevel  客户等级
		 * 
		 */
		public final static String DISTRIBUTE_MULTIMEDIA_CALL = "/DistributeMultimediaCall.html?Id={0}&Mediatype={1}&Businesstype={2}&Customlevel={3}";
		
		/**
		 * 取消分配坐席接口
		 * @param： Id 唯一标识
		 */
		public final static String CANCEL_DISTRIBUTE_MULTIMEDIA_CALL="/CancelDistributeMultimediaCall.html?Id={0}";
		
		/**
		 * 任务转发接口
		 * @param： Id 唯一标识
		 * @param：Srcagentid 转发发起方坐席工号
		 * @param： Destinationagentid  转发接收方坐席工号
		 */
		public final static String TRANSFER = "/Transfer.html?Id={0}&Srcagentid={1}&&Destinationagentid={2}";
		
		/**
		 * 取消转发接口
		 * @param：Id 唯一标识
		 */
		public final static String CANCEL_TRANSFER ="/CancelTransfer.html?Id={0}";
		
		/**
		 * 会话结束座席释放接口
		 * @param：Id 唯一标识
		 * @param：Agentid 坐席号
		 */
		public final static String MULTIMEDIA_CALL_FINISHED = "/MultimediaCallFinished.html?Id={0}&Agentid={1}";
		
		/**
		 * 设置坐席可处理多媒体会话接口 
		 * @param：id  唯一标识此次多媒体会话id 
		 * @param：AgentId 坐席号
		 * @param：AgentState 要设置成的坐席状态 notready ，暂停 当前支持这一个状态
		 * @param： Code 如果AgentState设置成 notready 那么此code值是暂停原因
		 */
		public final static String SET_AGENTSTATE_FOR_MULTIMEDIA = "/SetAgentStateForMultimediaCall.html?AgentId={0}&AgentState={1}&Code={2}";
		
		/**
		 * 获取坐席实时状态接口
		 */
		public final static String GET_AGENTS_STATE = "/GetAgentsState.html";
		
		/***
		 * 记录url连接
		 * <AUTHOR>
		 *
		 */
		public enum VISITURL{
			DISTRIBUTE_MULTIMEDIA_CALL,
			CANCEL_DISTRIBUTE_MULTIMEDIA_CALL,
			TRANSFER,
			CANCEL_TRANSFER,
			MULTIMEDIA_CALL_FINISHED,
			SET_AGENTSTATE_FOR_MULTIMEDIA,
			GET_AGENTS_STATE
		}
		
		
		/**
		 * 访问外网接口 
		 * @param visitUrl
		 * @param strings 
		 * @return
		 *  XXX 有待改进
		 */
		public static synchronized String getVisitUrl(VISITURL visitUrl,Object...strings){
			String paramUrl = "";
			String paramStr = JSONObject.toJSONString(strings);//CommonUtil.beanToJson(strings, false);
			logger.info("--> avaya--> 传递的参数信息为：" + paramStr);
			//拼接访问路径
			if(visitUrl.equals(VISITURL.DISTRIBUTE_MULTIMEDIA_CALL)){
				
				paramUrl = DISTRIBUTE_MULTIMEDIA_CALL;
				
			}else if(visitUrl.equals(VISITURL.CANCEL_DISTRIBUTE_MULTIMEDIA_CALL)){
				
				paramUrl = CANCEL_DISTRIBUTE_MULTIMEDIA_CALL;
				
			}else if(visitUrl.equals(VISITURL.TRANSFER)){
				
				paramUrl= TRANSFER;
				
			}else if(visitUrl.equals(VISITURL.CANCEL_TRANSFER)){
				
				paramUrl = CANCEL_TRANSFER;
				
			}else if(visitUrl.equals(VISITURL.MULTIMEDIA_CALL_FINISHED)){
				
				paramUrl = MULTIMEDIA_CALL_FINISHED;
				
			}else if(visitUrl.equals(VISITURL.SET_AGENTSTATE_FOR_MULTIMEDIA)){
				
				paramUrl = SET_AGENTSTATE_FOR_MULTIMEDIA;
			}else if(visitUrl.equals(VISITURL.GET_AGENTS_STATE)){
				
				paramUrl = GET_AGENTS_STATE;
			}else{
				logger.info("-->avaya-> 根据传递的参数为找相应的访问接口");
			}
			String allURL = paramUrl;
			allURL = MessageFormat.format(allURL, strings);
			logger.info("-->avaya-> 访问接口后缀路径信息为：" + allURL);
			return allURL;
		}
		
		
}
