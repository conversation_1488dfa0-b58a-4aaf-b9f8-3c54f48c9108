package cn.sh.ideal.as.service.local.impl;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.as.service.local.AvayaService;
import cn.sh.ideal.as.util.AvayaUtil;
import cn.sh.ideal.as.util.AvayaUtil.VISITURL;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.util.NetUtil;


/**
 * ayava相关业务.
 *
 * <AUTHOR>
 * @version 1.1.3,12/23/14
 */
@Service("avayaService")
public class AvayaServiceImpl implements AvayaService {

	/** The logger. */
	private static Logger logger  = LoggerFactory.getLogger(AvayaServiceImpl.class);
	
	@Autowired
	private SysInitService initService;
	
	public String setAgentStateForMultimedia(String agentId,String agentState,String code){
		logger.info("-->avaya-> 调用坐席可处理多媒体会话接口；参数为：agentId={},agentState={},code={}",
					agentId,agentState,code);
		String avayaUrl = AvayaUtil.getVisitUrl(VISITURL.SET_AGENTSTATE_FOR_MULTIMEDIA, agentId,agentState,code);
		String backResult = "";
		try{
			String pushAvaya = initService.getSysParam(AvayaUtil.AVAYA_URL).getParamValue();
			if(pushAvaya == null || "".equals(pushAvaya)){
				logger.info("-->avaya->  数据库中没有配置avaya访问地址连接 ");
			}else{
				backResult = NetUtil.send(pushAvaya + avayaUrl, NetUtil.GET, "");
			}
			logger.info("-->avaya->  访问返回的数据为：" + backResult);
		}catch(Exception e){
			logger.info("-->avaya->  设置坐席可处理多媒体会话接口出错：" + e.getMessage(),e);
			backResult = "";
		}
		return backResult;
	}
	
	
	public String getAgentState(){
		logger.info("-->avaya-> 获取坐席当前实时状态接口；无参数");
		String avayaUrl = AvayaUtil.getVisitUrl(VISITURL.GET_AGENTS_STATE, "");
		String backResult = "";
		try{
			String pushAvaya = initService.getSysParam(AvayaUtil.AVAYA_URL).getParamValue();
			if(pushAvaya == null || "".equals(pushAvaya)){
				logger.info("-->avaya->  数据库中没有配置avaya访问地址连接 ");
			}else{
				backResult = NetUtil.send(pushAvaya + avayaUrl, NetUtil.GET, "");
			}
			logger.info("-->avaya->  访问返回的数据为：" + backResult);
		}catch(Exception e){
			logger.info("-->avaya->  调用查询坐席实时状态会话接口出错：" + e.getMessage(),e);
			backResult = "";
		}
		return backResult;
	}
	@Scheduled(fixedDelay=30000)
	public void runAvayaState(){
		String avayaStateJson = "";
		try {
			String isSendAvaya = initService.getSysParam(AvayaUtil.AVAYA_ISSEND).getParamValue();
			if(isSendAvaya == null || "".equals(isSendAvaya)){
				isSendAvaya = "false";
			}
			logger.debug("--> avaya -->验证是否调用avaya接口返回状态为：(true/false)" + isSendAvaya);
			AvayaUtil.AVAYA_STATE = isSendAvaya;
			if(isSendAvaya.equals("true")){
				avayaStateJson = this.getAgentState();
				if(avayaStateJson == null || avayaStateJson.equals("")){
					throw new Exception("调用远程结果信息失败,返回的结果信息空");
				}
				@SuppressWarnings("unchecked")
				List<Map<String,String>> a_list = (List<Map<String,String>>) JSONObject.parseObject(avayaStateJson, List.class);
				if(a_list == null){
					throw new Exception("调用远程结果信息失败,返回的结果信息为null");
				}
				if(a_list.size() == 0){
					throw new Exception("调用远程结果信息失败,返回的结果信息 没有坐席数据");
				}
				AvayaUtil.AVAYA_STATUS.clear();
				AvayaUtil.AVAYA_STATUS.putAll(a_list.get(0));
			}else{
				logger.debug("当前系统未启用avaya[isAvaya=true表示启用]接口信息");
			}
		} catch (Exception e) {
			logger.error("*************获取远程avaya状态信息出错*************",e);
		}
	}


	@Override
	public String getAgentState(String workNo) {
		try {
			String url = initService.assemblyUrl(AvayaUtil.AVAYA_URL, "/agent/%s/state");
			return NetUtil.send(String.format(url, workNo), NetUtil.GET, "");
		}catch(Exception e) {
			logger.error("获取坐席状态异常");
		}
		
		return null;
	}


	@Override
	public void updateAgentState(String workNo, String newState,String extNo) {
		try {
			String url = initService.assemblyUrl(AvayaUtil.AVAYA_URL, "/resources/%s/link?newState=%s&metaData=0&workMode=Manual&pwd=4532&accountId=%s");
			NetUtil.send(String.format(url, workNo,newState,extNo), NetUtil.POST, "");
		}catch(Exception e) {
			logger.info("设置语言状态异常",e);
		}
	
	}


	@Override
	public void updateAgentMultiState(String workNo, String newState, String channelCode,String customerAccount) {
		try {
			String isSendAvaya = initService.getSysParam(AvayaUtil.AVAYA_ISSEND).getParamValue();
			if(!"true".equals(isSendAvaya)) return;
			
			String channelType = "";
			if("1003".equals(channelCode)) {
				channelType = "1";
			}else if("1012".equals(channelCode)) {
				channelType = "3";
			}else {
				channelType = "2";
			}
			String url = initService.assemblyUrl(AvayaUtil.AVAYA_URL, "/channel/%s/link?channeltype=%s&accountId=%s&state=%s");
			NetUtil.send(String.format(url, workNo,channelType,customerAccount,newState), NetUtil.POST, "");
		}catch(Exception e) {
			logger.info("设置语言状态异常",e);
		}
	}
	
}
