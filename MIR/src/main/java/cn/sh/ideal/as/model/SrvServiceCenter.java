package cn.sh.ideal.as.model;

import java.io.Serializable;

public class SrvServiceCenter implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private int id;
	private String tenantCode;	//租户代码	
	private String centerCode;	//服务中心代码
	private String centerName;	//服务中心名称
	private String location;	//坐标
	private String remark;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getCenterCode() {
		return centerCode;
	}
	public void setCenterCode(String centerCode) {
		this.centerCode = centerCode;
	}
	public String getCenterName() {
		return centerName;
	}
	public void setCenterName(String centerName) {
		this.centerName = centerName;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
}
