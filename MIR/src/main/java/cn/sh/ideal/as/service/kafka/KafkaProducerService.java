package cn.sh.ideal.as.service.kafka;

import cn.sh.ideal.as.model.AgentStatusTraceMessage;
import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Properties;
import java.util.concurrent.Future;

/**
 * Kafka消息生产者服务
 * 负责发送坐席状态跟踪消息到Kafka
 */
@Service
public class KafkaProducerService {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerService.class);
    
    @Value("${kafka.bootstrap.servers}")
    private String bootstrapServers;
    
    @Value("${kafka.topic.agent.trace}")
    private String agentTraceTopic;
    
    @Value("${kafka.security.protocol}")
    private String securityProtocol;
    
    @Value("${kafka.sasl.mechanism}")
    private String saslMechanism;
    
    @Value("${kafka.sasl.jaas.config}")
    private String saslJaasConfig;
    
    @Value("${kafka.producer.acks}")
    private String acks;
    
    @Value("${kafka.producer.retries}")
    private int retries;
    
    @Value("${kafka.producer.batch.size}")
    private int batchSize;
    
    @Value("${kafka.producer.linger.ms}")
    private int lingerMs;
    
    @Value("${kafka.producer.buffer.memory}")
    private long bufferMemory;
    
    @Value("${kafka.producer.key.serializer}")
    private String keySerializer;
    
    @Value("${kafka.producer.value.serializer}")
    private String valueSerializer;
    
    private KafkaProducer<String, String> producer;
    
    @PostConstruct
    public void init() {
        try {
            Properties props = new Properties();
            props.put("bootstrap.servers", bootstrapServers);
            props.put("security.protocol", securityProtocol);
            props.put("sasl.mechanism", saslMechanism);
            props.put("sasl.jaas.config", saslJaasConfig);
            props.put("acks", acks);
            props.put("retries", retries);
            props.put("batch.size", batchSize);
            props.put("linger.ms", lingerMs);
            props.put("buffer.memory", bufferMemory);
            props.put("key.serializer", keySerializer);
            props.put("value.serializer", valueSerializer);
            
            producer = new KafkaProducer<String, String>(props);
            logger.info("Kafka Producer initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize Kafka Producer", e);
        }
    }
    
    @PreDestroy
    public void destroy() {
        if (producer != null) {
            try {
                producer.close();
                logger.info("Kafka Producer closed successfully");
            } catch (Exception e) {
                logger.error("Error closing Kafka Producer", e);
            }
        }
    }
    
    /**
     * 发送坐席状态跟踪消息
     * 
     * @param message 坐席状态跟踪消息
     */
    public void sendAgentStatusTrace(AgentStatusTraceMessage message) {
        if (producer == null) {
            logger.warn("Kafka Producer is not initialized, skipping message send");
            return;
        }
        
        try {
            final String messageJson = JSON.toJSONString(message);
            String key = message.getWorkNo();

            ProducerRecord<String, String> record = new ProducerRecord<String, String>(
                agentTraceTopic, key, messageJson);

            // 异步发送消息
            Future<RecordMetadata> future = producer.send(record,
                new org.apache.kafka.clients.producer.Callback() {
                    @Override
                    public void onCompletion(RecordMetadata metadata, Exception exception) {
                        if (exception != null) {
                            logger.error("Failed to send agent status trace message: " + messageJson, exception);
                        } else {
                            logger.debug("Agent status trace message sent successfully: topic={}, partition={}, offset={}",
                                metadata.topic(), metadata.partition(), metadata.offset());
                        }
                    }
                });

            logger.info("Agent status trace message queued for sending: {}", messageJson);
            
        } catch (Exception e) {
            logger.error("Error sending agent status trace message: " + message, e);
        }
    }
}
