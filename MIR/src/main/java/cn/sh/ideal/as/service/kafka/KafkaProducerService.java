package cn.sh.ideal.as.service.kafka;

import cn.sh.ideal.as.model.AgentStatusTraceMessage;
import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Properties;
import java.util.concurrent.Future;

/**
 * Kafka消息生产者服务
 * 负责发送坐席状态跟踪消息到Kafka
 */
@Service
public class KafkaProducerService {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerService.class);

    @Resource(name = "config")
    private Properties config;
    
    private KafkaProducer<String, String> producer;
    
    @PostConstruct
    public void init() {
        try {
            Properties props = new Properties();
            props.put("bootstrap.servers", config.getProperty("kafka.bootstrap.servers"));
            props.put("security.protocol", config.getProperty("kafka.security.protocol"));
            props.put("sasl.mechanism", config.getProperty("kafka.sasl.mechanism"));
            props.put("sasl.jaas.config", config.getProperty("kafka.sasl.jaas.config"));
            props.put("acks", config.getProperty("kafka.producer.acks"));
            props.put("retries", Integer.parseInt(config.getProperty("kafka.producer.retries", "3")));
            props.put("batch.size", Integer.parseInt(config.getProperty("kafka.producer.batch.size", "16384")));
            props.put("linger.ms", Integer.parseInt(config.getProperty("kafka.producer.linger.ms", "1")));
            props.put("buffer.memory", Long.parseLong(config.getProperty("kafka.producer.buffer.memory", "33554432")));
            props.put("key.serializer", config.getProperty("kafka.producer.key.serializer"));
            props.put("value.serializer", config.getProperty("kafka.producer.value.serializer"));

            producer = new KafkaProducer<String, String>(props);
            logger.info("Kafka Producer initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize Kafka Producer", e);
        }
    }
    
    @PreDestroy
    public void destroy() {
        if (producer != null) {
            try {
                producer.close();
                logger.info("Kafka Producer closed successfully");
            } catch (Exception e) {
                logger.error("Error closing Kafka Producer", e);
            }
        }
    }
    
    /**
     * 发送坐席状态跟踪消息
     * 
     * @param message 坐席状态跟踪消息
     */
    public void sendAgentStatusTrace(AgentStatusTraceMessage message) {
        if (producer == null) {
            logger.warn("Kafka Producer is not initialized, skipping message send");
            return;
        }
        
        try {
            final String messageJson = JSON.toJSONString(message);
            String key = message.getWorkNo();
            String agentTraceTopic = config.getProperty("kafka.topic.agent.trace");

            ProducerRecord<String, String> record = new ProducerRecord<String, String>(
                agentTraceTopic, key, messageJson);

            // 异步发送消息
            Future<RecordMetadata> future = producer.send(record,
                new org.apache.kafka.clients.producer.Callback() {
                    @Override
                    public void onCompletion(RecordMetadata metadata, Exception exception) {
                        if (exception != null) {
                            logger.error("Failed to send agent status trace message: " + messageJson, exception);
                        } else {
                            logger.debug("Agent status trace message sent successfully: topic={}, partition={}, offset={}",
                                metadata.topic(), metadata.partition(), metadata.offset());
                        }
                    }
                });

            logger.info("Agent status trace message queued for sending: {}", messageJson);
            
        } catch (Exception e) {
            logger.error("Error sending agent status trace message: " + message, e);
        }
    }
}
