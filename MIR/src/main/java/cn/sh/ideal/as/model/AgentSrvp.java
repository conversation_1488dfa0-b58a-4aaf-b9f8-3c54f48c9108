package cn.sh.ideal.as.model;

import java.io.Serializable;

public class AgentSrvp  implements Serializable {
	
	private static final long serialVersionUID = 1L;
	//租户
	private String tenantCode;
	//工号
	private String workNo;
	//业务类型
	private String business;
	//支持渠道
	private String channel;
	//评论数
	private long comments;
	//满意度
	private String satisfy;
	//人气
	private Integer sentiment;	
	
	private String agentName;
	
	//状态
	private String  status;
	
	//排队人数量
	private long personNum;
	
	public long getComments() {
		return comments;
	}
	public void setComments(long comments) {
		this.comments = comments;
	}
	public Integer getSentiment() {
		return sentiment;
	}
	public void setSentiment(Integer sentiment) {
		this.sentiment = sentiment;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getBusiness() {
		return business;
	}
	public void setBusiness(String business) {
		this.business = business;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getSatisfy() {
		return satisfy;
	}
	public void setSatisfy(String satisfy) {
		this.satisfy = satisfy;
	}
	public long getPersonNum() {
		return personNum;
	}
	public void setPersonNum(long personNum) {
		this.personNum = personNum;
	}
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}


}
