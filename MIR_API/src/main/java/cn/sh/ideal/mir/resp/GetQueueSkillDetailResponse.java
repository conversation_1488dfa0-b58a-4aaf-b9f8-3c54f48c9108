package cn.sh.ideal.mir.resp;

import java.util.List;

import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.SkillQueue;

/**
 * Created by ChenBo on 16-3-25.
 */
public class GetQueueSkillDetailResponse extends BaseResponse{

    /**
	 * 
	 */
	private static final long serialVersionUID = 9221796644065090250L;

	public GetQueueSkillDetailResponse(){}
	public GetQueueSkillDetailResponse(String resultCode, String resultMsg) {
        super(resultCode, resultMsg);
    }

    private List<SkillQueue> data;

	public List<SkillQueue> getData() {
		return data;
	}
	public void setData(List<SkillQueue> data) {
		this.data = data;
	}

   
}
