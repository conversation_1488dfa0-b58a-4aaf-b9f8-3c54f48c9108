package cn.sh.ideal.mir.service;

import cn.sh.ideal.mir.req.MessageDirectRequest;
import cn.sh.ideal.mir.req.MessageReceiveRequest;
import cn.sh.ideal.mir.resp.MessageDirectResponse;
import cn.sh.ideal.mir.resp.MessageReceiveResponse;

/**
 * 消息接收处理服务
 * Created by ChenBo on 16-3-25.
 */
public interface MessageService {

    /**
     * 消息接收
     * @param request
     * @return
     */
    public MessageReceiveResponse receive(MessageReceiveRequest request);

    /**
     * 消息直接分配
     * @param request
     * @return
     */
    public MessageDirectResponse direct(MessageDirectRequest request);
}
