package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * Created by <PERSON>B<PERSON> on 16-3-25.
 */
public class TakeOverTaskRequest extends BaseRequest {

    /**
	 * 
	 */
	private static final long serialVersionUID = 3186745657816323891L;
	@NotEmpty
    private String tenantCode;
	@NotEmpty
    private String targetWn;
	@NotEmpty
    private String sessionId;
    private String skillQueue;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTargetWn() {
        return targetWn;
    }

    public void setTargetWn(String targetWn) {
        this.targetWn = targetWn;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSkillQueue() {
        return skillQueue;
    }

    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }

	@Override
	public String toString() {
		return "TakeOverTaskRequest [tenantCode=" + tenantCode + ", targetWn=" + targetWn + ", sessionId=" + sessionId
				+ ", skillQueue=" + skillQueue + "]";
	}
    
    
}
