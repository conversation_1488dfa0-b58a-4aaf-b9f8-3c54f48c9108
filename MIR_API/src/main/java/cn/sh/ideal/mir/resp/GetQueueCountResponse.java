package cn.sh.ideal.mir.resp;

import java.util.Map;

import cn.sh.ideal.model.BaseResponse;

/**
 * Created by ChenBo on 16-3-25.
 */
public class GetQueueCountResponse extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3469173199334613240L;

	public GetQueueCountResponse() {
	}

	public GetQueueCountResponse(String resultCode, String resultMsg) {
		super(resultCode, resultMsg);
	}

	private int total;

	private Map<String, Long> data;

	public int getTotal() {
		return total;
	}

	public void setTotal(int total) {
		this.total = total;
	}

	public Map<String, Long> getData() {
		return data;
	}

	public void setData(Map<String, Long> data) {
		this.data = data;
	}
}
