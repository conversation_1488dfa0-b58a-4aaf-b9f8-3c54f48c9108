package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * Created by <PERSON>B<PERSON> on 16-3-25.
 */
public class RemoveQueueSessionRequest extends BaseRequest {
    /**
	 * 
	 */
	private static final long serialVersionUID = 969328492458084971L;
	@NotEmpty
    private String tenantCode;
	@NotEmpty
    private String queueId;
	@NotEmpty
    private String sessionId;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getQueueId() {
        return queueId;
    }

    public void setQueueId(String queueId) {
        this.queueId = queueId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

	@Override
	public String toString() {
		return "RemoveQueueSessionRequest [tenantCode=" + tenantCode + ", queueId=" + queueId + ", sessionId="
				+ sessionId + "]";
	}
    
    
}
