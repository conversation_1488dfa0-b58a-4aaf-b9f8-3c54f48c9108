package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 */
public class PreQueueOptRequest extends  BaseRequest{


    /**
	 *
	 */
	private static final long serialVersionUID = 1L;
	@NotEmpty
	private String tenantCode;
	@NotEmpty
    private String sessionId;
	@NotEmpty
	private String optType;
	private String sendType;

	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getOptType() {
		return optType;
	}

	public void setOptType(String optType) {
		this.optType = optType;
	}

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}
}
