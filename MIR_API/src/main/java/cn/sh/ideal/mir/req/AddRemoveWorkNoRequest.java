package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * 添加或移除会话工号
 * <AUTHOR>
 *
 */
public class AddRemoveWorkNoRequest extends BaseRequest {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3800361280078813170L;
	@NotEmpty
	private String sessionId;//会话ID
	@NotEmpty
	private String tenantCode;//租户号
	@NotEmpty
	private String skillQueue;//队列
	@NotEmpty
	private String workNo;//工号
	@NotEmpty
	private String type;//类型  add update del

	private Object extData;//扩展数据
	
	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Object getExtData() {
		return extData;
	}

	public void setExtData(Object extData) {
		this.extData = extData;
	}

	@Override
	public String toString() {
		return "AddRemoveWorkNoRequest [sessionId=" + sessionId + ", tenantCode=" + tenantCode + ", skillQueue="
				+ skillQueue + ", workNo=" + workNo + ", type=" + type + ", extData=" + extData + "]";
	}
	
	
}
