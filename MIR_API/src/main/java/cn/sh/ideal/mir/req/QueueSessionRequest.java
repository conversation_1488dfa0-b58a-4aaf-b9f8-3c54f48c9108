package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * Created by ChenBo on 16-3-25.
 */
public class QueueSessionRequest extends  BaseRequest{


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@NotEmpty
	private String tenantCode;
    private String queueId;
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getQueueId() {
		return queueId;
	}
	public void setQueueId(String queueId) {
		this.queueId = queueId;
	}
}
