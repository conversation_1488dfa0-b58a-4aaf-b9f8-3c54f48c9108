package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;
import cn.sh.ideal.model.SessionInfo;

/**
 * Created by ChenBo on 16-3-25.
 */
public class PushTipRequest extends  BaseRequest{

    /**
	 * 
	 */
	private static final long serialVersionUID = -4294395796280502209L;


    private String tenantCode;
    private String workNo;
    private String type;
    private String sessionId;
    private String content;
    private String channelCode;
    private String lastActiveUser;
    private String sendType;
    private String timeOutAction;
    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    private Object extData;
    private SessionInfo session;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Object getExtData() {
        return extData;
    }

    public void setExtData(Object extData) {
        this.extData = extData;
    }

    public SessionInfo getSession() {
        return session;
    }

    public void setSession(SessionInfo session) {
        this.session = session;
    }
    
    public String getLastActiveUser() {
		return lastActiveUser;
	}

	public void setLastActiveUser(String lastActiveUser) {
		this.lastActiveUser = lastActiveUser;
	}
	
	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}
	
	
	public String getTimeOutAction() {
		return timeOutAction;
	}

	public void setTimeOutAction(String timeOutAction) {
		this.timeOutAction = timeOutAction;
	}

	@Override
	public String toString() {
		return "PushTipRequest [tenantCode=" + tenantCode + ", workNo=" + workNo + ", type=" + type + ", sessionId="
				+ sessionId + ", content=" + content + ", extData=" + extData + ", session=" + session + ",lastActiveUser=" +lastActiveUser + "]";
	}
    
}
