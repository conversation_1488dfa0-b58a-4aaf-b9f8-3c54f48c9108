package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;

/**
 * Created by <PERSON><PERSON><PERSON> on 16-3-25.
 */
public class NoticePushRequest extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8799527476880950061L;

//	@Override
//    public void check() throws RequestException {
//
//    }

    private String autoId;

    private String sender;

    private String title;

    private String content;

    private String beginTime;

    private String endTime;

    private String filePath;

    private String isSend;

    private String tenantId;

    private String status;

    private String receiver;

    public String getAutoId() {
        return autoId;
    }

    public void setAutoId(String autoId) {
        this.autoId = autoId;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getIsSend() {
        return isSend;
    }

    public void setIsSend(String isSend) {
        this.isSend = isSend;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    
    @Override
   	public String toString() {
   		return "NoticePushRequest [autoId=" + autoId + ", sender=" + sender + ", title=" + title + ", content="
   				+ content + ", beginTime=" + beginTime + ", endTime=" + endTime + ", filePath=" + filePath + ", isSend="
   				+ isSend + ", tenantId=" + tenantId + ", status=" + status + ", receiver=" + receiver + "]";
   	}
}
