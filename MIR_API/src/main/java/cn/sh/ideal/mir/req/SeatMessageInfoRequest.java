package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;
import cn.sh.ideal.model.MessageInfo;

/**
 * 坐席消息推送请求类
 * <AUTHOR>
 *
 */
public class SeatMessageInfoRequest extends BaseRequest{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 451044381280574137L;
	private MessageInfo messageInfo;

	public MessageInfo getMessageInfo() {
		return messageInfo;
	}

	public void setMessageInfo(MessageInfo messageInfo) {
		this.messageInfo = messageInfo;
	}
	
//	@Override
//	public void check() throws RequestException {
//        RequestCheckUtils.checkNotEmpty(messageInfo,"messageInfo");
//	}

	@Override
	public String toString() {
		return "SeatMessageInfoRequest [messageInfo=" + messageInfo + "]";
	}
	
}
