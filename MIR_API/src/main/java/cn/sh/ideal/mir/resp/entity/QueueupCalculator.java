package cn.sh.ideal.mir.resp.entity;

import java.io.Serializable;
import java.util.List;

public class QueueupCalculator implements Serializable{
	private static final long serialVersionUID = 1L;
	private String queueId;
	private String queueName;
	private List<SessionQueue> sessions;

	public String getQueueId() {
		return queueId;
	}

	public void setQueueId(String queueId) {
		this.queueId = queueId;
	}

	public String getQueueName() {
		return queueName;
	}

	public void setQueueName(String queueName) {
		this.queueName = queueName;
	}

	/**
	 * @return the sessions
	 */
	public List<SessionQueue> getSessions() {
		return sessions;
	}

	/**
	 * @param sessions the sessions to set
	 */
	public void setSessions(List<SessionQueue> sessions) {
		this.sessions = sessions;
	}
	
}
