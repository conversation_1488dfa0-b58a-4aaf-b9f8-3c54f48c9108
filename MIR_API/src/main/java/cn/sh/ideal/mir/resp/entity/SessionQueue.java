package cn.sh.ideal.mir.resp.entity;

import java.io.Serializable;

public class SessionQueue implements Serializable{
	private static final long serialVersionUID = 1L;
	private String queueTime;
	private String sessionId;
	private String tenantCode;
	private String channelCode;
	private String sendAccount;
	private String acceptAccount;
	private String customerId;
	private String customerName;
	private String callId;
	
	public String getSendAccount() {
		return sendAccount;
	}
	public String getCallId() {
		return callId;
	}
	public void setCallId(String callId) {
		this.callId = callId;
	}
	public void setSendAccount(String sendAccount) {
		this.sendAccount = sendAccount;
	}
	public String getAcceptAccount() {
		return acceptAccount;
	}
	public void setAcceptAccount(String acceptAccount) {
		this.acceptAccount = acceptAccount;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getQueueTime() {
		return queueTime;
	}
	public void setQueueTime(String queueTime) {
		this.queueTime = queueTime;
	}
	public String getChannelCode() {
		return channelCode;
	}
	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
}
