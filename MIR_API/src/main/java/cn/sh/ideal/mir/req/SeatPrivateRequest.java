package cn.sh.ideal.mir.req;

import java.util.Date;

import cn.sh.ideal.model.BaseRequest;

/**
 * Created by ChenBo on 16-3-25.
 */
public class SeatPrivateRequest extends  BaseRequest {

    /**
	 * 
	 */
	private static final long serialVersionUID = 764957643962519293L;

//	@Override
//    public void check() throws RequestException {
//
//    }

    private int id;

    private String uuid;

    private String tenantCode;

    private String name;//可以是工号 名称 登录名

    private String workNo;//发送消息的工号

    private String accept;//接收消息的工号,这里只能是同一租户下的工号

    private Date createTime;

    private String type;//text face image attach

    private String content;//text url face_code

    private Object agent;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getAccept() {
        return accept;
    }

    public void setAccept(String accept) {
        this.accept = accept;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Object getAgent() {
        return agent;
    }

    public void setAgent(Object agent) {
        this.agent = agent;
    }

	@Override
	public String toString() {
		return "SeatPrivateRequest [id=" + id + ", uuid=" + uuid + ", tenantCode=" + tenantCode + ", name=" + name
				+ ", workNo=" + workNo + ", accept=" + accept + ", createTime=" + createTime + ", type=" + type
				+ ", content=" + content + ", agent=" + agent + "]";
	}
    
    
}
