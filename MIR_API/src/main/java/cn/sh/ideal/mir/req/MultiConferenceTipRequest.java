package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * 多方会议提示请求
 * <AUTHOR>
 *
 */
public class MultiConferenceTipRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6111625044760898424L;
	@NotEmpty
    private String sessionId;
	@NotEmpty
    private String tenantCode;
	@NotEmpty
    private String workNo;
	@NotEmpty
    private String target;
    private Object extData;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public Object getExtData() {
        return extData;
    }

    public void setExtData(Object extData) {
        this.extData = extData;
    }

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}
}
