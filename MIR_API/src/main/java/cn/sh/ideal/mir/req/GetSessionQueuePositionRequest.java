package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * Created by <PERSON>B<PERSON> on 16-3-25.
 */
public class GetSessionQueuePositionRequest extends BaseRequest {
    /**
	 * 
	 */
	private static final long serialVersionUID = 1025868541878047254L;
	@NotEmpty
    private String tenantCode;
	@NotEmpty
    private String queueId;
    private String sessionId;

    public String getQueueId() {
        return queueId;
    }

    public void setQueueId(String queueId) {
        this.queueId = queueId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	@Override
	public String toString() {
		return "GetSessionQueuePositionRequest [tenantCode=" + tenantCode + ", queueId=" + queueId + ", sessionId="
				+ sessionId + "]";
	}
    
}
