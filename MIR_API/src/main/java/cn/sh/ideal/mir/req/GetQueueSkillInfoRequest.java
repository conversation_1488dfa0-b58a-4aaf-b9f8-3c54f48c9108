package cn.sh.ideal.mir.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

/**
 * Created by ChenBo on 16-3-25.
 */
public class GetQueueSkillInfoRequest extends BaseRequest {

    /**
	 * 
	 */
	private static final long serialVersionUID = 3662232009224664663L;

	@NotEmpty
    private String tenantCode;
    private String queueIds;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

	public String getQueueIds() {
		return queueIds;
	}

	public void setQueueIds(String queueIds) {
		this.queueIds = queueIds;
	}

}
