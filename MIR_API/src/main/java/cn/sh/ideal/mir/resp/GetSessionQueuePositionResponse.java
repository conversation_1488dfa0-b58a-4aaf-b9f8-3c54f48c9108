package cn.sh.ideal.mir.resp;

import java.util.Map;

import cn.sh.ideal.model.BaseResponse;

/**
 * Created by ChenBo on 16-3-25.
 */
public class GetSessionQueuePositionResponse extends BaseResponse{
	
	
    /**
	 * 
	 */
	private static final long serialVersionUID = -3230443555465939835L;
	
	public GetSessionQueuePositionResponse() {
		
	}
	public GetSessionQueuePositionResponse(String resultCode, String resultMsg) {
        super(resultCode, resultMsg);
    }

	private Map<String,Object> data;

	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}

}
