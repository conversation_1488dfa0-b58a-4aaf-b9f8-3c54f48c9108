package cn.sh.ideal.mir.service;

import cn.sh.ideal.mir.req.*;
import cn.sh.ideal.mir.resp.AddRemoveWorkNoResponse;
import cn.sh.ideal.mir.resp.GetQueueCountResponse;
import cn.sh.ideal.mir.resp.GetQueueSkillDetailResponse;
import cn.sh.ideal.mir.resp.GetQueueSkillInfoResponse;
import cn.sh.ideal.mir.resp.GetSessionQueuePositionResponse;
import cn.sh.ideal.mir.resp.MultiConferenceTipResponse;
import cn.sh.ideal.mir.resp.NoticePushResponse;
import cn.sh.ideal.mir.resp.PushTipResponse;
import cn.sh.ideal.mir.resp.QueryPrivateMsgResponse;
import cn.sh.ideal.mir.resp.QueueSessionResponse;
import cn.sh.ideal.mir.resp.RemoveQueueSessionResponse;
import cn.sh.ideal.mir.resp.SeatMessageInfoResponse;
import cn.sh.ideal.mir.resp.SeatPrivateResponse;
import cn.sh.ideal.mir.resp.TakeOverTaskResponse;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;

/**
 * 消息分配服务接口
 *
 * <AUTHOR>
 * @date 2016-03-17
 */
public interface AllocateService {
    public  SeatMessageInfoResponse mediaSend(MessageInfo paramMessageInfo);

    public  AddRemoveWorkNoResponse mediaWorkNo(AddRemoveWorkNoRequest paramAddRemoveWorkNoRequest);

    public  MultiConferenceTipResponse inviteMeet(MultiConferenceTipRequest paramMultiConferenceTipRequest);

    public  SeatPrivateResponse sendPrivate(SeatPrivateRequest paramSeatPrivateRequest);

    public  GetQueueSkillDetailResponse getQueueInfo(GetQueueSkillDetailRequest paramGetQueueSkillDetailRequest);

    public  GetQueueSkillInfoResponse getSkillQueue(GetQueueSkillInfoRequest paramGetQueueSkillInfoRequest);

    public  TakeOverTaskResponse takeOverTask(TakeOverTaskRequest paramTakeOverTaskRequest);

    public  NoticePushResponse pushPost(NoticePushRequest paramNoticePushRequest);

    public  GetSessionQueuePositionResponse rank(GetSessionQueuePositionRequest paramGetSessionQueuePositionRequest);

    public  PushTipResponse pushTip(PushTipRequest paramPushTipRequest);

    public  QueryPrivateMsgResponse queryPriMsgs(QueryPrivateMsgRequest paramQueryPrivateMsgRequest);

    public  RemoveQueueSessionResponse removeSession(RemoveQueueSessionRequest paramRemoveQueueSessionRequest);

    public  GetQueueCountResponse getQueueCount(GetQueueCountRequest paramGetQueueCountRequest);

    public  QueueSessionResponse getQueueSessions(QueueSessionRequest paramQueueSessionRequest);

    public  BaseResponse pullSession(PullSessionRequest paramPullSessionRequest);

    public BaseResponse preQueueOpt(PreQueueOptRequest preQueueRequest);

}
