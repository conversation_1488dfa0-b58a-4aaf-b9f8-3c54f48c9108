package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;

/**
 * Created by <PERSON><PERSON><PERSON> on 16-3-25.
 */
public class MessageDirectRequest extends BaseRequest {
	
    /**
	 * 
	 */
	private static final long serialVersionUID = 3524868991662377003L;

//	@Override
//    public void check() throws RequestException {
//        RequestCheckUtils.checkNotEmpty(tenantCode,"tenantCode");
//        RequestCheckUtils.checkNotEmpty(workNo,"workNo");
//        RequestCheckUtils.checkNotEmpty(sessionId,"sessionId");
//    }

    private String tenantCode;
    private String workNo;
    private String sessionId;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

	@Override
	public String toString() {
		return "MessageDirectRequest [tenantCode=" + tenantCode + ", workNo=" + workNo + ", sessionId=" + sessionId
				+ "]";
	}
    
    
}
