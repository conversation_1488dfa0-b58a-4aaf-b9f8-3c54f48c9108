package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;

/**
 * Created by ChenBo on 16-3-25.
 */
public class GetQueueCountRequest extends BaseRequest {

    /**
	 * 
	 */
	private static final long serialVersionUID = -1261177450647999373L;

    private String tenantCode;
    private String skillQueue;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
   

	@Override
	public String toString() {
		return "GetQueueCountRequest [tenantCode=" + tenantCode + ", skillQueue=" + skillQueue + "]";
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

}
