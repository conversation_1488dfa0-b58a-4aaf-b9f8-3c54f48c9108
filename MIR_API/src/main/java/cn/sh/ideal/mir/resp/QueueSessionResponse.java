package cn.sh.ideal.mir.resp;

import java.util.List;

import cn.sh.ideal.mir.resp.entity.QueueupCalculator;
import cn.sh.ideal.model.BaseResponse;

/**
 * Created by ChenBo on 16-3-25.
 */
public class QueueSessionResponse extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3469173199334613240L;

	public QueueSessionResponse() {
	}
	
	public QueueSessionResponse(String resultCode,String resultMsg,List<QueueupCalculator> data){
		super(resultCode, resultMsg);
		this.setData(data);
	}

	/**
	 * @return the data
	 */
	public List<QueueupCalculator> getData() {
		return data;
	}

	/**
	 * @param data the data to set
	 */
	public void setData(List<QueueupCalculator> data) {
		this.data = data;
	}

	private List<QueueupCalculator> data;

}
