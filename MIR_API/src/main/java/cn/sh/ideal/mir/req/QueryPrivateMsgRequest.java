package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;

/**
 * Created by ChenBo on 16-3-25.
 */
public class QueryPrivateMsgRequest extends BaseRequest {

    /**
	 * 
	 */
	private static final long serialVersionUID = 5103880474603231013L;
	
    private String tenantCode;
    private int count=2;
    private String lastId;
    private String uuid;
    private String workNo;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

	@Override
	public String toString() {
		return "QueryPrivateMsgRequest [tenantCode=" + tenantCode + ", count=" + count + ", lastId=" + lastId
				+ ", uuid=" + uuid + ", workNo=" + workNo + "]";
	}
    
    
}
