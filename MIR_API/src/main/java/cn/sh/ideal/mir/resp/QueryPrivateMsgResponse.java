package cn.sh.ideal.mir.resp;

import java.util.List;

import cn.sh.ideal.mir.resp.entity.PrivateMsgModel;
import cn.sh.ideal.model.BaseResponse;

/**
 * Created by ChenBo on 16-3-25.
 */
public class QueryPrivateMsgResponse extends  BaseResponse {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -6105499399014308146L;

	public QueryPrivateMsgResponse(){}
	
    public QueryPrivateMsgResponse(String resultCode, String resultMsg) {
        super(resultCode, resultMsg);
    }

    private List<PrivateMsgModel> data;

	public List<PrivateMsgModel> getData() {
		return data;
	}

	public void setData(List<PrivateMsgModel> data) {
		this.data = data;
	}

}
