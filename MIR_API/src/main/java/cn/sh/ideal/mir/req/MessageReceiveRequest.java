package cn.sh.ideal.mir.req;

import cn.sh.ideal.model.BaseRequest;
import cn.sh.ideal.model.MessageInfo;

/**
 * Created by <PERSON>Bo on 16-3-25.
 */
public class MessageReceiveRequest extends BaseRequest {

    /**
	 * 
	 */
	private static final long serialVersionUID = -5755010870423823325L;

//	@Override
//    public void check() throws RequestException {
//    }

    private MessageInfo messageInfo;

    public MessageInfo getMessageInfo() {
        return messageInfo;
    }

    public void setMessageInfo(MessageInfo messageInfo) {
        this.messageInfo = messageInfo;
    }

	@Override
	public String toString() {
		return "MessageReceiveRequest [messageInfo=" + messageInfo + "]";
	}
    
    
}
