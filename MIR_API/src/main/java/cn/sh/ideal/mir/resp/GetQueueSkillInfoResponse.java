package cn.sh.ideal.mir.resp;

import java.util.List;

import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.TenantSkillQueue;

/**
 * Created by ChenBo on 16-3-25.
 */
public class GetQueueSkillInfoResponse extends BaseResponse {

    /**
	 * 
	 */
	private static final long serialVersionUID = -7380467122569917095L;
	
	
	public GetQueueSkillInfoResponse(){}
	
	public GetQueueSkillInfoResponse(String resultCode, String resultMsg) {
        super(resultCode, resultMsg);
    }

    private List<TenantSkillQueue> data;


	public List<TenantSkillQueue> getData() {
		return data;
	}

	public void setData(List<TenantSkillQueue> data) {
		this.data = data;
	}

  
}
