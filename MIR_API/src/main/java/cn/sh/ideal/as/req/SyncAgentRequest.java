package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;
/**
 * 
 * <AUTHOR>
 *
 */
public class SyncAgentRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -141591485349025824L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	/** 工号 */
	@NotEmpty
	private String workNo;
	/** 同步类型  add del*/
	@NotEmpty
	private String type;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}

}
