package cn.sh.ideal.as.resp;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.model.BaseResponse;

public class AgentInfoResponse extends BaseResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = 7887662199463429474L;
	
	private List<Map<Object,Object>> data;
	private Map<String,Integer> counter;
	
	public AgentInfoResponse(String resultCode, String resultMsg,List<Map<Object,Object>> data,Map<String,Integer> counter) {
		super(resultCode, resultMsg);
		this.data = data;
		this.counter = counter;
	}

	public List<Map<Object,Object>> getData() {
		return data;
	}

	public void setData(List<Map<Object,Object>> data) {
		this.data = data;
	}

	public Map<String, Integer> getCounter() {
		return counter;
	}

	public void setCounter(Map<String, Integer> counter) {
		this.counter = counter;
	}

}
