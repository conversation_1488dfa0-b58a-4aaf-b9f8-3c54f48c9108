package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class IvrStatusRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5071265358736258504L;
	@NotEmpty
	private String agentId;
	@NotEmpty
	private String status;
	
	public String getAgentId() {
		return agentId;
	}
	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}
