package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;
/**
 * 坐席实时报表request
 * <AUTHOR>
 *
 */
public class AgentRealTimeRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 2415308660582770848L;
	@NotEmpty
	private String tenantCode;
	private String skillQueue;
	private String channelArr;
	private String workNo;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getSkillQueue() {
		return skillQueue;
	}
	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}
	public String getChannelArr() {
		return channelArr;
	}
	public void setChannelArr(String channelArr) {
		this.channelArr = channelArr;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

}
