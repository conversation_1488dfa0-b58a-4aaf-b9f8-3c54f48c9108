package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class AgentBusiAcceptanceRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5951285776815358266L;
	@NotEmpty
	private String tenantCode;
	private String workNo;
	private String startTime;
	private String endTime;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
}
