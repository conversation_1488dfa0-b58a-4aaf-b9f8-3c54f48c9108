package cn.sh.ideal.as.service;


import java.util.List;

import cn.sh.ideal.as.req.AgentCoordinateRequest;
import cn.sh.ideal.as.req.AgentInfoRequest;
import cn.sh.ideal.as.req.AgentSingleRequest;
import cn.sh.ideal.as.req.BusinessTypeRequest;
import cn.sh.ideal.as.req.BusyRequest;
import cn.sh.ideal.as.req.FreeRequest;
import cn.sh.ideal.as.req.IvrStatusRequest;
import cn.sh.ideal.as.req.LoginRequest;
import cn.sh.ideal.as.req.LogoutRequest;
import cn.sh.ideal.as.req.ScanLoginRequest;
import cn.sh.ideal.as.req.SigninRequest;
import cn.sh.ideal.as.req.SignoutRequest;
import cn.sh.ideal.as.req.SrvpRequest;
import cn.sh.ideal.as.req.TransferRequest;
import cn.sh.ideal.as.resp.AgentInfoResponse;
import cn.sh.ideal.as.resp.AgentSingleResponse;
import cn.sh.ideal.as.resp.BusinessTypeResponse;
import cn.sh.ideal.as.resp.BusyResponse;
import cn.sh.ideal.as.resp.FreeResponse;
import cn.sh.ideal.as.resp.LoginResponse;
import cn.sh.ideal.as.resp.SigninResponse;
import cn.sh.ideal.as.resp.SignoutResponse;
import cn.sh.ideal.as.resp.SrvpResponse;
import cn.sh.ideal.model.BaseResponse;

public interface AgentService {
	/**
	 * 登录
	 * 
	 * @param request
	 * @return 
	 */
	public LoginResponse login(LoginRequest request) ;
	
	/**
	 * 签入
	 * @param request
	 * @return
	 */
	public SigninResponse signIn(SigninRequest request) ;
	
	/**
	 * 示闲
	 * @param request
	 * @return
	 */
	public FreeResponse setFree(FreeRequest request) ;
	
	/**
	 * 示忙
	 * @param request
	 * @return
	 */
	public BusyResponse setBusy(BusyRequest request) ;
	
	/**
	 * 签出
	 * @param request
	 * @return
	 */
	public SignoutResponse signOut(SignoutRequest request) ;
	
	/**
	 * 登出
	 * @param request
	 * @return
	 */
	public BaseResponse logout(LogoutRequest request) ;
	
	/**
	 * 坐席信息-列表
	 * @param request
	 * @return
	 */
	public AgentInfoResponse agentInfo(AgentInfoRequest request);
	
	/**
	 * 坐席信息-单个
	 * @param request
	 * @return
	 */
	public AgentSingleResponse agentInfo(AgentSingleRequest request);
	
	/**
	 * 转发到坐席
	 * @param request
	 * @return
	 */
	public BaseResponse transferTaskUser(TransferRequest request) ;
	
	/**
	 * 批量转发到坐席
	 * @param request
	 * @return
	 */
	public BaseResponse batchTransferTaskSkillQueue(List<TransferRequest> request);
	
	/**
	 * 批量转发到坐席
	 * @param request
	 * @return
	 */
	public BaseResponse batchTransferTaskUser(List<TransferRequest> request);
	
	/**
	 * 转发到技能组
	 * @param request
	 * @return
	 */
	public BaseResponse transferTaskSkillQueue(TransferRequest request) ;
	
	/**
	 * 坐标上传
	 * @param request
	 * @return
	 */
	public BaseResponse uploadLocation(AgentCoordinateRequest request) ;
	
	/**
	 * 扫码登录
	 * @param request
	 * @return
	 */
	public BaseResponse scanLogin(ScanLoginRequest request);
	
	/**
	 * 服务专员信息
	 * @param request
	 * @return
	 */
	public SrvpResponse srvp(SrvpRequest request);
	
	/**
	 * 业务类型数据
	 * @param jsonParam
	 * @return
	 */
	public BusinessTypeResponse getBusinessType(BusinessTypeRequest request);
	
	/**
	 * 坐席语音状态更新
	 * @param IvrStatusRequest
	 * @return
	 */
	public BaseResponse updateVoiceAgentStatus(IvrStatusRequest request);
}
