package cn.sh.ideal.as.service;

import cn.sh.ideal.as.req.ResetAgentRequest;
import cn.sh.ideal.as.req.SyncAgentRequest;
import cn.sh.ideal.as.req.SyncSkillQueueRequest;
import cn.sh.ideal.as.req.SyncSkillTypeRequest;
import cn.sh.ideal.model.BaseResponse;
/**
 * 数据同步接口
 * <AUTHOR>
 *
 */
public interface SyncService {
	/**
	 * 同步坐席
	 * @param request
	 * @return
	 */
	public BaseResponse syncAgentInfo(SyncAgentRequest request);
	
	/**
	 * 同步技能组
	 * @param request
	 * @return
	 */
	public BaseResponse syncSkillQueue(SyncSkillQueueRequest request);
	
	/**
	 * 同步技能组类型
	 * @param request
	 * @return
	 */
	public BaseResponse syncSkillType(SyncSkillTypeRequest request);
	
	/**
	 * 重置坐席信息
	 * @param request
	 * @return
	 */
	public BaseResponse resetAgent(ResetAgentRequest request);
	
	/**
	 * 同步系统参数
	 * @return
	 */
	public BaseResponse syncSysParam();
	
	/**
	 * 重新加载缓存数据
	 * @return
	 */
	public BaseResponse reload();
}
