package cn.sh.ideal.as.resp;

import java.util.List;

import cn.sh.ideal.as.resp.entity.AgentSatisfactionReportModel;
import cn.sh.ideal.model.BaseResponse;

public class AgentSatisfiedResponse extends BaseResponse{
	/**
	 * 
	 */
	private static final long serialVersionUID = -3408221973436299909L;
	private List<AgentSatisfactionReportModel> data;

	public AgentSatisfiedResponse(String resultCode, String resultMsg, List<AgentSatisfactionReportModel> data) {
		super(resultCode, resultMsg);
		this.setData(data);
	}

	public List<AgentSatisfactionReportModel> getData() {
		return data;
	}

	public void setData(List<AgentSatisfactionReportModel> data) {
		this.data = data;
	}

}
