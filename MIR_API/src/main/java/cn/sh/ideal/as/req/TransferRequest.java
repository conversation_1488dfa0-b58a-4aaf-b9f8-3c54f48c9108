package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class TransferRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5135210932543679520L;
	@NotEmpty
	private String sessionId;
	@NotEmpty
	private String forward;
	@NotEmpty
	private String tenantCode;
	//类型 1转发到坐席 2转发到技能组
	@NotEmpty
	private String type;
	
	private String isForce;
	private String remark;
	
	private String ignoreStatus;
	
	public String getIgnoreStatus() {
		return ignoreStatus;
	}
	public void setIgnoreStatus(String ignoreStatus) {
		this.ignoreStatus = ignoreStatus;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getForward() {
		return forward;
	}
	public void setForward(String forward) {
		this.forward = forward;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getIsForce() {
		return isForce;
	}
	public void setIsForce(String isForce) {
		this.isForce = isForce;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

}
