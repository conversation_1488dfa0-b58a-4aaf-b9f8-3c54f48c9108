package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class LoginRequest extends BaseRequest{
	private static final long serialVersionUID = -6804979861966647541L;
	@NotEmpty
	private String userName;
	@NotEmpty
	private String passWord;
	@NotEmpty
	private String port;
	/** 客户端类型：
	 * 0-普通，
	 * 10-移动客户端(iPhone)，
	 * 10-移动客户端(iPad)，
	 * 20-移动客户端(Android Mobile)，
	 * 21-移动客户端(Android Pad) 
	 */
	private String clientType;
	private String clientSessionId;
	/** 是否强制登录 Y N */
	private String isForce;
	/** 服务中心 */
	private String serviceCenter;
	/** 是否是统一登陆 1是 */
	private String websso;
	
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getPassWord() {
		return passWord;
	}
	public void setPassWord(String passWord) {
		this.passWord = passWord;
	}
	public String getPort() {
		return port;
	}
	public void setPort(String port) {
		this.port = port;
	}
	public String getClientType() {
		return clientType;
	}
	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	public String getClientSessionId() {
		return clientSessionId;
	}
	public void setClientSessionId(String clientSessionId) {
		this.clientSessionId = clientSessionId;
	}
	public String getIsForce() {
		return isForce;
	}
	public void setIsForce(String isForce) {
		this.isForce = isForce;
	}
	public String getWebsso() {
		return websso;
	}
	public void setWebsso(String websso) {
		this.websso = websso;
	}
	
	public static void main(String[] args) throws Exception {
		LoginRequest lr = new LoginRequest();
		lr.check();
	}
	public String getServiceCenter() {
		return serviceCenter;
	}
	public void setServiceCenter(String serviceCenter) {
		this.serviceCenter = serviceCenter;
	}
	
}
