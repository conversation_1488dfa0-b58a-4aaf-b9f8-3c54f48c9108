package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class SrvpRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8514333620103088348L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	/** 工号 */
	private String workNo;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

}
