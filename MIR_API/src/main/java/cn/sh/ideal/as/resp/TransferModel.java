package cn.sh.ideal.as.resp;

import java.io.Serializable;

import cn.sh.ideal.model.BaseResponse;

public class TransferModel extends BaseResponse implements Serializable{

	private static final long serialVersionUID = 578038348823994764L;
	
	private Object data;

	public TransferModel(String resultCode, String resultMsg,Object data){
		super(resultCode, resultMsg);
		this.data = data;
	}
	

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}
	
	

}
