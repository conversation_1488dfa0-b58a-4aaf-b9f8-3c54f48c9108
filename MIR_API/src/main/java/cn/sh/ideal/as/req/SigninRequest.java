package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class SigninRequest extends BaseRequest{
	private static final long serialVersionUID = 7619709064661411854L;
	@NotEmpty
	private String tenantCode;
	@NotEmpty
	private String workNo;
	@NotEmpty
	private String skillQueue;
	
	private String isForce;

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getIsForce() {
		return isForce;
	}

	public void setIsForce(String isForce) {
		this.isForce = isForce;
	}
}
