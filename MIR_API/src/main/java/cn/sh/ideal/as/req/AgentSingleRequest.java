package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;
/**
 * 单个坐席状态信息request
 * <AUTHOR>
 *
 */
public class AgentSingleRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5792657101562112933L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	/** 工号 */
	@NotEmpty
	private String workNo;
	/** 技能组id */
	private String skillQueue;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getSkillQueue() {
		return skillQueue;
	}
	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}
}
