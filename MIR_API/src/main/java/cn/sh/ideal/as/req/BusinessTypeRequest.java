package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class BusinessTypeRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -453051979391180513L;
	@NotEmpty
	private String tenantCode;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
}
