package cn.sh.ideal.as.service;

import cn.sh.ideal.as.req.AgentBusiAcceptanceRequest;
import cn.sh.ideal.as.req.AgentInfoRequest;
import cn.sh.ideal.as.req.AgentSatisfiedRequest;
import cn.sh.ideal.as.req.AgentServiceRequest;
import cn.sh.ideal.as.resp.AgentBusiAcceptanceResponse;
import cn.sh.ideal.as.resp.AgentInfoResponse;
import cn.sh.ideal.as.resp.AgentSatisfiedResponse;
import cn.sh.ideal.as.resp.AgentServiceResponse;
/**
 * 坐席报表service
 * <AUTHOR>
 *
 */
public interface AgentReportService {
	/**
	 * 坐席实时数据
	 * @param request
	 * @return
	 */
	public AgentInfoResponse agentStateReport(AgentInfoRequest request);
	
	/**
	 * 坐席服务数量及服务时长
	 * @param request
	 * @return
	 */
	public AgentBusiAcceptanceResponse agentBusinessAcceptanceReport(AgentBusiAcceptanceRequest request);
	
	/**
	 * 坐席满意度
	 * @param request
	 * @return
	 */
	public AgentSatisfiedResponse agentSatisfiedReport (AgentSatisfiedRequest request);
	
	/**
	 * 坐席服务信息
	 * @param request
	 * @return
	 */
	public AgentServiceResponse agentServiceReport(AgentServiceRequest request);
	
}
