package cn.sh.ideal.as.req;

import com.alibaba.fastjson.JSONObject;

public class BusyRequest extends SigninRequest{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4178722474810224826L;
	private String busyType;
	
	public String getBusyType() {
		return busyType;
	}
	public void setBusyType(String busyType) {
		this.busyType = busyType;
	}
	
	public static void main(String[] args) {
		String json = "{'tenantCode':'530000','busyType':'1'}";
		BusyRequest br = JSONObject.parseObject(json, BusyRequest.class);
		
		System.out.println(br.getTenantCode());
	}
}
