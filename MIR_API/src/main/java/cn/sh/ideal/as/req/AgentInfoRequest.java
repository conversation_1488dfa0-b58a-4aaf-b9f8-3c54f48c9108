package cn.sh.ideal.as.req;

import org.apache.commons.lang.StringUtils;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class AgentInfoRequest extends BaseRequest implements Cloneable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 9027108722805264616L;
	/**组织架构ID*/
	private String deptId;
	
	/**工号*/
	private String workNo;
	
	/**用户名*/
	private String userName;
	
	/**租户*/
	@NotEmpty
	private String tenantCode;
	
	/**班组*/
	private String teamId;
	
	/**坐席类型*/
	private String agentType;
	
	/**分机号*/
	private String exNo;
	
	/**技能组*/
	private String skillQueue;
	
	/**坐席状态*/
	private String status;
	
	/**可接入渠道*/
	private String channelArr;
	
	private String channel;
	
	private String businessArr;
	/** 是否合并工号技能组 1 合并 0 不合并*/
	private String merge;

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getTeamId() {
		return teamId;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public String getAgentType() {
		return agentType;
	}

	public void setAgentType(String agentType) {
		this.agentType = agentType;
	}

	public String getExNo() {
		return exNo;
	}

	public void setExNo(String exNo) {
		this.exNo = exNo;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}


	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getChannelArr() {
		return StringUtils.defaultIfEmpty(channelArr, channel);
	}

	public void setChannelArr(String channelArr) {
		this.channelArr = channelArr;
	}

	public String getBusinessArr() {
		return businessArr;
	}

	public void setBusinessArr(String businessArr) {
		this.businessArr = businessArr;
	}

	public String getMerge() {
		return merge;
	}

	public void setMerge(String merge) {
		this.merge = merge;
	}

	public String getChannel() {
		return null;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}
}
