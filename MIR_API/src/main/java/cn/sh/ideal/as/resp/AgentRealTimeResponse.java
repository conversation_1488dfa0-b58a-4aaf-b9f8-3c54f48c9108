package cn.sh.ideal.as.resp;

import java.util.Map;

import cn.sh.ideal.model.BaseResponse;

public class AgentRealTimeResponse extends BaseResponse{
	/**
	 * 
	 */
	private static final long serialVersionUID = 8349830034264218722L;
	
	private Map<String, Object> data;

	public AgentRealTimeResponse(String resultCode, String resultMsg, Map<String, Object> data) {
		super(resultCode, resultMsg);
		this.data = data;
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}

}
