package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;
/**
 * 同步技能组分类request
 * <AUTHOR>
 *
 */
public class SyncSkillTypeRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5509982565580431474L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
}
