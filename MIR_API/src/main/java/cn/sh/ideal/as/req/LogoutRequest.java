package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class LogoutRequest extends BaseRequest{
	/**
	 * 
	 */
	private static final long serialVersionUID = -5756364424490635223L;
	@NotEmpty
	private String tenantCode;
	@NotEmpty
	private String workNo;
	

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
}
