package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class ScanLoginRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8463491252383759260L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	/** 工号 */
	@NotEmpty
	private String workNo;
	/** 新websocket通道id */
	@NotEmpty
	private String websocketSessionIdNew;
	/** 老websocket通道id */
	@NotEmpty
	private String websocketSessionIdOld;
	
	private String websocketIPNew;
	/** 端口号 */
	@NotEmpty
	private String port;
	/** 客户端类型：1，pc,2,移动端 */
	private String clientType;
	/** 是否需要验证验证码，0：不需要验证；1：需要验证，默认需要验证 */
	private String type;
	/** 验证码*/
	private String verifyCode;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getWebsocketSessionIdNew() {
		return websocketSessionIdNew;
	}
	public void setWebsocketSessionIdNew(String websocketSessionIdNew) {
		this.websocketSessionIdNew = websocketSessionIdNew;
	}
	public String getWebsocketSessionIdOld() {
		return websocketSessionIdOld;
	}
	public void setWebsocketSessionIdOld(String websocketSessionIdOld) {
		this.websocketSessionIdOld = websocketSessionIdOld;
	}
	public String getClientType() {
		return clientType;
	}
	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getVerifyCode() {
		return verifyCode;
	}
	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}
	public String getPort() {
		return port;
	}
	public void setPort(String port) {
		this.port = port;
	}
	public String getWebsocketIPNew() {
		return websocketIPNew;
	}
	public void setWebsocketIPNew(String websocketIPNew) {
		this.websocketIPNew = websocketIPNew;
	}
	
}
