package cn.sh.ideal.as.resp.entity;

import java.io.Serializable;

public class AgentSatisfactionReportModel implements Serializable {
	
	private static final long serialVersionUID = 1L;

    private String workNo;//工号

    private String totalScore;//总分
    
    private String satisfactionRate;//满意率

    private String totalNum;//坐席评论数量
    
    private String averageScore;//坐席平均分数
    
    private String userName;//坐席姓名

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(String totalScore) {
		this.totalScore = totalScore;
	}

	public String getSatisfactionRate() {
		return satisfactionRate;
	}

	public void setSatisfactionRate(String satisfactionRate) {
		this.satisfactionRate = satisfactionRate;
	}

	public String getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(String totalNum) {
		this.totalNum = totalNum;
	}

	public String getAverageScore() {
		return averageScore;
	}

	public void setAverageScore(String averageScore) {
		this.averageScore = averageScore;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
}