package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;
/**
 * 同步技能组request
 * <AUTHOR>
 *
 */
public class SyncSkillQueueRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = 19204980072589885L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	/** 技能组id */
	@NotEmpty
	private String skillQueue;
	/** 同步类型  add、update、del */
	@NotEmpty
	private String type;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getSkillQueue() {
		return skillQueue;
	}
	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}

}
