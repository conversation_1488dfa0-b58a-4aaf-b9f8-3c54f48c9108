package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;

public class AgentCoordinateRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8108752274112701824L;
	@NotEmpty
	private String tenantCode;
	@NotEmpty
	private String workNo;
	@NotEmpty
	private String X;
	@NotEmpty
	private String Y;
	private String label;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public String getX() {
		return X;
	}
	public void setX(String x) {
		X = x;
	}
	public String getY() {
		return Y;
	}
	public void setY(String y) {
		Y = y;
	}
	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
}
