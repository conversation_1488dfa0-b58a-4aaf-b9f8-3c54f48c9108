package cn.sh.ideal.as.req;

import cn.sh.ideal.annotation.NotEmpty;
import cn.sh.ideal.model.BaseRequest;
/**
 * 坐席状态重置request
 * <AUTHOR>
 *
 */
public class ResetAgentRequest extends BaseRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -454306407882970590L;
	/** 租户code */
	@NotEmpty
	private String tenantCode;
	/** 工号 */
	@NotEmpty
	private String workNo;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

}
