package cn.sh.ideal.as.resp;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.model.BaseResponse;

public class LoginResponse extends BaseResponse{

	private static final long serialVersionUID = 384165772518354792L;
	
	private List<Map<Object,Object>> data;
	
	public LoginResponse(String resultCode, String resultMsg,List<Map<Object,Object>> data) {
		super(resultCode, resultMsg);
		this.data = data;
	}

	public List<Map<Object, Object>> getData() {
		return data;
	}

	public void setData(List<Map<Object, Object>> data) {
		this.data = data;
	}
	
}
