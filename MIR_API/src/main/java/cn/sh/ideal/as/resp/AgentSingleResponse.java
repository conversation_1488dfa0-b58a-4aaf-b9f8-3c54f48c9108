package cn.sh.ideal.as.resp;

import cn.sh.ideal.model.AgentInfo;
import cn.sh.ideal.model.BaseResponse;

public class AgentSingleResponse extends BaseResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6215114651923450312L;
	private AgentInfo data;

	public AgentSingleResponse(String resultCode, String resultMsg,AgentInfo data) {
		super(resultCode, resultMsg);
		this.data = data;
	}

	public AgentInfo getData() {
		return data;
	}

	public void setData(AgentInfo data) {
		this.data = data;
	}

}
