package cn.sh.ideal.as.resp;

import java.util.List;

import cn.sh.ideal.as.resp.entity.AgentReportModel;
import cn.sh.ideal.model.BaseResponse;

public class AgentBusiAcceptanceResponse extends BaseResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1140681626199687696L;
	
	private List<AgentReportModel> data;

	public AgentBusiAcceptanceResponse(String resultCode, String resultMsg, List<AgentReportModel> data) {
		super(resultCode, resultMsg);
		this.data = data;
	}

	public List<AgentReportModel> getData() {
		return data;
	}

	public void setData(List<AgentReportModel> data) {
		this.data = data;
	}

}
