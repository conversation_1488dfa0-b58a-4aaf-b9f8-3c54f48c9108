/**
 * 
 */
package cn.sh.ideal.transcoding.model.res;

import java.io.Serializable;

/**
 * @project MGW_API
 * @Package cn.sh.ideal.mgw.server.model.response
 * @typeName BaseResponse
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月31日 上午10:32:39
 * @version 
 */
public class BaseResponseDTO implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	public final static String SUCCESS_CODE = "0";
	
	public final static String ERROR_CODE = "-1";
	
	public final static String SUCCESS_MSG = "success.";
	
	
	private String resultCode;//返回编码
	private Object resultMsg;//返回消息
	
	public BaseResponseDTO(String resultCode,Object resultMsg){
		this.resultCode = resultCode;
		this.resultMsg = resultMsg;
	}
	
	public BaseResponseDTO() {
		super();
	}

	public String getResultCode() {
		return resultCode;
	}
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	public Object getResultMsg() {
		return resultMsg;
	}
	public void setResultMsg(Object resultMsg) {
		this.resultMsg = resultMsg;
	}

	@Override
	public String toString() {
		return "BaseResponseDto [resultCode=" + resultCode + ", resultMsg="
				+ resultMsg + "]";
	}
	
}
