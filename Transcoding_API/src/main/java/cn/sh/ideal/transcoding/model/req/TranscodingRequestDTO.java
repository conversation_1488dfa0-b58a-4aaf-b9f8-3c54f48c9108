/**
 * 
 */
package cn.sh.ideal.transcoding.model.req;

import java.io.Serializable;

/**
 * @project Transcoding_API
 * @Package cn.sh.ideal.transcoding.model
 * @typeName TranscodingRequestDTO
 * <AUTHOR>
 * @Description:  
 * @date 2016年4月11日 下午3:39:24
 * @version 
 */
public class TranscodingRequestDTO implements Serializable {
	
	// 文件路径
	private String path;
	
	private String ftpPath;
	
	private String ftpConfig;
	
	// 文件名
	private String fileName;
	// 源文件类型
	private String sourceFileType;
	// 目标文件类型
	private String targetFileType;
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public String getFtpPath() {
		return ftpPath;
	}
	public void setFtpPath(String ftpPath) {
		this.ftpPath = ftpPath;
	}
	public String getFtpConfig() {
		return ftpConfig;
	}
	public void setFtpConfig(String ftpConfig) {
		this.ftpConfig = ftpConfig;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public String getSourceFileType() {
		return sourceFileType;
	}
	public void setSourceFileType(String sourceFileType) {
		this.sourceFileType = sourceFileType;
	}
	public String getTargetFileType() {
		return targetFileType;
	}
	public void setTargetFileType(String targetFileType) {
		this.targetFileType = targetFileType;
	}
}
