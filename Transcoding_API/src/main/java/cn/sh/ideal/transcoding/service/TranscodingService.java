/**
 * 
 */
package cn.sh.ideal.transcoding.service;

import cn.sh.ideal.transcoding.model.req.TranscodingRequestDTO;
import cn.sh.ideal.transcoding.model.res.BaseResponseDTO;

/**
 * @project Transcoding_API
 * @Package cn.sh.ideal.transcoding.server
 * @typeName TranscodingService
 * <AUTHOR>
 * @Description:  
 * @date 2016年3月28日 下午5:15:37
 * @version 
 */
public interface TranscodingService {
	
	/**
	 * @Description 
	 */
	public BaseResponseDTO transcoding(TranscodingRequestDTO transcodingRequestDTO);
	
	
}
