package cn.sh.ideal.exception;

import java.io.InputStream;
import java.util.Properties;

import org.apache.commons.lang3.StringUtils;

/**
 * 统一异常
 * <AUTHOR>
 *
 */
public class MultiMediaException extends RuntimeException{
	/**
	 * 
	 */
	private static final long serialVersionUID = -3698708308350480474L;
	
	//错误码 
	private String excpCode;
	
	//错误描述
	private String message;
	
	//错误码对照文件
	private static Properties p;
	
	private final static String UNKNOW_EXCEPTION = "未知错误";
	
	static{
		InputStream ins=MultiMediaException.class.getResourceAsStream("exception.properties");  
		p = new Properties(); 
		try {  
			p.load(ins);  
		} catch (Exception e) {  
			e.printStackTrace();  
		}  
	}
	
	public MultiMediaException(){
		super();
	}
	
	public MultiMediaException(Throwable e){
		super(e);
	}
	
	public MultiMediaException(String excpCode){
		this.excpCode = excpCode;
	}
	
	public MultiMediaException(String excpCode,String message){
		this.excpCode = excpCode;
		this.message = message;
	}

	public String getExcpCode() {
		return excpCode;
	}

	@Override
	public String getMessage() {
		return StringUtils.isEmpty(this.message) ? p.getProperty(this.excpCode,UNKNOW_EXCEPTION) : this.message;
	}
	
	public static void main(String[] args) {
		Throwable  e = new MultiMediaException("0000","www");
		System.out.println(e.getMessage());
	}

}
