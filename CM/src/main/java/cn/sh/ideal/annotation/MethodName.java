/**
n  * Project Name:SM Maven Webapp
 * File Name:MethodName.java
 * Package Name:cn.sh.ideal.mir.session.annotation
 * Date:2015年1月20日上午10:26:14
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ClassName:MethodName <br/>
 * Function: 自定义annotation 用户标记方法注释
 * Date:     2015年1月20日 上午10:26:14 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface MethodName {
    /**
     * 
     * name:(方法名称). <br/>
     * 用于注释  目标方法  目前aop拦截到的方法有此注释可记入到日志中
     * <AUTHOR>
     * @return 
     * @return String
     * @since JDK 1.6
     */
    String name();
    /**
     * 
     * moduleName:(模块名称，区分多个项目). <br/>
     *
     * <AUTHOR>
     * @return 
     * @return String
     * @since JDK 1.6
     */
    String moduleName() default "";

}
