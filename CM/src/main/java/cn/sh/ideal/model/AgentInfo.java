package cn.sh.ideal.model;

import java.io.Serializable;


/**
 * 坐席详细信息
 * 
 * Date:     2015年4月17日 下午13:10:00 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6  多媒体v1.1.8
 *
 */
public class AgentInfo implements Serializable,Cloneable{

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	

	private String autoId;
	
	/**组织架构ID*/
	private String deptId;
	
	/**工号*/
	private String workNo;
	
	/**用户名*/
	private String userName;
	
	/**租户*/
	private String tenantCode;
	
	/**电话*/
	private String tel;
	
	/**邮件*/
	private String email;
	/**班组*/
	private String teamId;
	
	private String teamName;
	
	/**自动示忙*/
	private String autoBusy;
	
	/**自动回复*/
	private String autoAnswer;
	
	/**坐席类型*/
	private String agentType;
	
	/**分机号*/
	private String exNo;
	
	/**技能组*/
	private String skillQueue;
	
	/**技能组名称*/
	private String skillQueueName;
	
	/**技能组类型code*/
	private String skillTypeNum;
	
	/**坐席状态*/
	private String status;
	
	/**坐席状态描述*/
	private String statusDesc;
	/** 登录时间*/
	private String loginTime;
	/**签入时间*/
	private String signTime;
	
	/**操作时间*/
	private String optTime;
	
	/**是否强制*/
	private String isForce;
	
	/**可接入渠道*/
	private String channelArr;
	
	private String businessArr;

	/**最大会话数*/
	private int maxSessionCount;

	/**当前会话数*/
	private int currSessionCount;
	/**
     * 客户端类型：
     * 0-普通，
     * 10-移动客户端(iPhone)，
     * 10-移动客户端(iPad)，
     * 20-移动客户端(Android Mobile)，
     * 21-移动客户端(Android Pad)
     * */
	private String clientType;
	
	/**客户端id*/
	private String clientSessionId;
	
	/**签入日志id*/
	private String signId;
	
	/**示忙/示闲日志id*/
	private String bfId;
	
	/**坐席坐标*/
	private Object location;
	
	/**占用率*/
	private float occupancy;
	/**上一次分配会话时间*/
	private String lastSessionTime;
	
	private String flagImage;

	private String serviceTipWords;
	private String ivrAgent;
	private Object extData;
	
	private Object serviceCenter;

	public String getFlagImage() {
		return flagImage;
	}

	public void setFlagImage(String flagImage) {
		this.flagImage = flagImage;
	}

	public float getOccupancy() {
		return occupancy;
	}

	public void setOccupancy(float occupancy) {
		this.occupancy = occupancy;
	}

	public String getLastSessionTime() {
		return lastSessionTime;
	}

	public void setLastSessionTime(String lastSessionTime) {
		this.lastSessionTime = lastSessionTime;
	}

	/**
	 * 坐席状态
	 *
	 */
	public enum AgentStatus{
		OFFLINE("0","未登录"),//离线
		LOGIN("1","登录未签入"),//登录未签入
		SIGNIN("2","签入"),//签入
		FREE("3","示闲"),//示闲
		BUSY("4","示忙");//示忙
		
		private String code;
		private String desc;
		
		AgentStatus(String code,String desc){
			this.setCode(code);
			this.setDesc(desc);
		}
		
		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public String getDesc() {
			return desc;
		}

		public void setDesc(String desc) {
			this.desc = desc;
		}
		
	}
	
	public String getAutoId() {
		return autoId;
	}

	public void setAutoId(String autoId) {
		this.autoId = autoId;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getTeamId() {
		return teamId;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public String getAutoBusy() {
		return autoBusy;
	}

	public void setAutoBusy(String autoBusy) {
		this.autoBusy = autoBusy;
	}

	public String getAutoAnswer() {
		return autoAnswer;
	}

	public void setAutoAnswer(String autoAnswer) {
		this.autoAnswer = autoAnswer;
	}

	public String getAgentType() {
		return agentType;
	}

	public void setAgentType(String agentType) {
		this.agentType = agentType;
	}

	public String getExNo() {
		return exNo;
	}

	public void setExNo(String exNo) {
		this.exNo = exNo;
	}


	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOptTime() {
		return optTime;
	}

	public void setOptTime(String optTime) {
		this.optTime = optTime;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	

	public String getChannelArr() {
		return channelArr;
	}

	public void setChannelArr(String channelArr) {
		this.channelArr = channelArr;
	}

	 @Override  
	 public Object clone() {  
		 AgentInfo agent = null;  
		 try {  
			 agent = (AgentInfo) super.clone();  
		 } catch (CloneNotSupportedException e) {  
			 e.printStackTrace();  
		 }  

		 return agent;  
	 }

	public String getSkillTypeNum() {
		return skillTypeNum;
	}

	public void setSkillTypeNum(String skillTypeNum) {
		this.skillTypeNum = skillTypeNum;
	}

	public int getMaxSessionCount() {
		return maxSessionCount;
	}

	public void setMaxSessionCount(int maxSessionCount) {
		this.maxSessionCount = maxSessionCount;
	}

	public int getCurrSessionCount() {
		return currSessionCount;
	}

	public void setCurrSessionCount(int currSessionCount) {
		this.currSessionCount = currSessionCount;
	}

	public String getBusinessArr() {
		return businessArr;
	}

	public void setBusinessArr(String businessArr) {
		this.businessArr = businessArr;
	}

	public String getIsForce() {
		return isForce;
	}

	public void setIsForce(String isForce) {
		this.isForce = isForce;
	}

	public String getSignId() {
		return signId;
	}

	public void setSignId(String signId) {
		this.signId = signId;
	}

	public String getBfId() {
		return bfId;
	}

	public void setBfId(String bfId) {
		this.bfId = bfId;
	}

	public String getSkillQueueName() {
		return skillQueueName;
	}

	public void setSkillQueueName(String skillQueueName) {
		this.skillQueueName = skillQueueName;
	}

	public String getClientType() {
		return clientType;
	}

	public void setClientType(String clientType) {
			this.clientType = clientType;
	}

	public Object getLocation() {
		return location;
	}

	public void setLocation(Object location) {
		this.location = location;
	}

	public String getLoginTime() {
		return loginTime;
	}

	public void setLoginTime(String loginTime) {
		this.loginTime = loginTime;
	}

	public String getSignTime() {
		return signTime;
	}

	public void setSignTime(String signTime) {
		this.signTime = signTime;
	}

	public String getClientSessionId() {
		return clientSessionId;
	}

	public void setClientSessionId(String clientSessionId) {
		this.clientSessionId = clientSessionId;
	}

	public String getServiceTipWords() {
		return serviceTipWords;
	}

	public void setServiceTipWords(String serviceTipWords) {
		this.serviceTipWords = serviceTipWords;
	}

	public String getIvrAgent() {
		return ivrAgent;
	}

	public void setIvrAgent(String ivrAgent) {
		this.ivrAgent = ivrAgent;
	}

	public Object getExtData() {
		return extData;
	}

	public void setExtData(Object extData) {
		this.extData = extData;
	}

	public String getTeamName() {
		return teamName;
	}

	public void setTeamName(String teamName) {
		this.teamName = teamName;
	}

	public Object getServiceCenter() {
		return serviceCenter;
	}

	public void setServiceCenter(Object serviceCenter) {
		this.serviceCenter = serviceCenter;
	}  
	
}
