package cn.sh.ideal.model;

import java.io.Serializable;

/**
 *This is an Entity, db table is:MGW_TENANT_SKILL_QUEUE
 *技能组信息
 *<AUTHOR>
 *@date 2014-05-26 18:35:08
 */
public class TenantSkillQueue implements Serializable {
    

    private static final long serialVersionUID = 1L;


    /** 主键 */
    private Integer id;

    /** 队列名称 */
    private String queueName;

    /** 队列编号 */
    private String queueCode;

    /** 队列大小 */
    private Integer queueSize;

    /** 所属客户 */
    private String tenantId;

    /** 多个业务分类以,分割 */
    private String ableBusinessTypes;

    /** 多个渠道以,分割 */
    private String ableChannels;

    /** 排队规则 */
    private String sortRule;

    /** 分配规则 */
    private String allocationRule;

    /** 0:无效 1:有效 */
    private String status;

    /** 备注 */
    private String remark;
    
    private String isDefault;
    private String routeRule;
    
    /**
     *并发线程数
     */
    private Integer concurrentCount;
    
    /**类型名称 */
    
    private int freeAgents;
   
    private String welcomeMessage;
    private String lineMessage;
    private String connectMessage; 
    private String workTime;
    
    private String weight;	//权重
   
    public String getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(String isDefault) {
		this.isDefault = isDefault;
	}

	public String getRouteRule() {
		return routeRule;
	}

	public void setRouteRule(String routeRule) {
		this.routeRule = routeRule;
	}

	public String getWelcomeMessage() {
		return welcomeMessage;
	}

	public void setWelcomeMessage(String welcomeMessage) {
		this.welcomeMessage = welcomeMessage;
	}


	public String getLineMessage() {
		return lineMessage;
	}

	public void setLineMessage(String lineMessage) {
		this.lineMessage = lineMessage;
	}

	public String getConnectMessage() {
		return connectMessage;
	}

	public void setConnectMessage(String connectMessage) {
		this.connectMessage = connectMessage;
	}

	public String getWorkTime() {
		return workTime;
	}

	public void setWorkTime(String workTime) {
		this.workTime = workTime;
	}

	/**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value
     *
     * @param id the value for MGW_TENANT_SKILL_QUEUE.ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     */
    public String getQueueName() {
        return queueName;
    }

    /**
     * Sets the value
     *
     * @param queueName the value for MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     */
    public void setQueueName(String queueName) {
        this.queueName = queueName == null ? null : queueName.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     */
    public String getQueueCode() {
        return queueCode;
    }

    /**
     * Sets the value
     *
     * @param queueCode the value for MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     */
    public void setQueueCode(String queueCode) {
        this.queueCode = queueCode == null ? null : queueCode.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     */
    public Integer getQueueSize() {
        return queueSize;
    }

    /**
     * Sets the value
     *
     * @param queueSize the value for MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     */
    public void setQueueSize(Integer queueSize) {
        this.queueSize = queueSize;
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.TENANT_ID
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * Sets the value
     *
     * @param tenantId the value for MGW_TENANT_SKILL_QUEUE.TENANT_ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     */
    public String getAbleBusinessTypes() {
        return ableBusinessTypes;
    }

    /**
     * Sets the value
     *
     * @param ableBusinessTypes the value for MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     */
    public void setAbleBusinessTypes(String ableBusinessTypes) {
        this.ableBusinessTypes = ableBusinessTypes == null ? null : ableBusinessTypes.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     */
    public String getAbleChannels() {
        return ableChannels;
    }

    /**
     * Sets the value
     *
     * @param ableChannels the value for MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     */
    public void setAbleChannels(String ableChannels) {
        this.ableChannels = ableChannels == null ? null : ableChannels.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.SORT_RULE
     */
    public String getSortRule() {
        return sortRule;
    }

    /**
     * Sets the value
     *
     * @param sortRule the value for MGW_TENANT_SKILL_QUEUE.SORT_RULE
     */
    public void setSortRule(String sortRule) {
        this.sortRule = sortRule == null ? null : sortRule.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     */
    public String getAllocationRule() {
        return allocationRule;
    }

    /**
     * Sets the value
     *
     * @param allocationRule the value for MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     */
    public void setAllocationRule(String allocationRule) {
        this.allocationRule = allocationRule == null ? null : allocationRule.trim();
    }

    /**
     * Gets the value 
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.REMARK
     */
    public String getRemark() {
        return remark;
    }

    /**
     * Sets the value
     *
     * @param remark the value for MGW_TENANT_SKILL_QUEUE.REMARK
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the freeAgents
	 */
	public int getFreeAgents() {
		return freeAgents;
	}

	/**
	 * @param freeAgents the freeAgents to set
	 */
	public void setFreeAgents(int freeAgents) {
		this.freeAgents = freeAgents;
	}

	public Integer getConcurrentCount() {
		return concurrentCount;
	}

	public void setConcurrentCount(Integer concurrentCount) {
		this.concurrentCount = concurrentCount;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}
	
	
}