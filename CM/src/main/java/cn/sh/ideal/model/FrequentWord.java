package cn.sh.ideal.model;

import java.io.Serializable;
import java.math.BigDecimal;


public class Frequent<PERSON><PERSON>  implements Serializable{
	
	private static final long serialVersionUID = 1L;

	private BigDecimal autoId;
	
	private String paramCode;
	
	private String paramValue;

	public BigDecimal getAutoId() {
		return autoId;
	}

	public void setAutoId(BigDecimal autoId) {
		this.autoId = autoId;
	}

	public String getParamCode() {
		return paramCode;
	}

	public void setParamCode(String paramCode) {
		this.paramCode = paramCode;
	}

	public String getParamValue() {
		return paramValue;
	}

	public void setParamValue(String paramValue) {
		this.paramValue = paramValue;
	}
	

}
