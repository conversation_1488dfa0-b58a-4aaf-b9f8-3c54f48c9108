package cn.sh.ideal.model;



import java.io.Serializable;
import java.util.Date;

import cn.sh.ideal.util.TipTypes;

import com.alibaba.fastjson.annotation.JSONField;


public class MessageInfo extends BaseModel implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.ID
     *
     * @mbggenerated sdfasf
     */
	private String messageId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.CHANNEL_CODE
     *
     * @mbggenerated
     */
    private String channelCode;
    
    private String sessionType;
    private String callId;
    
    
  //评论数
  	private int comments;
  	//转发数
  	private int reposts;
  	
  	//喜欢数
  	private int attitudes;
  	
  	//客户ID
  	private String customerId;
  	
  	private String sendType = TipTypes.DEFAULT.getCode();
  	private String timeOutAction;
    public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public int getComments() {
		return comments;
	}

	public void setComments(int comments) {
		this.comments = comments;
	}

	public int getReposts() {
		return reposts;
	}

	public void setReposts(int reposts) {
		this.reposts = reposts;
	}

	public int getAttitudes() {
		return attitudes;
	}

	public void setAttitudes(int attitudes) {
		this.attitudes = attitudes;
	}

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.BUSSINESS_TYPE
     *
     * @mbggenerated
     */
    private String businessType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.TENANT_CODE
     *
     * @mbggenerated
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.USER_ID
     *
     * @mbggenerated
     */
    private String userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.SOURCE
     *
     * @mbggenerated
     */
    private String source;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.NICKNAME
     *
     * @mbggenerated
     */
    private String nickname;
    
    private String  picUrls;

    public String getPicUrls() {
		return picUrls;
	}

	public void setPicUrls(String picUrls) {
		this.picUrls = picUrls;
	}

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.SEND_ACCOUNT
     *
     * @mbggenerated
     */
    private String sendAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.ACCEPTED_ACCOUNT
     *
     * @mbggenerated
     */
    private String acceptedAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.REPLY_ACCOUNT
     *
     * @mbggenerated
     */
    private String replyAccount;
    
    private String skillType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.CREATE_TIME
     *
     * @mbggenerated
     */
    @JSONField (format="yyyy-MM-dd HH:mm:ss")  
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.SEND_TIME
     *
     * @mbggenerated
     */
    @JSONField (format="yyyy-MM-dd HH:mm:ss")  
    private Date sendTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.SESSION_ID
     *
     * @mbggenerated
     */
    private String sessionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.SKILL_QUEUE
     *
     * @mbggenerated
     */
    private String skillQueue;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.WORK_NO
     *
     * @mbggenerated
     */
    private String workNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.STATUS
     *
     * @mbggenerated
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.MSG_TYPE
     *
     * @mbggenerated
     */
    private String msgType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.CONTENT
     *
     * @mbggenerated
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.FOLLOW_DATA
     *
     * @mbggenerated
     */
    private String followData;
    
    /**
     * 是否强制分配 Y强制，其他默认非强制
     * 
     */
    private String force;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_MESSAGE_INFO.REMARK
     *
     * @mbggenerated
     */
    private String remark;
    /**消息发送者 1为用户发送 2为坐席发送 3为系统(替用户发送)、4为系统(替坐席发送) 、5提示坐席、6提示用户*/
    private String messageSource;
    //新浪微博操作类型
    private String operType;

    public String getOperType() {
		return operType;
	}

	public void setOperType(String operType) {
		this.operType = operType;
	}

	public String getMessageSource() {
		return messageSource;
	}

	public void setMessageSource(String messageSource) {
		this.messageSource = messageSource;
	}

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table MGW_MESSAGE_INFO
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.CHANNEL_CODE
     *
     * @return the value of MGW_MESSAGE_INFO.CHANNEL_CODE
     *
     * @mbggenerated
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.CHANNEL_CODE
     *
     * @param channelCode the value for MGW_MESSAGE_INFO.CHANNEL_CODE
     *
     * @mbggenerated
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.TENANT_CODE
     *
     * @return the value of MGW_MESSAGE_INFO.TENANT_CODE
     *
     * @mbggenerated
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.TENANT_CODE
     *
     * @param tenantCode the value for MGW_MESSAGE_INFO.TENANT_CODE
     *
     * @mbggenerated
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.USER_ID
     *
     * @return the value of MGW_MESSAGE_INFO.USER_ID
     *
     * @mbggenerated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.USER_ID
     *
     * @param userId the value for MGW_MESSAGE_INFO.USER_ID
     *
     * @mbggenerated
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.SOURCE
     *
     * @return the value of MGW_MESSAGE_INFO.SOURCE
     *
     * @mbggenerated
     */
    public String getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.SOURCE
     *
     * @param source the value for MGW_MESSAGE_INFO.SOURCE
     *
     * @mbggenerated
     */
    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.NICKNAME
     *
     * @return the value of MGW_MESSAGE_INFO.NICKNAME
     *
     * @mbggenerated
     */
    public String getNickname() {
        return nickname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.NICKNAME
     *
     * @param nickname the value for MGW_MESSAGE_INFO.NICKNAME
     *
     * @mbggenerated
     */
    public void setNickname(String nickname) {
        this.nickname = nickname == null ? null : nickname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.SEND_ACCOUNT
     *
     * @return the value of MGW_MESSAGE_INFO.SEND_ACCOUNT
     *
     * @mbggenerated
     */
    public String getSendAccount() {
        return sendAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.SEND_ACCOUNT
     *
     * @param sendAccount the value for MGW_MESSAGE_INFO.SEND_ACCOUNT
     *
     * @mbggenerated
     */
    public void setSendAccount(String sendAccount) {
        this.sendAccount = sendAccount == null ? null : sendAccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.ACCEPTED_ACCOUNT
     *
     * @return the value of MGW_MESSAGE_INFO.ACCEPTED_ACCOUNT
     *
     * @mbggenerated
     */
    public String getAcceptedAccount() {
        return acceptedAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.ACCEPTED_ACCOUNT
     *
     * @param acceptedAccount the value for MGW_MESSAGE_INFO.ACCEPTED_ACCOUNT
     *
     * @mbggenerated
     */
    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount == null ? null : acceptedAccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.REPLY_ACCOUNT
     *
     * @return the value of MGW_MESSAGE_INFO.REPLY_ACCOUNT
     *
     * @mbggenerated
     */
    public String getReplyAccount() {
        return replyAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.REPLY_ACCOUNT
     *
     * @param replyAccount the value for MGW_MESSAGE_INFO.REPLY_ACCOUNT
     *
     * @mbggenerated
     */
    public void setReplyAccount(String replyAccount) {
        this.replyAccount = replyAccount == null ? null : replyAccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.CREATE_TIME
     *
     * @return the value of MGW_MESSAGE_INFO.CREATE_TIME
     *
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.CREATE_TIME
     *
     * @param createTime the value for MGW_MESSAGE_INFO.CREATE_TIME
     *
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.SEND_TIME
     *
     * @return the value of MGW_MESSAGE_INFO.SEND_TIME
     *
     * @mbggenerated
     */
    public Date getSendTime() {
        return sendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.SEND_TIME
     *
     * @param sendTime the value for MGW_MESSAGE_INFO.SEND_TIME
     *
     * @mbggenerated
     */
    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.SESSION_ID
     *
     * @return the value of MGW_MESSAGE_INFO.SESSION_ID
     *
     * @mbggenerated
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.SESSION_ID
     *
     * @param sessionId the value for MGW_MESSAGE_INFO.SESSION_ID
     *
     * @mbggenerated
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.SKILL_QUEUE
     *
     * @return the value of MGW_MESSAGE_INFO.SKILL_QUEUE
     *
     * @mbggenerated
     */
    public String getSkillQueue() {
        return skillQueue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.SKILL_QUEUE
     *
     * @param skillQueue the value for MGW_MESSAGE_INFO.SKILL_QUEUE
     *
     * @mbggenerated
     */
    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue == null ? null : skillQueue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.WORK_NO
     *
     * @return the value of MGW_MESSAGE_INFO.WORK_NO
     *
     * @mbggenerated
     */
    public String getWorkNo() {
        return workNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.WORK_NO
     *
     * @param workNo the value for MGW_MESSAGE_INFO.WORK_NO
     *
     * @mbggenerated
     */
    public void setWorkNo(String workNo) {
        this.workNo = workNo == null ? null : workNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.STATUS
     *
     * @return the value of MGW_MESSAGE_INFO.STATUS
     *
     * @mbggenerated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.STATUS
     *
     * @param status the value for MGW_MESSAGE_INFO.STATUS
     *
     * @mbggenerated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.MSG_TYPE
     *
     * @return the value of MGW_MESSAGE_INFO.MSG_TYPE
     *
     * @mbggenerated
     */
    public String getMsgType() {
        return msgType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.MSG_TYPE
     *
     * @param msgType the value for MGW_MESSAGE_INFO.MSG_TYPE
     *
     * @mbggenerated
     */
    public void setMsgType(String msgType) {
        this.msgType = msgType == null ? null : msgType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.CONTENT
     *
     * @return the value of MGW_MESSAGE_INFO.CONTENT
     *
     * @mbggenerated
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.CONTENT
     *
     * @param content the value for MGW_MESSAGE_INFO.CONTENT
     *
     * @mbggenerated
     */
    public void setContent(String content) {
    	this.content = content == null ? null : content.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.FOLLOW_DATA
     *
     * @return the value of MGW_MESSAGE_INFO.FOLLOW_DATA
     *
     * @mbggenerated
     */
    public String getFollowData() {
        return followData;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.FOLLOW_DATA
     *
     * @param followData the value for MGW_MESSAGE_INFO.FOLLOW_DATA
     *
     * @mbggenerated
     */
    public void setFollowData(String followData) {
        this.followData = followData == null ? null : followData.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_MESSAGE_INFO.REMARK
     *
     * @return the value of MGW_MESSAGE_INFO.REMARK
     *
     * @mbggenerated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_MESSAGE_INFO.REMARK
     *
     * @param remark the value for MGW_MESSAGE_INFO.REMARK
     *
     * @mbggenerated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

	public String getSessionType() {
		return sessionType;
	}

	public void setSessionType(String sessionType) {
		this.sessionType = sessionType;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getSkillType() {
		return skillType;
	}

	public void setSkillType(String skillType) {
		this.skillType = skillType;
	}

	public String getForce() {
		return force;
	}

	public void setForce(String force) {
		this.force = force;
	}
    public String getCallId() {
        return callId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}

	public String getTimeOutAction() {
		return timeOutAction;
	}

	public void setTimeOutAction(String timeOutAction) {
		this.timeOutAction = timeOutAction;
	}
    
}