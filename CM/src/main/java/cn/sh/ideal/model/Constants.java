package cn.sh.ideal.model;

import java.io.IOException;

public class Constants {
	
	/*************************  平台   ***************************/
	
	public static final String PLATFORM_WECHAT = "WX";
	
	public static final String PLATFORM_YICHAT = "YX";
	
	public static final String PLATFORM_WEIBO = "WEIBO";
	
	public static final String PLATFORM_ALIPAY = "ALIPAY";
	
	public static final String PLATFORM_QY = "QY";
	
	

	/*************************  接口入参出参名   ***************************/
	
	public static final String PARAM_PID = "pid";
	
	public static final String PARAM_APPID = "appId";
	
	public static final String PARAM_TENANT_CODE = "tenantCode";
	
	public static final String PARAM_WORK_NO = "workNo";
	
	public static final String PARAM_MESSAGE_ID = "messageId";
	
	public static final String PARAM_SESSION_ID = "sessionId";
	
	public static final String PARAM_TOOLBAR_ID = "toolbarId";
	
	public static final String PARAM_MSG = "msg";
	
	public static final String PARAM_RESULT_CODE = "resultCode";
	
	public static final String PARAM_RESULT_MSG = "resultMsg";
	
	public static final String PARAM_MSG_ID = "msgId";
	
	public static final String PARAM_SECRET = "secret";
	
	public static final String PARAM_MENU = "menu";
	
	public static final String PARAM_NAME = "name";
	
	public static final String PARAM_KEYWORD = "keyword";
	
	public static final String PARAM_FLOW = "flow";
	
	public static final String PARAM_MSG_TYPE = "msgType";
	
	public static final String PARAM_INTERFACE_TYPE = "interfaceType";
	
	public static final String PARAM_INTERFACE_URL = "interfaceUrl";
	
	public static final String PARAM_INTERFACE_METHOD = "interfaceMethod";
	
	public static final String PARAM_CONTENT = "content";
	
	public static final String PARAM_MENUS = "menus";
	
	public static final String PARAM_CHANNEL = "channel";
	
	public static final String PARAM_CHANNEL_TYPE = "channelType";
	
	public static final String PARAM_NICKNAME= "nickname";
	
	public static final String PARAM_MENU_ID = "menuId";
	
	public static final String PARAM_APP_SECRET = "appSecret";
	
	public static final String PARAM_ACCOUNT = "account";
	
	public static final String PARAM_PASSWORD = "password";
	
	public static final String PARAM_POP_SERVER = "popServer";
	
	public static final String PARAM_POP_PORT = "popPort";
	
	public static final String PARAM_IMAP_SERVER = "imapServer";
	
	public static final String PARAM_IMAP_PORT = "imapPort";
	
	public static final String PARAM_SMTP_SERVER = "smtpServer";
	
	public static final String PARAM_SMTP_PORT = "smtpPort";
	
	public static final String PARAM_PRIVATE_KEY = "privateKey";
	
	public static final String PARAM_PUBLIC_KEY = "publicKey";
	
	public static final String PARAM_OAUTH_URL = "oauthUrl";
	
	public static final String PARAM_OWNER = "owner";
	
	public static final String PARAM_APPKEY = "appkey";
	
	public static final String PARAM_TYPE = "type";
	
	public static final String PARAM_VERIFICATION = "verification";
	
	public static final String PARAM_PLATFORM_ID = "platformId";
	
	public static final String PARAM_OPENID = "openId";
	
	public static final String PARAM_MAXID = "maxid";
	
	public static final String PARAM_PARTICIPANT = "participant";
	
	public static final String PARAM_FROMUSER = "fromuser";
	
	public static final String PARAM_TOUSER = "touser";
	
	public static final String PARAM_SUBJECT = "subject";
	
	public static final String PARAM_ATTACHMENT = "attachment";
	
	public static final String PARAM_COUNT = "count";
	
	public static final String PARAM_SESSIONS = "sessions";
	
	public static final String ACTION_NAME = "actionName";
	
	public static final String PARAM_AGENT_ID = "agentId";
	
	public static final String CHANNEL_CODE="channelCode";
	
	
	public static final String COMMON_MESSAGE="commonMessage";
	
	public final static String SUCCESS_CODE = "0";
	
	public final static String ERROR_CODE = "-1";
	
	public enum MSG_TYPE{
		TEXT("text","文本"),
		IMAGE("image","图片"),
		AUDIO("audio","语音"),
		LOCATION("location","坐标"),
		FILE("file","文件"),
		EMAIL("email","邮件"),
		WEIBO("weibo","微博"),
		EVENT("event","事件"),
		LINK("link","工单"),
		ONLINE_VIDEO("onlineVideo","在线视频"),
		SHORT_VIDEO("shortvideo","小视频"),
		VOICE("voice","语音"),
		OCX_VOICE("ocxVoice","语音"),
		NEWS("news","图文");
		
		private String type;
		private String name;
		
		MSG_TYPE(String type,String name){
			this.setType(type);
			this.setName(name);
		}
		/**
		 * @return the type
		 */
		public String getType() {
			return type;
		}
		/**
		 * @param type the type to set
		 */
		public void setType(String type) {
			this.type = type;
		}
		/**
		 * @return the name
		 */
		public String getName() {
			return name;
		}
		/**
		 * @param name the name to set
		 */
		public void setName(String name) {
			this.name = name;
		}
	}
	
	public static void main(String[] args) throws IOException {
		System.out.println(Constants.class.getResource("/template").toString());
		System.out.println(Thread.currentThread().getContextClassLoader().getResource("").getPath());
	}
	
}
