package cn.sh.ideal.model;

public enum RespCode {
	
	SUCCESS("0","success."),
	INVALID_ARGUMENTS("9998","参数校验失败."),
	WORKNO_NOT_EXITS_SESSION("9997","工号不存在会话中."),
	SESSION_STATUS_ERROR("9996","会话状态错误."),
	FAIL("-1","fail.");
	
	private String resultCode;
	private String resultMsg;
	
	RespCode(String resultCode,String resultMsg){
		this.resultCode=resultCode;
		this.resultMsg=resultMsg;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	
}
