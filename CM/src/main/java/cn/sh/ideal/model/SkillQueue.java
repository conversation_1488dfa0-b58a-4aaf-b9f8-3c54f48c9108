package cn.sh.ideal.model;

import java.io.Serializable;

/**
 * The Class SkillQueue.
 */
public class SkillQueue implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/** The id. */
	private String id;

	/** 租户技能队列配置信息. */
	private TenantSkillQueue tenantSkillQueue;

	/** The queue. */
	private Object allocateQueue;
	
	private SysRule allocateRule;

	/** 所属客户 */
	private String tenantCode;
	
	private int queueSize;

	private SysRule sortRule;
	
	private int concurrentCount;
	
	/**
	 * Instantiates a new skill queue.
	 *
	 * @param id
	 *            the id
	 * @param tenantSkillQueue
	 *            the tenant skill queue
	 * @param sortRule 
	 * @param sRule 
	 */
	public SkillQueue(String id, String tenantCode, TenantSkillQueue tenantSkillQueue,SysRule allocateRule, SysRule sortRule,int queueSize,int concurrentCount) {
		this.id = id;
		this.tenantCode = tenantCode;
		this.tenantSkillQueue = tenantSkillQueue;
		this.allocateRule = allocateRule;
		this.sortRule = sortRule;
		this.queueSize = queueSize;
		this.concurrentCount = concurrentCount;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public String getId() {
		return id;
	}

	public TenantSkillQueue getTenantSkillQueue() {
		return tenantSkillQueue;
	}

	public SysRule getAllocateRule() {
		return allocateRule;
	}

	public void setAllocateRule(SysRule allocateRule) {
		this.allocateRule = allocateRule;
	}

	public int getQueueSize() {
		return queueSize;
	}

	
	public SysRule getSortRule() {
		return sortRule;
	}

	public void setSortRule(SysRule sortRule) {
		this.sortRule = sortRule;
	}


	public void setId(String id) {
		this.id = id;
	}

	public void setTenantSkillQueue(TenantSkillQueue tenantSkillQueue) {
		this.tenantSkillQueue = tenantSkillQueue;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public void setQueueSize(int queueSize) {
		this.queueSize = queueSize;
	}

	public int getConcurrentCount() {
		return concurrentCount;
	}

	public void setConcurrentCount(int concurrentCount) {
		this.concurrentCount = concurrentCount;
	}

	public Object getAllocateQueue() {
		return allocateQueue;
	}

	public void setAllocateQueue(Object allocateQueue) {
		this.allocateQueue = allocateQueue;
	}

	
}
