/**
 * Project Name:SM Maven Webapp
 * File Name:TimeOutSession.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月10日上午11:11:36
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.model;

import java.io.Serializable;
import java.util.Date;

/**
 * ClassName:TimeOutSession <br/>
 * Function: 会话超时实体类
 * Date:     2014年12月10日 上午11:11:36 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class TimeOutSession implements Serializable{
    
    private static final long serialVersionUID = -4552374011464938854L;

    /*会话ID*/
    private String sessionId;
    
    /*租户code*/
    private String tenantCode;
    
    /*上一次活跃时间*/
    private Date lastActiveTime;
    
    /*上一次活跃方  用户 user  坐席 ag*/
    private String lastActiveUser;
    /*超时次数*/
    private int timeoutCount;
    
    /* 超时类型  tip提醒  close 关闭会话 转发*/
    private String timeoutType;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Date getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(Date lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public String getLastActiveUser() {
        return lastActiveUser;
    }

    public void setLastActiveUser(String lastActiveUser) {
        this.lastActiveUser = lastActiveUser;
    }

	public String getTimeoutType() {
		return timeoutType;
	}

	public void setTimeoutType(String timeoutType) {
		this.timeoutType = timeoutType;
	}

    public int getTimeoutCount() {
        return timeoutCount;
    }

    public void setTimeoutCount(int timeoutCount) {
        this.timeoutCount = timeoutCount;
    }
}
