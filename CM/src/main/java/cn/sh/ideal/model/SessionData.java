/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionData.java
 * Package Name:cn.sh.ideal.mir.session.data
 * Date:2014年12月4日下午5:10:58
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.model;


import java.io.Serializable;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

/**
 * ClassName:SessionData <br/>
 * Function: 创建sessionId需要的字段信息 <br/>
 * Date:     2014年12月4日 下午5:10:58 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionData implements Serializable{
    
    /**
	 * 
	 */
	private static final long serialVersionUID = -8474523847077661669L;

	/*发送账号*/
    private String sendAccount;
    
    /*接收账号*/
    private String acceptedAccount;
    
    /*发送渠道*/
    private String channelCode;
    /*身份标识*/
    private String idCard;
    
    /*会话ID*/
    private String sessionId;
    
    /*租户编号*/
    private String tenantCode;
    
    /*业务类型*/
    private String businessType;
    
    /*消息ID*/
    private String messageId;
    
    /*技能队列*/
    private String skillQueue;
    
    /*坐席工号*/
    private String workNo;
    
    /*会话状态*/
    private String status;
    
    /*设置超时时间 单位为秒*/
    public int time;
    
    /*技能组分类*/
    private String skillType;
    
    /*账号昵称*/
    private String nickname;
    
    /*每页显示数量*/
    private int pageSize;
    
    /*页码 从1开始*/
    private int pageNum;
    
    
    private String dataFrom; //分页开始游标 前坐席接口遗留使用格式 后面统一使用 pageSize pageNum
    
    private String dataNum; //分页结束游标  前坐席接口遗留使用格式 后面统一使用 pageSize pageNum
    
    /*查询开始时间*/
    private String beginDate;
    
    /*查询结束时间*/
    private String endDate;

    private String[] statuses;
    private String[] tenantCodes;
    private String callStatus;
    /*客户id*/
    private String customerId;
    /*电话callId*/
    private String callId;
    /*营销任务Id*/
    private String taskId;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    /*内容*/
    private String content;

    public String getCallStatus() {
        return callStatus;
    }

    public void setCallStatus(String callStatus) {
        this.callStatus = callStatus;
    }

    public String getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(String dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getDataNum() {
        return dataNum;
    }

    public void setDataNum(String dataNum) {
        this.dataNum = dataNum;
    }

    private Map<String, Object> data;
    
    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getSendAccount() {
        return sendAccount;
    }

    public void setSendAccount(String sendAccount) {
        this.sendAccount = sendAccount;
    }

    public String getAcceptedAccount() {
        return acceptedAccount;
    }

    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount;
    }

    

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
    
    

    public String getSkillType() {
        return skillType;
    }

    public void setSkillType(String skillType) {
        this.skillType = skillType;
    }

    public String[] getStatuses() {
        return statuses;
    }

    public void setStatuses(String[] statuses) {
        this.statuses = statuses;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }



    @Override
    public String toString() {
        return "SessionData [sendAccount=" + sendAccount + ", acceptedAccount="
                + acceptedAccount + ", channelCode=" + channelCode
                + ", sessionId=" + sessionId + ", tenantCode=" + tenantCode
                + ", businessType=" + businessType + ", messageId=" + messageId
                + ", skillQueue=" + skillQueue + ", workNo=" + workNo
                + ", status=" + status + ", time=" + time + ", skillType="
                + skillType + ", nickname=" + nickname + ", pageSize="
                + pageSize + ", pageNum=" + pageNum + ", dataFrom=" + dataFrom
                + ", dataNum=" + dataNum + ", beginDate=" + beginDate+ ", customerId=" + customerId
                + ", endDate=" + endDate + ", data=" + data + ", callId=" + callId +", taskId=" + taskId +"]";
    }

    public String getSessionModel(){
        idCard = StringUtils.defaultIfEmpty(idCard,"");
        //去除特殊字符
        String sessionMdeol = channelCode+sendAccount+acceptedAccount+idCard+tenantCode;
        sessionMdeol = sessionMdeol.replaceAll("[^A-Za-z0-9-\\\\.~]","");
        return sessionMdeol;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getSkillQueue() {
        return skillQueue;
    }

    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }
 
    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

  
    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String[] getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String[] tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCallId() {
        return callId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

	public SessionData(RequestSessionbean requestSessionBean) {
		super();
		this.sendAccount = requestSessionBean.getSendAccount();
		this.acceptedAccount = requestSessionBean.getAcceptedAccount();
		this.channelCode = requestSessionBean.getChannelCode();
		this.tenantCode = requestSessionBean.getTenantCode();
		this.businessType = requestSessionBean.getBusinessType();
		this.skillQueue = requestSessionBean.getSkillQueue();
		this.workNo = requestSessionBean.getWorkNo();
		this.skillType = requestSessionBean.getSkillType();
		this.nickname = requestSessionBean.getNickname();
		this.customerId = requestSessionBean.getCustomerId();
		this.sessionId=requestSessionBean.getSessionId();
		this.content=requestSessionBean.getContent();
		this.idCard = requestSessionBean.getIdCard();
        this.callId = requestSessionBean.getCallId();
	}

	public SessionData() {
		super();
	}
	
	
    
    
}
