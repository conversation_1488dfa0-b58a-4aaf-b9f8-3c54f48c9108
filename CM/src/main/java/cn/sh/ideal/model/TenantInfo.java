package cn.sh.ideal.model;

import java.io.Serializable;
import java.util.Date;

/**
 * This is an Entity, db table is:MGW_TENANT_INFO
 *租户信息
 * <AUTHOR>
 * @date 2014-05-26 18:35:08
 */
public class TenantInfo implements Serializable {

	private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 客户编码 */
    private String tenantCode;

    /** 客户名称 */
    private String tenantName;

    /** 接入账号 */
    private String account;

    /** 接入秘钥 */
    private String password;

    /** 1:BASE64 2:3DES */
    private String encryptType;

    /** 解密秘钥 */
    private String decodeKey;

    /** 租户能使用的渠道多个以,分隔 */
    private String channel;

    /** 路由规则 */
    private String routingRule;
    
    private String skillRule;
    
    private SysRule routeImpl;
    
    private SysRule skillImpl;
    
    /** 坐席数 */
    private Integer terminalNum;

    /** 到期时间 */
    private Date dueDate;

    /** 0:非正常 1:正常 */
    private String status;

    /** 备注 */
    private String remark;

    /** 权值 */
    private Integer priority;
    
	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.ID
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value
	 *
	 * @param id
	 *            the value for MGW_TENANT_INFO.ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.TENANT_NAME
	 */
	public String getTenantName() {
		return tenantName;
	}

	/**
	 * Sets the value
	 *
	 * @param tenantName
	 *            the value for MGW_TENANT_INFO.TENANT_NAME
	 */
	public void setTenantName(String tenantName) {
		this.tenantName = tenantName == null ? null : tenantName.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.ACCOUNT
	 */
	public String getAccount() {
		return account;
	}

	/**
	 * Sets the value
	 *
	 * @param account
	 *            the value for MGW_TENANT_INFO.ACCOUNT
	 */
	public void setAccount(String account) {
		this.account = account == null ? null : account.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.PASSWORD
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * Sets the value
	 *
	 * @param password
	 *            the value for MGW_TENANT_INFO.PASSWORD
	 */
	public void setPassword(String password) {
		this.password = password == null ? null : password.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.ENCRYPT_TYPE
	 */
	public String getEncryptType() {
		return encryptType;
	}

	/**
	 * Sets the value
	 *
	 * @param encryptType
	 *            the value for MGW_TENANT_INFO.ENCRYPT_TYPE
	 */
	public void setEncryptType(String encryptType) {
		this.encryptType = encryptType == null ? null : encryptType.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.DECODE_KEY
	 */
	public String getDecodeKey() {
		return decodeKey;
	}

	/**
	 * Sets the value
	 *
	 * @param decodeKey
	 *            the value for MGW_TENANT_INFO.DECODE_KEY
	 */
	public void setDecodeKey(String decodeKey) {
		this.decodeKey = decodeKey == null ? null : decodeKey.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.ROUTING_RULE
	 */
	public String getRoutingRule() {
		return routingRule;
	}

	/**
	 * Sets the value
	 *
	 * @param routingRule
	 *            the value for MGW_TENANT_INFO.ROUTING_RULE
	 */
	public void setRoutingRule(String routingRule) {
		this.routingRule = routingRule == null ? null : routingRule.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.TERMINAL_NUM
	 */
	public Integer getTerminalNum() {
		return terminalNum;
	}

	/**
	 * Sets the value
	 *
	 * @param terminalNum
	 *            the value for MGW_TENANT_INFO.TERMINAL_NUM
	 */
	public void setTerminalNum(Integer terminalNum) {
		this.terminalNum = terminalNum;
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.DUE_DATE
	 */
	public Date getDueDate() {
		return dueDate;
	}

	/**
	 * Sets the value
	 *
	 * @param dueDate
	 *            the value for MGW_TENANT_INFO.DUE_DATE
	 */
	public void setDueDate(Date dueDate) {
		this.dueDate = dueDate;
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.STATUS
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value
	 *
	 * @param status
	 *            the value for MGW_TENANT_INFO.STATUS
	 */
	public void setStatus(String status) {
		this.status = status == null ? null : status.trim();
	}

	/**
	 * Gets the value
	 *
	 * @return the value of MGW_TENANT_INFO.REMARK
	 */
	public String getRemark() {
		return remark;
	}

	/**
	 * Sets the value
	 *
	 * @param remark
	 *            the value for MGW_TENANT_INFO.REMARK
	 */
	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getSkillRule() {
		return skillRule;
	}

	public void setSkillRule(String skillRule) {
		this.skillRule = skillRule;
	}

	public SysRule getRouteImpl() {
		return routeImpl;
	}

	public void setRouteImpl(SysRule routeImpl) {
		this.routeImpl = routeImpl;
	}

	public SysRule getSkillImpl() {
		return skillImpl;
	}

	public void setSkillImpl(SysRule skillImpl) {
		this.skillImpl = skillImpl;
	}
	
}