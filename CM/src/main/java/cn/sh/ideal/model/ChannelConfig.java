package cn.sh.ideal.model;

import java.io.Serializable;
import java.util.Date;

/**
 *This is an Entity, db table is:MGW_CHANNEL_INFO
 *
 *<AUTHOR>
 *@date 2014-05-26 18:35:08
 */
public class ChannelConfig implements Serializable {
    

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;


    /** 渠道编码 */
    private String channelCode;
    /** 渠道编码 */
    private String channelName;
    /** 渠道账号 */
    private String channelAccount;
    /** 渠道配置参数 */
    private String accountConfig;

    /** 渠道是否启用 0停用 1启用 */
    private String channelEnable;

    /** 租户CODE */
    private String tenantCode;

    /** 备注 */
    private String remark;
    /**自助状态*/
    private String selfType;
    
    /**渠道类型 1为自有渠道*/
    private String channelType;
    
    /** 是否为实时渠道 1实时 0非实时*/
    private String realTime;
    
    private Date expDate;
    /*渠道会话过期时间（点对点消息可调用时间）*/
	private int sessionDueTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getChannelAccount() {
		return channelAccount;
	}

	public void setChannelAccount(String channelAccount) {
		this.channelAccount = channelAccount;
	}

	public String getAccountConfig() {
		return accountConfig;
	}

	public void setAccountConfig(String accountConfig) {
		this.accountConfig = accountConfig;
	}

	public String getChannelEnable() {
		return channelEnable;
	}

	public void setChannelEnable(String channelEnable) {
		this.channelEnable = channelEnable;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * @return the expDate
	 */
	public Date getExpDate() {
		return expDate;
	}

	/**
	 * @param expDate the expDate to set
	 */
	public void setExpDate(Date expDate) {
		this.expDate = expDate;
	}

	public String getSelfType() {
		return selfType;
	}

	public void setSelfType(String selfType) {
		this.selfType = selfType;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getRealTime() {
		return realTime;
	}

	public void setRealTime(String realTime) {
		this.realTime = realTime;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public int getSessionDueTime() {
		return sessionDueTime;
	}

	public void setSessionDueTime(int sessionDueTime) {
		this.sessionDueTime = sessionDueTime;
	}
}