package cn.sh.ideal.model;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.sh.ideal.annotation.NotEmpty;

/**
 * 请求对象Base类
 * 通用的参数可添加在该类里面
 * <AUTHOR>
 */
public abstract class BaseRequest implements Serializable{
	private static Logger logger = LoggerFactory.getLogger(BaseRequest.class);
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public static final String ERROR_CODE_ARGUMENTS_MISSING = "40";

	/**
	 * 参数非空验证
	 * 
	 * @throws Exception
	 */
	public void check() throws Exception{
		doCheck(this.getClass(),"","",this.getClass().getSimpleName());
	}
	
	@SuppressWarnings("rawtypes")
	public void doCheck(Class c,String args,String missArgs,String requestClassName) throws Exception{
		if(c.toString().equals(BaseRequest.class.toString())){
			args = args != null && args.length() != 0 ? args.substring(0,args.lastIndexOf(",")) : args;
			logger.info(requestClassName + " request arguments:{" + args + "}");
			
			if(missArgs != null && missArgs.length() != 0){
				throw new Exception("client-error:Missing required arguments:" + 
						missArgs.substring(0,missArgs.lastIndexOf(",")));
			}
			return;
		}
		Field[] fields = c.getDeclaredFields();
		for(Field field : fields){
			String fieldName = field.getName();
			if("serialVersionUID".equals(fieldName)) continue;
			String methodName = "get".concat(fieldName.substring(0, 1).toUpperCase()).concat(fieldName.substring(1));
			Method method = this.getClass().getMethod(methodName);
			Object value =  method.invoke(this);
			
			if (field.getGenericType().toString().equals(
					"class java.lang.String")){
				String val =(String)value;
				if(val == null || val.length() == 0){
					if(field.isAnnotationPresent(NotEmpty.class)){
						missArgs += fieldName + ",";
					}
				}else{
					args += fieldName + ":" +  val + ",";
				}
			}else{
				if(null == value){
					if(field.isAnnotationPresent(NotEmpty.class)){
						missArgs += fieldName + ",";
					}
				}else{
					args += fieldName + ":" +  value + ",";
				}
			}
			
		}
		doCheck(c.getSuperclass(),args,missArgs,requestClassName);
	}
}
