/**
 * Project Name:SM Maven Webapp
 * File Name:Session.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月19日上午9:47:16
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.model;

import java.util.List;

/**
 * ClassName:Session <br/>
 * Function: 坐席提供一对多会话消息映射 <br/>
 * Date:     2014年12月19日 上午9:47:16 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class Session {
    
    /*会话ID*/
    private String sessionId;
    
    /*一对多消息列表*/
    private List<Message> messages;
    
    
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public List<Message> getMessages() {
        return messages;
    }
    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }
    

}
