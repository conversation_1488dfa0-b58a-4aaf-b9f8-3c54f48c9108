/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionInfo.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月4日下午3:01:53
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/
package cn.sh.ideal.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;





/**
 * ClassName:SessionInfo <br/>
 * Function: TODO 会话管理实体类 <br/>
 * Date:     2014年12月4日 下午3:01:53 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionInfo extends BaseModel {
    private static final long serialVersionUID = 7533882466452487588L;
    
    /*主键*/
    private String id;
    
    /*会话ID*/
    private String sessionId;
    
    /*会话模型 渠道、发送账号、接收账号 相同则相同*/
    private String sessionModel;
    
    /*当前会话类型 普通 三方 转发 延迟  */
    private String sessionType;
    
    /*会话状态 1：自助  2：路由  3：排序 4：分配 5：人工 6：临时自助 7：会话超时 8：会话转发 9：用户关闭会话 10：坐席关闭会话11：会话延迟 12 会话完结*/
    private String status;
    
    /*会话状态标示 如排队 为sort*/
    private String statusSymbol;
    
    /*会话创建时间*/
    private Date createTime;
    
    /*会话更新时间*/
    private Date optTime;
    
    /*会话开始时间*/
    private Date startTime;
    
    /*会话结束时间*/
    private Date endTime;
    
    /* 发送账号 */
    private String sendAccount;

    /* 接受账号 */
    private String acceptedAccount;
    
    /* 会话渠道 */
    private String channelCode;
    
    /*会话所属的消息*/
    private List<String> messageIds = new ArrayList<String>();  //放缓存
    
    /* 业务类型 */
    private String businessType;
    
    /* 所属租户 */
    private String tenantCode;


    /* 分配技能组 */
    private String skillQueue;
    
    /*分配的技能组分类*/
    private String skillType;

    /* 客服工号 多个以,分割 */
    private String workNos;
    
    /*任务优先级*/
    private int priority;
    
    /*渠道账号昵称*/
    private String nickname;
    
    /*渠道切换状态  0 未切换  1切换*/
    private String changeStatus;
    /*主被动接入  0 被动 1 主动*/
    private String callStatus;
    private String customerId;
    /*电话callId*/
    private String callId;
    /*营销任务Id*/
    private String taskId;
    
    private TimeOutSession  timeOutSession;
    
    public SessionInfo()
    {
    	this.messageIds = new ArrayList();
    }
    
    public TimeOutSession getTimeOutSession() {
        return timeOutSession;
    }

    public void setTimeOutSession(TimeOutSession timeOutSession) {
        this.timeOutSession = timeOutSession;
    }

    /*添加一个消息ID*/
    public void addMessageId(String messageId){
        this.messageIds.add(messageId);
    }
    
    public void addWorkNo(String workNo){
       
        if(StringUtils.isEmpty(this.workNos)){
            this.workNos = workNo;
        }else{
            boolean addFlag = true;
            String[] workNOArr=this.workNos.split(",");
            for(String oldWorkNo:workNOArr){
                if(oldWorkNo.equals(workNo)){
                    addFlag = false;
                }
            }
           if(addFlag) this.workNos += ","+workNo;
            
        }
        
    }
    
    public void removeWorkNo(String workNo){
        if(StringUtils.isNotEmpty(this.workNos)){
            String[] workNOArr=this.workNos.split(",");
            this.workNos = "";
            
            for(String oldWorkNo:workNOArr){
                if(!oldWorkNo.equals(workNo)){
                    this.workNos += oldWorkNo+",";
                }
            }
            
            if( workNos != null){
                if(workNos.endsWith(",")){
                   this.workNos = workNos.substring(0, workNos.length()-1);
                }
            }
            
        }
        
    }
   
    
    
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getSessionModel() {
        return sessionModel;
    }
    public void setSessionModel(String sessionModel) {
        this.sessionModel = sessionModel;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public String getStatusSymbol() {
        return statusSymbol;
    }
    public void setStatusSymbol(String statusSymbol) {
        this.statusSymbol = statusSymbol;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getOptTime() {
        return optTime;
    }
    public void setOptTime(Date optTime) {
        this.optTime = optTime;
    }

    
   
    public List<String> getMessageIds() {
        return messageIds;
    }
    public void setMessageIds(List<String> messageIds) {
        this.messageIds = messageIds;
    }
    

    public String getBusinessType() {
        return businessType;
    }
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    public String getSkillQueue() {
        return skillQueue;
    }
    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }
   
    
 
    public String getWorkNos() {
        return workNos;
    }

    public void setWorkNos(String workNos) {
        this.workNos = workNos;
    }

    public String getSendAccount() {
        return sendAccount;
    }


    public void setSendAccount(String sendAccount) {
        this.sendAccount = sendAccount;
    }


    public String getAcceptedAccount() {
        return acceptedAccount;
    }
    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount;
    }
    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }
   

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }



    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSessionType() {
        return sessionType;
    }

    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }

    public String getSkillType() {
        return skillType;
    }

    public void setSkillType(String skillType) {
        this.skillType = skillType;
    }

    public String getChangeStatus() {
        return changeStatus;
    }

    public void setChangeStatus(String changeStatus) {
        this.changeStatus = changeStatus;
    }

    public String getCallStatus() {
        return callStatus;
    }

    public void setCallStatus(String callStatus) {
        this.callStatus = callStatus;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCallId() {
        return callId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public enum ChangeStatus{
        NO("0"),
        YES("1");
       private String code;
       private ChangeStatus(String code){
           this.code=code;
       }
       
       //@Override
       public String getCode() {
           
           return this.code;
       }
    }
    
    


}
