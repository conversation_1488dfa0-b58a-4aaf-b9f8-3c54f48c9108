package cn.sh.ideal.model;

import java.io.Serializable;

public class BaseResponse implements Serializable{
	private static final long serialVersionUID = 1L;
	private String resultCode;//返回编码
	private String resultMsg;//返回消息
	
	public BaseResponse(){}
	
	public BaseResponse(String resultCode,String resultMsg){
		this.resultCode = resultCode;
		this.resultMsg = resultMsg;
	}
	public String getResultCode() {
		return resultCode;
	}
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	public String getResultMsg() {
		return resultMsg;
	}
	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	
}
