/**
 * Project Name:SM Maven Webapp
 * File Name:Message.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月19日上午9:47:30
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.model;

import java.util.Date;

/**
 * ClassName:Message <br/>
 * Function: 坐席提供多对一会话消息映射 <br/>
 * Date:     2014年12月19日 上午9:47:30 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class Message {
    
    /*消息主键*/
    private String id;
    
    /*消息ID*/
    private String messageId;
    
    /*工具条ID*/
    private String toolbarId;
    
    /*消息渠道*/
    private String messageChannel;
    
    
   // private String userId;
    
    //private String userLevel;
    
    /*发送账号*/
    private String sendAccout;
    
    /*接收账号*/
    private String acceptedAccount;
    
    /*消息创建时间*/
    private Date createTime;
    
    /*会话ID*/
    private String sessionId;
    
    /*转发前技能组*/
    private String beforeSkillQueue;
    
    /*技能组*/
    private String skillQueue;
    
    /*转发前工号*/
    private String beforeWorkNo;
    
    /*工号*/
    private String workNo;
    
    /*消息状态  */
    private String status;
    
    /*备注*/
    private String remark;
    
    /*消息内容*/
    private String extData;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getToolbarId() {
        return toolbarId;
    }

    public void setToolbarId(String toolbarId) {
        this.toolbarId = toolbarId;
    }



    public String getMessageChannel() {
        return messageChannel;
    }

    public void setMessageChannel(String messageChannel) {
        this.messageChannel = messageChannel;
    }

  

    public String getSendAccout() {
        return sendAccout;
    }

    public void setSendAccout(String sendAccout) {
        this.sendAccout = sendAccout;
    }

  

    public String getAcceptedAccount() {
        return acceptedAccount;
    }

    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getBeforeSkillQueue() {
        return beforeSkillQueue;
    }

    public void setBeforeSkillQueue(String beforeSkillQueue) {
        this.beforeSkillQueue = beforeSkillQueue;
    }

    public String getSkillQueue() {
        return skillQueue;
    }

    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }

    public String getBeforeWorkNo() {
        return beforeWorkNo;
    }

    public void setBeforeWorkNo(String beforeWorkNo) {
        this.beforeWorkNo = beforeWorkNo;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    @Override
    public String toString() {
        return "Message [id=" + id + ", messageId=" + messageId
                + ", toolbarId=" + toolbarId + ", messageChannel="
                + messageChannel + ", sendAccount=" + sendAccout
                + ", acceptedAccount=" + acceptedAccount + ", createTime="
                + createTime + ", sessionId=" + sessionId
                + ", beforeSkillQueue=" + beforeSkillQueue + ", skillQueue="
                + skillQueue + ", beforeWorkNo=" + beforeWorkNo + ", workNo="
                + workNo + ", status=" + status + ", remark=" + remark
                + ", extData=" + extData + "]";
    }
    
    
    
    

}
