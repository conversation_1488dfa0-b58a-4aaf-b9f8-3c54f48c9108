package cn.sh.ideal.model;

import java.io.Serializable;


/**
 * 坐席基本数据对象
 * 
 * Date:     2015年4月17日 下午15:25<br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6  多媒体v1.1.8
 *
 */
public class SkillType implements Serializable,Cloneable{

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**ID*/
	private String id;
	
	/**技能组CODE*/
	private String skillTypeCode;
	
	/**技能组名称*/
	private String skillTypeName;
	
	/**是否能用*/
	private String status;
	
	/**租户code*/
	private String tenantCode;
	
	/**备注*/
	private String remark;
	
	/**是否记录会话*/
	private String isRecord;
	
	/**是否回复消息*/
	private String isReplay;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSkillTypeCode() {
		return skillTypeCode;
	}

	public void setSkillTypeCode(String skillTypeCode) {
		this.skillTypeCode = skillTypeCode;
	}

	public String getSkillTypeName() {
		return skillTypeName;
	}

	public void setSkillTypeName(String skillTypeName) {
		this.skillTypeName = skillTypeName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getIsRecord() {
		return isRecord;
	}

	public void setIsRecord(String isRecord) {
		this.isRecord = isRecord;
	}

	public String getIsReplay() {
		return isReplay;
	}

	public void setIsReplay(String isReplay) {
		this.isReplay = isReplay;
	}
	

}
