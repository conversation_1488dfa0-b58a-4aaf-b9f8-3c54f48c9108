package cn.sh.ideal.model;

import java.io.Serializable;
import java.util.Date;

public class MessageInfoSend  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String id;
	private Integer msgId;
	private String channelCode;
	private String sendAccount;
	//(仅私信会用到)
	private String acceptAccount;
	//就是"weibo"
	private String msgType;  
	//1:发布微博接口;2:微博评论接口;3:回复评论接口;4:删除微博接口;5,回复私信接口
	private String type;
	private String weiboId;
	private String commentId;
	private String replyId;
	//以json格式存入数据库  发送微博内容，评论内容；回复内容等其他请求参数
	private String content; 
	
	private Date createTime;
	//状态（0:success 1:fail） 发布微博成功与否；删除微博；回复评论；微博评论；私信回复接口
	private String status;
	//返回内容
	private String result;
	
	//是否定时发送
	private String isTime;
	//定时发送时间
	private String date;
	
	private String workNo;
	//活动，非活动
	private String activeType;
	//租户Code
	private String tenantCode;
	
	private Date startTime;
	
	private Date endTime;
	
	private int pageNum;
	
	private int pageSize;
	
	private String sessionId;
	
	private String sendStatus;

	//昵称
	private String nickname;
	
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getSendStatus() {
		return sendStatus;
	}
	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getNickname() {
		return nickname;
	}
	public void setNickname(String nickname) {
		this.nickname = nickname;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getIsTime() {
		return isTime;
	}
	public void setIsTime(String isTime) {
		this.isTime = isTime;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getActiveType() {
		return activeType;
	}
	public void setActiveType(String activeType) {
		this.activeType = activeType;
	}
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public int getPageNum() {
		return pageNum;
	}
	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	public Integer getMsgId() {
		return msgId;
	}
	public void setMsgId(Integer msgId) {
		this.msgId = msgId;
	}
	public String getChannelCode() {
		return channelCode;
	}
	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
	public String getSendAccount() {
		return sendAccount;
	}
	public void setSendAccount(String sendAccount) {
		this.sendAccount = sendAccount;
	}
	public String getAcceptAccount() {
		return acceptAccount;
	}
	public void setAcceptAccount(String acceptAccount) {
		this.acceptAccount = acceptAccount;
	}
	public String getMsgType() {
		return msgType;
	}
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getWeiboId() {
		return weiboId;
	}
	public void setWeiboId(String weiboId) {
		this.weiboId = weiboId;
	}
	public String getCommentId() {
		return commentId;
	}
	public void setCommentId(String commentId) {
		this.commentId = commentId;
	}
	public String getReplyId() {
		return replyId;
	}
	public void setReplyId(String replyId) {
		this.replyId = replyId;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	} 
	
}
