/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionSatus.java
 * Package Name:cn.sh.ideal.mir.session.util
 * Date:2014年12月4日下午4:28:42
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.model;
/**
 * ClassName:SessionSatus <br/>
 * Function: 会话状态标识<br/>
 * Date:     2014年12月4日 下午4:28:42 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public enum SessionStatus {
    
	 SELF("1"),//自助
	    ROUTE("2"),//路由
	    SORT("3"), //排序
	    ALLOCAT("4"),//分配
	    MANUAL("5"), //人工
	    TEMP_SELF("6"),//临时自助
	    TIMEOUT("7"), //会话超时
	    FORWORD("8"), //会话转发
	    USER_CLOSE("9"), //用户关闭会话
	    AG_CLOSE("10"),  //坐席关闭会话
	    DELAY("11"),  //会话延迟
	    FINISH("12");    //会话完结
    
    private String code;
    
    private SessionStatus(String code){
        this.code = code;
    }
    
    @Override
    public String toString() {
        
        return code;
    }

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}
    
    
    

}
