/**
 * Project Name:SM Maven Webapp
 * File Name:SessionType.java
 * Package Name:cn.sh.ideal.mir.session.util
 * Date:2015年3月6日下午1:55:39
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.model;
/**
 * ClassName:SessionType <br/>
 * Function: 会话类型
 * Date:     2015年3月6日 下午1:55:39 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public enum SessionType {
    /*普通*/
    COMMON,
    /*转发*/
    FORWORD,
    /*延迟*/
    DELAY,
    /*多方*/
    MULTI;
    public String getName(){
        return this.name();
    }
    

}
