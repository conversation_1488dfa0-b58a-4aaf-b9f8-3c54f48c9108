package cn.sh.ideal.model;

import cn.sh.ideal.model.MessageInfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 */
public class RequestSessionbean implements java.io.Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -4194757312821881834L;
    private String sendAccount;
    private String acceptedAccount;
    private String channelCode;
    private String tenantCode;
    private String businessType;
    private String workNo;
    private String sessionId;
    private String nickname;
    private String skillQueue;
    private String skillType;
    @JSONField(serialize = false)
    private String msg;
    private String customerId;
    private String content;
    //区分会话
    private String idCard;
    //用户头像信息
    private String heanimgUrl;
    private String callId;


    public String getHeanimgUrl() {
        return heanimgUrl;
    }


    public void setHeanimgUrl(String heanimgUrl) {
        this.heanimgUrl = heanimgUrl;
    }


    public String getIdCard() {
        return idCard;
    }


    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }


    public RequestSessionbean() {
        super();
        // TODO Auto-generated constructor stub
    }


    public RequestSessionbean(String sendAccount, String acceptedAccount,
                              String channelCode, String tenantCode) {
        super();
        this.sendAccount = sendAccount;
        this.acceptedAccount = acceptedAccount;
        this.channelCode = channelCode;
        this.tenantCode = tenantCode;
    }

    public RequestSessionbean(String sendAccount, String acceptedAccount,
                              String channelCode, String tenantCode, String heanimgUrl) {
        super();
        this.sendAccount = sendAccount;
        this.acceptedAccount = acceptedAccount;
        this.channelCode = channelCode;
        this.tenantCode = tenantCode;
        this.heanimgUrl = heanimgUrl;
    }

    public RequestSessionbean(MessageInfo messageInfo) {
        super();
        this.sendAccount = messageInfo.getSendAccount();
        this.acceptedAccount = messageInfo.getAcceptedAccount();
        this.channelCode = messageInfo.getChannelCode();
        this.tenantCode = messageInfo.getTenantCode();
        this.businessType = messageInfo.getBusinessType();
        this.nickname = messageInfo.getNickname();
        this.skillQueue = messageInfo.getSkillQueue();
        this.skillType = messageInfo.getSkillType();
        this.content = messageInfo.getContent();
        this.callId = messageInfo.getCallId();
    }


    public String getContent() {
        return content;
    }


    public void setContent(String content) {
        this.content = content;
    }


    public String getSendAccount() {
        return sendAccount;
    }

    public void setSendAccount(String sendAccount) {
        this.sendAccount = sendAccount;
    }

    public String getAcceptedAccount() {
        return acceptedAccount;
    }

    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }


    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMsg() {

        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public String getSkillQueue() {
        return skillQueue;
    }


    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }


    public String getSkillType() {
        return skillType;
    }


    public void setSkillType(String skillType) {
        this.skillType = skillType;
    }


    public String getCustomerId() {
        return customerId;
    }


    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCallId() {
        return callId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


}
