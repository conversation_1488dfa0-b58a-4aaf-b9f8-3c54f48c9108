package cn.sh.ideal.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 微博点赞转发，评论数
 * <AUTHOR>
 *
 */
public class WeiboComentsSum  implements Serializable{
	private static final long serialVersionUID = 1L;
	//微博ID
	private String ids;
	public String getIds() {
		return ids;
	}
	public void setIds(String ids) {
		this.ids = ids;
	}
	private String id;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	private int praises;
	//评论数
	private int comments;
	//转发数
	private int reposts;
	
	//喜欢数
	private int attitudes;
	
	private String marking;
	public String getAccessTokens() {
		return accessTokens;
	}
	public void setAccessTokens(String accessTokens) {
		this.accessTokens = accessTokens;
	}
	private Date createTime;
	private Date updateTime;
	
	private String accessTokens;
	
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public int getAttitudes() {
		return attitudes;
	}
	public void setAttitudes(int attitudes) {
		this.attitudes = attitudes;
	}
	private String messge;
	public String getMessge() {
		return messge;
	}
	public void setMessge(String messge) {
		this.messge = messge;
	}
	
	public int getPraises() {
		return praises;
	}
	public void setPraises(int praises) {
		this.praises = praises;
	}
	public int getComments() {
		return comments;
	}
	public void setComments(int comments) {
		this.comments = comments;
	}
	public int getReposts() {
		return reposts;
	}
	public void setReposts(int reposts) {
		this.reposts = reposts;
	}
	
	public String getMarking() {
		return marking;
	}
	public void setMarking(String marking) {
		this.marking = marking;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
