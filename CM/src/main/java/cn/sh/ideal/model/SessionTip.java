package cn.sh.ideal.model;

import java.io.Serializable;

import cn.sh.ideal.model.BaseModel;

/**
 * Created by genghc on 2016/1/25.
 * 会话提示实体类
 */
public class SessionTip extends BaseModel implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private int id;
    /*租户code*/
    private String tenantCode;
    /*坐席超时提示*/
    private String agTimeoutTip;
    
    //坐席超时提示用户
    private String agTimeOutToUserTip;
    /*坐席超时关闭提示*/
    private String agTimeoutCloseTip;
    //坐席超时关闭提示用户
    private String agTimeOutCloseToUserTip;
    
    /*坐席关闭会话提示*/
    private String agCloseSessionTip;
    //坐席关闭会话提示用户
    private String agCloseSessionToUserTip;
   
    /*用户超时用户提示*/
    private String userTimeoutTip;
    //用户超时提示坐席
    private String userTimeOutToAGTip;

    /*用户超时关闭会话提示*/
    private String userTimeoutCloseTip;
    //用户超时关闭提示坐席
    private String userTimeOutCloseToAGTip;
    
    /*用户关闭会话提示*/
    private String userCloseSessionTip;
    //用户关闭会话提示坐席
    private String userCloseSessionToAGTip;
    
    /*坐席关闭三方会话提示*/
    private String agCloseMutualTip;
    
    //所属技能组
    private String skillQueue;
    //所属渠道
    private String acceptedAccount;
    
    
    
    /*工作时间*/
    private String  workTime;
    /*欢迎语*/
    private String  welcomeMessage;
    /*进入人工话术*/
    private String  connectMessage;
    /*排队话术*/
    private String  sortMessage;
    /*是否显示排队人数*/
    private String  isShowQueuecount;
    /*下班提示话术*/
    private String  workTimeOut;
    /*排队是否超时*/
    private String  isSortTimeOut;
    /*排队超时提示话术*/
    private String  sortTimeOutTip;
    /*排队人数话术*/
    private String  sortQueueCountTip;
    /*排队中回复话术*/
    private String  sortingMessageTip;
    /*节假日上班时间*/
    private String  workTimeHoliday;
    /*节假日上班欢迎话术*/
    private String  welcomeMessageHoliday;
    /*节假日下班提示话术*/
    private String  workTimeOutHoliday;
    /*节假日进人工话术*/
    private String  connectMessageHoliday;
    /*节假日排队话术*/
    private String  sortMessageHoliday;
    /*节假日是否显示排队人数*/
    private String  isShowQueuecountHoliday;
    /*节假日排队是否超时*/
    private String  isSortTimeoutHoliday;
    /*节假日排队超时话术*/
    private String  sortTimeoutTipHoliday;
    /*节假日排队人数话术*/
    private String  sortQueuecountTipHoliday;
    /*节假日排队中回复话术*/
    private String  sortingMessageTipHoliday;
    /*节假日*/
    private String  holiday;
    
    

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getAgTimeoutTip() {
        return agTimeoutTip;
    }

    public void setAgTimeoutTip(String agTimeoutTip) {
        this.agTimeoutTip = agTimeoutTip;
    }

    public String getAgTimeoutCloseTip() {
        return agTimeoutCloseTip;
    }

    public void setAgTimeoutCloseTip(String agTimeoutCloseTip) {
        this.agTimeoutCloseTip = agTimeoutCloseTip;
    }

    public String getUserTimeoutTip() {
        return userTimeoutTip;
    }

    public void setUserTimeoutTip(String userTimeoutTip) {
        this.userTimeoutTip = userTimeoutTip;
    }

    public String getUserCloseSessionTip() {
        return userCloseSessionTip;
    }

    public void setUserCloseSessionTip(String userCloseSessionTip) {
        this.userCloseSessionTip = userCloseSessionTip;
    }

    public String getAgCloseSessionTip() {
        return agCloseSessionTip;
    }

    public void setAgCloseSessionTip(String agCloseSessionTip) {
        this.agCloseSessionTip = agCloseSessionTip;
    }

    public String getAgCloseMutualTip() {
        return agCloseMutualTip;
    }

    public void setAgCloseMutualTip(String agCloseMutualTip) {
        this.agCloseMutualTip = agCloseMutualTip;
    }

    public String getUserTimeoutCloseTip() {
        return userTimeoutCloseTip;
    }

    public void setUserTimeoutCloseTip(String userTimeoutCloseTip) {
        this.userTimeoutCloseTip = userTimeoutCloseTip;
    }

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getAgTimeOutToUserTip() {
		return agTimeOutToUserTip;
	}

	public void setAgTimeOutToUserTip(String agTimeOutToUserTip) {
		this.agTimeOutToUserTip = agTimeOutToUserTip;
	}

	public String getAgTimeOutCloseToUserTip() {
		return agTimeOutCloseToUserTip;
	}

	public void setAgTimeOutCloseToUserTip(String agTimeOutCloseToUserTip) {
		this.agTimeOutCloseToUserTip = agTimeOutCloseToUserTip;
	}

	public String getAgCloseSessionToUserTip() {
		return agCloseSessionToUserTip;
	}

	public void setAgCloseSessionToUserTip(String agCloseSessionToUserTip) {
		this.agCloseSessionToUserTip = agCloseSessionToUserTip;
	}

	public String getUserTimeOutToAGTip() {
		return userTimeOutToAGTip;
	}

	public void setUserTimeOutToAGTip(String userTimeOutToAGTip) {
		this.userTimeOutToAGTip = userTimeOutToAGTip;
	}

	public String getUserCloseSessionToAGTip() {
		return userCloseSessionToAGTip;
	}

	public void setUserCloseSessionToAGTip(String userCloseSessionToAGTip) {
		this.userCloseSessionToAGTip = userCloseSessionToAGTip;
	}

	public String getUserTimeOutCloseToAGTip() {
		return userTimeOutCloseToAGTip;
	}

	public void setUserTimeOutCloseToAGTip(String userTimeOutCloseToAGTip) {
		this.userTimeOutCloseToAGTip = userTimeOutCloseToAGTip;
	}

	public String getAcceptedAccount() {
		return acceptedAccount;
	}

	public void setAcceptedAccount(String acceptedAccount) {
		this.acceptedAccount = acceptedAccount;
	}


	public String getWorkTime() {
		return workTime;
	}

	public void setWorkTime(String workTime) {
		this.workTime = workTime;
	}

	public String getWelcomeMessage() {
		return welcomeMessage;
	}

	public void setWelcomeMessage(String welcomeMessage) {
		this.welcomeMessage = welcomeMessage;
	}

	public String getConnectMessage() {
		return connectMessage;
	}

	public void setConnectMessage(String connectMessage) {
		this.connectMessage = connectMessage;
	}

	public String getSortMessage() {
		return sortMessage;
	}

	public void setSortMessage(String sortMessage) {
		this.sortMessage = sortMessage;
	}

	public String getIsShowQueuecount() {
		return isShowQueuecount;
	}

	public void setIsShowQueuecount(String isShowQueuecount) {
		this.isShowQueuecount = isShowQueuecount;
	}

	public String getWorkTimeOut() {
		return workTimeOut;
	}

	public void setWorkTimeOut(String workTimeOut) {
		this.workTimeOut = workTimeOut;
	}

	public String getIsSortTimeOut() {
		return isSortTimeOut;
	}

	public void setIsSortTimeOut(String isSortTimeOut) {
		this.isSortTimeOut = isSortTimeOut;
	}

	public String getSortTimeOutTip() {
		return sortTimeOutTip;
	}

	public void setSortTimeOutTip(String sortTimeOutTip) {
		this.sortTimeOutTip = sortTimeOutTip;
	}

	public String getSortQueueCountTip() {
		return sortQueueCountTip;
	}

	public void setSortQueueCountTip(String sortQueueCountTip) {
		this.sortQueueCountTip = sortQueueCountTip;
	}

	public String getSortingMessageTip() {
		return sortingMessageTip;
	}

	public void setSortingMessageTip(String sortingMessageTip) {
		this.sortingMessageTip = sortingMessageTip;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getWorkTimeHoliday() {
		return workTimeHoliday;
	}

	public void setWorkTimeHoliday(String workTimeHoliday) {
		this.workTimeHoliday = workTimeHoliday;
	}

	public String getWelcomeMessageHoliday() {
		return welcomeMessageHoliday;
	}

	public void setWelcomeMessageHoliday(String welcomeMessageHoliday) {
		this.welcomeMessageHoliday = welcomeMessageHoliday;
	}

	public String getWorkTimeOutHoliday() {
		return workTimeOutHoliday;
	}

	public void setWorkTimeOutHoliday(String workTimeOutHoliday) {
		this.workTimeOutHoliday = workTimeOutHoliday;
	}

	public String getConnectMessageHoliday() {
		return connectMessageHoliday;
	}

	public void setConnectMessageHoliday(String connectMessageHoliday) {
		this.connectMessageHoliday = connectMessageHoliday;
	}

	public String getSortMessageHoliday() {
		return sortMessageHoliday;
	}

	public void setSortMessageHoliday(String sortMessageHoliday) {
		this.sortMessageHoliday = sortMessageHoliday;
	}

	public String getIsShowQueuecountHoliday() {
		return isShowQueuecountHoliday;
	}

	public void setIsShowQueuecountHoliday(String isShowQueuecountHoliday) {
		this.isShowQueuecountHoliday = isShowQueuecountHoliday;
	}

	public String getIsSortTimeoutHoliday() {
		return isSortTimeoutHoliday;
	}

	public void setIsSortTimeoutHoliday(String isSortTimeoutHoliday) {
		this.isSortTimeoutHoliday = isSortTimeoutHoliday;
	}

	public String getSortTimeoutTipHoliday() {
		return sortTimeoutTipHoliday;
	}

	public void setSortTimeoutTipHoliday(String sortTimeoutTipHoliday) {
		this.sortTimeoutTipHoliday = sortTimeoutTipHoliday;
	}

	public String getSortQueuecountTipHoliday() {
		return sortQueuecountTipHoliday;
	}

	public void setSortQueuecountTipHoliday(String sortQueuecountTipHoliday) {
		this.sortQueuecountTipHoliday = sortQueuecountTipHoliday;
	}

	public String getSortingMessageTipHoliday() {
		return sortingMessageTipHoliday;
	}

	public void setSortingMessageTipHoliday(String sortingMessageTipHoliday) {
		this.sortingMessageTipHoliday = sortingMessageTipHoliday;
	}

	public String getHoliday() {
		return holiday;
	}

	public void setHoliday(String holiday) {
		this.holiday = holiday;
	}
	
}
