package cn.sh.ideal.dao;

import java.util.ArrayList;
import java.util.List;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.FrequentWord;
import cn.sh.ideal.model.SkillType;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.model.TenantInfo;
import cn.sh.ideal.model.TenantSkillQueue;
/**
 * sql执行类
 * 
 * <AUTHOR>
 * 2015-04-20
 * @since v1.1.6 
 *
 */
@Repository("sqlExecute")
public class SqlExecute {
	
	@Autowired
	private SqlSessionTemplate sqlSessionTemplate;
	 
	public List<SysParam> getSysParam(){
		List<SysParam> params = new ArrayList<SysParam>();
		for(Object o :sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getSysParam")){
			params.add((SysParam)o);
		}
		return params;
	}
	
	public List<TenantInfo> getTenantInfo(){
		List<TenantInfo> infos = new ArrayList<TenantInfo>();
		
		for(Object o :sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getTenantInfo")){
			infos.add((TenantInfo)o);
		}
		
		return infos;
	}
	
	public List<TenantSkillQueue> getTenantSkillQueue(){
		List<TenantSkillQueue> infos = new ArrayList<TenantSkillQueue>();
		
		for(Object o :sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getSkillQueue")){
			infos.add((TenantSkillQueue)o);
		}
		
		return infos;
	}
	
	public List<ChannelConfig> getChannelInfo(){
		List<ChannelConfig> infos = new ArrayList<ChannelConfig>();
		
		for(Object o :sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getChannelInfo")){
			infos.add((ChannelConfig)o);
		}
		
		return infos;
	}
	
	public List<SkillType> getSkillType(){
		List<SkillType> infos = new ArrayList<SkillType>();
		
		for(Object o : sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getSkillType")){
			infos.add((SkillType)o);
		}
		
		return infos;
	}
	
	public List<SysParam> getRedisParam(){
		return sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getRedisParam");
	}
	
	public SysRule getRuleById(String id){
		return sqlSessionTemplate.selectOne("cn.sh.ideal.dao.SysInitMapper.getRuleById",id);
	}
	public SysRule getRuleByCode(String code){
		return sqlSessionTemplate.selectOne("cn.sh.ideal.dao.SysInitMapper.getRuleByCode",code);
	}
	public List<FrequentWord> getAllImrFreWord() {
		
		List<FrequentWord> params = new ArrayList<FrequentWord>();
		for(Object o :sqlSessionTemplate.selectList("cn.sh.ideal.dao.SysInitMapper.getAllImrFreWord")){
			params.add((FrequentWord)o);
		}
		return params;
		
	}
}
