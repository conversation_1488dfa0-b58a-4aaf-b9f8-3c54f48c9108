package cn.sh.ideal.dao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.PipelineOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.stereotype.Repository;

@Repository("redisDao")
public class RedisDao<K, V extends Serializable>
{
	private static final Logger logger = LoggerFactory.getLogger(RedisDao.class);
	@Autowired
	private RedisTemplate<K, V> rt;

	public void saveValue(K key, V value)
	{
		this.rt.opsForValue().set(key, value);
	}

	public void saveValue(K key, V value, int timeout, TimeUnit timeUnit)
	{
		this.rt.opsForValue().set(key, value, timeout, timeUnit);
	}

	public V readValue(K key)
	{
		return (V)this.rt.opsForValue().get(key);
	}

	public void deleteValue(K key)
	{
		this.rt.delete(key);
	}

	public boolean saveNxValue(K key, V value)
	{
		return this.rt.opsForValue().setIfAbsent(key, value).booleanValue();
	}

	public boolean saveNxValue(K key, V value, int timeout, TimeUnit timeUnit)
	{
		if (saveNxValue(key, value))
		{
			if(!this.rt.expire(key, timeout, timeUnit)){
				logger.warn(key + "加锁设置超时时间失败");
			}
			return true;
		}
		return false;
	}

	public Set<K> getKeysByPattern(K pattern)
	{
		return this.rt.keys(pattern);
	}

	public void mapPut(K key, Object hashKey, Object value)
	{
		this.rt.opsForHash().put(key, hashKey, value);
	}

	public void mapPutAll(K key, Map<Object, Object> map)
	{
		this.rt.opsForHash().putAll(key, map);
	}

	public Object mapGetValue(K key, Object hashKey)
	{
		return this.rt.opsForHash().get(key, hashKey);
	}

	public Map<Object, Object> mapGet(K key)
	{
		return this.rt.opsForHash().entries(key);
	}

	public void mapRemove(K key, Object... hashKeys)
	{
		this.rt.opsForHash().delete(key, hashKeys);
	}

	public void listlPush(K key, V value)
	{
		this.rt.opsForList().leftPush(key, value);
	}

	public void listlPush(K key, List<V> values)
	{
		this.rt.opsForList().leftPushAll(key, values);
	}

	public void listrPush(K key, V value)
	{
		this.rt.opsForList().rightPush(key, value);
	}

	public void listrPush(K key, List<V> values)
	{
		this.rt.opsForList().rightPushAll(key, values);
	}

	public V listlPop(K key)
	{
		return (V)this.rt.opsForList().leftPop(key);
	}

	public V listrPop(K key)
	{
		return (V)this.rt.opsForList().rightPop(key);
	}

	public List<V> listPopAll(K key, long start, long end)
	{
		List<V> list = new ArrayList();
		for (long i = start; i <= end; i += 1L)
		{
			V value = listlPop(key);
			if (null == value) {
				break;
			}
			list.add(value);
		}
		return list;
	}

	public List<V> listRange(K key, long start, long end)
	{
		List<V> list = null;
		list = this.rt.opsForList().range(key, start, end);
		return null == list ? new ArrayList() : list;
	}

	public List<V> listRange(K key, long num)
	{
		return listRange(key, 0L, num - 1L);
	}

	public List<V> listRangeAll(K key)
	{
		return listRange(key, 0L, listSize(key));
	}

	public void listRemove(K key, V value)
	{
		this.rt.opsForList().remove(key, 1L, value);
	}

	public long listSize(K key)
	{
		return this.rt.opsForList().size(key).longValue();
	}

	public void setAdd(K key, V... values)
	{
		this.rt.opsForSet().add(key, values);
	}

	public void setRemove(K key, Object... values)
	{
		this.rt.opsForSet().remove(key, values);
	}

	public void setRemove(K key, Object value)
	{
		this.rt.opsForSet().remove(key, new Object[] { value });
	}

	public boolean setIsMemeber(K key, Object value)
	{
		return this.rt.opsForSet().isMember(key, value).booleanValue();
	}

	public Set<V> setMembers(K key)
	{
		return this.rt.opsForSet().members(key);
	}

	public V setPop(K key)
	{
		return (V)this.rt.opsForSet().pop(key);
	}

	public long setSize(K key)
	{
		return this.rt.opsForSet().size(key).longValue();
	}

	public void zsetAdd(K key, V value, double score)
	{
		this.rt.opsForZSet().add(key, value, score);
	}

	public void zsetAddAll(K key, Set<ZSetOperations.TypedTuple<V>> tuples)
	{
		this.rt.opsForZSet().add(key, tuples);
	}

	public Set<V> zsetRange(K key, long start, long end)
	{
		Set<V> set = null;
		set = this.rt.opsForZSet().range(key, start, end);
		return set == null ? new HashSet() : set;
	}

	public Set<V> zsetRangeByScore(K key, double min, double max)
	{
		Set<V> set = null;
		set = this.rt.opsForZSet().rangeByScore(key, min, max);
		return set == null ? new HashSet() : set;
	}

	public Set<V> zsetRange(K key, long num)
	{
		return zsetRange(key, 0L, num - 1L);
	}

	public Set<V> zsetRangeAll(K key)
	{
		return zsetRange(key, 0L, zsetSize(key));
	}

	public Set<V> zsetReverseRange(K key, long num)
	{
		Set<V> set = null;
		long size = zsetSize(key);
		set = this.rt.opsForZSet().range(key, size - num, size);
		return set == null ? new HashSet() : set;
	}

	public Set<V> zsetReverseRangeByScore(K key, double min, double max)
	{
		Set<V> set = null;
		set = this.rt.opsForZSet().reverseRangeByScore(key, min, max);
		return set == null ? new HashSet() : set;
	}

	public void zsetRemoveRange(K key, long start, long end)
	{
		this.rt.opsForZSet().removeRange(key, start, end);
	}

	public void zsetRemoveRange(K key, long num)
	{
		zsetRemoveRange(key, 0L, num - 1L);
	}

	public void zsetRemoveByScore(K key, double min, double max)
	{
		this.rt.opsForZSet().removeRangeByScore(key, min, max);
	}

	public void zsetReverseRomove(K key, long num)
	{
		long size = zsetSize(key);
		this.rt.opsForZSet().removeRange(key, size - num, size);
	}

	public void zsetRemove(K key, Object... values)
	{
		this.rt.opsForZSet().remove(key, values);
	}

	public long zsetSize(K key)
	{
		return this.rt.opsForZSet().zCard(key).longValue();
	}

	public long rank(K key, V value)
	{
		return this.rt.opsForZSet().rank(key, value).longValue();
	}

	public boolean exist(K key)
	{
		return this.rt.hasKey(key).booleanValue();
	}

	public long increment(K key, long count)
	{
		return this.rt.opsForValue().increment(key, count).longValue();
	}

	public void expire(K key, long timeout, TimeUnit unit)
	{
		this.rt.expire(key, timeout, unit);
	}

	public void publishMessage(String channel, V message)
	{
		this.rt.convertAndSend(channel, message);
	}

	public List<V> pipelineGet(Set<K> keys)
	{
		return this.rt.opsForPipeline().get(keys);
	}

	public void pipelineSet(Map<K, V> valueMap)
	{
		this.rt.opsForPipeline().set(valueMap);
	}

	public void pipelineDel(Set<K> keys)
	{
		this.rt.opsForPipeline().del(keys);
	}

	public List<Map<Object, Object>> pipelineHmget(Set<K> keys)
	{
		return this.rt.opsForPipeline().hmget(keys);
	}

	public void pipelineHmset(Map<K, Map<Object, Object>> valuesMap)
	{
		this.rt.opsForPipeline().hmset(valuesMap);
	}
}
