package cn.sh.ideal.dao;

import java.util.List;

import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.FrequentWord;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.model.TenantInfo;
import cn.sh.ideal.model.TenantSkillQueue;

/**
 * 初始化dao
 * 
 * <AUTHOR>
 * 2015-04-20
 * @since v1.1.6 
 *
 */
public interface SysInitMapper {
	/**
	 * 获取所有系统参数
	 * 
	 * @return
	 */
	public List<SysParam> getSysParam();
	
	/**
	 * 获取所有租户信息
	 * 
	 * @return
	 */
	public List<TenantInfo> getTenantInfo();
	
	/**
	 * 获取所有技能组信息
	 * 
	 * @return
	 */
	public List<TenantSkillQueue> getTenantSkillQueue();
	
	/**
	 * 获取所有渠道配置信息
	 * 
	 * @return
	 */
	public List<ChannelConfig> getChannelInfo();
	
	/**
	 * 获取redis配置参数
	 * 
	 * @return
	 */
	public List<SysParam> getRedisParam();
	
	/**
	 * 根据ID获取系统规则
	 * 
	 * @param id
	 * @return
	 */
	public SysRule getRuleById(String id);
	
	/**
	 * 根据CODE获取系统规则
	 * 
	 * @param code
	 * @return
	 */
	public SysRule getRuleByCode(String code);
	
	/**
	 * 
	 * @Description 
	 * @return
	 */
	public List<FrequentWord> getAllImrFreWord();
}
