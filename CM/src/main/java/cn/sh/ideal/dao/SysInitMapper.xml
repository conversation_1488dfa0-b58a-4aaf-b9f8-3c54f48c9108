<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.dao.SysInitMapper" >
  
  <resultMap id="tenantInfoMap" type="cn.sh.ideal.model.TenantInfo" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="TENANT_NAME" property="tenantName" jdbcType="VARCHAR" />
    <result column="ACCOUNT" property="account" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="ENCRYPT_TYPE" property="encryptType" jdbcType="VARCHAR" />
    <result column="DECODE_KEY" property="decodeKey" jdbcType="VARCHAR" />
    <result column="CHANNEL" property="channel" jdbcType="VARCHAR" />
    <result column="ROUTING_RULE" property="routingRule" jdbcType="VARCHAR" />
    <result column="SKILL_RULE" property="skillRule" jdbcType="VARCHAR" />
    <result column="TERMINAL_NUM" property="terminalNum" jdbcType="INTEGER" />
    <result column="DUE_DATE" property="dueDate" jdbcType="TIMESTAMP" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  
  <resultMap id="skillQueueMap" type="cn.sh.ideal.model.TenantSkillQueue" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="QUEUE_NAME" property="queueName" jdbcType="VARCHAR" />
    <result column="QUEUE_CODE" property="queueCode" jdbcType="VARCHAR" />
    <result column="QUEUE_SIZE" property="queueSize" jdbcType="INTEGER" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
    <result column="ABLE_BUSINESS_TYPES" property="ableBusinessTypes" jdbcType="VARCHAR" />
    <result column="ABLE_CHANNELS" property="ableChannels" jdbcType="VARCHAR" />
    <result column="SORT_RULE" property="sortRule" jdbcType="VARCHAR" />
    <result column="ALLOCATION_RULE" property="allocationRule" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="CONCURRENT_COUNT" property="concurrentCount" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap id="sysRuleMap" type="cn.sh.ideal.model.SysRule" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="RULE_NAME" property="ruleName" jdbcType="VARCHAR" />
    <result column="RULE_TYPE" property="ruleType" jdbcType="VARCHAR" />
    <result column="RULE_CODE" property="ruleCode" jdbcType="VARCHAR" />
    <result column="RULE_METHOD" property="ruleMethod" jdbcType="VARCHAR" />
    <result column="IS_GLOBAL" property="isGlobal" jdbcType="VARCHAR" />
    <result column="INTERFACE_URL" property="interfaceUrl" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  
   <resultMap id="channelInfoMap" type="cn.sh.ideal.model.ChannelConfig" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR" />
    <result column="CHANNEL_ACCOUNT" property="channelAccount" jdbcType="VARCHAR" />
    <result column="ACCOUNT_CONFIG" property="accountConfig" jdbcType="VARCHAR" />
    <result column="CHANNEL_ENABLE" property="channelEnable" jdbcType="VARCHAR" />
    <result column="TENANT" property="tenantCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_TYPE" property="channelType" jdbcType="VARCHAR" />
    <result column="REAL_TIME" property="realTime" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="SESSION_DUETIME" property="sessionDueTime" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap id="freWordMap" type="cn.sh.ideal.model.FrequentWord" >
    <id column="AUTO_ID" property="autoId" jdbcType="DECIMAL" />
    <result column="PARAM_CODE" property="paramCode" jdbcType="VARCHAR" />
    <result column="PARAM_VALUE" property="paramValue" jdbcType="VARCHAR" />
  </resultMap>
  
  <!--获得IMR数据库中的IMR话术内容  -->
  <select id="getAllImrFreWord" resultMap="freWordMap" >
  	select  AUTO_ID, PARAM_CODE, PARAM_VALUE 
    from IMR_FREQUENT_WORD
  </select>
 
  <!-- 获取系统参数 --> 
	<select id="getSysParam" resultType="cn.sh.ideal.model.SysParam">
	 	select auto_id id,
	 		   param_code paramCode,
	 		   param_value paramValue,
	 		   param_info paramInfo,
	 		   param_desc paramDesc,
	 		   order_type orderType,
	 		   remark
	    from cms_sys_param
	</select>
  
  
 <!--  获取所有租户信息 -->
  <select id="getTenantInfo" resultMap="tenantInfoMap">
    SELECT  * FROM MGW_TENANT_INFO
    WHERE STATUS = 1
  </select>
  
   <!--  获取所有技能组信息 -->
  <select id="getSkillQueue" resultMap="skillQueueMap">
  	select A.* from mgw_tenant_skill_queue A where A.status = 1
  </select>  
   <!--  获取redis配置信息 -->
  <select id="getRedisParam" resultType="cn.sh.ideal.model.SysParam">
		<!-- select  
			max(decode(param_code,'REDIS_MODEL',param_value)) REDIS_MODEL,
	        max(decode(param_code,'REDIS_HOST',param_value)) REDIS_HOST,
	        max(decode(param_code,'REDIS_SENTINEL',param_value)) REDIS_SENTINEL,
	        max(decode(param_code,'REDIS_MASTER_NAME',param_value,'200')) REDIS_MASTER_NAME,
	        max(decode(param_code,'REDIS_PASSWORD',param_value)) REDIS_PASSWORD
		from cms_sys_param -->
		select auto_id id,
	 		   param_code paramCode,
	 		   param_value paramValue,
	 		   param_info paramInfo,
	 		   param_desc paramDesc,
	 		   order_type orderType,
	 		   remark
	    from cms_sys_param
	    where param_code like '%REDIS%'
	</select>
	<!-- 根据id获取规则信息 -->
	<select id="getRuleById" resultMap="sysRuleMap" parameterType="string">
		SELECT * FROM MGW_SYS_RULE
		WHERE ID = #{id,jdbcType=VARCHAR}
	</select>
	<!-- 根据code获取规则信息 -->
	<select id="getRuleByCode" resultMap="sysRuleMap" parameterType="string">
		SELECT * FROM MGW_SYS_RULE
		WHERE RULE_CODE = #{code,jdbcType=VARCHAR}
	</select>
	
	 <select id="getChannelInfo" resultMap="channelInfoMap" >
    SELECT  A.*,B.CHANNEL_NAME,B.SESSION_DUETIME  FROM MGW_TENANT_CHANNEL_CONFIG A LEFT JOIN MGW_CHANNEL_INFO B
	ON A.CHANNEL_CODE = B.CHANNEL_CODE
	where A.CHANNEL_ENABLE =1 AND B.IS_ENABLE = 1
  </select>
  
   <select id="getSkillType" resultType="cn.sh.ideal.model.SkillType">
		  SELECT t.id ,
		       t.skill_type_code skillTypeCode,
		       t.skill_type_name skillTypeName,
		       t.status,
		       t.tenant_id tenantCode,
		       t.remark,
		       t.is_record isRecord,
		       t.is_replay isReplay 
		  FROM mgw_tenant_skill_type t
		  WHERE t.status=1 
  </select>
</mapper>