package cn.sh.ideal.init;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.dao.SqlExecute;
import cn.sh.ideal.model.FrequentWord;

@Service("frequentWordInitService")
public class FrequentWordInitService {
	
	public static final String KEY_FREQUENT_WORD = "IMR_FREQUENT_WORD";
	
	private static Logger log = Logger.getLogger(FrequentWordInitService.class);

	
	@Autowired
	private SqlExecute sqlExecute;
	
	@Autowired
	private RedisDao<String, Serializable> redisDao;

	
	public void init(){
		initFrequentWord();
		Map<String ,FrequentWord> map = (Map<String ,FrequentWord>)this.checkFreWordLoad();
		if(map == null){
			throw new RuntimeException("初始化IMR话术失败!");
		}
	}
	
	public void initFrequentWord(){
		log.info("初始化IMR话术……");
		try {
			HashMap<String,FrequentWord> freWordMap = new HashMap<String,FrequentWord>();
			List<FrequentWord> list = sqlExecute.getAllImrFreWord();
			for (FrequentWord frequentWord : list) {
				freWordMap.put(frequentWord.getParamCode(), frequentWord);
			}
			redisDao.saveValue(KEY_FREQUENT_WORD, freWordMap);
			log.info("初始化IMR话术成功!");
		} catch (Exception e) {
			log.error("初始化IMR话术异常:",e);
		}
	}
	
	
	
	public FrequentWord getFreWordByParamCode(String paramCode){
		Map<String ,FrequentWord> map = (Map<String ,FrequentWord>)this.checkFreWordLoad();
		return map.get(paramCode);
	}
	
	
	
	
	@SuppressWarnings("unchecked")
	public Map<String, FrequentWord> checkFreWordLoad(){
		Map<String, FrequentWord> map = (Map<String,FrequentWord>)redisDao.readValue(KEY_FREQUENT_WORD);
		//如果缓存中没数据尝试重新加载一次
		if(map == null){
			this.initFrequentWord();
			map = (Map<String,FrequentWord>)redisDao.readValue(KEY_FREQUENT_WORD);
		}
		return map;
	}
}
