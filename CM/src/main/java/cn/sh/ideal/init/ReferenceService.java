package cn.sh.ideal.init;

import static cn.sh.ideal.util.Constants.AGENT_IPS;

import java.io.Serializable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.ConfigUtils;
import com.alibaba.dubbo.config.ReferenceConfig;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.util.ReferenceConfigUtil;

@Service("referenceService")
public class ReferenceService<T> {
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	/**
	 * 
	 * @param tenantCode
	 * @param workNo
	 * @param version
	 * @param interfaceClass
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public T getSiService(String tenantCode,String workNo,String version,Class interfaceClass){
		
		String ip = (String)redisDao.mapGetValue(AGENT_IPS, tenantCode.concat(workNo));
		
		String url = ConfigUtils.getProperty("dubbo.protocol")+"://" + ip;
		
		ReferenceConfig reference = ReferenceConfigUtil.getConfig(version,url,interfaceClass);
		
		return (T)reference.get();
	}
}
