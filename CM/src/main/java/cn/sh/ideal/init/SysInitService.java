package cn.sh.ideal.init;
import static cn.sh.ideal.util.Constants.CHANNELINFO;
import static cn.sh.ideal.util.Constants.DEFAULT_ALLOCATE_RULE;
import static cn.sh.ideal.util.Constants.DEFAULT_ROUTING_RULE;
import static cn.sh.ideal.util.Constants.DEFAULT_SKILLQUEUE_RULE;
import static cn.sh.ideal.util.Constants.DEFAULT_SORT_RULE;
import static cn.sh.ideal.util.Constants.QUEUELIST;
import static cn.sh.ideal.util.Constants.SKILL_TYPE_CODE;
import static cn.sh.ideal.util.Constants.SYS_PARAM;
import static cn.sh.ideal.util.Constants.SYS_PARAM_MIR_DEFAULT_QUEUE_SIZE;
import static cn.sh.ideal.util.Constants.TENANTINFO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.dao.SqlExecute;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.SkillQueue;
import cn.sh.ideal.model.SkillType;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.model.SysRule;
import cn.sh.ideal.model.TenantInfo;
import cn.sh.ideal.model.TenantSkillQueue;
import cn.sh.ideal.util.Constants;


/**
 * system initial class
 * 
 * <AUTHOR>
 * 2015-4-17
 *
 * @since 
 */

@Component("sysInitService")
@SuppressWarnings("unchecked")
public class SysInitService {
	private static final Logger logger = LoggerFactory.getLogger(SysInitService.class);
	
	@Autowired
	private SqlExecute sqlExecute;
	
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	
	/**
	 * the main init method
	 * 
	 * @param initSp whether init sysparam
	 * @param initTi whether init tenantinfo
	 * @param initSq whether init skillqueue
	 * @param initCi whether init channelinfo
	 * @param initCi whether init skilltype
	 */
	public void init(boolean initSp,boolean initTi,boolean initSq,boolean initCi,boolean initSk){
		if(initSp) initSysParam();
		if(initTi) initTenantInfo();
		if(initSq) initSkillQueue();
		if(initCi) initChannelInfo();
		if(initSk) initSkillType();
	}
	
	/**
	 * init sysparam
	 * 
	 * redis key:SYS_PARAM
	 * redis vallue:HashMap<String,SysParam>
	 * map key:paramCode
	 */
    public void initSysParam(){
		logger.info("初始化系统参数……");
		HashMap<String,SysParam> sysParams  = new HashMap<String,SysParam>();
		
		try{
			List<SysParam> params = sqlExecute.getSysParam();
	    	
	    	for(SysParam sysParam : params){
	    		sysParams.put(sysParam.getParamCode(), sysParam);
	    	}
	    	
	    	redisDao.saveValue(SYS_PARAM, sysParams);
	    	
		}catch(Exception e){
			logger.error("初始化系统参数异常:",e);
		}
    }
	
    /**
	 * init tenantinfo
	 * 
	 * redis key:TENANTINFO
	 * redis vallue:HashMap<String,TenantInfo>
	 * map key:tenantCode
	 */
	public void initTenantInfo() {
		logger.info("初始化租户信息……");
		
		HashMap<String,TenantInfo> tenantMap = new HashMap<String,TenantInfo>();
		
		try {
			List<TenantInfo> tenantInfos = sqlExecute.getTenantInfo();
			
			for(TenantInfo info : tenantInfos){
				SysRule routeRule = null;
				try {
					routeRule = sqlExecute.getRuleById(info.getRoutingRule());
					routeRule = routeRule == null ? sqlExecute.getRuleByCode(DEFAULT_ROUTING_RULE): routeRule;
				} catch (Exception e) {
					routeRule = routeRule == null ? sqlExecute.getRuleByCode(DEFAULT_ROUTING_RULE): routeRule;
				}
				
				SysRule skillRule = null;
				try {
					skillRule = sqlExecute.getRuleById(info.getSkillRule());
					skillRule = skillRule == null ? sqlExecute.getRuleByCode(DEFAULT_SKILLQUEUE_RULE): skillRule;
				} catch (Exception e) {
					skillRule = skillRule == null ? sqlExecute.getRuleByCode(DEFAULT_SKILLQUEUE_RULE): skillRule;
				}
				
				info.setRouteImpl(routeRule);
				info.setSkillImpl(skillRule);
				
				tenantMap.put(info.getTenantCode(), info);
			}
			
			redisDao.saveValue(TENANTINFO, tenantMap);
		} catch (Exception e) {
			logger.error("初始化租户异常:{}", e.getMessage());
		}
	}
	
	/**
	 * init channelinfo
	 * 
	 * redis key:CHANNELINFO
	 * redis vallue:HashMap<String,ChannelInfo>
	 * map key:channelAccount
	 */
	public void initChannelInfo(){
		logger.info("初始化渠道配置……");
		HashMap<String,ChannelConfig> channels  = new HashMap<String,ChannelConfig>();
		
		try{
			List<ChannelConfig> infos = sqlExecute.getChannelInfo();
	    	
	    	for(ChannelConfig info : infos){
	    		channels.put(info.getChannelAccount(), info);
	    	}
	    	
	    	redisDao.saveValue(CHANNELINFO, channels);
	    	
		}catch(Exception e){
			logger.error("初始化渠道配置异常:{}",e.getMessage());
		}
	}
	
	/**
	 * init skillqueue
	 * 
	 * redis key:QUEUELIST
	 * redis vallue: ArrayList<SkillQueue>
	 */
	public void initSkillQueue() {
		logger.info("初始化技能组信息……");
		try {
			List<TenantSkillQueue> tenantSkillQueues = sqlExecute.getTenantSkillQueue();
			
			ArrayList<SkillQueue> queues = new ArrayList<SkillQueue>();
			
			for(TenantSkillQueue tenantSkillQueue : tenantSkillQueues){
				queues.add(skillQueue(tenantSkillQueue));
			}
			
			redisDao.saveValue(QUEUELIST, queues);
		} catch (Exception e) {
			logger.error("初始化租户技能组异常", e);
		}
	}
	
	
	/**
	 * init skilltype
	 * 
	 * */
	public void initSkillType() {
		logger.info("初始化技能组分类信息……");
		try{
			List<SkillType> skillTypes = sqlExecute.getSkillType();
			for(SkillType st : skillTypes){
				redisDao.mapPut(SKILL_TYPE_CODE, st.getTenantCode().concat(st.getSkillTypeCode()), st);
			}
		
		}catch(Exception e){
			logger.error("初始化技能组分类异常:{}",e.getMessage());
		}
	}
	
	/**
	 * assemble url
	 * 
	 * @param paramCode
	 * @param suffixUrl
	 * @return
	 */
	public String assemblyUrl(String paramCode,String suffixUrl) {
		if(null == suffixUrl || "".equals(suffixUrl.trim())){
			throw new NullPointerException("the suffix of url is null");
		}
		return getSysParam(paramCode).getParamValue().concat(suffixUrl);
	}
	
	/**
	 * return the sysparam with given paramcode
	 * 
	 * @param paramCode
	 * @return
	 */
	public SysParam getSysParam(String paramCode) {
		Map<String,SysParam> sysParams = (HashMap<String,SysParam>)redisDao.readValue(SYS_PARAM);
		//如果缓存中没数据尝试重新加载一次
		if(null == sysParams){
			this.initSysParam();
			sysParams = (HashMap<String,SysParam>)redisDao.readValue(SYS_PARAM);
		}
				
		if(null == sysParams || null == sysParams.get(paramCode)){
			throw new NullPointerException("the sys param is null");
		}
		
		return sysParams.get(paramCode);
	}
	
	/**
	 * return tenantinfo with the given tenantcode
	 * 
	 * @param tenantCode
	 * @return
	 */
	public TenantInfo getTenantInfo(String tenantCode) {
		Map<String,TenantInfo> tenantMap = (HashMap<String,TenantInfo>)redisDao.readValue(TENANTINFO);
		
		//如果缓存中没数据尝试重新加载一次
		if(null == tenantMap){
			this.initTenantInfo();
			tenantMap = (HashMap<String,TenantInfo>)redisDao.readValue(TENANTINFO);
		}
		
		if(null == tenantMap || tenantMap.get(tenantCode) == null){
			throw new NullPointerException("the tenant info is not exist");
		}
		return tenantMap.get(tenantCode);
	}
	
	/**
	 * return the channelinfo with given channelaccount
	 * 
	 * @param channelAccount
	 * @return
	 */
	public ChannelConfig getChannelInfo(String channelAccount){
		Map<String,ChannelConfig> channelMap = (HashMap<String,ChannelConfig>)redisDao.readValue(CHANNELINFO);
		
		//如果缓存中没数据尝试重新加载一次
		if(null == channelMap){
			this.initChannelInfo();
			channelMap = (HashMap<String,ChannelConfig>)redisDao.readValue(CHANNELINFO);
		}
		
		if(null == channelMap || channelMap.get( channelAccount) == null){
			throw new NullPointerException("the channel info is not exist");
		}
		return channelMap.get(channelAccount);
	}
	
	/**
	 * return skilltype with given tenantcode and typecode
	 * @param tenantCode
	 * @param skillTypeCode
	 * @return
	 */
	public SkillType getSkillType(String tenantCode,String skillTypeCode){
		String key = tenantCode.concat(skillTypeCode);
		SkillType type = (SkillType)redisDao.mapGetValue(SKILL_TYPE_CODE, key);
		
		if(type == null){
			this.initSkillType();
			type = (SkillType)redisDao.mapGetValue(SKILL_TYPE_CODE, key);
		}
		
		if(type == null){
			throw new NullPointerException("the SkillType info is not exist");
		}
		
		return type;
	}
	
	/**
	 * return the skillqueue list
	 * 
	 * @return
	 */
	public List<SkillQueue> getSkillQueues(){
		List<SkillQueue> queues = (List<SkillQueue>)redisDao.readValue(QUEUELIST);
		
		return queues;
	}
	
	public SkillQueue skillQueue(TenantSkillQueue tenantSkillQueue){
		SysRule alloRule = null;
		SysRule sRule = null;

		// 初始化排序规则
		try {
			sRule = sqlExecute.getRuleById(String.valueOf(tenantSkillQueue.getSortRule()));
			sRule = sRule == null ? sqlExecute.getRuleByCode(DEFAULT_SORT_RULE) : sRule;
		} catch (Exception e) {
			sRule = sqlExecute.getRuleByCode(DEFAULT_SORT_RULE);
		}
		try {
			alloRule = sqlExecute.getRuleById(tenantSkillQueue.getAllocationRule());
			alloRule = alloRule == null ? sqlExecute.getRuleByCode(DEFAULT_ALLOCATE_RULE) : alloRule;
		} catch (Exception e) {
			alloRule = sqlExecute.getRuleByCode(DEFAULT_ALLOCATE_RULE);
		}

		int queueSize = null != tenantSkillQueue.getQueueSize() ? tenantSkillQueue.getQueueSize() : 
			Integer.parseInt(this.getSysParam(SYS_PARAM_MIR_DEFAULT_QUEUE_SIZE).getParamValue());
		
		int concurrentCount = null == tenantSkillQueue.getConcurrentCount() ? 1 : tenantSkillQueue.getConcurrentCount();

		SkillQueue queue = new SkillQueue(String.valueOf(tenantSkillQueue.getId()), tenantSkillQueue.getTenantId(),
				tenantSkillQueue,alloRule, sRule,queueSize,concurrentCount);


		return queue;
	}
	
	/**
	 * return the address push message to agent
	 * 
	 * @param tenantCode
	 * @param workNo
	 * @param infUrl
	 * @param clientType
	 * @return
	 */
	public String getServiceInvokerAddress(String tenantCode,String workNo,String infUrl){
		String ip = (String)redisDao.mapGetValue(Constants.AGENT_IPS, tenantCode.concat(workNo));
		Set<String> keys = redisDao.getKeysByPattern(Constants.TENANTCODE + tenantCode + Constants.SKILLQUEUE + "*" + Constants.AGENT + workNo);
		String clientType = null;
		for(String key : keys){
			clientType = (String)redisDao.mapGetValue(key, "clientType");break;
		}
		
		String siAddress = StringUtils.isEmpty(clientType) || "0".equals(clientType) 
				? Constants.SYS_PARAM_SERVICE_INVOKER_ADDRESS : Constants.SYS_PARAM_MOBILE_SI_ADDRESS ;
		if(!StringUtils.isEmpty(ip)){
			String agAddress = getSysParam(siAddress).getParamValue();
			int findex = agAddress.indexOf("//") + 2;
			int lindex = agAddress.indexOf("/",findex);
			
			return agAddress.substring(0,findex).concat(ip).concat(agAddress.substring(lindex)).concat(infUrl);
		}
 		
		return null;
	}
}
