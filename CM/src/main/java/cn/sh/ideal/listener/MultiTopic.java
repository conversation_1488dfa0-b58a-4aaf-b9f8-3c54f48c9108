package cn.sh.ideal.listener;

import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.Topic;
import org.springframework.util.Assert;

public class MultiTopic {
	public final static String TOPIC_TYPE_CHANNEL = "channel";
	public final static String TOPIC_TYPE_PATTERN = "pattern";
	
	private String multiName;
	private String type;//
	

	/**
	 * Constructs a new <code>MultiTopic</code> instance.
	 * 
	 * @param name
	 */
	public MultiTopic(String name) {
		Assert.notNull(name, "a valid topic is required");
		this.multiName = name;
		this.type = TOPIC_TYPE_CHANNEL;
	}
	
	public MultiTopic(String name,String type) {
		Assert.notNull(name, "a valid topic is required");
		Assert.notNull(name, "a valid topic type is required");
		this.multiName = name;
		this.type = type;
	}

	/**
	 * Returns the topic name.
	 * 
	 * @return topic name
	 */
	public String getMultiName() {
		return multiName;
	}
	
	public Topic getTopic(){
		if(TOPIC_TYPE_CHANNEL.equals(this.getType())) return new ChannelTopic(multiName);
		else if(TOPIC_TYPE_PATTERN.equals(this.getType())) return new PatternTopic(multiName);
		else throw new IllegalArgumentException("Unknown topic type '" + this.getType() + "'");
	}

	@Override
	public String toString() {
		return multiName;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
