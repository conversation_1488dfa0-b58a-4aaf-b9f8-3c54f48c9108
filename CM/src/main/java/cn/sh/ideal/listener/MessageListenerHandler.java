package cn.sh.ideal.listener;

import java.util.HashSet;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
/**
 * redis message listener handler
 * if you want to listen redis message publish
 * you need autowire this class
 * <AUTHOR>
 *
 */
@Component
public class MessageListenerHandler {
	@Autowired
	private RedisMessageListenerContainer listenerContainer;
	/**
	 * add listener by given channels
	 * @param listener
	 * @param channels
	 */
	public MessageListenerHandler addListenerByChannel(IMessageListener listener,String ... channels){
		Set<Topic> topics = new HashSet<Topic>();
		for(String channel : channels){
			if(StringUtils.isEmpty(channel)) continue;
			topics.add(new ChannelTopic(channel));
		}
		
		return addListener(listener,topics);
	}
	
	/**
	 * add listener by given patterns
	 * @param listener
	 * @param patterns
	 */
	public MessageListenerHandler addListenerByPattern(IMessageListener listener,String ... patterns){
		Set<Topic> topics = new HashSet<Topic>();
		for(String pattern : patterns){
			if(StringUtils.isEmpty(pattern)) continue;
			topics.add(new PatternTopic(pattern));
		}
		
		return addListener(listener,topics);
	}
	
	
	/**
	 * add listener by given multitopics
	 * @param listener
	 * @param mtopics
	 */
	public MessageListenerHandler addListenerByPattern(IMessageListener listener,Set<MultiTopic> mtopics){
		Set<Topic> topics = new HashSet<Topic>();
		for(MultiTopic mtopic : mtopics){
			topics.add(mtopic.getTopic());
		}
		
		return addListener(listener,topics);
	}
	
	/**
	 *  add listener by given topics
	 * @param listener
	 * @param topics
	 */
	public MessageListenerHandler addListener(IMessageListener listener,Set<Topic> topics){
		MessageListenerAdapter mla = new MessageListenerAdapter(listener);
		mla.setSerializer(new StringRedisSerializer());
		mla.afterPropertiesSet();
		listenerContainer.addMessageListener(mla, topics);
		return this;
	}
	
}
