package cn.sh.ideal.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日期时间工具类
 * <AUTHOR>
 *
 */
public class DateUtils {
	//默认日期格式
	public static final String DEFAULT_FORMAT = "yyyy-MM-dd";
	/**
	 * 日期字符串是否符合yyyy-mm-dd格式
	 * @param date
	 * @return
	 */
	public static boolean isIllege(String date){
		Pattern p=Pattern.compile("(\\d{4})-(\\d{1,2})-(\\d{1,2})");  
		Matcher m=p.matcher(date);  
		return m.matches();
	}
	
	public static void main(String[] args){
		String nowStr = DateUtils.date2Str(new Date(),"HH:mm:ss");
		System.out.println(nowStr);
	}
	
	/**
	 * 将日期字符串转换为日期
	 * @param str
	 */
	public static Date str2Date(String str,String format){
		Date date = null;
		format = format ==null ? DEFAULT_FORMAT : format;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			 date = sdf.parse(str);
			
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date;
	}
	
	public static Date str2Date(String str){
		return str2Date(str,null);
	}
	
	/**
	 * 日期转字符串
	 * @param date
	 * @param format 格式
	 * @return
	 */
	public static String date2Str(Date date,String format){
		String dateStr = "";
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		dateStr = sdf.format(date);
		return dateStr;
	}
	
	public static String date2Str(Date date){
		return date2Str(date,DEFAULT_FORMAT);
	}
	 /**
     * 获取当前日期是星期几<br>
     * 
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }
    
    public static int getWeek(Date dt) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
        return week == 0 ? 7 : week;
    }
	
	  /**  
     *  返回年份  
     *    
     *  @param  date  
     *                        日期  
     *  @return  返回年份  
     */  
   public  static  int  getYear(Date  date)  {  
               java.util.Calendar  c  =  java.util.Calendar.getInstance();  
               c.setTime(date);  
               return  c.get(java.util.Calendar.YEAR);  
   }  

   /**  
     *  返回月份  
     *    
     *  @param  date  
     *                        日期  
     *  @return  返回月份  
     */  
   public  static  int  getMonth(Date  date)  {  
               java.util.Calendar  c  =  java.util.Calendar.getInstance();  
               c.setTime(date);  
               return  c.get(java.util.Calendar.MONTH)  +  1;  
   }  

   /**  
     *  返回日份  
     *    
     *  @param  date  
     *                        日期  
     *  @return  返回日份  
     */  
   public  static  int  getDay(Date  date)  {  
               java.util.Calendar  c  =  java.util.Calendar.getInstance();  
               c.setTime(date);  
               return  c.get(java.util.Calendar.DAY_OF_MONTH);  
   }  
   
   public  static  int  getHour(Date  date)  {  
       java.util.Calendar  c  =  java.util.Calendar.getInstance();  
       c.setTime(date);  
       return  c.get(java.util.Calendar.HOUR_OF_DAY);  
}  
   
   /**
    * 获取某一时间前后间隔时间段的日期
    * @param date
    * @param field
    * @param mount
    * @return
    */
   public static Date getIntervalDate(Date date,int field,int mount){
	   java.util.Calendar  c  =  java.util.Calendar.getInstance();  
	   c.setTime(date); 
	   c.add(field, mount);
	   return c.getTime();
   }
   

   /**
    * 某一个月第一天和最后一天
    * @param date
    * @return
    */
   public static Map<String, String> getFirstday_Lastday_Month(Date date) {
       SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
       Calendar calendar = Calendar.getInstance();
       calendar.setTime(date);
       calendar.add(Calendar.MONTH, -1);
       Date theDate = calendar.getTime();
       
       //上个月第一天
       GregorianCalendar gcLast = (GregorianCalendar) Calendar.getInstance();
       gcLast.setTime(theDate);
       gcLast.set(Calendar.DAY_OF_MONTH, 1);
       String day_first = df.format(gcLast.getTime());
       StringBuffer str = new StringBuffer().append(day_first).append(" 00:00:00");
       day_first = str.toString();

       //上个月最后一天
       calendar.add(Calendar.MONTH, 1);    //加一个月
       calendar.set(Calendar.DATE, 1);        //设置为该月第一天
       calendar.add(Calendar.DATE, -1);    //再减一天即为上个月最后一天
       String day_last = df.format(calendar.getTime());
       StringBuffer endStr = new StringBuffer().append(day_last).append(" 23:59:59");
       day_last = endStr.toString();

       Map<String, String> map = new HashMap<String, String>();
       map.put("first", day_first);
       map.put("last", day_last);
       return map;
   }

   /**
    * 当月第一天
    * @return
    */
   public static String getFirstDayMonth(Date date) {
       SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
       
       GregorianCalendar gcLast = (GregorianCalendar) Calendar.getInstance();
       Calendar calendar = Calendar.getInstance();
       calendar.setTime(date);
       gcLast.setTime(date);
       gcLast.set(Calendar.DAY_OF_MONTH, 1);
       calendar.add(Calendar.MONTH, 1);
       String day_first = df.format(gcLast.getTime());
       StringBuffer str = new StringBuffer().append(day_first).append(" 00:00:00");
       return str.toString();

   }
   
   /**
    * 当月最后一天
    * @return
    */
   public static String getLastDayMonth(Date date) {
       SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
       GregorianCalendar calendar = (GregorianCalendar) Calendar.getInstance();
       calendar.setTime(date);
       calendar.add(Calendar.MONTH, 1);    //加一个月
       calendar.set(Calendar.DATE, 1);        //设置为该月第一天
       calendar.add(Calendar.DATE, -1);
       String s = df.format(calendar.getTime());
       StringBuffer str = new StringBuffer().append(s).append(" 23:59:59");
       return str.toString();

   }
   
   
   public static Date getWeekStart(Date date){
	   
	   Calendar currentDate = new GregorianCalendar();
	   currentDate.setTime(date);
	   currentDate.setFirstDayOfWeek(Calendar.MONDAY);  
	             
	   currentDate.set(Calendar.HOUR_OF_DAY, 0);  
	   currentDate.set(Calendar.MINUTE, 0);  
	   currentDate.set(Calendar.SECOND, 0);  
	   currentDate.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);  
	   
	   return (Date)currentDate.getTime().clone(); 
   }
   
   public static Date getWeekEnd(Date date){

	   Calendar currentDate = new GregorianCalendar();
	   currentDate.setTime(date);
	   currentDate.setFirstDayOfWeek(Calendar.MONDAY);  

	   currentDate.set(Calendar.HOUR_OF_DAY, 23);  
	   currentDate.set(Calendar.MINUTE, 59);  
	   currentDate.set(Calendar.SECOND, 59);  
	   currentDate.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);  

	   return (Date)currentDate.getTime().clone(); 
   }

   /**
    * 当月第一天(no 00:00:00)
    * @return
    */
   public static String getFirstDayMonthNoSB(Date date) {
       SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
       
       GregorianCalendar gcLast = (GregorianCalendar) Calendar.getInstance();
       Calendar calendar = Calendar.getInstance();
       calendar.setTime(date);
       gcLast.setTime(date);
       gcLast.set(Calendar.DAY_OF_MONTH, 1);
       calendar.add(Calendar.MONTH, 1);
       String day_first = df.format(gcLast.getTime());
       return day_first;

   }
}
