//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.sh.ideal.util;

import cn.sh.ideal.dao.RedisDao;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class RedisLock {
	public static final String LOCKED = "TRUE";
	public static final long MILLI_NANO_CONVERSION = 1000000L;
	public static final long DEFAULT_TIME_OUT = 1000L;
	public static final Random RANDOM = new Random();
	public static final int EXPIRE = 180;
	private String key;
	private RedisDao<String, String> redisDao;

	public RedisLock(String key) {
		this.key = "lock_" + key;
		this.redisDao = (RedisDao)SpringContextUtil.getBean("redisDao");
	}

	public boolean lock(long timeout) {
		long nano = System.nanoTime();
		timeout *= 1000000L;

		try {
			while(System.nanoTime() - nano < timeout) {
				if (this.redisDao.saveNxValue(this.key, "TRUE", 180, TimeUnit.SECONDS)) {
					return true;
				}

				Thread.sleep(3L, RANDOM.nextInt(500));
			}

			return false;
		} catch (Exception var6) {
			throw new RuntimeException("Locking error", var6);
		}
	}

	public boolean lock(long timeout, int expire) {
		long nano = System.nanoTime();
		timeout *= 1000000L;

		try {
			while(System.nanoTime() - nano < timeout) {
				if (this.redisDao.saveNxValue(this.key, "TRUE", expire, TimeUnit.SECONDS)) {
					return true;
				}

				Thread.sleep(3L, RANDOM.nextInt(500));
			}

			return false;
		} catch (Exception var7) {
			throw new RuntimeException("Locking error", var7);
		}
	}

	public boolean lock() {
		return this.lock(1000L);
	}

	public void unlock() {
		try {
			if (this.redisDao.readValue(this.key) != null) {
				this.redisDao.deleteValue(this.key);
			}

		} finally {
			;
		}
	}

	public static RedisLock getRedisLock(String lockKey) {
		return new RedisLock(lockKey);
	}

	public String getKey() {
		return this.key;
	}

	public void setKey(String key) {
		this.key = key;
	}
}
