package cn.sh.ideal.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.exception.MultiMediaException;

/**
 * ACTION基类
 * 
 * <AUTHOR>
 *
 */
public class BaseController {
	
	public final static String SUCCESS_CODE = "0";
	
	public final static String ERROR_CODE = "-1";
	
	public final static String SUCCESS_MSG = "success.";
	
	
	/*private AuthorityUtil authorityUtil = AuthorityUtil.getInstance();

    @ModelAttribute
	public void checkAuthority() throws Exception {
	           //验证License
	    authorityUtil.checkLicense();
	}
	*/
	/**
	 * 流->字符串
	 * @param in
	 * @return
	 * @throws IOException
	 */
	public String streamToString(InputStream in)
			throws IOException {
		Scanner scanner = new Scanner(in, "UTF-8");
		StringBuffer buffer = new StringBuffer();
		while(scanner.hasNextLine()) {
			buffer.append(scanner.nextLine());
		}
		scanner.close();
		return buffer.toString();
	}

	/**
	 * 返回成功JSON
	 * @return
	 */
	public JSONObject getSuccessJsonObj() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("resultCode", SUCCESS_CODE);
		jsonObject.put("resultMsg", SUCCESS_MSG);
		return jsonObject;
	}
	
	public String getSuccessResult() {
		return getSuccessJsonObj().toJSONString();
	}
	
	public JSONObject getErrorJsonObj(String errmsg) {
		return getErrorJsonObj(ERROR_CODE, errmsg);
	}
	
	/**
	 * 返回失败JSON
	 * @param code
	 * @param errmsg
	 * @return
	 */
	public JSONObject getErrorJsonObj(String code, String errmsg) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("resultCode", code);
		jsonObject.put("resultMsg", errmsg);
		return jsonObject;
	}
	
	public String getErrorResult(String code, String errmsg) {
		return getErrorJsonObj(code, errmsg).toJSONString();
	}
	
	public String getErrorResult(String errmsg) {
		return getErrorResult(ERROR_CODE, errmsg);
	}
	
	public Map<String,Object> getSuccMap(){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("resultCode", SUCCESS_CODE);
		map.put("result", SUCCESS_CODE);
		map.put("resultMsg", SUCCESS_MSG);
		
		return map;
	}
	public Map<String,Object> getSuccMap(Object data){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("resultCode", SUCCESS_CODE);
		map.put("result", SUCCESS_CODE);
		map.put("resultMsg", SUCCESS_MSG);
		map.put("data", data);
		
		return map;
	}
	
	public Map<String,Object> getErrorMap(String msg){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("resultCode", ERROR_CODE);
		map.put("result", ERROR_CODE);
		map.put("resultMsg", msg);
		
		return map;
	}
	
	public Map<String,Object> getErrorMap(String msg,Exception e){
		if(!(e instanceof MultiMediaException)){
			e = new MultiMediaException("0000");
		}
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("resultCode", ERROR_CODE);
		map.put("result", ERROR_CODE);
		
		
		map.put("resultMsg", msg.concat(e.getMessage()));
		
		return map;
	}
	
	/**
	 * 判断字符串是否是jsonObject
	 * 
	 * @param jsonString
	 * @return
	 */
	public static Object getJsonObj(String jsonString) {
		if (jsonString.startsWith("{")) {
			JSONObject jsonObject = JSON.parseObject(jsonString);
			return jsonObject;
		} else {
			return jsonString;
		}

	}
	
}