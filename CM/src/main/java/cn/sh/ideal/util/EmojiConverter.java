package cn.sh.ideal.util;


/**
 * emoji编码转换器，解决数据库存储失败问题
 * Created by Nemo on 2015/7/21.
 */
public class EmojiConverter {
   // private static Log log = LogFactory.getLog(EmojiConverter.class);
    /***
     * 将包含emoji表情的字符串转换为Unicode编码。如：字符串+emoji Unicode+字符串122A
     * @param str 待转换的字符串
     * @return 字符串+emoji Unicode+字符串
     */
    public static String encode(String str){
       String unicodeStr =  UnicodeConverter.toEncodedUnicode(str,false);
        //将emoji表情的Unicode编码变成双斜杠开头，$表示自身 $0表示group0
        unicodeStr = unicodeStr.replaceAll("\\\\uD[0-9A-F]{3}|\\\\uFE0F|\\\\u2[0135679B]{1}[0-9A-F]{2}","\\\\$0");
       //通过fromEncodedUnicode方法将unicodeStr转成中文+Unicode编码的字符串
        return UnicodeConverter.fromEncodedUnicode(unicodeStr.toCharArray(), 0, unicodeStr.toCharArray().length);
    }

    /***
     * 将中文+Unicode编码的字符串转换为存中文的字符串
     * @param str 转换后的字符串
     * @return 原文字符串
     */
    public static String decode(String str){
        return UnicodeConverter.fromEncodedUnicode(str.toCharArray(), 0, str.toCharArray().length);
    }

    public static void main(String [] args){
        String str = "\\uD83D\\uDE04\\u4E2D\\u6587\\uD83D\\uDE04\\u4E2D\\u6587\\uD83D\\uDE04\\uD83D\\uDE04\\uD83D\\uDE04122A\\u263A\\uFE0F\\u26EA\\uFE0F\\u26FA\\uFE0F\\u26F2\\uFE0F\\u26F5\\uFE0F\\u2693\\uFE0F\\u2708\\uFE0F\\u20E3\\u2B06\\uFE0F\\u2B07\\uFE0F\\u2B05\\uFE0F\\u27A1\\uFE0F\\u2197\\uFE0F\\u2196\\uFE0F\\u2198\\uFE0F\\u2199\\uFE0F\\u2194\\uFE0F\\u2195\\uFE0F\\u25C0\\uFE0F\\u25B6\\uFE0F\\u21A9\\uFE0F\\u21AA\\uFE0F\\u2139\\uFE0F\\u23EA\\u23E9\\u23EB\\u23EC\\u2935\\uFE0F";
        str = UnicodeConverter.fromEncodedUnicode(str.toCharArray(), 0, str.toCharArray().length);
        System.out.println("转码前的emoji表情："+str);
        long start =System.currentTimeMillis();
        String emojiStr = EmojiConverter.encode(str);
        long end =System.currentTimeMillis();
        System.out.println("转码后的emoji表情："+emojiStr+" 耗时："+(end-start));
        start =System.currentTimeMillis();
        emojiStr = EmojiConverter.decode(emojiStr);
        end =System.currentTimeMillis();
        System.out.println("恢复后的emoji表情："+emojiStr+" 耗时："+(end-start));

       // System.out.println("恢复后的emoji表情："+EmojiConverter.decode("12312321\\"));
    }



}
