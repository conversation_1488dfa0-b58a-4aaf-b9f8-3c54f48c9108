package cn.sh.ideal.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.TimeZone;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.log4j.Logger;

/**
 * 校长FTP工具类、依赖jar:apache.FTPClient.jar
 * 提供简单的上传、下载功能
 * <AUTHOR>
 * @version 1.0 2013-10-26
 */
public class MasterFtp {

	private FTPClient client=new FTPClient();
	private static final Logger log = Logger.getLogger(MasterFtp.class);
	
	private String address;
	public FTPClient getClient() {
		return client;
	}

	public void setClient(FTPClient client) {
		this.client = client;
	}

	private int port;
	private String username;
	private String password;
	
	public MasterFtp() {
		super();
	}

	/**
	 * @param address
	 * @param port
	 * @param username
	 * @param password
	 */
	public MasterFtp(String address, int port, String username, String password) {
		super();
		this.address = address;
		this.port = port;
		this.username = username;
		this.password = password;
	}


	public boolean login(){
		boolean isLogin = false;
		FTPClientConfig ftpClientConfig = new FTPClientConfig();
		ftpClientConfig.setServerTimeZoneId(TimeZone.getDefault().getID());
		this.client.setControlEncoding("GBK");
		this.client.configure(ftpClientConfig);
		try {
			if (this.port > 0) {
				this.client.connect(this.address, this.port);
			} else {
				this.client.connect(this.address);
			}
			// FTP服务器连接回答
			int reply = this.client.getReplyCode();
			if (!FTPReply.isPositiveCompletion(reply)) {
				this.client.disconnect();
				return isLogin;
			}
			isLogin=this.client.login(this.username, this.password);
			log.info("ftp login result:" + isLogin);
			// 设置传输协议
			this.client.enterLocalPassiveMode();
			this.client.setFileType(FTPClient.BINARY_FILE_TYPE);
		} catch (Exception e) {
			log.error(this.username + "登录FTP服务失败！" + e.getMessage());
		}
		this.client.setBufferSize(1024 * 2);
		this.client.setDataTimeout(5 * 1000);
		return isLogin;
	}
	
	public void logOut() {
		if (null != this.client && this.client.isConnected()) {
			try {
				boolean reuslt = this.client.logout();// 退出FTP服务器
				if (reuslt) {
					log.info("成功退出服务器");
				}
			} catch (IOException e) {
//				e.printStackTrace();
				log.error("退出FTP服务器异常！" + e.getMessage());
			} finally {
				try {
					if(this.client != null && this.client.isConnected())
						this.client.disconnect();// 关闭FTP服务器的连接
				} catch (IOException e) {
//					e.printStackTrace();
					log.error("关闭FTP服务器的连接异常！");
				}
			}
		}
	}
	
	/***
	 * 上传Ftp文件
	 * @param localFile 当地文件
	 * @param romotUpLoadePath上传服务器路径 - 应该以/结束
	 * */
	public String uploadFile(InputStream localFile, String rootPath,String uploadDir,String name) {
		BufferedInputStream inStream = null;
		String romotUpRootPath = rootPath + "/" + uploadDir;
		boolean success = false;
		try {
			boolean isLive=this.client.changeWorkingDirectory(romotUpRootPath);
			if(!isLive){
				this.client.makeDirectory(romotUpRootPath);
				this.client.changeWorkingDirectory(romotUpRootPath);
			}
			
			inStream = new BufferedInputStream(localFile);
			log.info(name + "开始上传.....");
			success = this.client.storeFile(name, localFile);
			if (success == true) {
				log.info(name + "上传成功");
				return uploadDir+"/"+name;
			}else{
				log.info(name + "上传失败");
				return null;
			}
		} catch (FileNotFoundException e) {
//			e.printStackTrace();
			log.error(localFile + "未找到" + e.getMessage());
		} catch (IOException e) {
//			e.printStackTrace();
			log.error(e.getMessage());
		} finally {
			if (inStream != null) {
				try {
					inStream.close();
				} catch (IOException e) {
//					e.printStackTrace();
					log.error(e.getMessage());
				}
			}
		}
		return null;
	}
	

	/***
	 * 下载文件
	 * @param remoteFileName   待下载文件名称
	 * @param localDires 下载到当地那个路径下
	 * @param remoteDownLoadPath remoteFileName所在的路径
	 * */

	public boolean downloadFile(String remoteFileName, String localDires,
			String remoteDownLoadPath) {
		String strFilePath = localDires + remoteFileName;
		BufferedOutputStream outStream = null;
		boolean success = false;
		try {
			this.client.changeWorkingDirectory(remoteDownLoadPath);
			outStream = new BufferedOutputStream(new FileOutputStream(
					strFilePath));
			log.info(remoteFileName + "开始下载....");
			success = this.client.retrieveFile(remoteFileName, outStream);
			if (success == true) {
				log.info(remoteFileName + "成功下载到" + strFilePath);
				return success;
			}
		} catch (Exception e) {
//			e.printStackTrace();
			log.info(remoteFileName + "下载失败" + e.getMessage());
		} finally {
			if (null != outStream) {
				try {
					outStream.flush();
					outStream.close();
				} catch (IOException e) {
//					e.printStackTrace();
					log.error(e.getMessage());
				}
			}
		}
		if (success == false) {
			log.error(remoteFileName + "下载失败!!!");
		}
		return success;
	}

	//public boolean syncData(File localFile, String romotUpLoadePath){}
	
	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	
}
