package cn.sh.ideal.util;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Random;
import java.util.Scanner;

public class Util {

    public static String filterNull(String str) {
        if (str == null || str.equals("null")) {
            str = "";
        }
        return str;
    }
    public static String streamToString(InputStream in){
		Scanner scanner = new Scanner(in, "UTF-8");
		StringBuffer buffer = new StringBuffer();
		while(scanner.hasNextLine()) {
			buffer.append(scanner.nextLine());
		}
		scanner.close();
		return buffer.toString();
	}
    
    public static Long dateDiff(String startTime, String endTime,   
            String format, String str) {   
        // 按照传入的格式生成一个simpledateformate对象   
        SimpleDateFormat sd = new SimpleDateFormat(format);   
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数   
        long nh = 1000 * 60 * 60;// 一小时的毫秒数   
        long nm = 1000 * 60;// 一分钟的毫秒数   
        long ns = 1000;// 一秒钟的毫秒数   
        long diff;   
        long day = 0;   
        long hour = 0;   
        long min = 0;   
        long sec = 0;   
        // 获得两个时间的毫秒时间差异   
        try {   
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();   
            day = diff / nd;// 计算差多少天   
            hour = diff / nh + day * 24;// 计算差多少小时   
            min = diff / nm + day * 24 * 60;// 计算差多少分钟   
            sec = diff / ns;// 计算差多少秒   
            // 输出结果   
//            System.out.println("时间相差：" + day + "天" + (hour - day * 24) + "小时"  
//                    + (min - day * 24 * 60) + "分钟" + sec + "秒。");   
//            System.out.println("hour=" + hour + ",min=" + min);   
            if (str.equalsIgnoreCase("h")) {   
                return hour;   
            } else if (str.equalsIgnoreCase("m"))  {   
                return min;   
            }else{
            	return sec;
            }
        } catch (Exception e) {   
            e.printStackTrace();   
        }   
        if (str.equalsIgnoreCase("h")) {   
            return hour;   
        } else {   
            return min;   
        }   
    }

    
    public static String filterForObject(Object Obj) {
        String str = "";
        if (Obj == null || Obj.toString().trim().equalsIgnoreCase("null")) {
          str = "";
        }
        else {
          str = Obj.toString();
        }

        return str.trim();
      }

    public static int getFirstIndex(String[] arr, String str) {
        int index = -1;
        for (int i = 0; i < arr.length; i++) {
            if(str.equals(arr[i])) {
                index = i;
                break;
            }
        }
        return index;
    }

    public static ArrayList convertStrToArray(String[] arr) {
        ArrayList arrayList = new ArrayList();
        for (int i = 0; i < arr.length; i++) {
        	arrayList.add(arr[i]);
        }
        return arrayList;
    }
    
    public static String parseJSONStr(String str){
    	return str.replace("\\", "\\\\").replace("\"", "\\\"").replace("\r", "\\r").replace("\n", "\\n");
    }
	
	/**
	 * byte数组转换成16进制字符串
	 * @param src
	 * @return
	 */
	public static String bytesToHexString(byte[] src) {
		StringBuilder stringBuilder = new StringBuilder();
		if (src == null || src.length <= 0) {
			return null;
		}
		for (int i = 0; i < src.length; i++) {
			int v = src[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}

	/**
	 * 获取十六进制的颜色代码.例如 "#6E36B4" , For HTML ,
	 * 
	 * @return String
	 */
	public static String getRandColorCode() {
		String r, g, b;
		Random random = new Random();
		r = Integer.toHexString(random.nextInt(256)).toUpperCase();
		g = Integer.toHexString(random.nextInt(256)).toUpperCase();
		b = Integer.toHexString(random.nextInt(256)).toUpperCase();

		r = r.length() == 1 ? "0" + r : r;
		g = g.length() == 1 ? "0" + g : g;
		b = b.length() == 1 ? "0" + b : b;

		return "#"+r + g + b;
	}

	
	/**
	 * 随机获取字符串
	 * @param length length表示生成字符串的长度
	 * @return
	 */
	public static String getRandomString(int length) { 
	    String base = "abcdefghijklmnopqrstuvwxyz0123456789";   
	    Random random = new Random();   
	    StringBuffer sb = new StringBuffer();   
	    for (int i = 0; i < length; i++) {   
	        int number = random.nextInt(base.length());   
	        sb.append(base.charAt(number));   
	    }   
	    return sb.toString();   
	 }  
	
	public static String unescape(String src) {
		StringBuffer tmp = new StringBuffer();
		tmp.ensureCapacity(src.length());
		int lastPos = 0, pos = 0;
		char ch;
		while (lastPos < src.length()) {
			pos = src.indexOf("%", lastPos);
			if (pos == lastPos) {
				if (src.charAt(pos + 1) == 'u') {
					ch = (char) Integer.parseInt(src.substring(pos + 2, pos + 6), 16);
					tmp.append(ch);
					lastPos = pos + 6;
				} else {
					ch = (char) Integer.parseInt(src.substring(pos + 1, pos + 3), 16);
					tmp.append(ch);
					lastPos = pos + 3;
				}
			} else {
				if (pos == -1) {
					tmp.append(src.substring(lastPos));
					lastPos = src.length();
				} else {
					tmp.append(src.substring(lastPos, pos));
					lastPos = pos;
				}
			}
		}
		return tmp.toString();
	}
}
