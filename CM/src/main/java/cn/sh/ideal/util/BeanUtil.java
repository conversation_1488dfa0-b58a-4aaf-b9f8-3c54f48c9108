package cn.sh.ideal.util;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class BeanUtil {

    public static Object mapToObject(Map<Object, Object> map, Class<?> beanClass) throws Exception {    
        if (map == null)   
            return null;    
   
        Object obj = beanClass.newInstance();  
   
        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());    
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();    
        for (PropertyDescriptor property : propertyDescriptors) {  
            Method setter = property.getWriteMethod();    
            if (setter != null) {  
                setter.invoke(obj, map.get(property.getName()));   
            }  
        }  
   
        return obj;  
    }    
       
    public static Map<Object, Object> objectToMap(Object obj) throws Exception {    
        if(obj == null)  
            return null;      
   
        Map<Object, Object> map = new HashMap<Object, Object>();   
   
        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());    
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();    
        for (PropertyDescriptor property : propertyDescriptors) {    
            String key = property.getName();    
            if (key.compareToIgnoreCase("class") == 0) {   
                continue;  
            }  
            Method getter = property.getReadMethod();  
            Object value = getter!=null ? getter.invoke(obj) : null;  
            map.put(key, value);  
        }    
   
        return map;  
    }    
    
    
    public static void main(String[] args) throws Exception {
		Map<Object,Object> map = new HashMap<Object,Object>();
		map.put("currSessionCount", 1);
		map.put("maxSessionCount", 1);
		map.put("occupancy", 1);
		int i = 1;
		while(true){
			//AgentInfo oinfo = (AgentInfo)BeanUtil.mapToObject( map, AgentInfo.class);
			System.out.println(i++);
		}
	}
}
