package cn.sh.ideal.util;

import java.util.Map;

import cn.sh.ideal.model.SysParam;

/**
 * url格式化
 * 由于服务器IP都在数据库配置,所以该工具类用来将IP注入到URL中
 * 
 * <AUTHOR>
 *
 */
public class UrlFormat {

	
	public static String format(String url, Map<String, SysParam> args) {
		
		if(url == null || url.length() == 0)
			return url;
		
		if(args == null || args.size() == 0)
			return url;
		
		int begin = url.indexOf("{");
		
		if(begin == -1)
			return url;
		
		int end = url.indexOf("}");
		
		if(end == -1 || end < begin)
			return url;
		
		String key = url.substring(begin + 1, end);
		SysParam value = args.get(key);
		
		if(value != null) {
			StringBuilder builder = new StringBuilder();
			builder.append(url.substring(0, begin));
			builder.append(value.getParamValue());
			if(url.length() > (end + 1))
				builder.append(url.substring(end + 1));
			url = builder.toString();
		}
			
		return url;
	}
	
	public static void main(String[] args) {
		/*String url = "ff{HELLO}ff";
		Map<String, String> map = new HashMap<String, String>();
		map.put("HELLO", "http://************:8082/MIR");
		
		System.out.println(UrlFormat.format(url, map));*/
		
	}
}
