package cn.sh.ideal.util;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import com.alibaba.dubbo.common.utils.ConfigUtils;
import com.alibaba.dubbo.config.ReferenceConfig;

/**
 * dubbo reference util
 * <AUTHOR>
 *
 */
public class ReferenceConfigUtil{
	@SuppressWarnings("rawtypes")
	private static Map<String,ReferenceConfig> configs = new HashMap<String,ReferenceConfig>();
	
	
	public static void setProperties(Properties p){
		ConfigUtils.setProperties(p);
	}
	
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private static ReferenceConfig addConfig(String version,String interfaceUrl,Class interfaceClass){
		ReferenceConfig config = null;
		synchronized (ReferenceConfigUtil.class) {
			config = new ReferenceConfig();
			config.setUrl(interfaceUrl);
			config.setInterface(interfaceClass);
			config.setVersion(version);
			config.setTimeout(10000);
			configs.put(getKey(version, interfaceUrl, interfaceClass) , config);
		}
		return config;
	}
	
	@SuppressWarnings("rawtypes")
	public static ReferenceConfig getConfig(String version,String interfaceUrl,Class interfaceClass){
		if(ConfigUtils.getProperties() == null){
			throw new RuntimeException("must call setProperties() to pre-load dubbo config");
		}
		ReferenceConfig config = configs.get(getKey(version, interfaceUrl, interfaceClass));
		if(null == config){
			config = addConfig(version,interfaceUrl, interfaceClass);
		}
		
		return config;
	}
	
	@SuppressWarnings("rawtypes")
	private static String getKey( String version,String interfaceUrl,Class interfaceClass) {
		return interfaceClass.getName() + "_v" + version + interfaceUrl;
	}
}
