package cn.sh.ideal.util;

import java.awt.image.BufferedImage;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

public class QRFactory {
	
	private static int width=400;
	private static int height=400;
	
	@SuppressWarnings("finally")
	public static BufferedImage create(String content){
		BufferedImage bi=null;
		try {
		     MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
		     BitMatrix bitMatrix = multiFormatWriter.encode(new String(content.getBytes("UTF-8"),"iso-8859-1"), BarcodeFormat.QR_CODE, width, height);
		     bi=MatrixToImageWriter.toBufferedImage(bitMatrix);
		 } catch (Exception e) {
		     e.printStackTrace();
		 }finally{
			 return bi;
		 }
	}
	
	@SuppressWarnings("finally")
	public static BufferedImage create(String content,int width,int height){
		BufferedImage bi=null;
		try {
		     MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
		     BitMatrix bitMatrix = multiFormatWriter.encode(new String(content.getBytes("UTF-8"),"iso-8859-1"), BarcodeFormat.QR_CODE, width, height);
		     bi=MatrixToImageWriter.toBufferedImage(bitMatrix);
		 } catch (Exception e) {
		     e.printStackTrace();
		 }finally{
			 return bi;
		 }
	}
	
	
}
