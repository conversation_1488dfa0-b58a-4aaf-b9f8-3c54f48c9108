package cn.sh.ideal.util;

public enum TipTypes {
	DEFAULT("0"),
	WELCOME("1"),//欢迎
	SORT("2"),//排队
	SORTING("3"),//排队中
	CONNECTING("4"),//进入人工
	AGTIMEOUT("5"),//坐席超时
	AGTIMEOUTCLOSE("6"),//坐席超时关闭
	USERCLOSE("7"),//用户关闭会话
	AGCLOSE("8"),//坐席关闭会话
	USERSENDSHARP("9"),//用户发送#
	USERTIMEOUT("10"),//用户超时
	USERTIMEOUTCLOSE("11");//用户超时关闭
	private String code;
	private TipTypes(String code){
        this.code = code;
    }
	
	public String toString() {
        return code;
    }
	
	public String getCode() {
		return code;
	}
}
