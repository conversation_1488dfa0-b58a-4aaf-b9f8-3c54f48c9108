<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>multi_media</groupId>
    <artifactId>multi_media</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>MIR</module>
        <module>MIR_API</module>
        <module>SI_API</module>
        <module>IMR</module>
        <module>IMR_API</module>
		<module>MGW</module>
		<module>MGW_API</module>
		<module>Transcoding</module>
		<module>Transcoding_API</module>
		<module>SM</module>
		<module>SM_API</module>
		<module>CM</module>
    </modules>

    <packaging>pom</packaging>

    <properties>
        <java.version>1.7</java.version>
        <dubbo.version>2.5.3</dubbo.version>
        <netty.version>3.2.10.Final</netty.version>
        <javassist.version>3.20.0-GA</javassist.version>
        <spring.version>4.2.7.RELEASE</spring.version>
        <spring-remoting.version>2.0.8</spring-remoting.version>
        <lombok.version>0.11.4</lombok.version>
        <guava.version>16.0</guava.version>
        <zk.version>0.3</zk.version>
        <junit.version>4.11</junit.version>
        <gson.version>2.4</gson.version>
        <commons-lang.version>2.6</commons-lang.version>
        <javax-servlet.version>3.1.0</javax-servlet.version>
        <javax-jsp-api.verson>2.0</javax-jsp-api.verson>
        <taglibs.version>1.1.2</taglibs.version>
        <jstl.version>1.1.2</jstl.version>
        <cglib.version>2.2</cglib.version>
        <aspectjweaver.version>1.6.9</aspectjweaver.version>
        <jsr.version>2.0.1</jsr.version>
        <druid.version>1.0.17</druid.version>
        <log4j.version>1.2.17</log4j.version>
        <slf4j.version>1.7.7</slf4j.version>
        <spring-mybatis.version>1.2.2</spring-mybatis.version>
        <mybatis.version>3.2.7</mybatis.version>
        <mysql-connector-java.version>5.1.29</mysql-connector-java.version>
        <spring-data-redis.version>1.8.2</spring-data-redis.version>
        <spring-data-commons.version>1.9.4.RELEASE</spring-data-commons.version>
        <jedis.version>2.9.1</jedis.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <commons-beanutils.version>1.9.1</commons-beanutils.version>
        <commons-pool2.version>2.4.2</commons-pool2.version>
        <fastjson.version>1.2.28</fastjson.version>
        <nutz.version>1.b.50</nutz.version>
        <kafka.version>0.10.2.1</kafka.version>

        <javaee-web-api.version>7.0</javaee-web-api.version>
        <websocket-api.version>1.2.5.RELEASE</websocket-api.version>
        <encryption-common.version>1.0.0-RELEASE</encryption-common.version>
		<zXing.version>1.0</zXing.version>
		<commons-codec.version>1.9</commons-codec.version>
		<commons-fileupload.version>1.2.1</commons-fileupload.version>
		<commons-net.version>3.0.1</commons-net.version>
		<commons-io.version>1.1</commons-io.version>
		<xmlbeans.version>2.6.0</xmlbeans.version>
		<poi.version>3.12</poi.version>
        <alipay-sdk.version>1.4</alipay-sdk.version>
		<CM.version>1.0-SNAPSHOT</CM.version>
		<SI_API.version>1.0-SNAPSHOT</SI_API.version>
        <MGW_API.version>1.0-SNAPSHOT</MGW_API.version>
        <MIR_API.version>1.0-SNAPSHOT</MIR_API.version>
        <IMR_API.version>1.0-SNAPSHOT</IMR_API.version>
        <SM_API.version>1.0-SNAPSHOT</SM_API.version>

        <!--plugins-version-->
        <maven-compiler-plugin.version>3.1</maven-compiler-plugin.version>
        <maven-source-plugin.version>2.2.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>2.9.1</maven-javadoc-plugin.version>
        <maven-deploy-plugin.version>2.7</maven-deploy-plugin.version>
        <maven-resources-plugin.versin>2.6</maven-resources-plugin.versin>
        <maven-shade-plugin.version>2.1</maven-shade-plugin.version>
    </properties>


    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo1.maven.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-net-cn</id>
            <name>Maven China Mirror</name>
            <url>https://nexus.immortalbo.top:20443/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>maven-net-cn</id>
            <name>Maven China Mirror</name>
            <url>https://nexus.immortalbo.top:20443/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>


    <dependencies>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-remoting</artifactId>
            <version>${spring-remoting.version}</version>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <version>${cglib.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectjweaver.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>${jsr.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>${javassist.version}</version>
		</dependency>
		<dependency>
			<groupId>org.jboss.netty</groupId>
			<artifactId>netty</artifactId>
			<version>${netty.version}</version>
		</dependency>

        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
            <version>${zk.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
            <version>${jstl.version}</version>
        </dependency>
        <dependency>
            <groupId>taglibs</groupId>
            <artifactId>standard</artifactId>
            <version>${taglibs.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <version>${javax-jsp-api.verson}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${javax-servlet.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <!--log-->
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
			<version>${spring-data-redis.version}</version>
		</dependency>
		 <dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-commons</artifactId>
			<version>${spring-data-commons.version}</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>${jedis.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>${commons-pool2.version}</version>
		</dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${spring-mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>
            <dependency>
                <groupId>oracle.jdbc</groupId>
                <artifactId>ojdbc</artifactId>
                <version>6</version>
            </dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons-lang3.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons-beanutils.version}</version>
			</dependency>
			<dependency>
				<groupId>org.nutz</groupId>
				<artifactId>nutz</artifactId>
				<version>${nutz.version}</version>
			</dependency>
			<dependency>
				<groupId>javax</groupId>
				<artifactId>javaee-web-api</artifactId>
				<version>${javaee-web-api.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>cn.sh.ideal</groupId>
				<artifactId>websocket-api</artifactId>
				<version>${websocket-api.version}</version>
			</dependency>
			<dependency>
			  <groupId>cn.sh.ideal</groupId>
			  <artifactId>encryption-common</artifactId>
			  <version>${encryption-common.version}</version>
			</dependency>
			<dependency>
			  <groupId>com.google.zXing</groupId>
			  <artifactId>zXing</artifactId>
			  <version>${zXing.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>${commons-codec.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-net</groupId>
				<artifactId>commons-net</artifactId>
				<version>${commons-net.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons-io.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml-schemas</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
			    <groupId>org.apache.xmlbeans</groupId>
			    <artifactId>xmlbeans</artifactId>
			    <version>${xmlbeans.version}</version>
			</dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>alipay-sdk</artifactId>
                <version>${alipay-sdk.version}</version>
            </dependency>

			<dependency>
	            <groupId>multi_media</groupId>
	            <artifactId>CM</artifactId>
	            <version>${CM.version}</version>
	        </dependency>
			<dependency>
	            <groupId>multi_media</groupId>
	            <artifactId>SI_API</artifactId>
	            <version>${SI_API.version}</version>
	        </dependency>
            <dependency>
                <groupId>multi_media</groupId>
                <artifactId>MGW_API</artifactId>
                <version>${MGW_API.version}</version>
            </dependency>
            <dependency>
                <groupId>multi_media</groupId>
                <artifactId>MIR_API</artifactId>
                <version>${MIR_API.version}</version>
            </dependency>
            <dependency>
                <groupId>multi_media</groupId>
                <artifactId>IMR_API</artifactId>
                <version>${IMR_API.version}</version>
            </dependency>
            <dependency>
                <groupId>multi_media</groupId>
                <artifactId>SM_API</artifactId>
                <version>${SM_API.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.versin}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <!-- explicitly define maven-deploy-plugin after other to force exec
                    order -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <executions>
                    <execution>
                        <id>deploy</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>deploy</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
