 ### set log levels ###
#log4j.rootLogger = info , stdout , D , E
log4j.rootLogger = info , stdout , D,

###  output to the console ###
log4j.appender.stdout = org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target = System.out
log4j.appender.stdout.layout = org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss} [%c]-[%p] %m%n

### Output to the log file ###
log4j.appender.D = org.apache.log4j.DailyRollingFileAppender
log4j.appender.D.File = ../logs/IMR/error.log
log4j.appender.D.Threshold = INFO 
log4j.appender.D.layout = org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss} [ %t:%r ] - [ %p ] %m%n
log4j.appender.D.DatePattern='.'yyyy-MM-dd_HH'.log'  

#mybatis
#log4j.logger.org.apache.ibatis=debug
log4j.logger.org.mybatis=info
#log4j.logger.java.sql.ResultSet=DEBUG
#log4j.logger.java.sql.Connection=DEBUG
#log4j.logger.java.sql.Statement=DEBUG
#log4j.logger.java.sql.PreparedStatement=DEBUG

#
log4j.logger.cn.sh.ideal=info
log4j.logger.org.springframework=INFO