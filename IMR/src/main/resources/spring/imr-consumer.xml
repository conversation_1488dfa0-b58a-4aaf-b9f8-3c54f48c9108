<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	
	
	<dubbo:registry address="zookeeper://${zookeeper.address}" />
	
    <!-- 所有消费服务的默认超时设置 -->
    <dubbo:consumer timeout="${dubbo.consumer.timeout}" check="false">
        <!-- 停止服务超时设置，单位毫秒 -->
        <dubbo:parameter  key="shutdown.timeout" value="${dubbo.shutdown.timeout}" />
    </dubbo:consumer>
    
    <dubbo:reference id="dubboSessionService" interface="cn.sh.ideal.sm.service.SessionService" version="1.0.0" retries="0"/>
	<dubbo:reference id="dubboMessageService" interface="cn.sh.ideal.mir.service.MessageService" version="1.0.0" retries="0"/>
	
</beans>