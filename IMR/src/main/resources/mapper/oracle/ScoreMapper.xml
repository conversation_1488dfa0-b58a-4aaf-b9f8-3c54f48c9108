<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.ScoreDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.Score" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
    <result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
    <result column="SATIFIED" property="satified" jdbcType="DECIMAL" />
    <result column="GETSCORETIME" property="getscoretime" jdbcType="VARCHAR" />
    <result column="GETREQUESTTIME" property="getrequesttime" jdbcType="VARCHAR" />
    <result column="SUGGEST" property="suggest" jdbcType="VARCHAR" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    ID, SESSION_ID, WORK_NO, SATIFIED, GETSCORETIME, GETREQUESTTIME, SUGGEST,TENANT_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_SCORE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    delete from IMR_SCORE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.Score" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
  <!--   <selectKey keyProperty="id" resultType="int" order="BEFORE">
			select SEQ_IMR_SCORE.NEXTVAL FROM DUAL
		</selectKey> -->
    insert into IMR_SCORE (ID, SESSION_ID, WORK_NO, 
      SATIFIED, GETSCORETIME, GETREQUESTTIME, 
      SUGGEST,TENANT_CODE,CUSTOMER_ID)
    values (SEQ_IMR_SCORE.NEXTVAL, #{sessionId,jdbcType=VARCHAR}, #{workNo,jdbcType=VARCHAR}, 
      #{satified,jdbcType=DECIMAL}, #{getscoretime,jdbcType=VARCHAR}, #{getrequesttime,jdbcType=VARCHAR}, 
      #{suggest,jdbcType=VARCHAR},#{tenantCode,jdbcType=VARCHAR},#{customerId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.Score" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    insert into IMR_SCORE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="sessionId != null" >
        SESSION_ID,
      </if>
      <if test="workNo != null" >
        WORK_NO,
      </if>
      <if test="satified != null" >
        SATIFIED,
      </if>
      <if test="getscoretime != null" >
        GETSCORETIME,
      </if>
      <if test="getrequesttime != null" >
        GETREQUESTTIME,
      </if>
      <if test="suggest != null" >
        SUGGEST,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="sessionId != null" >
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="workNo != null" >
        #{workNo,jdbcType=VARCHAR},
      </if>
      <if test="satified != null" >
        #{satified,jdbcType=DECIMAL},
      </if>
      <if test="getscoretime != null" >
        #{getscoretime,jdbcType=VARCHAR},
      </if>
      <if test="getrequesttime != null" >
        #{getrequesttime,jdbcType=VARCHAR},
      </if>
      <if test="suggest != null" >
        #{suggest,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.Score" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    update IMR_SCORE
    <set >
      <if test="sessionId != null" >
        SESSION_ID = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="workNo != null" >
        WORK_NO = #{workNo,jdbcType=VARCHAR},
      </if>
      <if test="satified != null" >
        SATIFIED = #{satified,jdbcType=DECIMAL},
      </if>
      <if test="getscoretime != null" >
        GETSCORETIME = #{getscoretime,jdbcType=VARCHAR},
      </if>
      <if test="getrequesttime != null" >
        GETREQUESTTIME = #{getrequesttime,jdbcType=VARCHAR},
      </if>
      <if test="suggest != null" >
        SUGGEST = #{suggest,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.Score" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    update IMR_SCORE
    set SESSION_ID = #{sessionId,jdbcType=VARCHAR},
      WORK_NO = #{workNo,jdbcType=VARCHAR},
      SATIFIED = #{satified,jdbcType=DECIMAL},
      GETSCORETIME = #{getscoretime,jdbcType=VARCHAR},
      GETREQUESTTIME = #{getrequesttime,jdbcType=VARCHAR},
      SUGGEST = #{suggest,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <insert id="scoreFlow_insert" parameterType="cn.sh.ideal.imr.model.Score">
		<selectKey keyProperty="id" resultType="int">
			select SEQ_IMR_SCORE.NEXTVAL FROM DUAL
		</selectKey>
		insert into IMR_SCORE(ID,SESSION_ID,WORK_NO,SATIFIED,GETSCORETIME,GETREQUESTTIME,SUGGEST)
		values (#{id,jdbcType = DECIMAL},#sessionId:VARCHAR#,#workNo:VARCHAR#,#satisfied:DECIMAL#,#getScoreTime:VARCHAR#,#getRequestTime:VARCHAR#,#suggest:VARCHAR#)
	</insert>
	
	<select id="scoreFlow_select" parameterType="String" resultMap="BaseResultMap" >
		select ID,SESSION_ID,WORK_NO,SATIFIED,GETSCORETIME,GETREQUESTTIME,SUGGEST from IMR_SCORE where  SESSION_ID = #{sessionId,jdbcType=VARCHAR} and rownum=1
	</select>
	
	<select id="select" resultMap="BaseResultMap" parameterType="string" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:31:40 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_SCORE where SESSION_ID = #{sessionId,jdbcType=VARCHAR}
      
  </select>
</mapper>