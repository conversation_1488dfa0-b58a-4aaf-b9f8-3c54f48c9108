<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.NewTaskDao" >
  <resultMap id="TaskInfoMap" type="cn.sh.ideal.imr.model.NewTask" >
		<result column="task_id" property="taskId"/>
		<result column="tenant_code" property="tenantCode"/>
		<result column="task_type" property="taskType"/>
		<result column="title" property="title"/>
		<result column="channel" property="channel" />
		<result column="skill_queue" property="skillQueue" /> 
		<result column="description" property="description"/>
		<result column="attach_name" property="attachName"/>
		<result column="customer_sample_id" property="customerSampleId"/>
		<result column="multimedia_type" property="multimediaType"/>
		<result column="image_text_type" property="imageTextType"/>
		<result column="content" property="content"/>
		<result column="ivr_name" property="ivrName"/>
		<result column="allocation_rule" property="allocationRule"/>
		<result column="attach_ftp_path" property="attachFtpPath"/>
		<result column="ivr_ftp_path" property="ivrFtpPath"/>
		<result column="imageText_ftp_path" property="imageTextFtpPath" />
		<result column="start_time_type" property="startTimeType"/>
		<result column="end_time_type" property="endTimeType"/>
		<result column="start_time" property="startTime"/>
		<result column="end_time" property="endTime"/>
		<result column="progress" property="progress"/>
		<result column="key_words" property="keyWords"/>
		<result column="key_words_flag" property="keyWordsFlag"/>
		<result column="status" property="status"/>
		<result column="create_user" property="createUser"/>
		<result column="create_date" property="createDate"/>
		<result column="update_date" property="updateDate"/>
		<result column="remark" property="remark"/>
		<result column="subject" property="subject" />
	</resultMap>
	
	<select id="query" parameterType="cn.sh.ideal.imr.model.NewTask" resultMap="TaskInfoMap">
		select 
		task_id,
		tenant_code,
		task_type,
		title,
		skill_queue,
		channel,
		description,
		attach_name,
		imageText_ftp_path,
		customer_sample_id,
		multimedia_type,
		image_text_type,
		content,
		ivr_name,
		allocation_rule,
		attach_ftp_path,
		ivr_ftp_path,
		start_time_type,
		end_time_type,
		start_time,
		end_time,
		progress,
		key_words,
		key_words_flag,
		status,
		create_user,
		create_date,
		update_date,
		remark,
		subject
		from task_info
		<where>
			<if test="taskId != null and taskId != ''">
				and task_id = #{taskId,jdbcType=INTEGER}
			</if>
			<if test="keyWords != null and keyWords != ''">
				and upper(key_words) = #{keyWords,jdbcType=INTEGER}
			</if>
			<if test="status != null and status != ''">
				and status = #{status,jdbcType=INTEGER}
			</if>
			<if test="tenantCode != null and tenantCode != ''">
				and tenant_code = #{tenantCode,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
</mapper>