<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.SemanticConfigDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.SemanticConfig" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CH_ACCOUNT_ID" property="chAccountId" jdbcType="DECIMAL" />
    <result column="SEMANTIC_TYPE" property="semanticType" jdbcType="VARCHAR" />
    <result column="SEMANTIC_WEBSERVICE_URL" property="semanticWebserviceUrl" jdbcType="VARCHAR" />
    <result column="SEMANTIC_WEBSERVICE_FUNCTION" property="semanticWebserviceFunction" jdbcType="VARCHAR" />
    <result column="INTERFACE_APPID" property="interfaceAppid" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="INTERFACE_TYPE" property="interfaceType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    ID, CH_ACCOUNT_ID, SEMANTIC_TYPE, SEMANTIC_WEBSERVICE_URL, SEMANTIC_WEBSERVICE_FUNCTION, 
    INTERFACE_APPID, REMARK,INTERFACE_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from MGW_SEMANTIC_CONFIG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    delete from MGW_SEMANTIC_CONFIG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.SemanticConfig" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    insert into MGW_SEMANTIC_CONFIG (ID, CH_ACCOUNT_ID, SEMANTIC_TYPE, 
      SEMANTIC_WEBSERVICE_URL, SEMANTIC_WEBSERVICE_FUNCTION, 
      INTERFACE_APPID, REMARK,INTERFACE_TYPE)
    values (#{id,jdbcType=DECIMAL}, #{chAccountId,jdbcType=DECIMAL}, #{semanticType,jdbcType=VARCHAR}, 
      #{semanticWebserviceUrl,jdbcType=VARCHAR}, #{semanticWebserviceFunction,jdbcType=VARCHAR}, 
      #{interfaceAppid,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},#{interfaceType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.SemanticConfig" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    insert into MGW_SEMANTIC_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="chAccountId != null" >
        CH_ACCOUNT_ID,
      </if>
      <if test="semanticType != null" >
        SEMANTIC_TYPE,
      </if>
      <if test="semanticWebserviceUrl != null" >
        SEMANTIC_WEBSERVICE_URL,
      </if>
      <if test="semanticWebserviceFunction != null" >
        SEMANTIC_WEBSERVICE_FUNCTION,
      </if>
      <if test="interfaceAppid != null" >
        INTERFACE_APPID,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="chAccountId != null" >
        #{chAccountId,jdbcType=DECIMAL},
      </if>
      <if test="semanticType != null" >
        #{semanticType,jdbcType=VARCHAR},
      </if>
      <if test="semanticWebserviceUrl != null" >
        #{semanticWebserviceUrl,jdbcType=VARCHAR},
      </if>
      <if test="semanticWebserviceFunction != null" >
        #{semanticWebserviceFunction,jdbcType=VARCHAR},
      </if>
      <if test="interfaceAppid != null" >
        #{interfaceAppid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.SemanticConfig" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    update MGW_SEMANTIC_CONFIG
    <set >
      <if test="chAccountId != null" >
        CH_ACCOUNT_ID = #{chAccountId,jdbcType=DECIMAL},
      </if>
      <if test="semanticType != null" >
        SEMANTIC_TYPE = #{semanticType,jdbcType=VARCHAR},
      </if>
      <if test="semanticWebserviceUrl != null" >
        SEMANTIC_WEBSERVICE_URL = #{semanticWebserviceUrl,jdbcType=VARCHAR},
      </if>
      <if test="semanticWebserviceFunction != null" >
        SEMANTIC_WEBSERVICE_FUNCTION = #{semanticWebserviceFunction,jdbcType=VARCHAR},
      </if>
      <if test="interfaceAppid != null" >
        INTERFACE_APPID = #{interfaceAppid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.SemanticConfig" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 15:12:48 CST 2015.
    -->
    update MGW_SEMANTIC_CONFIG
    set CH_ACCOUNT_ID = #{chAccountId,jdbcType=DECIMAL},
      SEMANTIC_TYPE = #{semanticType,jdbcType=VARCHAR},
      SEMANTIC_WEBSERVICE_URL = #{semanticWebserviceUrl,jdbcType=VARCHAR},
      SEMANTIC_WEBSERVICE_FUNCTION = #{semanticWebserviceFunction,jdbcType=VARCHAR},
      INTERFACE_APPID = #{interfaceAppid,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <select id="query" resultMap="BaseResultMap"  parameterType="cn.sh.ideal.imr.model.SemanticConfig">
  	select 
    <include refid="Base_Column_List" />
    from MGW_SEMANTIC_CONFIG
    
    <where>
    	<if test="chAccountId != null" >
       and  CH_ACCOUNT_ID = #{chAccountId,jdbcType=DECIMAL},
      </if>
      <if test="semanticType != null" >
       and  SEMANTIC_TYPE = #{semanticType,jdbcType=VARCHAR},
      </if>
      <if test="semanticWebserviceUrl != null" >
       and  SEMANTIC_WEBSERVICE_URL = #{semanticWebserviceUrl,jdbcType=VARCHAR},
      </if>
      <if test="semanticWebserviceFunction != null" >
       and  SEMANTIC_WEBSERVICE_FUNCTION = #{semanticWebserviceFunction,jdbcType=VARCHAR},
      </if>
      <if test="interfaceAppid != null" >
       and  INTERFACE_APPID = #{interfaceAppid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
       and  REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </where>
  </select>
</mapper>