<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.FlowDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.Flow" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    <id column="FLOW_ID" property="flowId" jdbcType="DECIMAL" />
    <result column="MENU_ID" property="menuId" jdbcType="DECIMAL" />
    <result column="FLOW_NAME" property="flowName" jdbcType="VARCHAR" />
    <result column="FLOW_INTERFACE" property="flowInterface" jdbcType="VARCHAR" />
    <result column="URL" property="url" jdbcType="VARCHAR" />
    <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
    <result column="IS_REPLY" property="isReply" jdbcType="VARCHAR" />
    <result column="REPLY_CONTENT" property="replyContent" jdbcType="VARCHAR" />
    <result column="NEWS_ID" property="newsId" jdbcType="VARCHAR" />
    <result column="FLOW_CODE" property="flowCode" jdbcType="VARCHAR" />
    <result column="INTERFACE_TYPE" property="interfaceType" jdbcType="VARCHAR" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="ERROR_TYPE" property="errorType" jdbcType="VARCHAR" />
    <result column="ERROR_VALUE" property="errorValue" jdbcType="VARCHAR" />
    <result column="IS_INTERFACE" property="isInterface" jdbcType="VARCHAR" />
    <result column="IS_PARAMETER" property="isParameter" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    FLOW_ID, MENU_ID, FLOW_NAME, FLOW_INTERFACE, URL, PARENT_ID, IS_REPLY, REPLY_CONTENT, 
    NEWS_ID, FLOW_CODE, INTERFACE_TYPE, TENANT_ID, CHANNEL_ID, ERROR_TYPE, ERROR_VALUE, 
    IS_INTERFACE, IS_PARAMETER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_FLOW
    where FLOW_ID = #{flowId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    delete from IMR_FLOW
    where FLOW_ID = #{flowId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.Flow" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    
    insert into IMR_FLOW (FLOW_ID, MENU_ID, FLOW_NAME, 
      FLOW_INTERFACE, URL, PARENT_ID, 
      IS_REPLY, REPLY_CONTENT, NEWS_ID, 
      FLOW_CODE, INTERFACE_TYPE, TENANT_ID, 
      CHANNEL_ID, ERROR_TYPE, ERROR_VALUE, 
      IS_INTERFACE, IS_PARAMETER)
    values (#{flowId,jdbcType=DECIMAL}, #{menuId,jdbcType=DECIMAL}, #{flowName,jdbcType=VARCHAR}, 
      #{flowInterface,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, 
      #{isReply,jdbcType=VARCHAR}, #{replyContent,jdbcType=VARCHAR}, #{newsId,jdbcType=VARCHAR}, 
      #{flowCode,jdbcType=VARCHAR}, #{interfaceType,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{channelId,jdbcType=VARCHAR}, #{errorType,jdbcType=VARCHAR}, #{errorValue,jdbcType=VARCHAR}, 
      #{isInterface,jdbcType=VARCHAR}, #{isParameter,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.Flow" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    insert into IMR_FLOW
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="flowId != null" >
        FLOW_ID,
      </if>
      <if test="menuId != null" >
        MENU_ID,
      </if>
      <if test="flowName != null" >
        FLOW_NAME,
      </if>
      <if test="flowInterface != null" >
        FLOW_INTERFACE,
      </if>
      <if test="url != null" >
        URL,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="isReply != null" >
        IS_REPLY,
      </if>
      <if test="replyContent != null" >
        REPLY_CONTENT,
      </if>
      <if test="newsId != null" >
        NEWS_ID,
      </if>
      <if test="flowCode != null" >
        FLOW_CODE,
      </if>
      <if test="interfaceType != null" >
        INTERFACE_TYPE,
      </if>
      <if test="tenantId != null" >
        TENANT_ID,
      </if>
      <if test="channelId != null" >
        CHANNEL_ID,
      </if>
      <if test="errorType != null" >
        ERROR_TYPE,
      </if>
      <if test="errorValue != null" >
        ERROR_VALUE,
      </if>
      <if test="isInterface != null" >
        IS_INTERFACE,
      </if>
      <if test="isParameter != null" >
        IS_PARAMETER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="flowId != null" >
        #{flowId,jdbcType=DECIMAL},
      </if>
      <if test="menuId != null" >
        #{menuId,jdbcType=DECIMAL},
      </if>
      <if test="flowName != null" >
        #{flowName,jdbcType=VARCHAR},
      </if>
      <if test="flowInterface != null" >
        #{flowInterface,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="isReply != null" >
        #{isReply,jdbcType=VARCHAR},
      </if>
      <if test="replyContent != null" >
        #{replyContent,jdbcType=VARCHAR},
      </if>
      <if test="newsId != null" >
        #{newsId,jdbcType=VARCHAR},
      </if>
      <if test="flowCode != null" >
        #{flowCode,jdbcType=VARCHAR},
      </if>
      <if test="interfaceType != null" >
        #{interfaceType,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null" >
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="errorType != null" >
        #{errorType,jdbcType=VARCHAR},
      </if>
      <if test="errorValue != null" >
        #{errorValue,jdbcType=VARCHAR},
      </if>
      <if test="isInterface != null" >
        #{isInterface,jdbcType=VARCHAR},
      </if>
      <if test="isParameter != null" >
        #{isParameter,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.Flow" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    update IMR_FLOW
    <set >
      <if test="menuId != null" >
        MENU_ID = #{menuId,jdbcType=DECIMAL},
      </if>
      <if test="flowName != null" >
        FLOW_NAME = #{flowName,jdbcType=VARCHAR},
      </if>
      <if test="flowInterface != null" >
        FLOW_INTERFACE = #{flowInterface,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="isReply != null" >
        IS_REPLY = #{isReply,jdbcType=VARCHAR},
      </if>
      <if test="replyContent != null" >
        REPLY_CONTENT = #{replyContent,jdbcType=VARCHAR},
      </if>
      <if test="newsId != null" >
        NEWS_ID = #{newsId,jdbcType=VARCHAR},
      </if>
      <if test="flowCode != null" >
        FLOW_CODE = #{flowCode,jdbcType=VARCHAR},
      </if>
      <if test="interfaceType != null" >
        INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null" >
        CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="errorType != null" >
        ERROR_TYPE = #{errorType,jdbcType=VARCHAR},
      </if>
      <if test="errorValue != null" >
        ERROR_VALUE = #{errorValue,jdbcType=VARCHAR},
      </if>
      <if test="isInterface != null" >
        IS_INTERFACE = #{isInterface,jdbcType=VARCHAR},
      </if>
      <if test="isParameter != null" >
        IS_PARAMETER = #{isParameter,jdbcType=VARCHAR},
      </if>
    </set>
    where FLOW_ID = #{flowId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.Flow" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    update IMR_FLOW
    set MENU_ID = #{menuId,jdbcType=DECIMAL},
      FLOW_NAME = #{flowName,jdbcType=VARCHAR},
      FLOW_INTERFACE = #{flowInterface,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      PARENT_ID = #{parentId,jdbcType=VARCHAR},
      IS_REPLY = #{isReply,jdbcType=VARCHAR},
      REPLY_CONTENT = #{replyContent,jdbcType=VARCHAR},
      NEWS_ID = #{newsId,jdbcType=VARCHAR},
      FLOW_CODE = #{flowCode,jdbcType=VARCHAR},
      INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR},
      TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      ERROR_TYPE = #{errorType,jdbcType=VARCHAR},
      ERROR_VALUE = #{errorValue,jdbcType=VARCHAR},
      IS_INTERFACE = #{isInterface,jdbcType=VARCHAR},
      IS_PARAMETER = #{isParameter,jdbcType=VARCHAR}
    where FLOW_ID = #{flowId,jdbcType=DECIMAL}
  </update>
  <select id="query" resultMap="BaseResultMap" parameterType="cn.sh.ideal.imr.model.Flow" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:37:57 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_FLOW
    
    <where>
    	 <if test="menuId != null" >
       and  MENU_ID = #{menuId,jdbcType=DECIMAL}
      </if>
      <if test="flowName != null" >
       and  FLOW_NAME = #{flowName,jdbcType=VARCHAR}
      </if>
      <if test="flowInterface != null" >
       and  FLOW_INTERFACE = #{flowInterface,jdbcType=VARCHAR}
      </if>
      <if test="url != null" >
        and URL = #{url,jdbcType=VARCHAR}
      </if>
      <if test="parentId != null" >
       and  PARENT_ID = #{parentId,jdbcType=VARCHAR}
      </if>
      <if test="isReply != null" >
       and  IS_REPLY = #{isReply,jdbcType=VARCHAR}
      </if>
      <if test="replyContent != null" >
       and  REPLY_CONTENT = #{replyContent,jdbcType=VARCHAR}
      </if>
      <if test="newsId != null" >
       and  NEWS_ID = #{newsId,jdbcType=VARCHAR}
      </if>
      <if test="flowCode != null" >
       and  FLOW_CODE = #{flowCode,jdbcType=VARCHAR}
      </if>
      <if test="interfaceType != null" >
       and  INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR}
      </if>
      <if test="tenantId != null" >
       and  TENANT_ID = #{tenantId,jdbcType=VARCHAR}
      </if>
      <if test="channelId != null" >
       and  CHANNEL_ID = #{channelId,jdbcType=VARCHAR}
      </if>
      <if test="errorType != null" >
       and  ERROR_TYPE = #{errorType,jdbcType=VARCHAR}
      </if>
      <if test="errorValue != null" >
       and  ERROR_VALUE = #{errorValue,jdbcType=VARCHAR}
      </if>
      <if test="isInterface != null" >
       and  IS_INTERFACE = #{isInterface,jdbcType=VARCHAR}
      </if>
      <if test="isParameter != null" >
       and  IS_PARAMETER = #{isParameter,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>