<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.imr.dao.MessageInfoDAO">
  <resultMap id="BaseResultMap" type="cn.sh.ideal.model.MessageInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    <id column="MESSAGE_ID" jdbcType="VARCHAR" property="messageId" />
    <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="TENANT_CODE" jdbcType="VARCHAR" property="tenantCode" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="NICKNAME" jdbcType="VARCHAR" property="nickname" />
    <result column="SEND_ACCOUNT" jdbcType="VARCHAR" property="sendAccount" />
    <result column="ACCEPTED_ACCOUNT" jdbcType="VARCHAR" property="acceptedAccount" />
    <result column="REPLY_ACCOUNT" jdbcType="VARCHAR" property="replyAccount" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="SESSION_ID" jdbcType="VARCHAR" property="sessionId" />
    <result column="SKILL_QUEUE" jdbcType="VARCHAR" property="skillQueue" />
    <result column="WORK_NO" jdbcType="VARCHAR" property="workNo" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="MSG_TYPE" jdbcType="VARCHAR" property="msgType" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="FOLLOW_DATA" jdbcType="VARCHAR" property="followData" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="SKILL_TYPE" jdbcType="VARCHAR" property="skillType" />
    <result column="MESSAGE_SOURCE" jdbcType="VARCHAR" property="messageSource" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    MESSAGE_ID, CHANNEL_CODE, BUSINESS_TYPE, TENANT_CODE, USER_ID, SOURCE, NICKNAME, 
    SEND_ACCOUNT, ACCEPTED_ACCOUNT, REPLY_ACCOUNT, CREATE_TIME, SEND_TIME, SESSION_ID, 
    SKILL_QUEUE, WORK_NO, STATUS, MSG_TYPE, CONTENT, FOLLOW_DATA, REMARK, SKILL_TYPE, MESSAGE_SOURCE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from MGW_MESSAGE_INFO
    where MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    delete from MGW_MESSAGE_INFO
    where MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.model.MessageInfo">
   <selectKey resultType="java.lang.String" order="BEFORE" keyProperty="messageId" >
      SELECT seq_mgw_message_info.NEXTVAL FROM DUAL
    </selectKey>
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    insert into MGW_MESSAGE_INFO (MESSAGE_ID, CHANNEL_CODE, BUSINESS_TYPE, 
      TENANT_CODE, USER_ID, SOURCE, 
      NICKNAME, SEND_ACCOUNT, ACCEPTED_ACCOUNT, 
      REPLY_ACCOUNT, CREATE_TIME, SEND_TIME, 
      SESSION_ID, SKILL_QUEUE, WORK_NO, 
      STATUS, MSG_TYPE, CONTENT, 
      FOLLOW_DATA, REMARK, SKILL_TYPE, MESSAGE_SOURCE,send_type
      )
    values (#{messageId,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, '1', 
      #{nickname,jdbcType=VARCHAR}, #{sendAccount,jdbcType=VARCHAR}, #{acceptedAccount,jdbcType=VARCHAR}, 
      #{replyAccount,jdbcType=VARCHAR}, sysdate, sysdate, 
      #{sessionId,jdbcType=VARCHAR}, #{skillQueue,jdbcType=VARCHAR}, #{workNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{msgType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{followData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{skillType,jdbcType=VARCHAR}, 
      #{messageSource,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR}
      )
  </insert>
  
  <select id="getId" resultType="string">
  	select seq_mgw_message_info.currval from dual
  </select>
  
  <insert id="insertSelective" parameterType="cn.sh.ideal.model.MessageInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    insert into MGW_MESSAGE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        MESSAGE_ID,
      </if>
      <if test="channelCode != null">
        CHANNEL_CODE,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="tenantCode != null">
        TENANT_CODE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="source != null">
        SOURCE,
      </if>
      <if test="nickname != null">
        NICKNAME,
      </if>
      <if test="sendAccount != null">
        SEND_ACCOUNT,
      </if>
      <if test="acceptedAccount != null">
        ACCEPTED_ACCOUNT,
      </if>
      <if test="replyAccount != null">
        REPLY_ACCOUNT,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="sendTime != null">
        SEND_TIME,
      </if>
      <if test="sessionId != null">
        SESSION_ID,
      </if>
      <if test="skillQueue != null">
        SKILL_QUEUE,
      </if>
      <if test="workNo != null">
        WORK_NO,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="msgType != null">
        MSG_TYPE,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="followData != null">
        FOLLOW_DATA,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="skillType != null">
        SKILL_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="sendAccount != null">
        #{sendAccount,jdbcType=VARCHAR},
      </if>
      <if test="acceptedAccount != null">
        #{acceptedAccount,jdbcType=VARCHAR},
      </if>
      <if test="replyAccount != null">
        #{replyAccount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="skillQueue != null">
        #{skillQueue,jdbcType=VARCHAR},
      </if>
      <if test="workNo != null">
        #{workNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="followData != null">
        #{followData,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="skillType != null">
        #{skillType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.model.MessageInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    update MGW_MESSAGE_INFO
    <set>
      <if test="channelCode != null">
        CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        SOURCE = #{source,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        NICKNAME = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="sendAccount != null">
        SEND_ACCOUNT = #{sendAccount,jdbcType=VARCHAR},
      </if>
      <if test="acceptedAccount != null">
        ACCEPTED_ACCOUNT = #{acceptedAccount,jdbcType=VARCHAR},
      </if>
      <if test="replyAccount != null">
        REPLY_ACCOUNT = #{replyAccount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionId != null">
        SESSION_ID = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="skillQueue != null">
        SKILL_QUEUE = #{skillQueue,jdbcType=VARCHAR},
      </if>
      <if test="workNo != null">
        WORK_NO = #{workNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        MSG_TYPE = #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="followData != null">
        FOLLOW_DATA = #{followData,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="skillType != null">
        SKILL_TYPE = #{skillType,jdbcType=VARCHAR},
      </if>
    </set>
    where MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.model.MessageInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 10:54:22 CST 2015.
    -->
    update MGW_MESSAGE_INFO
    set CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=VARCHAR},
      NICKNAME = #{nickname,jdbcType=VARCHAR},
      SEND_ACCOUNT = #{sendAccount,jdbcType=VARCHAR},
      ACCEPTED_ACCOUNT = #{acceptedAccount,jdbcType=VARCHAR},
      REPLY_ACCOUNT = #{replyAccount,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      SESSION_ID = #{sessionId,jdbcType=VARCHAR},
      SKILL_QUEUE = #{skillQueue,jdbcType=VARCHAR},
      WORK_NO = #{workNo,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      MSG_TYPE = #{msgType,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      FOLLOW_DATA = #{followData,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      SKILL_TYPE = #{skillType,jdbcType=VARCHAR}
    where MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
  </update>
  
  <select id="getCurDate" resultType="string">
	  SELECT TO_CHAR(sysdate, 'MM-DD') FROM DUAL
  </select>
</mapper>