<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.MenuDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.Menu" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    <id column="MENU_ID" property="menuId" jdbcType="VARCHAR" />
    <result column="MENU_NAME" property="menuName" jdbcType="VARCHAR" />
    <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="MENU_DESCRIPTION" property="menuDescription" jdbcType="VARCHAR" />
    <result column="MENU_STATUS" property="menuStatus" jdbcType="VARCHAR" />
    <result column="OPERATION_TIME" property="operationTime" jdbcType="TIMESTAMP" />
    <result column="EVENT_ID" property="eventId" jdbcType="VARCHAR" />
    <result column="IS_PLATFORM_MENU" property="isPlatformMenu" jdbcType="VARCHAR" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
    <result column="KEYWORDS" property="keywords" jdbcType="VARCHAR" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="FLOW_ID" property="flowId" jdbcType="VARCHAR" />
    <result column="MENU_TYPE" property="menuType" jdbcType="VARCHAR" />
    <result column="VIEW_URL" property="viewUrl" jdbcType="VARCHAR" />
    <result column="MENU_CODE" property="menuCode" jdbcType="VARCHAR" />
    <result column="SKILL_GROUP" property="skillGroup" jdbcType="VARCHAR" />
    <result column="IS_INITIATIVE" property="isInitiative" jdbcType="VARCHAR" />
    <result column="START_DATE" property="startDate" jdbcType="DATE" />
    <result column="END_DATE" property="endDate" jdbcType="DATE" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    MENU_ID, MENU_NAME, PARENT_ID, ORDER_TYPE, MENU_DESCRIPTION, MENU_STATUS, OPERATION_TIME, 
    EVENT_ID, IS_PLATFORM_MENU, TENANT_ID, KEYWORDS, CHANNEL_ID, FLOW_ID, MENU_TYPE, 
    VIEW_URL, MENU_CODE, SKILL_GROUP, IS_INITIATIVE, START_DATE, END_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_MENU
    where MENU_ID = #{menuId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    delete from IMR_MENU
    where MENU_ID = #{menuId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.Menu" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    insert into IMR_MENU (MENU_ID, MENU_NAME, PARENT_ID, 
      ORDER_TYPE, MENU_DESCRIPTION, MENU_STATUS, 
      OPERATION_TIME, EVENT_ID, IS_PLATFORM_MENU, 
      TENANT_ID, KEYWORDS, CHANNEL_ID, 
      FLOW_ID, MENU_TYPE, VIEW_URL, 
      MENU_CODE, SKILL_GROUP, IS_INITIATIVE
      )
    values (#{menuId,jdbcType=VARCHAR}, #{menuName,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=VARCHAR}, #{menuDescription,jdbcType=VARCHAR}, #{menuStatus,jdbcType=VARCHAR}, 
      #{operationTime,jdbcType=TIMESTAMP}, #{eventId,jdbcType=VARCHAR}, #{isPlatformMenu,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{keywords,jdbcType=VARCHAR}, #{channelId,jdbcType=VARCHAR}, 
      #{flowId,jdbcType=VARCHAR}, #{menuType,jdbcType=VARCHAR}, #{viewUrl,jdbcType=VARCHAR}, 
      #{menuCode,jdbcType=VARCHAR}, #{skillGroup,jdbcType=VARCHAR}, #{isInitiative,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.Menu" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    insert into IMR_MENU
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="menuId != null" >
        MENU_ID,
      </if>
      <if test="menuName != null" >
        MENU_NAME,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="menuDescription != null" >
        MENU_DESCRIPTION,
      </if>
      <if test="menuStatus != null" >
        MENU_STATUS,
      </if>
      <if test="operationTime != null" >
        OPERATION_TIME,
      </if>
      <if test="eventId != null" >
        EVENT_ID,
      </if>
      <if test="isPlatformMenu != null" >
        IS_PLATFORM_MENU,
      </if>
      <if test="tenantId != null" >
        TENANT_ID,
      </if>
      <if test="keywords != null" >
        KEYWORDS,
      </if>
      <if test="channelId != null" >
        CHANNEL_ID,
      </if>
      <if test="flowId != null" >
        FLOW_ID,
      </if>
      <if test="menuType != null" >
        MENU_TYPE,
      </if>
      <if test="viewUrl != null" >
        VIEW_URL,
      </if>
      <if test="menuCode != null" >
        MENU_CODE,
      </if>
      <if test="skillGroup != null" >
        SKILL_GROUP,
      </if>
      <if test="isInitiative != null" >
        IS_INITIATIVE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="menuId != null" >
        #{menuId,jdbcType=VARCHAR},
      </if>
      <if test="menuName != null" >
        #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="menuDescription != null" >
        #{menuDescription,jdbcType=VARCHAR},
      </if>
      <if test="menuStatus != null" >
        #{menuStatus,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null" >
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventId != null" >
        #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="isPlatformMenu != null" >
        #{isPlatformMenu,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null" >
        #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null" >
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null" >
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="menuType != null" >
        #{menuType,jdbcType=VARCHAR},
      </if>
      <if test="viewUrl != null" >
        #{viewUrl,jdbcType=VARCHAR},
      </if>
      <if test="menuCode != null" >
        #{menuCode,jdbcType=VARCHAR},
      </if>
      <if test="skillGroup != null" >
        #{skillGroup,jdbcType=VARCHAR},
      </if>
      <if test="isInitiative != null" >
        #{isInitiative,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.Menu" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    update IMR_MENU
    <set >
      <if test="menuName != null" >
        MENU_NAME = #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="menuDescription != null" >
        MENU_DESCRIPTION = #{menuDescription,jdbcType=VARCHAR},
      </if>
      <if test="menuStatus != null" >
        MENU_STATUS = #{menuStatus,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null" >
        OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventId != null" >
        EVENT_ID = #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="isPlatformMenu != null" >
        IS_PLATFORM_MENU = #{isPlatformMenu,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null" >
        KEYWORDS = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null" >
        CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null" >
        FLOW_ID = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="menuType != null" >
        MENU_TYPE = #{menuType,jdbcType=VARCHAR},
      </if>
      <if test="viewUrl != null" >
        VIEW_URL = #{viewUrl,jdbcType=VARCHAR},
      </if>
      <if test="menuCode != null" >
        MENU_CODE = #{menuCode,jdbcType=VARCHAR},
      </if>
      <if test="skillGroup != null" >
        SKILL_GROUP = #{skillGroup,jdbcType=VARCHAR},
      </if>
      <if test="isInitiative != null" >
        IS_INITIATIVE = #{isInitiative,jdbcType=VARCHAR},
      </if>
    </set>
    where MENU_ID = #{menuId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.Menu" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 13:39:19 CST 2015.
    -->
    update IMR_MENU
    set MENU_NAME = #{menuName,jdbcType=VARCHAR},
      PARENT_ID = #{parentId,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      MENU_DESCRIPTION = #{menuDescription,jdbcType=VARCHAR},
      MENU_STATUS = #{menuStatus,jdbcType=VARCHAR},
      OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP},
      EVENT_ID = #{eventId,jdbcType=VARCHAR},
      IS_PLATFORM_MENU = #{isPlatformMenu,jdbcType=VARCHAR},
      TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      KEYWORDS = #{keywords,jdbcType=VARCHAR},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      FLOW_ID = #{flowId,jdbcType=VARCHAR},
      MENU_TYPE = #{menuType,jdbcType=VARCHAR},
      VIEW_URL = #{viewUrl,jdbcType=VARCHAR},
      MENU_CODE = #{menuCode,jdbcType=VARCHAR},
      SKILL_GROUP = #{skillGroup,jdbcType=VARCHAR},
      IS_INITIATIVE = #{isInitiative,jdbcType=VARCHAR}
    where MENU_ID = #{menuId,jdbcType=VARCHAR}
  </update>
  
  <select id="query" resultMap="BaseResultMap" parameterType="cn.sh.ideal.imr.model.Menu">
  	select  MENU_ID, MENU_NAME, PARENT_ID, ORDER_TYPE, MENU_DESCRIPTION, MENU_STATUS, OPERATION_TIME, 
    EVENT_ID, IS_PLATFORM_MENU, TENANT_ID, KEYWORDS, CHANNEL_ID, (select p.flow_id from IMR_FLOW p where p.menu_id =
		t.menu_id and (p.parent_id is null or p.parent_id = '' or p.parent_id = '0')) as FLOW_ID, MENU_TYPE, 
    VIEW_URL, MENU_CODE, SKILL_GROUP, IS_INITIATIVE, START_DATE, END_DATE
    from IMR_MENU t
    <where>
    	<if test="menuName != null" >
       and  MENU_NAME = #{menuName,jdbcType=VARCHAR}
      </if>
      <if test="parentId != null" >
       and  PARENT_ID = #{parentId,jdbcType=VARCHAR}
      </if>
      <if test="orderType != null" >
       and  ORDER_TYPE = #{orderType,jdbcType=VARCHAR}
      </if>
      <if test="menuDescription != null" >
       and  MENU_DESCRIPTION = #{menuDescription,jdbcType=VARCHAR}
      </if>
      <if test="menuStatus != null" >
       and  MENU_STATUS = #{menuStatus,jdbcType=VARCHAR}
      </if>
      <if test="operationTime != null" >
       and  OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP}
      </if>
      <if test="eventId != null" >
       and  EVENT_ID = #{eventId,jdbcType=VARCHAR}
      </if>
      <if test="isPlatformMenu != null" >
       and  IS_PLATFORM_MENU = #{isPlatformMenu,jdbcType=VARCHAR}
      </if>
      <if test="tenantId != null" >
       and  TENANT_ID = #{tenantId,jdbcType=VARCHAR}
      </if>
      <if test="keywords != null" >
       and  KEYWORDS = #{keywords,jdbcType=VARCHAR}
      </if>
      <if test="channelId != null" >
       and FIND_IN_SET(#{channelId,jdbcType=VARCHAR}, CHANNEL_ID) > 0 
      </if>
      <if test="flowId != null" >
       and  FLOW_ID = #{flowId,jdbcType=VARCHAR}
      </if>
      <if test="menuType != null" >
       and  MENU_TYPE = #{menuType,jdbcType=VARCHAR}
      </if>
      <if test="viewUrl != null" >
       and  VIEW_URL = #{viewUrl,jdbcType=VARCHAR}
      </if>
      <if test="menuCode != null" >
       and  MENU_CODE = #{menuCode,jdbcType=VARCHAR}
      </if>
      <if test="skillGroup != null" >
       and  SKILL_GROUP = #{skillGroup,jdbcType=VARCHAR}
      </if>
      <if test="isInitiative != null" >
       and  IS_INITIATIVE = #{isInitiative,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  
  <select id="queryKeyword" resultMap="BaseResultMap">
		select 
   		 <include refid="Base_Column_List" />
		 from IMR_MENU where keywords is not
		null and length(keywords) > 0 and menu_status ='1' order by length(keywords) desc
	</select>
</mapper>