<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.MenuBusiDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.MenuBusi" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:07:54 CST 2015.
    -->
    <result column="AUTO_ID" property="autoId" jdbcType="DECIMAL" />
    <result column="MENU_ID" property="menuId" jdbcType="DECIMAL" />
    <result column="BUSI_ID" property="busiId" jdbcType="DECIMAL" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.MenuBusi" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:07:54 CST 2015.
    -->
    insert into CMS_MENU_BUSI (AUTO_ID, MENU_ID, BUSI_ID, 
      TENANT_ID)
    values (#{autoId,jdbcType=DECIMAL}, #{menuId,jdbcType=DECIMAL}, #{busiId,jdbcType=DECIMAL}, 
      #{tenantId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.MenuBusi" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:07:54 CST 2015.
    -->
    insert into CMS_MENU_BUSI
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        AUTO_ID,
      </if>
      <if test="menuId != null" >
        MENU_ID,
      </if>
      <if test="busiId != null" >
        BUSI_ID,
      </if>
      <if test="tenantId != null" >
        TENANT_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        #{autoId,jdbcType=DECIMAL},
      </if>
      <if test="menuId != null" >
        #{menuId,jdbcType=DECIMAL},
      </if>
      <if test="busiId != null" >
        #{busiId,jdbcType=DECIMAL},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="query" parameterType="cn.sh.ideal.imr.model.MenuBusi" resultMap="BaseResultMap">
  	select AUTO_ID, MENU_ID, BUSI_ID, 
      TENANT_ID from CMS_MENU_BUSI 
  	<where>
  		<if test="autoId != null" >
       and AUTO_ID = #{autoId,jdbcType=DECIMAL}
      </if>
      <if test="menuId != null" >
       and MENU_ID = #{menuId,jdbcType=DECIMAL}
      </if>
      <if test="busiId != null" >
       and BUSI_ID = #{busiId,jdbcType=DECIMAL}
      </if>
      <if test="tenantId != null" >
       and TENANT_ID = #{tenantId,jdbcType=VARCHAR}
      </if>
  	</where>
  	</select>
</mapper>