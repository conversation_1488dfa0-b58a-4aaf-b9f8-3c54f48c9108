<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.SkillQueueDao" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.SkillQueue" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="QUEUE_NAME" property="queueName" jdbcType="VARCHAR" />
    <result column="QUEUE_CODE" property="queueCode" jdbcType="VARCHAR" />
    <result column="QUEUE_SIZE" property="queueSize" jdbcType="DECIMAL" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
    <result column="ABLE_BUSINESS_TYPES" property="ableBusinessTypes" jdbcType="VARCHAR" />
    <result column="ABLE_CHANNELS" property="ableChannels" jdbcType="VARCHAR" />
    <result column="SORT_RULE" property="sortRule" jdbcType="VARCHAR" />
    <result column="ALLOCATION_RULE" property="allocationRule" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="VARCHAR" />
    <result column="ROUTE_RULE" property="routeRule" jdbcType="VARCHAR" />
    <result column="SORT_SIZE" property="sortSize" jdbcType="DECIMAL" />
    <result column="SKILL_TYPE_CODE" property="skillTypeCode" jdbcType="VARCHAR" />
    <result column="WELCOME_MESSAGE" property="welcomeMessage" jdbcType="VARCHAR" />
    <result column="LINE_MESSAGE" property="lineMessage" jdbcType="VARCHAR" />
    <result column="CONNECT_MESSAGE" property="connectMessage" jdbcType="VARCHAR" />
    <result column="WORK_TIME" property="workTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    ID, QUEUE_NAME, QUEUE_CODE, QUEUE_SIZE, TENANT_ID, ABLE_BUSINESS_TYPES, ABLE_CHANNELS, 
    SORT_RULE, ALLOCATION_RULE, STATUS, REMARK, IS_DEFAULT, ROUTE_RULE, SORT_SIZE, 
    WELCOME_MESSAGE, LINE_MESSAGE, CONNECT_MESSAGE, WORK_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from MGW_TENANT_SKILL_QUEUE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    delete from MGW_TENANT_SKILL_QUEUE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.SkillQueue" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    insert into MGW_TENANT_SKILL_QUEUE (ID, QUEUE_NAME, QUEUE_CODE, 
      QUEUE_SIZE, TENANT_ID, ABLE_BUSINESS_TYPES, 
      ABLE_CHANNELS, SORT_RULE, ALLOCATION_RULE, 
      STATUS, REMARK, IS_DEFAULT, 
      ROUTE_RULE, SORT_SIZE, SKILL_TYPE_CODE, 
      WELCOME_MESSAGE, LINE_MESSAGE, CONNECT_MESSAGE, 
      WORK_TIME)
    values (#{id,jdbcType=DECIMAL}, #{queueName,jdbcType=VARCHAR}, #{queueCode,jdbcType=VARCHAR}, 
      #{queueSize,jdbcType=DECIMAL}, #{tenantId,jdbcType=VARCHAR}, #{ableBusinessTypes,jdbcType=VARCHAR}, 
      #{ableChannels,jdbcType=VARCHAR}, #{sortRule,jdbcType=VARCHAR}, #{allocationRule,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDefault,jdbcType=VARCHAR}, 
      #{routeRule,jdbcType=VARCHAR}, #{sortSize,jdbcType=DECIMAL}, #{skillTypeCode,jdbcType=VARCHAR}, 
      #{welcomeMessage,jdbcType=VARCHAR}, #{lineMessage,jdbcType=VARCHAR}, #{connectMessage,jdbcType=VARCHAR}, 
      #{workTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.SkillQueue" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    insert into MGW_TENANT_SKILL_QUEUE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="queueName != null" >
        QUEUE_NAME,
      </if>
      <if test="queueCode != null" >
        QUEUE_CODE,
      </if>
      <if test="queueSize != null" >
        QUEUE_SIZE,
      </if>
      <if test="tenantId != null" >
        TENANT_ID,
      </if>
      <if test="ableBusinessTypes != null" >
        ABLE_BUSINESS_TYPES,
      </if>
      <if test="ableChannels != null" >
        ABLE_CHANNELS,
      </if>
      <if test="sortRule != null" >
        SORT_RULE,
      </if>
      <if test="allocationRule != null" >
        ALLOCATION_RULE,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="routeRule != null" >
        ROUTE_RULE,
      </if>
      <if test="sortSize != null" >
        SORT_SIZE,
      </if>
      <if test="skillTypeCode != null" >
        SKILL_TYPE_CODE,
      </if>
      <if test="welcomeMessage != null" >
        WELCOME_MESSAGE,
      </if>
      <if test="lineMessage != null" >
        LINE_MESSAGE,
      </if>
      <if test="connectMessage != null" >
        CONNECT_MESSAGE,
      </if>
      <if test="workTime != null" >
        WORK_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="queueName != null" >
        #{queueName,jdbcType=VARCHAR},
      </if>
      <if test="queueCode != null" >
        #{queueCode,jdbcType=VARCHAR},
      </if>
      <if test="queueSize != null" >
        #{queueSize,jdbcType=DECIMAL},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="ableBusinessTypes != null" >
        #{ableBusinessTypes,jdbcType=VARCHAR},
      </if>
      <if test="ableChannels != null" >
        #{ableChannels,jdbcType=VARCHAR},
      </if>
      <if test="sortRule != null" >
        #{sortRule,jdbcType=VARCHAR},
      </if>
      <if test="allocationRule != null" >
        #{allocationRule,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=VARCHAR},
      </if>
      <if test="routeRule != null" >
        #{routeRule,jdbcType=VARCHAR},
      </if>
      <if test="sortSize != null" >
        #{sortSize,jdbcType=DECIMAL},
      </if>
      <if test="skillTypeCode != null" >
        #{skillTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="welcomeMessage != null" >
        #{welcomeMessage,jdbcType=VARCHAR},
      </if>
      <if test="lineMessage != null" >
        #{lineMessage,jdbcType=VARCHAR},
      </if>
      <if test="connectMessage != null" >
        #{connectMessage,jdbcType=VARCHAR},
      </if>
      <if test="workTime != null" >
        #{workTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.SkillQueue" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    update MGW_TENANT_SKILL_QUEUE
    <set >
      <if test="queueName != null" >
        QUEUE_NAME = #{queueName,jdbcType=VARCHAR},
      </if>
      <if test="queueCode != null" >
        QUEUE_CODE = #{queueCode,jdbcType=VARCHAR},
      </if>
      <if test="queueSize != null" >
        QUEUE_SIZE = #{queueSize,jdbcType=DECIMAL},
      </if>
      <if test="tenantId != null" >
        TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="ableBusinessTypes != null" >
        ABLE_BUSINESS_TYPES = #{ableBusinessTypes,jdbcType=VARCHAR},
      </if>
      <if test="ableChannels != null" >
        ABLE_CHANNELS = #{ableChannels,jdbcType=VARCHAR},
      </if>
      <if test="sortRule != null" >
        SORT_RULE = #{sortRule,jdbcType=VARCHAR},
      </if>
      <if test="allocationRule != null" >
        ALLOCATION_RULE = #{allocationRule,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT = #{isDefault,jdbcType=VARCHAR},
      </if>
      <if test="routeRule != null" >
        ROUTE_RULE = #{routeRule,jdbcType=VARCHAR},
      </if>
      <if test="sortSize != null" >
        SORT_SIZE = #{sortSize,jdbcType=DECIMAL},
      </if>
      <if test="skillTypeCode != null" >
        SKILL_TYPE_CODE = #{skillTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="welcomeMessage != null" >
        WELCOME_MESSAGE = #{welcomeMessage,jdbcType=VARCHAR},
      </if>
      <if test="lineMessage != null" >
        LINE_MESSAGE = #{lineMessage,jdbcType=VARCHAR},
      </if>
      <if test="connectMessage != null" >
        CONNECT_MESSAGE = #{connectMessage,jdbcType=VARCHAR},
      </if>
      <if test="workTime != null" >
        WORK_TIME = #{workTime,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.SkillQueue" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Feb 23 10:32:53 CST 2016.
    -->
    update MGW_TENANT_SKILL_QUEUE
    set QUEUE_NAME = #{queueName,jdbcType=VARCHAR},
      QUEUE_CODE = #{queueCode,jdbcType=VARCHAR},
      QUEUE_SIZE = #{queueSize,jdbcType=DECIMAL},
      TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      ABLE_BUSINESS_TYPES = #{ableBusinessTypes,jdbcType=VARCHAR},
      ABLE_CHANNELS = #{ableChannels,jdbcType=VARCHAR},
      SORT_RULE = #{sortRule,jdbcType=VARCHAR},
      ALLOCATION_RULE = #{allocationRule,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      IS_DEFAULT = #{isDefault,jdbcType=VARCHAR},
      ROUTE_RULE = #{routeRule,jdbcType=VARCHAR},
      SORT_SIZE = #{sortSize,jdbcType=DECIMAL},
      SKILL_TYPE_CODE = #{skillTypeCode,jdbcType=VARCHAR},
      WELCOME_MESSAGE = #{welcomeMessage,jdbcType=VARCHAR},
      LINE_MESSAGE = #{lineMessage,jdbcType=VARCHAR},
      CONNECT_MESSAGE = #{connectMessage,jdbcType=VARCHAR},
      WORK_TIME = #{workTime,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>