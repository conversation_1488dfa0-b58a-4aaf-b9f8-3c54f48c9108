<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.PlatformMessageDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.entity.PlatformMessage" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="MSG_ID" property="msgId" jdbcType="VARCHAR" />
    <result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
    <result column="TO_USER" property="toUser" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="MSG_TYPE" property="msgType" jdbcType="VARCHAR" />
    <result column="EVENT_ID" property="eventId" jdbcType="VARCHAR" />
    <result column="EVENT_TYPE" property="eventType" jdbcType="VARCHAR" />
    <result column="PIC_URL" property="picUrl" jdbcType="VARCHAR" />
    <result column="X" property="x" jdbcType="VARCHAR" />
    <result column="Y" property="y" jdbcType="VARCHAR" />
    <result column="MSG" property="msg" jdbcType="VARCHAR" />
    <result column="RECEIVE_TIME" property="receiveTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="cn.sh.ideal.imr.entity.PlatformMessage" extends="BaseResultMap" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    <result column="CREATE_TIME" property="createTime" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    ID, MSG_ID, OPEN_ID, TO_USER, CONTENT, MSG_TYPE, EVENT_ID, EVENT_TYPE, PIC_URL, X, 
    Y, MSG, RECEIVE_TIME
  </sql>
  <sql id="Blob_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from IMR_PLATFORM_MESSAGE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    delete from IMR_PLATFORM_MESSAGE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.entity.PlatformMessage" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    insert into IMR_PLATFORM_MESSAGE ( MSG_ID, OPEN_ID, 
      TO_USER, CONTENT, MSG_TYPE, 
      EVENT_ID, EVENT_TYPE, PIC_URL, 
      X, Y, MSG, RECEIVE_TIME, 
      CREATE_TIME)
    values (#{msgId,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, 
      #{toUser,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{msgType,jdbcType=VARCHAR}, 
      #{eventId,jdbcType=VARCHAR}, #{eventType,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR}, 
      #{x,jdbcType=VARCHAR}, #{y,jdbcType=VARCHAR}, #{msg,jdbcType=VARCHAR}, #{receiveTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=LONGVARCHAR})
  </insert>
  
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.entity.PlatformMessage" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    insert into IMR_PLATFORM_MESSAGE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="msgId != null" >
        MSG_ID,
      </if>
      <if test="openId != null" >
        OPEN_ID,
      </if>
      <if test="toUser != null" >
        TO_USER,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="msgType != null" >
        MSG_TYPE,
      </if>
      <if test="eventId != null" >
        EVENT_ID,
      </if>
      <if test="eventType != null" >
        EVENT_TYPE,
      </if>
      <if test="picUrl != null" >
        PIC_URL,
      </if>
      <if test="x != null" >
        X,
      </if>
      <if test="y != null" >
        Y,
      </if>
      <if test="msg != null" >
        MSG,
      </if>
      <if test="receiveTime != null" >
        RECEIVE_TIME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null" >
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="toUser != null" >
        #{toUser,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null" >
        #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null" >
        #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null" >
        #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null" >
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="x != null" >
        #{x,jdbcType=VARCHAR},
      </if>
      <if test="y != null" >
        #{y,jdbcType=VARCHAR},
      </if>
      <if test="msg != null" >
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null" >
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.entity.PlatformMessage" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    update IMR_PLATFORM_MESSAGE
    <set >
      <if test="msgId != null" >
        MSG_ID = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="toUser != null" >
        TO_USER = #{toUser,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null" >
        MSG_TYPE = #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null" >
        EVENT_ID = #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null" >
        EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null" >
        PIC_URL = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="x != null" >
        X = #{x,jdbcType=VARCHAR},
      </if>
      <if test="y != null" >
        Y = #{y,jdbcType=VARCHAR},
      </if>
      <if test="msg != null" >
        MSG = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null" >
        RECEIVE_TIME = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="cn.sh.ideal.imr.entity.PlatformMessage" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    update IMR_PLATFORM_MESSAGE
    set MSG_ID = #{msgId,jdbcType=VARCHAR},
      OPEN_ID = #{openId,jdbcType=VARCHAR},
      TO_USER = #{toUser,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      MSG_TYPE = #{msgType,jdbcType=VARCHAR},
      EVENT_ID = #{eventId,jdbcType=VARCHAR},
      EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
      PIC_URL = #{picUrl,jdbcType=VARCHAR},
      X = #{x,jdbcType=VARCHAR},
      Y = #{y,jdbcType=VARCHAR},
      MSG = #{msg,jdbcType=VARCHAR},
      RECEIVE_TIME = #{receiveTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.entity.PlatformMessage" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:35:02 CST 2015.
    -->
    update IMR_PLATFORM_MESSAGE
    set MSG_ID = #{msgId,jdbcType=VARCHAR},
      OPEN_ID = #{openId,jdbcType=VARCHAR},
      TO_USER = #{toUser,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      MSG_TYPE = #{msgType,jdbcType=VARCHAR},
      EVENT_ID = #{eventId,jdbcType=VARCHAR},
      EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
      PIC_URL = #{picUrl,jdbcType=VARCHAR},
      X = #{x,jdbcType=VARCHAR},
      Y = #{y,jdbcType=VARCHAR},
      MSG = #{msg,jdbcType=VARCHAR},
      RECEIVE_TIME = #{receiveTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>