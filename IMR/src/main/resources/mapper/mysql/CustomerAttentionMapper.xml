<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.CustomerAttentionDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.CustomerAttention" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="UPDATE_DATE" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="TENANT_ID" property="tenantId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    ID, OPEN_ID, STATUS, UPDATE_DATE, CHANNEL_ID, TENANT_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_CUSTOMER_ATTENTION
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    delete from IMR_CUSTOMER_ATTENTION
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.CustomerAttention" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    insert into IMR_CUSTOMER_ATTENTION ( OPEN_ID, STATUS, 
      UPDATE_DATE, CHANNEL_ID, TENANT_ID
      )
    values (#{openId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{channelId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.CustomerAttention" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    insert into IMR_CUSTOMER_ATTENTION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="openId != null" >
        OPEN_ID,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="updateDate != null" >
        UPDATE_DATE,
      </if>
      <if test="channelId != null" >
        CHANNEL_ID,
      </if>
      <if test="tenantId != null" >
        TENANT_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="openId != null" >
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelId != null" >
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.CustomerAttention" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    update IMR_CUSTOMER_ATTENTION
    <set >
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelId != null" >
        CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        TENANT_ID = #{tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.CustomerAttention" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:33:17 CST 2015.
    -->
    update IMR_CUSTOMER_ATTENTION
    set OPEN_ID = #{openId,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      TENANT_ID = #{tenantId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>