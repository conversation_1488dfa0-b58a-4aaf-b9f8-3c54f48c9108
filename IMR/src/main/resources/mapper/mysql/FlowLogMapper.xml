<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.FlowLogDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.FlowLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MENU_ID" property="menuId" jdbcType="VARCHAR" />
    <result column="FLOW_ID" property="flowId" jdbcType="VARCHAR" />
    <result column="FLOW_PARAMETER" property="flowParameter" jdbcType="VARCHAR" />
    <result column="ERROR_NUM" property="errorNum" jdbcType="DECIMAL" />
    <result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
    <result column="OPERATE_TIME" property="operateTime" jdbcType="TIMESTAMP" />
    <result column="S_ID" property="sId" jdbcType="VARCHAR" />
    <result column="USER_PARAMETER" property="userParameter" jdbcType="VARCHAR" />
    <result column="PLATFORM" property="platform" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    ID, MENU_ID, FLOW_ID, FLOW_PARAMETER, ERROR_NUM, OPEN_ID, OPERATE_TIME, S_ID, USER_PARAMETER, 
    PLATFORM
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_FLOW_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    delete from IMR_FLOW_LOG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.FlowLog" useGeneratedKeys="true"  keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    insert into IMR_FLOW_LOG ( MENU_ID, FLOW_ID, 
      FLOW_PARAMETER, ERROR_NUM, OPEN_ID, 
      OPERATE_TIME, S_ID, USER_PARAMETER, 
      PLATFORM)
    values (#{menuId,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, 
      #{flowParameter,jdbcType=VARCHAR}, #{errorNum,jdbcType=DECIMAL}, #{openId,jdbcType=VARCHAR}, 
      sysdate(), #{sId,jdbcType=VARCHAR}, #{userParameter,jdbcType=VARCHAR}, 
      #{platform,jdbcType=VARCHAR})
  </insert>
  <select id="getCustomerVisitLog" parameterType="long" resultType="cn.sh.ideal.imr.model.CustomerVisitLog">
  		select t.id visitId,
		date_format(t.operate_time,'%Y-%m-%d %T') operStartTime,
		m.menu_name operActionContent,
		'菜单' operActionType,
		'接口' operReplayType,
		'成功' operReplayContent,
		'自助' type,
		t.platform channel,
		t.open_id account,
		m.tenant_id tenantCode,
		t.s_id sessionId
		from imr_flow_log t,imr_menu m
		where t.menu_id = m.menu_id
		and t.id = #{id}
  </select>
  
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.FlowLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    insert into IMR_FLOW_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="menuId != null" >
        MENU_ID,
      </if>
      <if test="flowId != null" >
        FLOW_ID,
      </if>
      <if test="flowParameter != null" >
        FLOW_PARAMETER,
      </if>
      <if test="errorNum != null" >
        ERROR_NUM,
      </if>
      <if test="openId != null" >
        OPEN_ID,
      </if>
      <if test="operateTime != null" >
        OPERATE_TIME,
      </if>
      <if test="sId != null" >
        S_ID,
      </if>
      <if test="userParameter != null" >
        USER_PARAMETER,
      </if>
      <if test="platform != null" >
        PLATFORM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="menuId != null" >
        #{menuId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null" >
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="flowParameter != null" >
        #{flowParameter,jdbcType=VARCHAR},
      </if>
      <if test="errorNum != null" >
        #{errorNum,jdbcType=DECIMAL},
      </if>
      <if test="openId != null" >
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sId != null" >
        #{sId,jdbcType=VARCHAR},
      </if>
      <if test="userParameter != null" >
        #{userParameter,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        #{platform,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.FlowLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    update IMR_FLOW_LOG
    <set >
      <if test="menuId != null" >
        MENU_ID = #{menuId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null" >
        FLOW_ID = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="flowParameter != null" >
        FLOW_PARAMETER = #{flowParameter,jdbcType=VARCHAR},
      </if>
      <if test="errorNum != null" >
        ERROR_NUM = #{errorNum,jdbcType=DECIMAL},
      </if>
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        OPERATE_TIME = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sId != null" >
        S_ID = #{sId,jdbcType=VARCHAR},
      </if>
      <if test="userParameter != null" >
        USER_PARAMETER = #{userParameter,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        PLATFORM = #{platform,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.FlowLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 16:48:56 CST 2015.
    -->
    update IMR_FLOW_LOG
    set MENU_ID = #{menuId,jdbcType=VARCHAR},
      FLOW_ID = #{flowId,jdbcType=VARCHAR},
      FLOW_PARAMETER = #{flowParameter,jdbcType=VARCHAR},
      ERROR_NUM = #{errorNum,jdbcType=DECIMAL},
      OPEN_ID = #{openId,jdbcType=VARCHAR},
      OPERATE_TIME = #{operateTime,jdbcType=TIMESTAMP},
      S_ID = #{sId,jdbcType=VARCHAR},
      USER_PARAMETER = #{userParameter,jdbcType=VARCHAR},
      PLATFORM = #{platform,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>