<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.QrcodeDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.Qrcode" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CHANNEL_ACCOUNT" property="channelAccount" jdbcType="VARCHAR" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="PLATFORM_USER" property="platformUser" jdbcType="VARCHAR" />
    <result column="ACTION_NAME" property="actionName" jdbcType="VARCHAR" />
    <result column="SCENE_ID" property="sceneId" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="USAGE_TYPE" property="usageType" jdbcType="VARCHAR" />
    <result column="TICKET" property="ticket" jdbcType="VARCHAR" />
    <result column="FROM_CHANNEL_ACCOUNT" property="fromChannelAccount" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    ID, CHANNEL_ACCOUNT, TENANT_CODE, PLATFORM_USER, ACTION_NAME, SCENE_ID, CREATE_DATE, 
    USAGE_TYPE, TICKET, FROM_CHANNEL_ACCOUNT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from MGW_QRCODE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    delete from MGW_QRCODE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.Qrcode" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    insert into MGW_QRCODE (CHANNEL_ACCOUNT, TENANT_CODE, 
      PLATFORM_USER, ACTION_NAME, SCENE_ID, 
      CREATE_DATE, USAGE_TYPE, TICKET, 
      FROM_CHANNEL_ACCOUNT)
    values (#{channelAccount,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, 
      #{platformUser,jdbcType=VARCHAR}, #{actionName,jdbcType=VARCHAR}, #{sceneId,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{usageType,jdbcType=VARCHAR}, #{ticket,jdbcType=VARCHAR}, 
      #{fromChannelAccount,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.Qrcode" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    insert into MGW_QRCODE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="channelAccount != null" >
        CHANNEL_ACCOUNT,
      </if>
      <if test="tenantCode != null" >
        TENANT_CODE,
      </if>
      <if test="platformUser != null" >
        PLATFORM_USER,
      </if>
      <if test="actionName != null" >
        ACTION_NAME,
      </if>
      <if test="sceneId != null" >
        SCENE_ID,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="usageType != null" >
        USAGE_TYPE,
      </if>
      <if test="ticket != null" >
        TICKET,
      </if>
      <if test="fromChannelAccount != null" >
        FROM_CHANNEL_ACCOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="channelAccount != null" >
        #{channelAccount,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="platformUser != null" >
        #{platformUser,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null" >
        #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null" >
        #{sceneId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="usageType != null" >
        #{usageType,jdbcType=VARCHAR},
      </if>
      <if test="ticket != null" >
        #{ticket,jdbcType=VARCHAR},
      </if>
      <if test="fromChannelAccount != null" >
        #{fromChannelAccount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.Qrcode" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    update MGW_QRCODE
    <set >
      <if test="channelAccount != null" >
        CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="platformUser != null" >
        PLATFORM_USER = #{platformUser,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null" >
        ACTION_NAME = #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null" >
        SCENE_ID = #{sceneId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="usageType != null" >
        USAGE_TYPE = #{usageType,jdbcType=VARCHAR},
      </if>
      <if test="ticket != null" >
        TICKET = #{ticket,jdbcType=VARCHAR},
      </if>
      <if test="fromChannelAccount != null" >
        FROM_CHANNEL_ACCOUNT = #{fromChannelAccount,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.Qrcode" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:14:21 CST 2015.
    -->
    update MGW_QRCODE
    set CHANNEL_ACCOUNT = #{channelAccount,jdbcType=VARCHAR},
      TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      PLATFORM_USER = #{platformUser,jdbcType=VARCHAR},
      ACTION_NAME = #{actionName,jdbcType=VARCHAR},
      SCENE_ID = #{sceneId,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      USAGE_TYPE = #{usageType,jdbcType=VARCHAR},
      TICKET = #{ticket,jdbcType=VARCHAR},
      FROM_CHANNEL_ACCOUNT = #{fromChannelAccount,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <select id="queryForList" parameterType="cn.sh.ideal.imr.model.Qrcode" resultMap="BaseResultMap">
  	 select 
    <include refid="Base_Column_List" />
    from MGW_QRCODE
    
     <where>
      
      <if test="id != null" >
        and id = #{id,jdbcType=DECIMAL},
      </if>
      <if test="channelAccount != null" >
       and channel_account = #{channelAccount,jdbcType=VARCHAR}
      </if>
      <if test="tenantCode != null" >
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
      </if>
      <if test="platformUser != null" >
        and platform_user = #{platformUser,jdbcType=VARCHAR}
      </if>
      <if test="actionName != null" >
        and action_name = #{actionName,jdbcType=VARCHAR}
      </if>
      <if test="sceneId != null" >
        and scene_id = #{sceneId,jdbcType=VARCHAR}
      </if>
      <if test="createDate != null" >
        and create_date = #{createDate,jdbcType=TIMESTAMP}
      </if>
      <if test="usageType != null" >
        and usage_type = #{usageType,jdbcType=VARCHAR}
      </if>
      <if test="ticket != null" >
        and ticket = #{ticket,jdbcType=VARCHAR}
      </if>
      <if test="fromChannelAccount != null" >
        and from_channel_account = #{fromChannelAccount,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>