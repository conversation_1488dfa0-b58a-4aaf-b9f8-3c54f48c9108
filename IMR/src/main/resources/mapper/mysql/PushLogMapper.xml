<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.PushLogDao" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.PushLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 16:08:03 CST 2015.
    -->
    <result column="ID" property="id" jdbcType="DECIMAL" />
    <result column="USER_ACCOUNT" property="userAccount" jdbcType="VARCHAR" />
    <result column="SERVER_ACCOUNT" property="serverAccount" jdbcType="VARCHAR" />
    <result column="MARKET_TYPE" property="marketType" jdbcType="VARCHAR" />
    <result column="SEND_TIME" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="TASK_ID" property="taskId" jdbcType="VARCHAR" />
    <result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DECIMAL" />
    <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.PushLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 16:08:03 CST 2015.
    -->
    insert into MA_PUSH_LOG (ID, USER_ACCOUNT, SERVER_ACCOUNT, 
      MARKET_TYPE, SEND_TIME, TASK_ID, 
      SESSION_ID, STATUS, CUSTOMER_ID
      )
    values (#{id,jdbcType=DECIMAL}, #{userAccount,jdbcType=VARCHAR}, #{serverAccount,jdbcType=VARCHAR}, 
      #{marketType,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{taskId,jdbcType=VARCHAR}, 
      #{sessionId,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL}, #{customerId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.PushLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 16:08:03 CST 2015.
    -->
    insert into MA_PUSH_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="userAccount != null" >
        USER_ACCOUNT,
      </if>
      <if test="serverAccount != null" >
        SERVER_ACCOUNT,
      </if>
      <if test="marketType != null" >
        MARKET_TYPE,
      </if>
      <if test="sendTime != null" >
        SEND_TIME,
      </if>
      <if test="taskId != null" >
        TASK_ID,
      </if>
      <if test="sessionId != null" >
        SESSION_ID,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="customerId != null" >
        CUSTOMER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="userAccount != null" >
        #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="serverAccount != null" >
        #{serverAccount,jdbcType=VARCHAR},
      </if>
      <if test="marketType != null" >
        #{marketType,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null" >
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null" >
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null" >
        #{customerId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <update id="updateSessionId" parameterType="cn.sh.ideal.imr.model.PushLog">
  	update MA_PUSH_LOG set SESSION_ID = #{sessionId,jdbcType=VARCHAR} 
  	<where>
  		<choose>
  			<when test="taskId != null and taskId != '' and userAccount != null and userAccount != ''">
  				and TASK_ID = #{taskId,jdbcType=VARCHAR} and USER_ACCOUNT = #{userAccount,jdbcType=VARCHAR}
  			</when>
  			<otherwise>
  				and 1 = 2
  			</otherwise>
  		</choose>
  	</where>
  </update>
</mapper>