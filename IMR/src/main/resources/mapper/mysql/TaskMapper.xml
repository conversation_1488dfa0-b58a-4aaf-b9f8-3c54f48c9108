<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.TaskDao" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.Task" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="TASK_DES" property="taskDes" jdbcType="VARCHAR" />
    <result column="BEGIN_TIME" property="beginTime" jdbcType="TIMESTAMP" />
    <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP" />
    <result column="URL" property="url" jdbcType="VARCHAR" />
    <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="SKILL" property="skill" jdbcType="VARCHAR" />
    <result column="PUSH" property="push" jdbcType="VARCHAR" />
    <result column="TRIGGER_DES" property="triggerDes" jdbcType="VARCHAR" />
    <result column="CONTENT_TYPE" property="contentType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    ID, TYPE, TITLE, STATUS, TASK_DES, BEGIN_TIME, END_TIME, URL, TASK_CODE, TENANT_CODE, 
    CONTENT, SKILL, PUSH, TRIGGER_DES, CONTENT_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from MA_TASK
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    delete from MA_TASK
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.Task" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    insert into MA_TASK (ID, TYPE, TITLE, 
      STATUS, TASK_DES, BEGIN_TIME, 
      END_TIME, URL, TASK_CODE, 
      TENANT_CODE, CONTENT, SKILL, 
      PUSH, TRIGGER_DES, CONTENT_TYPE
      )
    values (#{id,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{taskDes,jdbcType=VARCHAR}, #{beginTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{url,jdbcType=VARCHAR}, #{taskCode,jdbcType=VARCHAR}, 
      #{tenantCode,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{skill,jdbcType=VARCHAR}, 
      #{push,jdbcType=VARCHAR}, #{triggerDes,jdbcType=VARCHAR}, #{contentType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.Task" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    insert into MA_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="taskDes != null" >
        TASK_DES,
      </if>
      <if test="beginTime != null" >
        BEGIN_TIME,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="url != null" >
        URL,
      </if>
      <if test="taskCode != null" >
        TASK_CODE,
      </if>
      <if test="tenantCode != null" >
        TENANT_CODE,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="skill != null" >
        SKILL,
      </if>
      <if test="push != null" >
        PUSH,
      </if>
      <if test="triggerDes != null" >
        TRIGGER_DES,
      </if>
      <if test="contentType != null" >
        CONTENT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="taskDes != null" >
        #{taskDes,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null" >
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="taskCode != null" >
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="skill != null" >
        #{skill,jdbcType=VARCHAR},
      </if>
      <if test="push != null" >
        #{push,jdbcType=VARCHAR},
      </if>
      <if test="triggerDes != null" >
        #{triggerDes,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null" >
        #{contentType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.Task" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    update MA_TASK
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="taskDes != null" >
        TASK_DES = #{taskDes,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null" >
        BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null" >
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="taskCode != null" >
        TASK_CODE = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="skill != null" >
        SKILL = #{skill,jdbcType=VARCHAR},
      </if>
      <if test="push != null" >
        PUSH = #{push,jdbcType=VARCHAR},
      </if>
      <if test="triggerDes != null" >
        TRIGGER_DES = #{triggerDes,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null" >
        CONTENT_TYPE = #{contentType,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.Task" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 18 14:51:53 CST 2015.
    -->
    update MA_TASK
    set TYPE = #{type,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      TASK_DES = #{taskDes,jdbcType=VARCHAR},
      BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      URL = #{url,jdbcType=VARCHAR},
      TASK_CODE = #{taskCode,jdbcType=VARCHAR},
      TENANT_CODE = #{tenantCode,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      SKILL = #{skill,jdbcType=VARCHAR},
      PUSH = #{push,jdbcType=VARCHAR},
      TRIGGER_DES = #{triggerDes,jdbcType=VARCHAR},
      CONTENT_TYPE = #{contentType,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <select id="query" resultMap="BaseResultMap" parameterType="cn.sh.ideal.imr.model.Task">
  	select 
    <include refid="Base_Column_List" />
    from MA_TASK
    <where>
      <if test="type != null and type != ''" >
        and TYPE = #{type,jdbcType=VARCHAR}
      </if>
      <if test="title != null and title != ''" >
        and TITLE = #{title,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''" >
        and STATUS = #{status,jdbcType=VARCHAR}
      </if>
      <if test="taskDes != null and taskDes!= ''" >
        and TASK_DES = #{taskDes,jdbcType=VARCHAR}
      </if>
      <if test="beginTime != null and beginTime!= ''" >
        and BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null and endTime != ''" >
        and END_TIME = #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="url != null and url != ''" >
        and URL = #{url,jdbcType=VARCHAR}
      </if>
      <if test="taskCode != null and taskCode != ''" >
        and upper(TASK_CODE) = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="tenantCode != null and tenantCode != ''" >
        and TENANT_CODE = #{tenantCode,jdbcType=VARCHAR}
      </if>
      <if test="content != null and content != ''" >
        and CONTENT = #{content,jdbcType=VARCHAR}
      </if>
      <if test="skill != null and skill != ''" >
        and SKILL = #{skill,jdbcType=VARCHAR}
      </if>
      <if test="push != null and push != ''" >
        and PUSH = #{push,jdbcType=VARCHAR}
      </if>
      <if test="triggerDes != null and triggerDes != ''" >
        and TRIGGER_DES = #{triggerDes,jdbcType=VARCHAR}
      </if>
      <if test="contentType != null and contentType != ''" >
        and CONTENT_TYPE = #{contentType,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>