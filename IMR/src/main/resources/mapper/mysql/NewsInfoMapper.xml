<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.imr.dao.NewsInfoDAO" >
  <resultMap id="BaseResultMap" type="cn.sh.ideal.imr.model.NewsInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="PICURL" property="picurl" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="E_STATUS" property="eStatus" jdbcType="VARCHAR" />
    <result column="PAGE_CONTENT" property="pageContent" jdbcType="CLOB" />
    <result column="EXTEND_URL" property="extendUrl" jdbcType="VARCHAR" />
    <result column="URL_TYPE" property="urlType" jdbcType="VARCHAR" />
    <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" />
    <result column="TENANT" property="tenant" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    ID, TITLE, PICURL, CONTENT, CREATE_TIME, STATUS, E_STATUS, PAGE_CONTENT, EXTEND_URL, 
    URL_TYPE, CHANNEL_CODE, TENANT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from IMR_NEWS_INFO
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    delete from IMR_NEWS_INFO
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="cn.sh.ideal.imr.model.NewsInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    insert into IMR_NEWS_INFO (ID, TITLE, PICURL, 
      CONTENT, CREATE_TIME, STATUS, 
      E_STATUS, PAGE_CONTENT, EXTEND_URL, 
      URL_TYPE, CHANNEL_CODE, TENANT
      )
    values (#{id,jdbcType=DECIMAL}, #{title,jdbcType=VARCHAR}, #{picurl,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, 
      #{eStatus,jdbcType=VARCHAR}, #{pageContent,jdbcType=OTHER}, #{extendUrl,jdbcType=VARCHAR}, 
      #{urlType,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR}, #{tenant,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.sh.ideal.imr.model.NewsInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    insert into IMR_NEWS_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="picurl != null" >
        PICURL,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="eStatus != null" >
        E_STATUS,
      </if>
      <if test="pageContent != null" >
        PAGE_CONTENT,
      </if>
      <if test="extendUrl != null" >
        EXTEND_URL,
      </if>
      <if test="urlType != null" >
        URL_TYPE,
      </if>
      <if test="channelCode != null" >
        CHANNEL_CODE,
      </if>
      <if test="tenant != null" >
        TENANT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="picurl != null" >
        #{picurl,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="eStatus != null" >
        #{eStatus,jdbcType=VARCHAR},
      </if>
      <if test="pageContent != null" >
        #{pageContent,jdbcType=OTHER},
      </if>
      <if test="extendUrl != null" >
        #{extendUrl,jdbcType=VARCHAR},
      </if>
      <if test="urlType != null" >
        #{urlType,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null" >
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="tenant != null" >
        #{tenant,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sh.ideal.imr.model.NewsInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    update IMR_NEWS_INFO
    <set >
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="picurl != null" >
        PICURL = #{picurl,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="eStatus != null" >
        E_STATUS = #{eStatus,jdbcType=VARCHAR},
      </if>
      <if test="pageContent != null" >
        PAGE_CONTENT = #{pageContent,jdbcType=OTHER},
      </if>
      <if test="extendUrl != null" >
        EXTEND_URL = #{extendUrl,jdbcType=VARCHAR},
      </if>
      <if test="urlType != null" >
        URL_TYPE = #{urlType,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null" >
        CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="tenant != null" >
        TENANT = #{tenant,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sh.ideal.imr.model.NewsInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 08 11:23:33 CST 2015.
    -->
    update IMR_NEWS_INFO
    set TITLE = #{title,jdbcType=VARCHAR},
      PICURL = #{picurl,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      STATUS = #{status,jdbcType=VARCHAR},
      E_STATUS = #{eStatus,jdbcType=VARCHAR},
      PAGE_CONTENT = #{pageContent,jdbcType=OTHER},
      EXTEND_URL = #{extendUrl,jdbcType=VARCHAR},
      URL_TYPE = #{urlType,jdbcType=VARCHAR},
      CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      TENANT = #{tenant,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <!-- 获取图文消息 -->
	<select id="getNewsMessage" parameterType="java.lang.String"
		resultType="cn.sh.ideal.imr.model.NewsInfo">
		SELECT id, title, content, create_time createTime, url_type urlType,EXTEND_URL extendUrl, t.status, t.picurl FROM IMR_NEWS_INFO  t
		where FIND_IN_SET(t.ID, (SELECT message_content FROM  IMR_NEWS_PUSH WHERE id  = #{id,jdbcType=VARCHAR})) > 0
	</select>

	<!-- 获取图文 -->
	<select id="getNewsInfoById" parameterType="java.lang.String" resultType="cn.sh.ideal.imr.model.NewsInfo">
		SELECT id, title, picurl, content, page_content
		pageContent, url_type urlType, EXTEND_URL extendUrl, t.status FROM
		IMR_NEWS_INFO t
		where id = #{id,jdbcType=VARCHAR}
  	</select>
  
  
  	<insert id="insertNewsInfoLog" parameterType="cn.sh.ideal.imr.model.NewsInfo">
  		 insert into IMR_NEWS_INFO_LOG (ID,NEWS_ID, TITLE, 
      CONTENT, CREATE_TIME, 
      TENANT_CODE
      )
    values (SEQ_IMR_NEWS_INFO_LOG.NEXTVAL,#{id,jdbcType=DECIMAL}, #{title,jdbcType=VARCHAR},  
      #{content,jdbcType=VARCHAR}, SYSDATE,
      #{tenant,jdbcType=VARCHAR}
      )
  	
  	</insert>
</mapper>