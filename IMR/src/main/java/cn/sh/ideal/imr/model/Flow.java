package cn.sh.ideal.imr.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class Flow implements Serializable{
	private static final long serialVersionUID = 1L;

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String flowId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.MENU_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private Long menuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.FLOW_NAME
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String flowName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.FLOW_INTERFACE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String flowInterface;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.URL
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String url;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.PARENT_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String parentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.IS_REPLY
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String isReply;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.REPLY_CONTENT
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String replyContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.NEWS_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String newsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.FLOW_CODE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String flowCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.INTERFACE_TYPE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String interfaceType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String channelId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.ERROR_TYPE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String errorType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.ERROR_VALUE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String errorValue;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.IS_INTERFACE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String isInterface;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW.IS_PARAMETER
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    private String isParameter;
    
    private List<Flow> subflow;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.FLOW_ID
     *
     * @return the value of IMR_FLOW.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getFlowId() {
        return flowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.FLOW_ID
     *
     * @param flowId the value for IMR_FLOW.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.MENU_ID
     *
     * @return the value of IMR_FLOW.MENU_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public Long getMenuId() {
        return menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.MENU_ID
     *
     * @param menuId the value for IMR_FLOW.MENU_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.FLOW_NAME
     *
     * @return the value of IMR_FLOW.FLOW_NAME
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getFlowName() {
        return flowName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.FLOW_NAME
     *
     * @param flowName the value for IMR_FLOW.FLOW_NAME
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.FLOW_INTERFACE
     *
     * @return the value of IMR_FLOW.FLOW_INTERFACE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getFlowInterface() {
        return flowInterface;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.FLOW_INTERFACE
     *
     * @param flowInterface the value for IMR_FLOW.FLOW_INTERFACE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setFlowInterface(String flowInterface) {
        this.flowInterface = flowInterface;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.URL
     *
     * @return the value of IMR_FLOW.URL
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getUrl() {
        return url;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.URL
     *
     * @param url the value for IMR_FLOW.URL
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.PARENT_ID
     *
     * @return the value of IMR_FLOW.PARENT_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.PARENT_ID
     *
     * @param parentId the value for IMR_FLOW.PARENT_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.IS_REPLY
     *
     * @return the value of IMR_FLOW.IS_REPLY
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getIsReply() {
        return isReply;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.IS_REPLY
     *
     * @param isReply the value for IMR_FLOW.IS_REPLY
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setIsReply(String isReply) {
        this.isReply = isReply;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.REPLY_CONTENT
     *
     * @return the value of IMR_FLOW.REPLY_CONTENT
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getReplyContent() {
        return replyContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.REPLY_CONTENT
     *
     * @param replyContent the value for IMR_FLOW.REPLY_CONTENT
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setReplyContent(String replyContent) {
        this.replyContent = replyContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.NEWS_ID
     *
     * @return the value of IMR_FLOW.NEWS_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getNewsId() {
        return newsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.NEWS_ID
     *
     * @param newsId the value for IMR_FLOW.NEWS_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setNewsId(String newsId) {
        this.newsId = newsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.FLOW_CODE
     *
     * @return the value of IMR_FLOW.FLOW_CODE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getFlowCode() {
        return flowCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.FLOW_CODE
     *
     * @param flowCode the value for IMR_FLOW.FLOW_CODE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.INTERFACE_TYPE
     *
     * @return the value of IMR_FLOW.INTERFACE_TYPE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getInterfaceType() {
        return interfaceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.INTERFACE_TYPE
     *
     * @param interfaceType the value for IMR_FLOW.INTERFACE_TYPE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setInterfaceType(String interfaceType) {
        this.interfaceType = interfaceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.TENANT_ID
     *
     * @return the value of IMR_FLOW.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.TENANT_ID
     *
     * @param tenantId the value for IMR_FLOW.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.CHANNEL_ID
     *
     * @return the value of IMR_FLOW.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.CHANNEL_ID
     *
     * @param channelId the value for IMR_FLOW.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.ERROR_TYPE
     *
     * @return the value of IMR_FLOW.ERROR_TYPE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getErrorType() {
        return errorType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.ERROR_TYPE
     *
     * @param errorType the value for IMR_FLOW.ERROR_TYPE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.ERROR_VALUE
     *
     * @return the value of IMR_FLOW.ERROR_VALUE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getErrorValue() {
        return errorValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.ERROR_VALUE
     *
     * @param errorValue the value for IMR_FLOW.ERROR_VALUE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setErrorValue(String errorValue) {
        this.errorValue = errorValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.IS_INTERFACE
     *
     * @return the value of IMR_FLOW.IS_INTERFACE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getIsInterface() {
        return isInterface;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.IS_INTERFACE
     *
     * @param isInterface the value for IMR_FLOW.IS_INTERFACE
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setIsInterface(String isInterface) {
        this.isInterface = isInterface;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW.IS_PARAMETER
     *
     * @return the value of IMR_FLOW.IS_PARAMETER
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public String getIsParameter() {
        return isParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW.IS_PARAMETER
     *
     * @param isParameter the value for IMR_FLOW.IS_PARAMETER
     *
     * @mbggenerated Wed Apr 08 13:37:57 CST 2015
     */
    public void setIsParameter(String isParameter) {
        this.isParameter = isParameter;
    }

	/**
	 * @return the subflow
	 */
	public List<Flow> getSubflow() {
		return subflow;
	}

	/**
	 * @param subflow the subflow to set
	 */
	public void setSubflow(List<Flow> subflow) {
		this.subflow = subflow;
	}
    

	public void setParameters(Map<String, String[]> parameters) {
		if(parameters == null)
			return;
		
		if(this.url == null || "".equals(this.url)) 
			return;
		
		if(!this.url.contains("?")) {
			this.url += "?";
		}
		
		for (Map.Entry<String, String[]> element : parameters.entrySet()) {
			if(element.getValue().length > 0)
			this.url += "&" + element.getKey() + "=" + element.getValue()[0];
		}
	}
}