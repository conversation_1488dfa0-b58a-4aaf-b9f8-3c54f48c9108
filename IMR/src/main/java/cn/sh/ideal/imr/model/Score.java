package cn.sh.ideal.imr.model;

import java.math.BigDecimal;

public class Score {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.ID
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private BigDecimal id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.SESSION_ID
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private String sessionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.WORK_NO
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private String tenantCode;
    
    public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String customerId;
	
	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	private String workNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.SATIFIED
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private Long satified;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.GETSCORETIME
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private String getscoretime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.GETREQUESTTIME
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private String getrequesttime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_SCORE.SUGGEST
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    private String suggest;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.ID
     *
     * @return the value of IMR_SCORE.ID
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public BigDecimal getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.ID
     *
     * @param id the value for IMR_SCORE.ID
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setId(BigDecimal id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.SESSION_ID
     *
     * @return the value of IMR_SCORE.SESSION_ID
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.SESSION_ID
     *
     * @param sessionId the value for IMR_SCORE.SESSION_ID
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.WORK_NO
     *
     * @return the value of IMR_SCORE.WORK_NO
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public String getWorkNo() {
        return workNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.WORK_NO
     *
     * @param workNo the value for IMR_SCORE.WORK_NO
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.SATIFIED
     *
     * @return the value of IMR_SCORE.SATIFIED
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public Long getSatified() {
        return satified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.SATIFIED
     *
     * @param satified the value for IMR_SCORE.SATIFIED
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setSatified(Long satified) {
        this.satified = satified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.GETSCORETIME
     *
     * @return the value of IMR_SCORE.GETSCORETIME
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public String getGetscoretime() {
        return getscoretime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.GETSCORETIME
     *
     * @param getscoretime the value for IMR_SCORE.GETSCORETIME
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setGetscoretime(String getscoretime) {
        this.getscoretime = getscoretime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.GETREQUESTTIME
     *
     * @return the value of IMR_SCORE.GETREQUESTTIME
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public String getGetrequesttime() {
        return getrequesttime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.GETREQUESTTIME
     *
     * @param getrequesttime the value for IMR_SCORE.GETREQUESTTIME
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setGetrequesttime(String getrequesttime) {
        this.getrequesttime = getrequesttime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_SCORE.SUGGEST
     *
     * @return the value of IMR_SCORE.SUGGEST
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public String getSuggest() {
        return suggest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_SCORE.SUGGEST
     *
     * @param suggest the value for IMR_SCORE.SUGGEST
     *
     * @mbggenerated Wed Apr 08 11:31:40 CST 2015
     */
    public void setSuggest(String suggest) {
        this.suggest = suggest;
    }
}