/**
 * Project Name:SM Maven Webapp
 * File Name:CmsSysParamServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2015年2月3日下午2:14:53
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.imr.service.impl;

import java.io.Serializable;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.service.SessionTipService;
import cn.sh.ideal.model.SessionTip;


@Service("sessionTipService")
public class SessionTipServiceImpl implements SessionTipService {
	private static Logger log = Logger.getLogger(SessionTipServiceImpl.class);
    @Resource(name = "redisDao")
    private RedisDao<String, Serializable> redisDao;

    public static String TIP_ ="TIP_";

    

    @Override
    public SessionTip get(String tenantCode,String skillQueue,String acceptedAccount) {
    	log.info("tenantCode:"+tenantCode+",skillQueue:"+skillQueue+",acceptedAccount:"+acceptedAccount);
    	log.info("key:"+TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
        return (SessionTip) redisDao.readValue(TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
    }
}
