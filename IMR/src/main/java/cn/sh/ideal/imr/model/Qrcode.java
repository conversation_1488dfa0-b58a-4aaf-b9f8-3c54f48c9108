package cn.sh.ideal.imr.model;

import java.util.Date;

public class Qrcode {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.ID
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.CHANNEL_ACCOUNT
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String channelAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.TENANT_CODE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String tenantCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.PLATFORM_USER
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String platformUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.ACTION_NAME
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String actionName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.SCENE_ID
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String sceneId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.CREATE_DATE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private Date createDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.USAGE_TYPE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String usageType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.TICKET
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String ticket;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_QRCODE.FROM_CHANNEL_ACCOUNT
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    private String fromChannelAccount;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.ID
     *
     * @return the value of MGW_QRCODE.ID
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.ID
     *
     * @param id the value for MGW_QRCODE.ID
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.CHANNEL_ACCOUNT
     *
     * @return the value of MGW_QRCODE.CHANNEL_ACCOUNT
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getChannelAccount() {
        return channelAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.CHANNEL_ACCOUNT
     *
     * @param channelAccount the value for MGW_QRCODE.CHANNEL_ACCOUNT
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setChannelAccount(String channelAccount) {
        this.channelAccount = channelAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.TENANT_CODE
     *
     * @return the value of MGW_QRCODE.TENANT_CODE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getTenantCode() {
        return tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.TENANT_CODE
     *
     * @param tenantCode the value for MGW_QRCODE.TENANT_CODE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.PLATFORM_USER
     *
     * @return the value of MGW_QRCODE.PLATFORM_USER
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getPlatformUser() {
        return platformUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.PLATFORM_USER
     *
     * @param platformUser the value for MGW_QRCODE.PLATFORM_USER
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setPlatformUser(String platformUser) {
        this.platformUser = platformUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.ACTION_NAME
     *
     * @return the value of MGW_QRCODE.ACTION_NAME
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getActionName() {
        return actionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.ACTION_NAME
     *
     * @param actionName the value for MGW_QRCODE.ACTION_NAME
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.SCENE_ID
     *
     * @return the value of MGW_QRCODE.SCENE_ID
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getSceneId() {
        return sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.SCENE_ID
     *
     * @param sceneId the value for MGW_QRCODE.SCENE_ID
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.CREATE_DATE
     *
     * @return the value of MGW_QRCODE.CREATE_DATE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.CREATE_DATE
     *
     * @param createDate the value for MGW_QRCODE.CREATE_DATE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.USAGE_TYPE
     *
     * @return the value of MGW_QRCODE.USAGE_TYPE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getUsageType() {
        return usageType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.USAGE_TYPE
     *
     * @param usageType the value for MGW_QRCODE.USAGE_TYPE
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setUsageType(String usageType) {
        this.usageType = usageType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.TICKET
     *
     * @return the value of MGW_QRCODE.TICKET
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getTicket() {
        return ticket;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.TICKET
     *
     * @param ticket the value for MGW_QRCODE.TICKET
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_QRCODE.FROM_CHANNEL_ACCOUNT
     *
     * @return the value of MGW_QRCODE.FROM_CHANNEL_ACCOUNT
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public String getFromChannelAccount() {
        return fromChannelAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_QRCODE.FROM_CHANNEL_ACCOUNT
     *
     * @param fromChannelAccount the value for MGW_QRCODE.FROM_CHANNEL_ACCOUNT
     *
     * @mbggenerated Wed Apr 08 11:14:21 CST 2015
     */
    public void setFromChannelAccount(String fromChannelAccount) {
        this.fromChannelAccount = fromChannelAccount;
    }
}