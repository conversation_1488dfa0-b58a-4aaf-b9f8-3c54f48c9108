package cn.sh.ideal.imr.service.impl;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.service.SessionService;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.sm.resp.RespGetSession;

@Service("sessionService")
public class SessionServiceImpl implements SessionService{
	
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	
	private static final String SUFFIX = "_EXT";
	
	private int validSeconds = 300;
	
	public static final String CONTENT_TYPE = "application/json";
	
	@Autowired
	private cn.sh.ideal.sm.service.SessionService sessionService;
	

	
	@Override
	public SessionInfoExt get(String sendAccount, String acceptedAccount, String channel, String tenantCode) throws Exception{

		SessionData sessionData = new SessionData();
		sessionData.setSendAccount(sendAccount);
		sessionData.setAcceptedAccount(acceptedAccount);
		sessionData.setChannelCode(channel);
		sessionData.setTenantCode(tenantCode);
		
		
		RespGetSession respGetSession = sessionService.getSession(sessionData);
		SessionInfo info = respGetSession.getData();
		
		if(info == null)
			return null;
		
		SessionInfoExt extendInfo = (SessionInfoExt)redisDao.readValue(info.getSessionId() + SUFFIX);
		
		if(extendInfo == null) {
			extendInfo = new SessionInfoExt();
			redisDao.saveValue(info.getSessionId() + SUFFIX, extendInfo, validSeconds, TimeUnit.SECONDS);
			
		}
		BeanUtils.copyProperties(extendInfo, info);
		
		
		return extendInfo;
		
	}
	
	@Override
	public SessionInfoExt get(String sessionId, String tenantCode)  throws Exception{


		SessionData sessionData = new SessionData();
		sessionData.setSessionId(sessionId);
		sessionData.setTenantCode(tenantCode);
		
		
		RespGetSession respGetSession = sessionService.getSession(sessionData);
		SessionInfo info = respGetSession.getData();
		
		if(info == null)
			return null;
		
		SessionInfoExt extendInfo = (SessionInfoExt)redisDao.readValue(info.getSessionId() + SUFFIX);
		
		if(extendInfo == null) {
			extendInfo = new SessionInfoExt();
			redisDao.saveValue(info.getSessionId() + SUFFIX, extendInfo, validSeconds, TimeUnit.SECONDS);
			
			BeanUtils.copyProperties(extendInfo, info);
		}
		
		return extendInfo;
	}



	@Override
	public void update(SessionInfoExt session, SessionStatus status,
			Map<String, Object> updateFields)  throws Exception{
		
		redisDao.saveValue(session.getSessionId() + SUFFIX, session, validSeconds, TimeUnit.SECONDS);
		
		if(status == null && updateFields == null)
			return;
		
		SessionData sessionData = new SessionData();
		sessionData.setSessionId(session.getSessionId());
		sessionData.setTenantCode(session.getTenantCode());
		sessionData.setStatus( status == null ? session.getStatus() : status.toString());
		sessionData.setData(updateFields);
		
		
		sessionService.updateSession(sessionData);
		
	}

	@Override
	public void addMessage(String sessionId, String tenantCode, String messageId) throws Exception{
		
		SessionData sessionData = new SessionData();
		sessionData.setSessionId(sessionId);
		sessionData.setTenantCode(tenantCode);
		sessionData.setMessageId(messageId);
		
		sessionService.addMessage(sessionData);
	}

	
	public static void main(String[] args) {
		//SessionServiceImpl impl = new SessionServiceImpl();
		//impl.get("123", "456", "1000", "1002");
	}

}
