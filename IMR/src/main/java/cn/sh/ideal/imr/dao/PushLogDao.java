package cn.sh.ideal.imr.dao;

import cn.sh.ideal.imr.model.PushLog;

public interface PushLogDao {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MA_PUSH_LOG
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    int insert(PushLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MA_PUSH_LOG
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    int insertSelective(PushLog record);
    
    void updateSessionId(PushLog log);
}