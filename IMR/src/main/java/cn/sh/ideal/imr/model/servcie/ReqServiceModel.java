package cn.sh.ideal.imr.model.servcie;

import java.util.ArrayList;
import java.util.List;

import cn.sh.ideal.imr.model.servcie.param.BaseParamModel;
/**
 * 接口调用提交实体
 * <AUTHOR>
 *
 */

public class ReqServiceModel {

	
	private String userID;
	private String menuID;
	private String flowID;
	private String tenantCode;
	private String sessionID;
	private String platform;
	private String appID;
	private String callbackUrl;
	private List <BaseParamModel> userParam = new ArrayList<BaseParamModel>();
	private List <BaseParamModel> flowParam = new ArrayList<BaseParamModel>();
	private String msg;
	private String followData;
	

	public ReqServiceModel() {
		super();
	}
	public String getUserID() {
		return userID;
	}
	public void setUserID(String userID) {
		this.userID = userID;
	}
	public String getMenuID() {
		return menuID;
	}
	public void setMenuID(String menuID) {
		this.menuID = menuID;
	}
	public String getFlowID() {
		return flowID;
	}
	public void setFlowID(String flowID) {
		this.flowID = flowID;
	}
	public String getSessionID() {
		return sessionID;
	}
	public void setSessionID(String sessionID) {
		this.sessionID = sessionID;
	}
	public String getPlatform() {
		return platform;
	}
	public void setPlatform(String platform) {
		this.platform = platform;
	}
	public List<BaseParamModel> getUserParam() {
		return userParam;
	}
	public void setUserParam(List<BaseParamModel> userParam) {
		this.userParam = userParam;
	}
	public List<BaseParamModel> getFlowParam() {
		return flowParam;
	}
	public void setFlowParam(List<BaseParamModel> flowParam) {
		this.flowParam = flowParam;
	}
	public String getAppID() {
		return appID;
	}
	public void setAppID(String appID) {
		this.appID = appID;
	}
	/**
	 * @return the callbackUrl
	 */
	public String getCallbackUrl() {
		return callbackUrl;
	}
	/**
	 * @param callbackUrl the callbackUrl to set
	 */
	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}
	/**
	 * @return the msg
	 */
	public String getMsg() {
		return msg;
	}
	/**
	 * @param msg the msg to set
	 */
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getFollowData() {
		return followData;
	}
	public void setFollowData(String followData) {
		this.followData = followData;
	}
	
}
