/**
 * 
 */
package cn.sh.ideal.imr.service.message.event;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.base.Constants;
import cn.sh.ideal.imr.dao.CustomerAttentionDAO;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.model.CustomerAttention;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.service.message.MessageService;
import cn.sh.ideal.model.ChannelConfig;

/**
 * 关注事件处理
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月31日
 */
@Service
public class SubscribeEventMessageService extends MessageService {

	@Autowired
	private CustomerAttentionDAO customerAttentionDAO;
	
	@PostConstruct
	protected void init() {
		register("subscribe", this);
		register("follow", this);
	}

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.message.MessageService#handle(cn.sh.ideal.imr.imr.entity.PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception {
		ChannelConfig channel = session.getChannel();
		
		CustomerAttention attention = new CustomerAttention();
		attention.setChannelId(String.valueOf(channel.getId()));
		attention.setTenantId(channel.getTenantCode());
		attention.setOpenId(message.getOpenId());
		attention.setStatus("1");
		
		customerAttentionDAO.insert(attention);
		
		Menu menu = getCurrentMenu(channel.getId() + "_SUBSCRIBE");
		
		if(menu == null) {
			return null;
		}
		
		return menuFlow(session, channel.getId() + "_SUBSCRIBE", message);
	}

	
}
