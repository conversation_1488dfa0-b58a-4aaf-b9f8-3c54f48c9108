/**
 * 
 */
package cn.sh.ideal.imr.service.message;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.imr.common.Constants;
import cn.sh.ideal.imr.dao.FlowLogDAO;
import cn.sh.ideal.imr.dao.MenuBusiDAO;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.Flow;
import cn.sh.ideal.imr.model.FlowLog;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.MenuBusi;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.model.servcie.ReqServiceModel;
import cn.sh.ideal.imr.model.servcie.RespServiceModel;
import cn.sh.ideal.imr.model.servcie.param.BaseParamModel;
import cn.sh.ideal.imr.model.servcie.param.TextParamModel;
import cn.sh.ideal.imr.service.NewsService;
import cn.sh.ideal.imr.service.SessionService;
import cn.sh.ideal.imr.service.impl.DataInitService;
import cn.sh.ideal.imr.service.systemflow.SystemFlowService;
import cn.sh.ideal.imr.util.ServiceClient;
import cn.sh.ideal.util.NetUtil;
import cn.sh.ideal.util.PropertiesUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月29日
 */
@Service
@SuppressWarnings("all")
public class MessageService {

	private static Logger log = Logger.getLogger(MessageService.class);
	
	private static HashMap<String, MessageService> services;
	
	static {
		services = new HashMap<String, MessageService>();
	}

	@Autowired
	protected DataInitService dataInitService;
	
	@Autowired
	protected SessionService sessionService;
	
	@Autowired
	protected NewsService newsService;
	
	@Autowired
	protected FlowLogDAO flowLogDAO;

	@Autowired
	private MenuBusiDAO menuBusiDAO;
	
	@Value("${msg.menuList}")
	protected String msgMenuList;

	@Value("${msg.menuList}")
	protected String thirdpartyNewsUrl;
	
	/**
	 * 注册Service
	 */
	public final static void register(String key, MessageService service) {
		if(services.containsKey(key))
			return;
		
		services.put(key, service);
	}

	/**
	 * 工厂
	 * @param messageType
	 * @return
	 */
	public final static MessageService getMessageService(String messageType) {
		return services.get(StringUtils.lowerCase(messageType));
	}
	
	
	/**
	 * 初始化
	 */
	protected void init() {}
	
	
	
	/**
	 * 消息处理
	 * @param message	消息
	 * @param session	会话
	 * @return			返回消息
	 * @throws Exception
	 */
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception{

		MessageService service = getMessageService(message.getMsgType());
		
		if(service == null)
			throw new NullPointerException("service not found by " + message.getMsgType());

		return service.handle(message, session);
	}
	

	/**
	 * 执行流程
	 * @param session
	 * @param message
	 * @param flow
	 * @return
	 * @throws Exception
	 */
	public ResponseMessage executeFlow(SessionInfoExt session, PlatformMessage message, Flow flow) throws Exception{

		if(flow == null)
			throw new Exception("flow is null at executeFlow method");
		
		log.info("execute flow [" + flow.getFlowId() + "]");
		
		// log
		flowLog(session);
		
		// 检查请求超出错误限制
		String msg = checkLimit(session, flow);
		if (StringUtils.isNotEmpty(msg)) {
			sessionService.update(session, null, null);
			return new ResponseTextMessage(msg);
		}
		
		//文本回复
		if("1".equals(flow.getIsReply())) {
			updateSessionFlow(session, "ok", "", null);
			return new ResponseTextMessage(flow.getReplyContent());
		}
		
		//图文回复
		if("2".equals(flow.getIsReply())) {
			updateSessionFlow(session, "ok", "", null);
			return newsService.getNewsMessage(flow.getNewsId(), message);
		}
		
		//第三方图文回复
		if("3".equals(flow.getIsReply())) {
			updateSessionFlow(session, "ok", "", null);
			return new ResponseTextMessage(NetUtil.send(thirdpartyNewsUrl + flow.getNewsId(), NetUtil.POST, "{\"id\":\"" + flow.getNewsId() + "\"}"));
		}
		
		RespServiceModel respModel = null;
		
		TextParamModel textParamModel = new TextParamModel(message.getContent());
		ReqServiceModel reqModel = requestParamBean(session,textParamModel);
		reqModel.setMsg(message.getMsg());
		reqModel.getUserParam().add(textParamModel);
		
		sessionService.update(session, null, null);
		
		if ("0".equals(flow.getInterfaceType())) {
			respModel = ServiceClient.clientImpl(flow.getUrl(), flow.getFlowInterface(), reqModel);
		} else if ("1".equals(flow.getInterfaceType())) {
			respModel = ServiceClient.httpClientImpl(flow.getUrl(), reqModel);
		}
		
		if(respModel == null) {
			log.info("第三方接口未返回数据");
			return null;
		}
		
		
		
		if (respModel.getFlowParam() != null
				&& respModel.getFlowParam().size() > 0) {
			// 将参数保留在会话中
			session.setFlowParam(respModel.getFlowParam());
		}
		
		// 更新会话
		this.updateSessionFlow(session,"1".equals(respModel.getResult()) ? Constants.FLOW_ERROR : Constants.FLOW_SUCCEED,
				respModel.getCode(), textParamModel);
		
		return (ResponseMessage) respModel.getMsg();
	}
	

	/**
	 * 检查超出限制
	 * @param session
	 * @param flow
	 * @return
	 */
	public String checkLimit(SessionInfoExt session, Flow flow) {
		
		if(flow == null)
			return null;
		
		//超出次数限制
		if ("1".equals(flow.getErrorType())) {
			if (session.getErrorNum() >= Integer.parseInt(flow.getErrorValue())) {
				session.invalidate();
				return "您已超过最大错误次数，请重新选择操作";
			}
		}
		
		//超出时间限制
		if ("2".equals(flow.getErrorType())) {
			long time = session.getOptTime().getTime();
			int value = Integer.parseInt(flow.getErrorValue());
			if (new Date().getTime() > (time + value)) {
				session.invalidate();
				return "您的操作超时，请重新选择操作";
			}
		}

		return null;
	}
	
	/**
	 * 更新会话
	 * 
	 * @param session
	 *            会话
	 * @param isError
	 * @param code
	 * @param parameter
	 */
	public void updateSessionFlow(SessionInfoExt session, String isError,
			String code, BaseParamModel parameter) throws Exception {
		Flow flow = null;
		List flowList = null;
		Flow childFlow = null;

		// 成功
		if (isError.equals(Constants.FLOW_SUCCEED)) {
			flow = session.getCurrentFlow();
			flow = (Flow)dataInitService.get(DataInitService.KEY_FLOW, flow != null ? flow.getFlowId() : null);
			session.setErrorNum(0);
			if (flow != null) {
				flowList = flow.getSubflow();
			}
			// 存在下级节点
			if (flowList != null && flowList.size() > 0) {

				// 如果code是END直接结束会话
				if (Constants.FLOW_END.equals(code)) {
					this.endFlowSession(session);
				}
				// 如果存在流程分支，会话中保存分支对应的流程
				else if (!"".equals(code)) {
					for (int i = 0; i < flowList.size(); i++) {
						childFlow = (Flow) flowList.get(i);
						childFlow = (Flow) dataInitService.get(DataInitService.KEY_FLOW, childFlow.getFlowId());
						if (code.equals(childFlow.getFlowCode())) {
							break;
						}
					}
				}

				// 如果没有流程分支取集合的唯一一个下给流程节点
				else {
					childFlow = (Flow) flowList.get(0);
				}
				// 如果当前流程需要保存参数，则把参数保存在内存中
				if ("1".equals(flow.getIsParameter()) && parameter != null) {
					List param = session.getParameter();
					param.add(parameter);
					session.setParameter(param);
				}

			}
			// 不存在下级节点，流程结束
			else {
				endFlowSession(session);
			}

		} else {
			if ("1".equals(session.getCurrentFlow().getErrorType())) {
				session.setErrorNum(session.getErrorNum() + 1);
			}
		}

		// 如果找到分支流程，把流程信息更新到内存中
		if(childFlow != null)
			session.setCurrentFlow(childFlow);
		sessionService.update(session, null, null);
	}
	

	/**
	 * 流程结束，清楚会话中的流程
	 * 
	 * @param session
	 */
	public void endFlowSession(SessionInfoExt session) throws Exception {
		session.invalidate();
		sessionService.update(session, null, null);
	}
	
	


	/**
	 * 记录日志
	 * 
	 * @param flowLog
	 */
	public void flowLog(SessionInfoExt session) {
		
		FlowLog flowLog = new FlowLog();
		String menuId = session.getCurrentMenu().getMenuId();
		String flowId = session.getCurrentFlow().getFlowId();
		String userParam = null;
		String flowParam = null;
		if(CollectionUtils.isNotEmpty(session.getUserParam())) {
			userParam = JSONObject.toJSONString(session.getUserParam());
		}
		if(CollectionUtils.isNotEmpty(session.getFlowParam())) {
			flowParam = JSONObject.toJSONString(session.getFlowParam());
		}
		if(flowId != null) {
			flowLog.setFlowId(flowId.matches("\\d*") ? flowId : "");
		}
		if(menuId != null) {
			flowLog.setMenuId(menuId.matches("\\d*") ? menuId : "");
		}
		flowLog.setFlowParameter(flowParam);
		flowLog.setUserParameter(userParam);
		flowLog.setOpenId(session.getSendAccount());
		flowLog.setsId(session.getSessionId());
		flowLog.setPlatform(session.getChannelCode());
		flowLogDAO.insert(flowLog);
	}
	
	
	
	

	/**
	 * 请求参数
	 * 
	 * @param flow
	 * @param param
	 * @return
	 */
	public ReqServiceModel requestParamBean(SessionInfoExt session, BaseParamModel message) {
		Flow flow;
		ReqServiceModel reqModel = new ReqServiceModel();
		reqModel.setUserID(session.getSendAccount());
		reqModel.setMenuID(session.getCurrentMenu() != null ? session.getCurrentMenu().getMenuId() : null);
		reqModel.setFlowID(session.getCurrentFlow() != null ? String.valueOf(session.getCurrentFlow().getFlowId()) : null);
		reqModel.setSessionID(session.getSessionId());
		reqModel.setPlatform(session.getChannelCode());
		reqModel.setAppID(session.getAcceptedAccount());
		reqModel.setCallbackUrl(session.getCallbackUrl());
		reqModel.setFollowData(session.getFollowData());
		// 判断是否有临时flow参数如果有传给后台的
		if (session.getFlowParamTemp() != null && session.getFlowParamTemp().size() > 0) {
			reqModel.setFlowParam(session.getFlowParamTemp());
			session.setFlowParamTemp(null);
		} else {
			reqModel.setFlowParam(session.getFlowParam());
		}
		
		flow = session.getCurrentFlow();
		if (flow != null && StringUtils.isNotBlank(flow.getIsInterface())
				&& "1".equals(flow.getIsInterface())) {
			if (session.getParameter() != null
					&& session.getParameter().size() > 0)
				reqModel.setUserParam(session.getParameter());
		}
		if (message != null)
		{
			if (reqModel.getUserParam() == null) {
				reqModel.setUserParam(new ArrayList());
				}
			reqModel.getUserParam().add(message);
		}
		return reqModel;
	}


	/**
	 * 获取菜单并判断是否系统菜单
	 */
	public Menu getCurrentMenu(String menuId) {
		Menu menu = (Menu)dataInitService.get(DataInitService.KEY_MENU, menuId);
		if(menu == null) {
			log.info("menu not found by id[" + menuId + "]");
			return null;
		}
		
		if(StringUtils.isNotEmpty(menu.getFlowId()))
			return menu;
		
		if(Constants.IN_AGENT.equals(menu.getMenuCode())) {
			MenuBusi condition  = new MenuBusi();
			condition.setMenuId(new BigDecimal(menu.getMenuId()));
			
			List<MenuBusi> businessList = menuBusiDAO.query(condition);
			
			if(businessList != null && ! businessList.isEmpty()) 
				menu.setBusinessType(String.valueOf(businessList.get(0).getBusiId()));
		}
		
		return menu;
	}
	
	/**
	 * 上级菜单
	 * 
	 * @param menuId
	 * @return
	 */
	public String childMenu(SessionInfoExt session, String menuId, String info) throws Exception{

		StringBuffer str = new StringBuffer();
		List<Menu> allList = session.getAllMenuList();
		if (allList == null) {
			allList = new ArrayList<Menu>();
		}
		Menu menuChild = null;
		if (StringUtils.isNotBlank(info)) {
			str.append(info + "\n");
		}
		List<Menu> list = session.getCurrentMenu().getSubmenu();
		int size = allList.size();
		if (list != null && list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				menuChild = (Menu) list.get(i);
				str.append((size + i + 1) + ") " + menuChild.getMenuName()
						+ "\n");
				menuChild.setNo((size + i + 1) + "");
				menuChild.setNoType(Constants.MENU_TYPE_MENU);
				allList.add(menuChild);

			}
		}
		session.setAllMenuList(allList);
		session.getCurrentMenu().setSubmenu(list);
		session.setCurrentFlow(null);
		sessionService.update(session, null, null);
		return str.toString();
	}
	

	/**
	 * 获取菜单流程
	 * 
	 * @param inStr
	 * @param session
	 * @param flag
	 * @return
	 */
	public ResponseMessage menuFlow(SessionInfoExt session, String menuId, PlatformMessage message) throws Exception{

		Menu menu = getCurrentMenu(menuId);
		if(menu == null)
			throw new RuntimeException("menu not found by id " + menuId);
		session.setCurrentMenu(menu);
		
		Flow flow = (Flow) dataInitService.get(DataInitService.KEY_FLOW, menu.getFlowId());
		if(flow == null) {
			if(StringUtils.isNotEmpty(menu.getMenuCode())) {
				log.info("execute system flow [" + menu.getMenuCode() + "]");
				return SystemFlowService.getFlowService(menu.getMenuCode()).service(message, session);
			}
			throw new RuntimeException("flow not found by id " + menu.getFlowId());
		}
		
		session.setCurrentFlow(flow);

		sessionService.update(session, null, null);
		
		// 执行流程
		return this.executeFlow(session, message, flow);
	}

}
