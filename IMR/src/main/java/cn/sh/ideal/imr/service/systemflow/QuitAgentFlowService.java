/**
 * 
 */
package cn.sh.ideal.imr.service.systemflow;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.common.Constants;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.service.SessionService;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.util.TipTypes;

/**
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2016年4月12日
 */
@Service
public class QuitAgentFlowService extends SystemFlowService {

	@Autowired
	private SessionService sessionService;
	
	@Value("${msg.quit}")
	private String quitMsg;

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.systemflow.SystemFlowService#init()
	 */
	@Override
	@PostConstruct
	protected void init() {
		register(Constants.OUT_AGENT, this);
	}

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.systemflow.SystemFlowService#service(cn.sh.ideal.imr.entity.PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage service(PlatformMessage message, SessionInfoExt session) throws Exception {
		
		if(SessionStatus.SELF.getCode().equals(session.getStatus()))
			return null;
		flowLog(session, Constants.OUT_AGENT);
		sessionService.update(session, SessionStatus.USER_CLOSE, null);
		
		return new ResponseTextMessage(quitMsg,TipTypes.USERSENDSHARP.getCode());
	}

	
}
