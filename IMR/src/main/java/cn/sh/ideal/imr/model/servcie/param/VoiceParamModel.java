/**
 * 
 */
package cn.sh.ideal.imr.model.servcie.param;

import cn.sh.ideal.imr.base.Constants;

/**
 * <AUTHOR>
 *
 */
public class VoiceParamModel extends BaseParamModel {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String mediaUrl;

	
	public VoiceParamModel() {
		super();
	}
	

	public VoiceParamModel(String mediaUrl) {
		super();
		super.setMsgType(Constants.REQ_MESSAGE_TYPE_VOICE);
		this.mediaUrl = mediaUrl;
	}


	public String getMediaUrl() {
		return mediaUrl;
	}


	public void setMediaUrl(String mediaUrl) {
		this.mediaUrl = mediaUrl;
	}


	@Override
	public String toString() {
		return "VoiceParamModel [mediaUrl=" + mediaUrl + ", toString()="
				+ super.toString() + "]";
	}


	
	
}
