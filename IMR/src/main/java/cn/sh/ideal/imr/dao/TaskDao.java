package cn.sh.ideal.imr.dao;

import java.util.List;

import cn.sh.ideal.imr.model.Task;

public interface TaskDao {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MA_TASK
     *
     * @mbggenerated Fri Dec 18 10:42:57 CST 2015
     */
    int insert(Task record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MA_TASK
     *
     * @mbggenerated Fri Dec 18 10:42:57 CST 2015
     */
    int insertSelective(Task record);

	List<Task> query(Task task);
}