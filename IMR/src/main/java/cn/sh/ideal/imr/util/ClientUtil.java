package cn.sh.ideal.imr.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

import org.apache.log4j.Logger;
import org.codehaus.xfire.client.Client;
import org.codehaus.xfire.transport.http.CommonsHttpMessageSender;

public class ClientUtil {
	/**
	 * HTTP-POST
	 */
	private static Logger log = Logger.getLogger(ClientUtil.class);
	private static long startTime;
	private static long endTime;

	public static String post(String url, String params) {
		OutputStreamWriter out = null;
		InputStream i_urlStream = null;
		OutputStream o_urlStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader bufferedReader = null;
		try {
			URL urlTemp = new URL(url);
			URLConnection connection = urlTemp.openConnection();
			connection.setConnectTimeout(30000);
			connection.setReadTimeout(30000);
			connection.setDoOutput(true);
			o_urlStream = connection.getOutputStream();
			out = new OutputStreamWriter(o_urlStream, "UTF-8");
			StringBuffer sb = new StringBuffer();
			sb.append(params);
			out.write(sb.toString());
			out.flush();
			i_urlStream = connection.getInputStream();
			String s;
			String xmlStr = "";
			inputStreamReader = new InputStreamReader(i_urlStream, "utf-8");
			bufferedReader = new java.io.BufferedReader(inputStreamReader);

			while ((s = bufferedReader.readLine()) != null) {
				xmlStr += s;
			}
			return xmlStr;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (out != null)
					out.close();
				if (bufferedReader != null)
					bufferedReader.close();
				if (inputStreamReader != null)
					inputStreamReader.close();
				if (o_urlStream != null)
					o_urlStream.close();
				if (i_urlStream != null)
					i_urlStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}

		}
		return "";
	}

	/**
	 * HTTP-SOAP
	 */

	public static String xfireSoap(String wsdlurl, String funName, String params) {
		Client client = null;
		HttpURLConnection connection = null;
		InputStream is = null;
		try {
			startTime = System.currentTimeMillis();
			URL url = new URL(wsdlurl);
			connection = (HttpURLConnection) url.openConnection();
			connection.setConnectTimeout(30000);// 连接超时时间，单位：毫秒
			connection.setReadTimeout(30000);// 读取数据超时时间，单位：毫秒
			connection.connect();
			is = connection.getInputStream();
			client = new Client(is, null);
			//客户端链接不复用（解决close_wait问题）
			client.setProperty(CommonsHttpMessageSender.DISABLE_KEEP_ALIVE, "true");
			Object[] result = client.invoke(funName, new Object[] { params });
			endTime = System.currentTimeMillis();
			log.info("----------调用" + funName + "接口消耗时长:"
					+ (endTime - startTime));
			return result[0].toString();
		} catch (Exception e) {
			e.printStackTrace();
			log.error("调用接口异常", e);
			return "";
		} finally {
			try {
				if (client != null)
					client.close();
				if (is != null)
					is.close();
				if (connection != null)
					connection.disconnect();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	public static void main(String[] args) {
		System.out
				.println(ClientUtil
						.xfireSoap(
								"http://10.4.250.57:8080/spdbcccWeChat/services/MicroPageService?wsdl",
								"getDreamerInfo", ""));
	}

}
