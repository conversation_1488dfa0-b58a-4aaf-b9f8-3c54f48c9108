package cn.sh.ideal.imr.model.servcie.param;

import cn.sh.ideal.imr.base.Constants;

public class LocationParamModel extends BaseParamModel {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String x;
	private String y ;
	private String label;
	public String getX() {
		return x;
	}
	public void setX(String x) {
		this.x = x;
	}
	public String getY() {
		return y;
	}
	public void setY(String y) {
		this.y = y;
	}
	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
	
	public LocationParamModel() {
		super();
		// TODO Auto-generated constructor stub
	}
	public LocationParamModel(String x, String y, String label) {
		super();
		super.setMsgType(Constants.REQ_MESSAGE_TYPE_LOCATION);
		this.x = x;
		this.y = y;
		this.label = label;
	}
	
	
	
}
