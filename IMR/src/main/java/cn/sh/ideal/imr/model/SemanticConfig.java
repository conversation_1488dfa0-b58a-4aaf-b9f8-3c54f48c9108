package cn.sh.ideal.imr.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class SemanticConfig implements Serializable{
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.ID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private BigDecimal id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.CH_ACCOUNT_ID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private Long chAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.SEMANTIC_TYPE
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private String semanticType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_URL
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private String semanticWebserviceUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_FUNCTION
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private String semanticWebserviceFunction;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.INTERFACE_APPID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private String interfaceAppid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_SEMANTIC_CONFIG.REMARK
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    private String remark;
    
    private String interfaceType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.ID
     *
     * @return the value of MGW_SEMANTIC_CONFIG.ID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public BigDecimal getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.ID
     *
     * @param id the value for MGW_SEMANTIC_CONFIG.ID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setId(BigDecimal id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.CH_ACCOUNT_ID
     *
     * @return the value of MGW_SEMANTIC_CONFIG.CH_ACCOUNT_ID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public Long getChAccountId() {
        return chAccountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.CH_ACCOUNT_ID
     *
     * @param chAccountId the value for MGW_SEMANTIC_CONFIG.CH_ACCOUNT_ID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setChAccountId(Long chAccountId) {
        this.chAccountId = chAccountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.SEMANTIC_TYPE
     *
     * @return the value of MGW_SEMANTIC_CONFIG.SEMANTIC_TYPE
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public String getSemanticType() {
        return semanticType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.SEMANTIC_TYPE
     *
     * @param semanticType the value for MGW_SEMANTIC_CONFIG.SEMANTIC_TYPE
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setSemanticType(String semanticType) {
        this.semanticType = semanticType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_URL
     *
     * @return the value of MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_URL
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public String getSemanticWebserviceUrl() {
        return semanticWebserviceUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_URL
     *
     * @param semanticWebserviceUrl the value for MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_URL
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setSemanticWebserviceUrl(String semanticWebserviceUrl) {
        this.semanticWebserviceUrl = semanticWebserviceUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_FUNCTION
     *
     * @return the value of MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_FUNCTION
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public String getSemanticWebserviceFunction() {
        return semanticWebserviceFunction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_FUNCTION
     *
     * @param semanticWebserviceFunction the value for MGW_SEMANTIC_CONFIG.SEMANTIC_WEBSERVICE_FUNCTION
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setSemanticWebserviceFunction(String semanticWebserviceFunction) {
        this.semanticWebserviceFunction = semanticWebserviceFunction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.INTERFACE_APPID
     *
     * @return the value of MGW_SEMANTIC_CONFIG.INTERFACE_APPID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public String getInterfaceAppid() {
        return interfaceAppid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.INTERFACE_APPID
     *
     * @param interfaceAppid the value for MGW_SEMANTIC_CONFIG.INTERFACE_APPID
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setInterfaceAppid(String interfaceAppid) {
        this.interfaceAppid = interfaceAppid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_SEMANTIC_CONFIG.REMARK
     *
     * @return the value of MGW_SEMANTIC_CONFIG.REMARK
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_SEMANTIC_CONFIG.REMARK
     *
     * @param remark the value for MGW_SEMANTIC_CONFIG.REMARK
     *
     * @mbggenerated Wed Apr 08 15:12:48 CST 2015
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

	public String getInterfaceType() {
		return interfaceType;
	}

	public void setInterfaceType(String interfaceType) {
		this.interfaceType = interfaceType;
	}
    
    
}