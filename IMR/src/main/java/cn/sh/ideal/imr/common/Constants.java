package cn.sh.ideal.imr.common;


public interface Constants {
	
    /**
     * 微信平台标识
     */
	public final static String PLATFORM_WECHAT ="WX";
	/**
	 * 易信平台标识
	 */
	public final static String PLATFORM_YIXIN ="YX";
	/**
	 * 支付宝平台标识
	 */
	public final static String PLATFORM_ALIPAY ="ALIPAY";
	/**
	 * 企业号平台标识
	 */
	public final static String PLATFORM_QY ="QY";
	
	/**
	 * 语义分析平台返回的消息类型,文本消息
	 */
	public final static  String SEMANTIC_RESP_TYPE_CONTENT="0";
	/**
	 * 语义分析平台返回的消息类型,标准问一类
	 */
	public final static  String SEMANTIC_RESP_TYPE_ASK="1";
	
	/**
	 *  语义分析平台返回的消息类型,标准问一类   (小I语义分析特殊要求)
	 */
	public final static  String SEMANTIC_RESP_TYPE_ASK_23="23";
	
	/**
	 * 语义分析平台返回的消息类型,指令码一类
	 */
	public final static  String SEMANTIC_RESP_TYPE_INSTRUCTION="2";
	
	/**
	 * 流程结束标识
	 */
	public final static String FLOW_END="END";
	/**
	 * 流程成功标识
	 */
	public final static String FLOW_SUCCEED="ok";
	/**
	 * 流程错误标识
	 */
	public final static String FLOW_ERROR="error";
	
	/**
	 * 不使用语音平台
	 */
	public final static String SEMANTIC_TYPE_DISABLE ="0";
	
	
	/**
	 * 小I语义语音分析平台
	 */
	public final static String SEMANTIC_TYPE_XIAOI ="1";
	
	/**
	 * 科大讯飞语音分析平台
	 */
	public final static String SEMANTIC_TYPE_XUNFEI ="2";
	/**
	 * 取消关注类型
	 */
	public final static String UNSUBSCRIBE_TYPE ="0";
	/**
	 * 全局menu列表的编号类型：菜单类型
	 */
	public final static String MENU_TYPE_MENU ="0";
	/**
	 * 全局menu列表的编号类型：相关问
	 */
	public final static String MENU_TYPE_ASK ="1";
	
	
	
	/************************************** MENU_CODE ******************************************/
	
	/**
	 * 进入人工标识
	 */
	public final static String IN_AGENT = "IN_AGENT";
	
	/**
	 * 退出人工
	 */
	public final static String OUT_AGENT = "OUT_AGENT";
	/**
	 * 关注
	 */
	public final static String SUBSCRIBE = "SUBSCRIBE";
	
	/**
	 * 取消关注
	 */
	public final static String UNSUBSCRIBE = "UNSUBSCRIBE";
	
	/**
	 * 扫码
	 */
	public final static String SCAN = "SCAN";
	
	
	/**
	 * 图片流程
	 */
	public final static String MENU_IMAGE = "MENU_IMAGE";
	
	
	/**
	 * 语音流程
	 */
	public final static String MENU_VOICE = "MENU_VOICE";
	
	
}
