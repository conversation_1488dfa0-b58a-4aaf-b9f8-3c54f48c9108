package cn.sh.ideal.imr.service.impl;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.thoughtworks.xstream.XStream;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.dao.FlowDAO;
import cn.sh.ideal.imr.dao.MenuDAO;
import cn.sh.ideal.imr.dao.SemanticConfigDAO;
import cn.sh.ideal.imr.model.Flow;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.SemanticConfig;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.util.UrlFormat;

@Service("dataInitService")
public class DataInitService {
	/**
	 * 一级KEY
	 */
	public static final String KEY_SEMANTIC = "IMR_SEMANTIC_CONFIG";

	public static final String KEY_MENU = "IMR_MENU";

	public static final String KEY_FLOW = "IMR_FLOW";

	public static final String KEY_CHANNEL = "IMR_CHANNEL";

	public static final String KEY_KEYWORD_MENU = "IMR_KEYWORD_MENU";

	private static Logger log = Logger.getLogger(DataInitService.class);

	@Autowired
	private FlowDAO flowDAO;

	@Autowired
	private MenuDAO menuDAO;

	@Autowired
	private SemanticConfigDAO semanticConfigDAO;

	@Autowired
	private SysInitService sysInitService;

	@Autowired
	private RedisDao<String, Serializable> redisDao;



	public DataInitService() {
	}

	/**
	 * 初始化菜单
	 */
	private void initMenu() {
		HashMap<Object, Object> menuMap = new HashMap<Object, Object>();

		Menu condition = new Menu();
		condition.setMenuStatus("1");

		List<Menu> list = menuDAO.query(condition);
		Menu menu;
		for (int i = 0; i < list.size(); i++) {
			menu = (Menu) list.get(i);
			condition.setParentId(menu.getMenuId());
			menu.setSubmenu(menuDAO.query(condition));
			menuMap.put(menu.getMenuId(), menu);

			if(StringUtils.isNotEmpty(menu.getMenuCode()) && StringUtils.isNotEmpty(menu.getChannelId())) {
				String [] channels = menu.getChannelId().split(",");
				for (String channel : channels) {
					menuMap.put(channel + "_" + menu.getMenuCode(), menu);
				}
			}
		}

		redisDao.saveValue(KEY_MENU, menuMap);

	}

	/**
	 * 初始化流程
	 */
	private void initFlow() {

		HashMap<Object, Object> flowMap = new HashMap<Object, Object>();
		Flow condition = new Flow();
		Flow flow = null;
		List<Flow> list = flowDAO.query(null);
		for (int i = 0; i < list.size(); i++) {
			flow = (Flow) list.get(i);
			condition.setParentId(flow.getFlowId().toString());

			flow.setSubflow(flowDAO.query(condition));

			flowMap.put(flow.getFlowId(), flow);
		}


		redisDao.saveValue(KEY_FLOW, flowMap);

	}

	/**
	 * 初始化关键字
	 */
	private void initMenuOfKeyword() {


		HashMap<Object, Map<String, Menu>> keywordMap = new HashMap<Object, Map<String, Menu>>();

		List<Menu> list = menuDAO.queryKeyword();

		if (CollectionUtils.isEmpty(list)) 
			return;

		String key;

		for (Menu item : list) {
			if(StringUtils.isEmpty(item.getKeywords()) 
					|| StringUtils.isEmpty(item.getChannelId()))
				continue;

			String [] keywords = item.getKeywords().split(";");
			String [] channels = item.getChannelId().split(",");

			for (String channel : channels) {

				key = item.getTenantId() + "_" + channel;

				Map<String, Menu> map = keywordMap.get(key);
				if(map == null) {
					map = new HashMap<String, Menu>();
					keywordMap.put(key, map);
				}

				for (int j = 0; j < keywords.length; j++) {
					if (StringUtils.isNotEmpty(keywords[j])) {
						map.put(StringUtils.upperCase(keywords[j]), item);
					}
				}
			}
		}

		redisDao.saveValue(KEY_KEYWORD_MENU, keywordMap);
	}


	public String getMenuIdByKeyword(String tenantId, String channelId, String keyword) {
		if(StringUtils.isEmpty(keyword))
			return "";

		HashMap<Object, Map<String, Menu>> keywordMap = (HashMap<Object, Map<String, Menu>>)redisDao.readValue(KEY_KEYWORD_MENU);

		if(keywordMap == null)
			return "";

		Map<String, Menu> map = keywordMap.get(tenantId + "_" + channelId);
		if(map == null)
			return "";

		Menu menu = map.get(keyword);
		Date currentDate = new Date();
		if(menu != null && (menu.getStartDate() == null || menu.getStartDate().before(currentDate)) && (menu.getEndDate() == null || menu.getEndDate().after(currentDate)))
			return menu.getMenuId();

		for(Map.Entry<String, Menu> entry : map.entrySet()) {
			if ((keyword.contains(entry.getKey())) || ((keyword.matches(entry.getKey())) && 
					(((menu = (Menu)entry.getValue()).getStartDate() == null) || (menu.getStartDate().before(currentDate))) && (
							(menu.getEndDate() == null) || (menu.getEndDate().after(currentDate))))) {
				return menu.getMenuId();
			}
			/*if(keyword.contains(entry.getKey()) && ((menu = entry.getValue()).getStartDate() == null || menu.getStartDate().before(currentDate)) && (menu.getEndDate() == null || menu.getEndDate().after(currentDate))) {
				return menu.getMenuId();
			}*/
		}
		return "";
	}



	public Object get(String firstKey, Object secondKey) {
		try {
			if(KEY_CHANNEL.equals(firstKey)) {
				return sysInitService.getChannelInfo(String.valueOf(secondKey));
			}

			Map<Object, Object> map = (Map<Object, Object>) redisDao.readValue(firstKey);

			if(map != null)
				return map.get(secondKey);

		} catch (Exception e) {
			log.error("获取缓存失败,key=" + firstKey + secondKey, e);
		}

		return null;
	}

	/**
	 * 语意分析配置
	 */
	public void initSemanticConfig() {
		List<SemanticConfig> semanticConfigs = semanticConfigDAO.query(null);
		if(semanticConfigs == null || semanticConfigs.isEmpty())
			return;
		HashMap<String, Object> data = new HashMap<String, Object>();
		SemanticConfig item;
		for (int i = 0; i < semanticConfigs.size(); i++) {
			item = semanticConfigs.get(i);
			data.put(String.valueOf(item.getChAccountId()), item);
		}

		redisDao.saveValue(KEY_SEMANTIC, data);
	}


	@PostConstruct
	public void init() {
		try {
			log.info("初始化数据");
			sysInitService.init(true, false, false, true, false);
			initFlow();
			initMenuOfKeyword();
			initMenu();
			initSemanticConfig();

		} catch (Exception e) {
			log.error("init error.", e);
		}
	}

}
