package cn.sh.ideal.imr.service.systemflow;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.imr.dao.MessageInfoDAO;
import cn.sh.ideal.imr.dao.NewTaskDao;
import cn.sh.ideal.imr.dao.PushLogDao;
import cn.sh.ideal.imr.dao.SkillQueueDao;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.NewTask;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.model.SkillQueue;
import cn.sh.ideal.imr.model.servcie.param.TextParamModel;
import cn.sh.ideal.imr.service.SessionService;
import cn.sh.ideal.mir.service.MessageService;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionStatus;
import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AgentFlowService extends SystemFlowService
{

	@Value("${msg.sorting}")
	private String sortMsg;

	@Value("${msg.worktimeout}")
	private String worktimeoutMsg;

	@Autowired
	private SkillQueueDao skillQueueDao;

	@Autowired
	private SessionService sessionService;

	@Autowired
	private NewTaskDao taskDao;

	@Autowired
	private PushLogDao pushLogDao;

	@Autowired
	private MessageService dubboMessageService;

	@Autowired
	private MessageInfoDAO messageInfoDAO;

	@Autowired
	private RedisDao<String, Serializable> redisDao;


	@Value("${msg.alreadyagent}")
	private String alreadyagent;


	@PostConstruct
	protected void init()
	{
		register("IN_AGENT", this);
	}

	public ResponseMessage service(PlatformMessage message, SessionInfoExt session)
			throws Exception
	{
		if(StringUtils.isNotEmpty(session.getSkillQueue())){
			return new ResponseTextMessage(alreadyagent);
		}

		String requestContent = "请求人工服务.";
		String welcomeMessage = this.sortMsg;

		String userID = message.getOpenId();
		ChannelConfig channel = session.getChannel();

		if (StringUtils.isNotEmpty(message.getContent())) {
			requestContent = message.getContent();
		}

		MessageInfo messageInfo = new MessageInfo();
		messageInfo.setTenantCode(channel.getTenantCode());
		messageInfo.setChannelCode(channel.getChannelCode());
		messageInfo.setSessionId(session.getSessionId());
		messageInfo.setSendAccount(userID);
		messageInfo.setAcceptedAccount(channel.getChannelAccount());
		messageInfo.setMessageSource("3");
		messageInfo.setNickname(session.getNickname());
		messageInfo.setMsgType("text");
		messageInfo.setFollowData(message.getFollowData());
		if (CollectionUtils.isNotEmpty(session.getFlowParam())) {
			messageInfo.setFollowData(((TextParamModel)session.getFlowParam().get(0)).getContent());
		}

		if ((session.getCurrentMenu() != null) &&
				(StringUtils.isNotEmpty(session.getCurrentMenu().getSkillGroup())))
		{
			String skillQueue = session.getCurrentMenu().getSkillGroup();

			if (StringUtils.isNotEmpty(skillQueue)) {
				SkillQueue queue = this.skillQueueDao.selectByPrimaryKey(new BigDecimal(skillQueue));

				if (queue != null)
				{
					if ((StringUtils.isNotEmpty(queue.getWorkTime())) && (queue.getWorkTime().matches("\\d+.\\d+-\\d+.\\d+"))) {
						String[] strs = queue.getWorkTime().split("-");
						String worktimeStart = strs[0];
						String worktimeEnd = strs[1];
						double hours = Calendar.getInstance().get(11);
						double minute = Calendar.getInstance().get(12);

						double currenttime = hours + minute / 100.0D;

						if ((currenttime < Double.parseDouble(worktimeStart)) || (currenttime >= Double.parseDouble(worktimeEnd))) {
							return new ResponseTextMessage(this.worktimeoutMsg);
						}
					}

					if (StringUtils.isNotEmpty(queue.getWelcomeMessage())) {
						welcomeMessage = queue.getWelcomeMessage();
					}
				}

			}

			session.setSkillQueue(skillQueue);

			Map updateFields = new HashMap();
			updateFields.put("skillQueue", skillQueue);

			if (StringUtils.isNotEmpty(session.getCurrentMenu().getBusinessType())) {
				updateFields.put("businessType", session.getCurrentMenu().getBusinessType());
			}

			if (SessionStatus.SELF.getCode().equals(session.getStatus())) {
				this.sessionService.update(session, SessionStatus.SELF, updateFields);
			}
			messageInfo.setSkillQueue(skillQueue);
		}

		if (StringUtils.isNotEmpty(message.getContent())) {
			String content = message.getContent();

			NewTask task = new NewTask();
			task.setKeyWords(content);

			List tasks = this.taskDao.query(task);
			if (CollectionUtils.isNotEmpty(tasks)) {
				task = (NewTask)tasks.get(0);

				Map updateFields = new HashMap();
				updateFields.put("taskId", String.valueOf(task.getTaskId()));

				if (SessionStatus.SELF.getCode().equals(session.getStatus())) {
					this.sessionService.update(session, SessionStatus.SELF, updateFields);
				}
				JSONObject contentObj = new JSONObject();
				contentObj.put("taskId", String.valueOf(task.getTaskId()));
				messageInfo.setFollowData(contentObj.toString());
			}

		}

		messageInfo.setContent(requestContent);
		this.messageInfoDAO.insert(messageInfo);
		this.redisDao.listrPush("WAITING_REQUEST_MESSAGE", messageInfo);

		return new ResponseTextMessage(welcomeMessage);
	}
}
