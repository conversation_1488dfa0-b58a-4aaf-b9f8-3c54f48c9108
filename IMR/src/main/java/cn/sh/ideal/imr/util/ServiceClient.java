package cn.sh.ideal.imr.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import cn.sh.ideal.imr.entity.Article;
import cn.sh.ideal.imr.entity.ResponseMusicMessage;
import cn.sh.ideal.imr.entity.ResponseNewsMessage;
import cn.sh.ideal.imr.model.req.TextMessage;
import cn.sh.ideal.imr.model.servcie.ReqServiceModel;
import cn.sh.ideal.imr.model.servcie.RespServiceModel;
import cn.sh.ideal.imr.model.servcie.param.TextParamModel;
import cn.sh.ideal.util.NetUtil;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

public class ServiceClient {

	private static final Logger log = Logger.getLogger(ServiceClient.class);
	
	public static RespServiceModel httpClientImpl(String url,ReqServiceModel reqServiceModel){
		RespServiceModel respServiceModel=null;
		JSONObject jsonObject;
		// 把实体转换成json
		String reqJson = JSONObject.fromObject(reqServiceModel).toString();
		String result = NetUtil.send(url, "POST",reqJson);
		
		if(StringUtils.isEmpty(result))
			return null;

		JsonConfig config = new JsonConfig();
		result=result.replaceAll("\r\n", "##").replaceAll("\\n", "##").replaceAll("\r", "##");
		jsonObject = JSONObject.fromObject(result);
		Map<String, Class> classMap = new HashMap<String, Class>();
		classMap.put("flowParam", TextParamModel.class);
		if ("text".equals(jsonObject.getJSONObject("msg").get("msgType"))) {
			classMap.put("msg", TextMessage.class);
		} else if ("music".equals(jsonObject.getJSONObject("msg").get(
				"msgType"))) {
			JSONObject msg=jsonObject.getJSONObject("msg");
			JSONObject music=new JSONObject();
			music.put("title", msg.get("title"));
			music.put("description", msg.get("description"));
			music.put("musicUrl", msg.get("musicUrl"));
			music.put("hQMusicUrl", msg.get("hqMusicUrl"));
			msg.put("music", music);
			msg.remove("title");
			msg.remove("description");
			msg.remove("musicUrl");
			msg.remove("hqMusicUrl");
			
			
			classMap.put("msg", ResponseMusicMessage.class);
		} else if ("news".equals(jsonObject.getJSONObject("msg").get(
				"msgType"))) {
			classMap.put("msg", ResponseNewsMessage.class);
			classMap.put("articles", Article.class);
			
		}

		config.setClassMap(classMap);
	try{
		 respServiceModel = (RespServiceModel) JSONObject
				.toBean(jsonObject, RespServiceModel.class, classMap);
	}catch(Exception e){
		log.error("解析出现问题", e);
	}
		return respServiceModel;
	
	}

	public static RespServiceModel clientImpl(String wsdlurl, String funName,
			ReqServiceModel reqServiceModel) {
		RespServiceModel respServiceModel=null;
		JSONObject jsonObject;
		// 把实体转换成json
		String reqJson = JSONObject.fromObject(reqServiceModel).toString();
		log.info(String.format("请求地址[%s],请求方法[%s],请求的json[%s]", wsdlurl, funName, reqJson));
		// 调用接口并获取返回的json字符串
		String request = ClientUtil.xfireSoap(wsdlurl, funName, reqJson);
//		String request = "{\"result\":\"0\",\"code\":\"\",\"type\":\"0\",\"menu\":\"FRTSE\",\"flowParam\":[{\"msgType\":\"text\",\"content\":\"文本\",\"paramName\":\"\"}],\"contextMenu\":\"ABCD,BCDE,NEF\",\"msg\":{\"msgType\":\"news\",\"articleCount\":\"4\",\"articles\":[{\"title\":\"sfdsfd\",\"description\":\"afafdsafdsafdsafdsafdsafsafsaf\",\"picUrl\":\"http://***************:8080/uploadFile/img/\",\"url\":\"http://***************:8080/WX_CUST_WEBSERVICE/selfserv/selfserviceAction.do?action=showHotInfomation&id=2382\"},{\"title\":\"靓号网厅淘存1元赠10元\",\"description\":\"活动期间客户通过网上营业厅（http://www.189.cn）自助选号成功后，可以自愿选择预存1元话费，成功办理入网后，一次性赠送客户10元话费（用户账户为11元，含1元预存+10元赠费）。领取的\",\"picUrl\":\"http://***************:8080/uploadFile/img/2e2f_88d0df66_9bcc_e8f1_92a4_1cd9d807d543_1[1]_201309241511.jpg\",\"url\":\"http://***************:8080/WX_CUST_WEBSERVICE/selfserv/selfserviceAction.do?action=showHotInfomation&id=201\"},{\"title\":\"11111113333we\",\"description\":\"213121213333\",\"picUrl\":\"http://***************:8080/uploadFile/img/\",\"url\":\"http://***************:8080/WX_CUST_WEBSERVICE/selfserv/selfserviceAction.do?action=showHotInfomation&id=2367\"},{\"title\":\"麦蒂：当初留在猛龙可以争冠\",\"description\":\"早报讯　特雷西·麦克格雷迪已经从NBA退役，他的职业生涯无疑有很多遗憾。日前，麦蒂（微博）接受了加拿大记者采访，表示自己如果不离开多伦多猛龙，就会有夺冠的机会。如果把时光倒回2000年的春天，那么你\",\"picUrl\":\"http://***************:8080/uploadFile/img/u4933p6t12d6783286f44dt20130915081701_201309151506.jpg\",\"url\":\"http://***************:8080/WX_CUST_WEBSERVICE/selfserv/selfserviceAction.do?action=showHotInfomation&id=241\"}]}}";
	
		log.info("返回的消息："+request);
		
		JsonConfig config = new JsonConfig();
		if (!"".equals(request)) {
			request=request.replaceAll("\r\n", "##").replaceAll("\\n", "##").replaceAll("\r", "##");
			jsonObject = JSONObject.fromObject(request);
			Map<String, Class> classMap = new HashMap<String, Class>();
			classMap.put("flowParam", TextParamModel.class);
			
			if(jsonObject.get("msg").getClass() != JSONObject.class) {
				jsonObject.put("msg", new TextMessage());
				classMap.put("msg", TextMessage.class);
			}
			
			if ("text".equals(jsonObject.getJSONObject("msg").get("msgType"))) {
				classMap.put("msg", TextMessage.class);
			} else if ("music".equals(jsonObject.getJSONObject("msg").get(
					"msgType"))) {
				JSONObject msg=jsonObject.getJSONObject("msg");
				JSONObject music=new JSONObject();
				music.put("title", msg.get("title"));
				music.put("description", msg.get("description"));
				music.put("musicUrl", msg.get("musicUrl"));
				music.put("hQMusicUrl", msg.get("hqMusicUrl"));
				msg.put("music", music);
				msg.remove("title");
				msg.remove("description");
				msg.remove("musicUrl");
				msg.remove("hqMusicUrl");
				
				
				classMap.put("msg", ResponseMusicMessage.class);
			} else if ("news".equals(jsonObject.getJSONObject("msg").get(
					"msgType"))) {
				classMap.put("msg", ResponseNewsMessage.class);
				classMap.put("articles", Article.class);
				
			}
			config.setClassMap(classMap);
		try{
			 respServiceModel = (RespServiceModel) JSONObject
					.toBean(jsonObject, RespServiceModel.class, classMap);
		}catch(Exception e){
			log.error("解析出现问题", e);
		}
			return respServiceModel;
		}else {
			return respServiceModel;
		}
	}

}
