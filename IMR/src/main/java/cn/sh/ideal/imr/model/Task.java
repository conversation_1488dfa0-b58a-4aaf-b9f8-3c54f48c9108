package cn.sh.ideal.imr.model;

import java.util.Date;

public class Task {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.ID
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.TYPE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.TITLE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.STATUS
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String status;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.TASK_DES
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String taskDes;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.BEGIN_TIME
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private Date beginTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.END_TIME
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private Date endTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.URL
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String url;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.TASK_CODE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String taskCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.TENANT_CODE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String tenantCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.CONTENT
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String content;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.SKILL
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String skill;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.PUSH
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String push;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.TRIGGER_DES
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String triggerDes;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column MA_TASK.CONTENT_TYPE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	private String contentType;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.ID
	 * @return  the value of MA_TASK.ID
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.ID
	 * @param id  the value for MA_TASK.ID
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.TYPE
	 * @return  the value of MA_TASK.TYPE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.TYPE
	 * @param type  the value for MA_TASK.TYPE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.TITLE
	 * @return  the value of MA_TASK.TITLE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.TITLE
	 * @param title  the value for MA_TASK.TITLE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.STATUS
	 * @return  the value of MA_TASK.STATUS
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.STATUS
	 * @param status  the value for MA_TASK.STATUS
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.TASK_DES
	 * @return  the value of MA_TASK.TASK_DES
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getTaskDes() {
		return taskDes;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.TASK_DES
	 * @param taskDes  the value for MA_TASK.TASK_DES
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setTaskDes(String taskDes) {
		this.taskDes = taskDes;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.BEGIN_TIME
	 * @return  the value of MA_TASK.BEGIN_TIME
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public Date getBeginTime() {
		return beginTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.BEGIN_TIME
	 * @param beginTime  the value for MA_TASK.BEGIN_TIME
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.END_TIME
	 * @return  the value of MA_TASK.END_TIME
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public Date getEndTime() {
		return endTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.END_TIME
	 * @param endTime  the value for MA_TASK.END_TIME
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.URL
	 * @return  the value of MA_TASK.URL
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getUrl() {
		return url;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.URL
	 * @param url  the value for MA_TASK.URL
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setUrl(String url) {
		this.url = url;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.TASK_CODE
	 * @return  the value of MA_TASK.TASK_CODE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getTaskCode() {
		return taskCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.TASK_CODE
	 * @param taskCode  the value for MA_TASK.TASK_CODE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setTaskCode(String taskCode) {
		this.taskCode = taskCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.TENANT_CODE
	 * @return  the value of MA_TASK.TENANT_CODE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getTenantCode() {
		return tenantCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.TENANT_CODE
	 * @param tenantCode  the value for MA_TASK.TENANT_CODE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.CONTENT
	 * @return  the value of MA_TASK.CONTENT
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getContent() {
		return content;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.CONTENT
	 * @param content  the value for MA_TASK.CONTENT
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setContent(String content) {
		this.content = content;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.SKILL
	 * @return  the value of MA_TASK.SKILL
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getSkill() {
		return skill;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.SKILL
	 * @param skill  the value for MA_TASK.SKILL
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setSkill(String skill) {
		this.skill = skill;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.PUSH
	 * @return  the value of MA_TASK.PUSH
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getPush() {
		return push;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.PUSH
	 * @param push  the value for MA_TASK.PUSH
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setPush(String push) {
		this.push = push;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.TRIGGER_DES
	 * @return  the value of MA_TASK.TRIGGER_DES
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getTriggerDes() {
		return triggerDes;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.TRIGGER_DES
	 * @param triggerDes  the value for MA_TASK.TRIGGER_DES
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setTriggerDes(String triggerDes) {
		this.triggerDes = triggerDes;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column MA_TASK.CONTENT_TYPE
	 * @return  the value of MA_TASK.CONTENT_TYPE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public String getContentType() {
		return contentType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column MA_TASK.CONTENT_TYPE
	 * @param contentType  the value for MA_TASK.CONTENT_TYPE
	 * @mbggenerated  Fri Dec 18 14:51:53 CST 2015
	 */
	public void setContentType(String contentType) {
		this.contentType = contentType;
	}
}