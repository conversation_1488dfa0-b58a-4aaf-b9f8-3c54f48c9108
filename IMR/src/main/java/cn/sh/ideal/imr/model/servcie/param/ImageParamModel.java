/**
 * 
 */
package cn.sh.ideal.imr.model.servcie.param;

import cn.sh.ideal.imr.base.Constants;

/**
 * <AUTHOR>
 *
 */
public class ImageParamModel extends BaseParamModel {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String picUrl;

	
	public ImageParamModel() {
		super();
	}
	

	public ImageParamModel(String picUrl) {
		super();
		super.setMsgType(Constants.REQ_MESSAGE_TYPE_IMAGE);
		this.picUrl = picUrl;
	}


	public String getPicUrl() {
		return picUrl;
	}


	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}


	@Override
	public String toString() {
		return "ImageParamModel [picUrl=" + picUrl + ", toString()="
				+ super.toString() + "]";
	}


	
	
}
