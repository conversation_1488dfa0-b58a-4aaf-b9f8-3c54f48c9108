package cn.sh.ideal.imr.dao;

import java.math.BigDecimal;

import cn.sh.ideal.imr.model.SkillQueue;

public interface SkillQueueDao {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MGW_TENANT_SKILL_QUEUE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    int deleteByPrimaryKey(BigDecimal id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MGW_TENANT_SKILL_QUEUE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    int insert(SkillQueue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MGW_TENANT_SKILL_QUEUE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    int insertSelective(SkillQueue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MGW_TENANT_SKILL_QUEUE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    SkillQueue selectByPrimaryKey(BigDecimal id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MGW_TENANT_SKILL_QUEUE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    int updateByPrimaryKeySelective(SkillQueue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table MGW_TENANT_SKILL_QUEUE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    int updateByPrimaryKey(SkillQueue record);
}