/**
 * 
 */
package cn.sh.ideal.imr.service.message.event;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.Flow;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.service.impl.DataInitService;
import cn.sh.ideal.imr.service.message.MessageService;
import cn.sh.ideal.imr.service.systemflow.SystemFlowService;

/**
 * Click事件处理
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月31日
 */
@Service
public class ClickEventMessageService extends MessageService {
	

	private static Logger log = Logger.getLogger(ClickEventMessageService.class);
	
	@PostConstruct
	public void init() {
		MessageService.register("click", this);
	}

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.message.MessageService#handle(cn.sh.ideal.imr.imr.entity.PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception {

			Menu menu = getCurrentMenu(message.getEventId());
			
			if(menu == null) 
				throw new Exception("menu not found by " + message.getEventId());
			
			session.setCurrentMenu(menu);
			
			Flow flow = (Flow) dataInitService.get(DataInitService.KEY_FLOW, menu.getFlowId());
			
			//1，返回子菜单
			if(flow == null) {
				
				if(StringUtils.isNotEmpty(menu.getMenuCode())) {
					log.info("execute system flow [" + menu.getMenuCode() + "]");
					return SystemFlowService.getFlowService(menu.getMenuCode()).service(message, session);
				}
				
				String content = childMenu(session, menu.getMenuId(), msgMenuList);
				return new ResponseTextMessage(content);
			}
			
			//2，执行流程
			/** 注入自助发起流程接口传入的参数  */
			flow.setParameters(message.getParameters());

			session.setCurrentFlow(flow);
			sessionService.update(session, null, null);
			
			return executeFlow(session, message, flow);
		
	}
	
}
