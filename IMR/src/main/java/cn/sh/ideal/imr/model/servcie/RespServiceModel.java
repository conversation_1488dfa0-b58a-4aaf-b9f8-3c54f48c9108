package cn.sh.ideal.imr.model.servcie;

import java.util.List;
/**
 * 接口调用返回是实体
 * <AUTHOR>
 *
 */
public class RespServiceModel {
	private String result;
	private String code;
	private String type;
	private String menu;
	private List flowParam;
	private String contextMenu;
	private Object msg;
	private String flag;
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getMenu() {
		return menu;
	}
	public void setMenu(String menu) {
		this.menu = menu;
	}
	public String getContextMenu() {
		return contextMenu;
	}
	public void setContextMenu(String contextMenu) {
		this.contextMenu = contextMenu;
	}
	
	
	
	public Object getMsg() {
		return msg;
	}
	public void setMsg(Object msg) {
		this.msg = msg;
	}
	public List getFlowParam() {
		return flowParam;
	}
	public void setFlowParam(List flowParam) {
		this.flowParam = flowParam;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	@Override
	public String toString() {
		return "RespServiceModel [code=" + code + ", contextMenu="
				+ contextMenu + ", flowParam=" + flowParam + ", menu=" + menu
				+ ", msg=" + msg + ", result=" + result + ", type=" + type
				+ "]";
	}

	
	
	
	
	

}
