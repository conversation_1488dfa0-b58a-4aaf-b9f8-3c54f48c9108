package cn.sh.ideal.imr.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.dao.NewsInfoDAO;
import cn.sh.ideal.imr.entity.Article;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseNewsMessage;
import cn.sh.ideal.imr.model.NewsInfo;
import cn.sh.ideal.imr.service.NewsService;
import cn.sh.ideal.util.PropertiesUtil;

@Service("newsService")
public class NewsServiceImpl implements NewsService {

	@Autowired
	private NewsInfoDAO newsDao;
	private static Logger log = Logger.getLogger(NewsServiceImpl.class);
	/**
	 * @param newsDao the newsDao to set
	 */
	public void setNewsDao(NewsInfoDAO newsDao) {
		this.newsDao = newsDao;
	}


	@SuppressWarnings( { "unchecked" })
	public ResponseNewsMessage getNewsMessage(String id,PlatformMessage message) {
		List<NewsInfo> infolist = newsDao.getNewsMessage(id);
		Article article;
		if (infolist != null && infolist.size() > 0) {
			List<Article> articleList = new ArrayList<Article>();
			for (NewsInfo info : infolist) {
				article = new Article();
				article.setTitle(info.getTitle());
				String content = info.getContent();
				if (content != null) {
					content = content.length() > 100 ? content.substring(0,
							99) : content;
				}
				article.setDescription(content);
				article.setPicUrl(info.getPicurl());
//				String deskey=PropertiesUtil
//						.getProperty(PropertiesUtil.CONFIG, "DESKEY");
				String openId="";
//				try {
//					 openId=DesUtil.encrypt(message.getOpenId(), deskey);
//				} catch (Exception e) {
//					log.error(e.getMessage(), e);
//				}
				openId=message.getOpenId();
				String url=StringUtils.isNotEmpty(info.getExtendUrl()) ? info.getExtendUrl() : PropertiesUtil
						.getProperty(PropertiesUtil.CONFIG, "NEWS_MESSAGE_URL") + info.getId();
				String flag="?";
				if(url.indexOf("?")>0){
					flag="&";
				}
				article.setUrl(url+(StringUtils.isEmpty(openId)?"":flag+"openId="+openId));
				articleList.add(article);
			}
			return new ResponseNewsMessage(infolist.size(), articleList);
		}

		return null;
	}

	@Override
	public NewsInfo getNewsInfoById(String id,String openId) {
		NewsInfo news=newsDao.getNewsInfoById(id);
		news.setOpenId(openId);
		newsDao.insertNewsInfoLog(news);
		return news;
	}

}
