package cn.sh.ideal.imr.model;

import java.math.BigDecimal;
import java.util.Date;

public class PushLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private BigDecimal id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.USER_ACCOUNT
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private String userAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.SERVER_ACCOUNT
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private String serverAccount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.MARKET_TYPE
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private String marketType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.SEND_TIME
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private Date sendTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.TASK_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private String taskId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.SESSION_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private String sessionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.STATUS
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private Short status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MA_PUSH_LOG.CUSTOMER_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    private String customerId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.ID
     *
     * @return the value of MA_PUSH_LOG.ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public BigDecimal getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.ID
     *
     * @param id the value for MA_PUSH_LOG.ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setId(BigDecimal id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.USER_ACCOUNT
     *
     * @return the value of MA_PUSH_LOG.USER_ACCOUNT
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public String getUserAccount() {
        return userAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.USER_ACCOUNT
     *
     * @param userAccount the value for MA_PUSH_LOG.USER_ACCOUNT
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.SERVER_ACCOUNT
     *
     * @return the value of MA_PUSH_LOG.SERVER_ACCOUNT
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public String getServerAccount() {
        return serverAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.SERVER_ACCOUNT
     *
     * @param serverAccount the value for MA_PUSH_LOG.SERVER_ACCOUNT
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setServerAccount(String serverAccount) {
        this.serverAccount = serverAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.MARKET_TYPE
     *
     * @return the value of MA_PUSH_LOG.MARKET_TYPE
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public String getMarketType() {
        return marketType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.MARKET_TYPE
     *
     * @param marketType the value for MA_PUSH_LOG.MARKET_TYPE
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setMarketType(String marketType) {
        this.marketType = marketType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.SEND_TIME
     *
     * @return the value of MA_PUSH_LOG.SEND_TIME
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public Date getSendTime() {
        return sendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.SEND_TIME
     *
     * @param sendTime the value for MA_PUSH_LOG.SEND_TIME
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.TASK_ID
     *
     * @return the value of MA_PUSH_LOG.TASK_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.TASK_ID
     *
     * @param taskId the value for MA_PUSH_LOG.TASK_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.SESSION_ID
     *
     * @return the value of MA_PUSH_LOG.SESSION_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.SESSION_ID
     *
     * @param sessionId the value for MA_PUSH_LOG.SESSION_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.STATUS
     *
     * @return the value of MA_PUSH_LOG.STATUS
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public Short getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.STATUS
     *
     * @param status the value for MA_PUSH_LOG.STATUS
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MA_PUSH_LOG.CUSTOMER_ID
     *
     * @return the value of MA_PUSH_LOG.CUSTOMER_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MA_PUSH_LOG.CUSTOMER_ID
     *
     * @param customerId the value for MA_PUSH_LOG.CUSTOMER_ID
     *
     * @mbggenerated Fri Dec 18 16:08:03 CST 2015
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}