package cn.sh.ideal.imr.model;

import java.io.Serializable;

public class CustomerVisitLog implements Serializable{

	private static final long serialVersionUID = 1L;

	private String visitId;
	private String customerId;
	private String source;
	private String type;
	private String referer;
	private String operStartTime;
	private String operEndTime;
	private String operDuration;
	
	private String operArea;
	private String operActionType;
	private String operActionContent;
	private String operActionContentRemark;
	private String operReplayType;
	private String operReplayContent;
	private String operReplayContentRemark;
	private String operDate;
	
	private String tenantCode;
	private String account;
	private String channel;
	private String sessionId;
	
	
	
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getOperDate() {
		return operDate;
	}
	public void setOperDate(String operDate) {
		this.operDate = operDate;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getAccount() {
		return account;
	}
	public void setAccount(String account) {
		this.account = account;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getVisitId() {
		return visitId;
	}
	public void setVisitId(String visitId) {
		this.visitId = visitId;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getReferer() {
		return referer;
	}
	public void setReferer(String referer) {
		this.referer = referer;
	}
	public String getOperStartTime() {
		return operStartTime;
	}
	public void setOperStartTime(String operStartTime) {
		this.operStartTime = operStartTime;
	}
	public String getOperEndTime() {
		return operEndTime;
	}
	public void setOperEndTime(String operEndTime) {
		this.operEndTime = operEndTime;
	}
	public String getOperDuration() {
		return operDuration;
	}
	public void setOperDuration(String operDuration) {
		this.operDuration = operDuration;
	}
	public String getOperArea() {
		return operArea;
	}
	public void setOperArea(String operArea) {
		this.operArea = operArea;
	}
	public String getOperActionType() {
		return operActionType;
	}
	public void setOperActionType(String operActionType) {
		this.operActionType = operActionType;
	}
	public String getOperActionContent() {
		return operActionContent;
	}
	public void setOperActionContent(String operActionContent) {
		this.operActionContent = operActionContent;
	}
	public String getOperActionContentRemark() {
		return operActionContentRemark;
	}
	public void setOperActionContentRemark(String operActionContentRemark) {
		this.operActionContentRemark = operActionContentRemark;
	}
	public String getOperReplayType() {
		return operReplayType;
	}
	public void setOperReplayType(String operReplayType) {
		this.operReplayType = operReplayType;
	}
	public String getOperReplayContent() {
		return operReplayContent;
	}
	public void setOperReplayContent(String operReplayContent) {
		this.operReplayContent = operReplayContent;
	}
	public String getOperReplayContentRemark() {
		return operReplayContentRemark;
	}
	public void setOperReplayContentRemark(String operReplayContentRemark) {
		this.operReplayContentRemark = operReplayContentRemark;
	}
	

}
