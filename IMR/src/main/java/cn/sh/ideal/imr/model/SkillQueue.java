package cn.sh.ideal.imr.model;

import java.math.BigDecimal;

public class SkillQueue {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.ID
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private BigDecimal id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String queueName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String queueCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private Long queueSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.TENANT_ID
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String ableBusinessTypes;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String ableChannels;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.SORT_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String sortRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String allocationRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.STATUS
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.REMARK
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.IS_DEFAULT
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String isDefault;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.ROUTE_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String routeRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.SORT_SIZE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private Long sortSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.SKILL_TYPE_CODE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String skillTypeCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.WELCOME_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String welcomeMessage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.LINE_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String lineMessage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.CONNECT_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String connectMessage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column MGW_TENANT_SKILL_QUEUE.WORK_TIME
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    private String workTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.ID
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ID
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public BigDecimal getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.ID
     *
     * @param id the value for MGW_TENANT_SKILL_QUEUE.ID
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setId(BigDecimal id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getQueueName() {
        return queueName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     *
     * @param queueName the value for MGW_TENANT_SKILL_QUEUE.QUEUE_NAME
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getQueueCode() {
        return queueCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     *
     * @param queueCode the value for MGW_TENANT_SKILL_QUEUE.QUEUE_CODE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setQueueCode(String queueCode) {
        this.queueCode = queueCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public Long getQueueSize() {
        return queueSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     *
     * @param queueSize the value for MGW_TENANT_SKILL_QUEUE.QUEUE_SIZE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setQueueSize(Long queueSize) {
        this.queueSize = queueSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.TENANT_ID
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.TENANT_ID
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.TENANT_ID
     *
     * @param tenantId the value for MGW_TENANT_SKILL_QUEUE.TENANT_ID
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getAbleBusinessTypes() {
        return ableBusinessTypes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     *
     * @param ableBusinessTypes the value for MGW_TENANT_SKILL_QUEUE.ABLE_BUSINESS_TYPES
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setAbleBusinessTypes(String ableBusinessTypes) {
        this.ableBusinessTypes = ableBusinessTypes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getAbleChannels() {
        return ableChannels;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     *
     * @param ableChannels the value for MGW_TENANT_SKILL_QUEUE.ABLE_CHANNELS
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setAbleChannels(String ableChannels) {
        this.ableChannels = ableChannels;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.SORT_RULE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.SORT_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getSortRule() {
        return sortRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.SORT_RULE
     *
     * @param sortRule the value for MGW_TENANT_SKILL_QUEUE.SORT_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setSortRule(String sortRule) {
        this.sortRule = sortRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getAllocationRule() {
        return allocationRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     *
     * @param allocationRule the value for MGW_TENANT_SKILL_QUEUE.ALLOCATION_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setAllocationRule(String allocationRule) {
        this.allocationRule = allocationRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.STATUS
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.STATUS
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.STATUS
     *
     * @param status the value for MGW_TENANT_SKILL_QUEUE.STATUS
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.REMARK
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.REMARK
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.REMARK
     *
     * @param remark the value for MGW_TENANT_SKILL_QUEUE.REMARK
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.IS_DEFAULT
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.IS_DEFAULT
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getIsDefault() {
        return isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.IS_DEFAULT
     *
     * @param isDefault the value for MGW_TENANT_SKILL_QUEUE.IS_DEFAULT
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.ROUTE_RULE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.ROUTE_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getRouteRule() {
        return routeRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.ROUTE_RULE
     *
     * @param routeRule the value for MGW_TENANT_SKILL_QUEUE.ROUTE_RULE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setRouteRule(String routeRule) {
        this.routeRule = routeRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.SORT_SIZE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.SORT_SIZE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public Long getSortSize() {
        return sortSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.SORT_SIZE
     *
     * @param sortSize the value for MGW_TENANT_SKILL_QUEUE.SORT_SIZE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setSortSize(Long sortSize) {
        this.sortSize = sortSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.SKILL_TYPE_CODE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.SKILL_TYPE_CODE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getSkillTypeCode() {
        return skillTypeCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.SKILL_TYPE_CODE
     *
     * @param skillTypeCode the value for MGW_TENANT_SKILL_QUEUE.SKILL_TYPE_CODE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setSkillTypeCode(String skillTypeCode) {
        this.skillTypeCode = skillTypeCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.WELCOME_MESSAGE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.WELCOME_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getWelcomeMessage() {
        return welcomeMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.WELCOME_MESSAGE
     *
     * @param welcomeMessage the value for MGW_TENANT_SKILL_QUEUE.WELCOME_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setWelcomeMessage(String welcomeMessage) {
        this.welcomeMessage = welcomeMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.LINE_MESSAGE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.LINE_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getLineMessage() {
        return lineMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.LINE_MESSAGE
     *
     * @param lineMessage the value for MGW_TENANT_SKILL_QUEUE.LINE_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setLineMessage(String lineMessage) {
        this.lineMessage = lineMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.CONNECT_MESSAGE
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.CONNECT_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getConnectMessage() {
        return connectMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.CONNECT_MESSAGE
     *
     * @param connectMessage the value for MGW_TENANT_SKILL_QUEUE.CONNECT_MESSAGE
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setConnectMessage(String connectMessage) {
        this.connectMessage = connectMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column MGW_TENANT_SKILL_QUEUE.WORK_TIME
     *
     * @return the value of MGW_TENANT_SKILL_QUEUE.WORK_TIME
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public String getWorkTime() {
        return workTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column MGW_TENANT_SKILL_QUEUE.WORK_TIME
     *
     * @param workTime the value for MGW_TENANT_SKILL_QUEUE.WORK_TIME
     *
     * @mbggenerated Tue Feb 23 10:32:53 CST 2016
     */
    public void setWorkTime(String workTime) {
        this.workTime = workTime;
    }
}