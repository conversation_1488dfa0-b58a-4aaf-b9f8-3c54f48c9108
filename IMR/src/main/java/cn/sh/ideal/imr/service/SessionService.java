package cn.sh.ideal.imr.service;

import java.util.Map;

import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.model.SessionStatus;

public interface SessionService {
	public SessionInfoExt get(String sendAccount, String acceptedAccount, String channel, String tenantCode) throws Exception;

	public SessionInfoExt get(String sessionId, String tenantCode) throws Exception;

	public void update(SessionInfoExt session, SessionStatus status, Map<String, Object> updateFields) throws Exception;

	public void addMessage(String sessionId, String tenantCode, String messageId) throws Exception;

}
