package cn.sh.ideal.imr.model;

import java.util.Date;

public class FlowLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.MENU_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String menuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String flowId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.FLOW_PARAMETER
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String flowParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.ERROR_NUM
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private Long errorNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.OPEN_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String openId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.OPERATE_TIME
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private Date operateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.S_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String sId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.USER_PARAMETER
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String userParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_FLOW_LOG.PLATFORM
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    private String platform;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.ID
     *
     * @return the value of IMR_FLOW_LOG.ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.ID
     *
     * @param id the value for IMR_FLOW_LOG.ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.MENU_ID
     *
     * @return the value of IMR_FLOW_LOG.MENU_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getMenuId() {
        return menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.MENU_ID
     *
     * @param menuId the value for IMR_FLOW_LOG.MENU_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.FLOW_ID
     *
     * @return the value of IMR_FLOW_LOG.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getFlowId() {
        return flowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.FLOW_ID
     *
     * @param flowId the value for IMR_FLOW_LOG.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.FLOW_PARAMETER
     *
     * @return the value of IMR_FLOW_LOG.FLOW_PARAMETER
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getFlowParameter() {
        return flowParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.FLOW_PARAMETER
     *
     * @param flowParameter the value for IMR_FLOW_LOG.FLOW_PARAMETER
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setFlowParameter(String flowParameter) {
        this.flowParameter = flowParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.ERROR_NUM
     *
     * @return the value of IMR_FLOW_LOG.ERROR_NUM
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public Long getErrorNum() {
        return errorNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.ERROR_NUM
     *
     * @param errorNum the value for IMR_FLOW_LOG.ERROR_NUM
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setErrorNum(Long errorNum) {
        this.errorNum = errorNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.OPEN_ID
     *
     * @return the value of IMR_FLOW_LOG.OPEN_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getOpenId() {
        return openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.OPEN_ID
     *
     * @param openId the value for IMR_FLOW_LOG.OPEN_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.OPERATE_TIME
     *
     * @return the value of IMR_FLOW_LOG.OPERATE_TIME
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public Date getOperateTime() {
        return operateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.OPERATE_TIME
     *
     * @param operateTime the value for IMR_FLOW_LOG.OPERATE_TIME
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.S_ID
     *
     * @return the value of IMR_FLOW_LOG.S_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getsId() {
        return sId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.S_ID
     *
     * @param sId the value for IMR_FLOW_LOG.S_ID
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setsId(String sId) {
        this.sId = sId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.USER_PARAMETER
     *
     * @return the value of IMR_FLOW_LOG.USER_PARAMETER
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getUserParameter() {
        return userParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.USER_PARAMETER
     *
     * @param userParameter the value for IMR_FLOW_LOG.USER_PARAMETER
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setUserParameter(String userParameter) {
        this.userParameter = userParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_FLOW_LOG.PLATFORM
     *
     * @return the value of IMR_FLOW_LOG.PLATFORM
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_FLOW_LOG.PLATFORM
     *
     * @param platform the value for IMR_FLOW_LOG.PLATFORM
     *
     * @mbggenerated Wed Apr 08 16:48:56 CST 2015
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }
}