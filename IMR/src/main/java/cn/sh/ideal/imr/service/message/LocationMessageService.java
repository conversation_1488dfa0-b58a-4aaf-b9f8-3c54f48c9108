/**
 * 
 */
package cn.sh.ideal.imr.service.message;

import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.model.Flow;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.model.servcie.ReqServiceModel;
import cn.sh.ideal.imr.model.servcie.RespServiceModel;
import cn.sh.ideal.imr.model.servcie.param.LocationParamModel;
import cn.sh.ideal.imr.util.ServiceClient;

/**
 * 地理位置
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月31日
 */
@Service
public class LocationMessageService extends MessageService {

	@PostConstruct
	protected void init() {
		register("location", this);
	}

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.message.MessageService#handle(cn.sh.ideal.imr.imr.entity.PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception {
		
		Flow flow = session.getCurrentFlow();
		
		if(flow == null) {
			return null;
		}
		
		LocationParamModel location = new LocationParamModel(message.getX(), message.getY(), message.getLabel());

		RespServiceModel respModel = null;
		ReqServiceModel reqModel = null;
		reqModel = requestParamBean(session,location);
		reqModel.getUserParam().add(location);

		respModel = ServiceClient.clientImpl(flow.getUrl(),
				flow.getFlowInterface(), reqModel);

		// 更新会话
		if ("1".equals(respModel.getResult())) {
			this.updateSessionFlow(session, "error", "", null);
		} else {
			if (respModel.getFlowParam() != null
					&& respModel.getFlowParam().size() > 0) {
				// 将参数保留在会话中
				session.setFlowParam(respModel.getFlowParam());
			}
			if (flow.getIsParameter().equals("1") && location != null) {
				List param = session.getParameter();
				param.add(location);
				session.setParameter(param);
			}
			this.updateSessionFlow(session, "ok", respModel.getCode(),
					null);

		}
		
		return (ResponseMessage) respModel.getMsg();
	}
	
	
}
