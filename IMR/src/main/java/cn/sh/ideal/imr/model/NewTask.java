package cn.sh.ideal.imr.model;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

public class NewTask implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private Integer taskId; 
	private Integer taskType;
	private String tenantCode;
	private String title; 
	private String channel;
	private String skillQueue;
	private String description;
	private String attachName; 
	private Integer customerSampleId; 
	private Integer multimediaType; 
	private Integer imageTextType; 
	private String content; 
	private String ivrName; 
	private Integer allocationRule; 
	private String attachFtpPath; 
	private String ivrFtpPath; 
	private String imageTextFtpPath;
	private Integer startTimeType; 
	private Integer endTimeType; 
	private String startTime; 
	private String endTime; 
	private String progress; 
	private String keyWords;
	private Integer keyWordsFlag; 
	private Integer status; 
	private String subject;
	private String createUser;
	private Integer predictorStatus;
	@JSONField (format="yyyy-MM-dd HH:mm:ss")
	private Date createDate;
	private Date updateDate;
	private String remark;

	
	public Integer getTaskId() {
		return taskId;
	}

	public void setTaskId(Integer taskId) {
		this.taskId = taskId;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public Integer getTaskType() {
		return taskType;
	}

	public void setTaskType(Integer taskType) {
		this.taskType = taskType;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getTitle() {
		return title;
	}

	public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getAttachName() {
		return attachName;
	}

	public void setAttachName(String attachName) {
		this.attachName = attachName;
	}

	public Integer getCustomerSampleId() {
		return customerSampleId;
	}

	public void setCustomerSampleId(Integer customerSampleId) {
		this.customerSampleId = customerSampleId;
	}

	public Integer getMultimediaType() {
		return multimediaType;
	}

	public void setMultimediaType(Integer multimediaType) {
		this.multimediaType = multimediaType;
	}

	public Integer getImageTextType() {
		return imageTextType;
	}

	public void setImageTextType(Integer imageTextType) {
		this.imageTextType = imageTextType;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getIvrName() {
		return ivrName;
	}

	public void setIvrName(String ivrName) {
		this.ivrName = ivrName;
	}

	public Integer getAllocationRule() {
		return allocationRule;
	}

	public void setAllocationRule(Integer allocationRule) {
		this.allocationRule = allocationRule;
	}

	public String getAttachFtpPath() {
		return attachFtpPath;
	}

	public void setAttachFtpPath(String attachFtpPath) {
		this.attachFtpPath = attachFtpPath;
	}

	public String getIvrFtpPath() {
		return ivrFtpPath;
	}

	public void setIvrFtpPath(String ivrFtpPath) {
		this.ivrFtpPath = ivrFtpPath;
	}

	public Integer getStartTimeType() {
		return startTimeType;
	}

	public void setStartTimeType(Integer startTimeType) {
		this.startTimeType = startTimeType;
	}

	public Integer getEndTimeType() {
		return endTimeType;
	}

	public void setEndTimeType(Integer endTimeType) {
		this.endTimeType = endTimeType;
	}

//	public Date getStartTime() {
//		return startTime;
//	}
//
//	public void setStartTime(Date startTime) {
//		this.startTime = startTime;
//	}
//
//	public Date getEndTime() {
//		return endTime;
//	}
//
//	public void setEndTime(Date endTime) {
//		this.endTime = endTime;
//	}


	public String getStartTime() {
		return startTime;
	}

	public String getImageTextFtpPath() {
		return imageTextFtpPath;
	}

	public void setImageTextFtpPath(String imageTextFtpPath) {
		this.imageTextFtpPath = imageTextFtpPath;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	
	public String getProgress() {
		return progress;
	}

	public void setProgress(String progress) {
		this.progress = progress;
	}

	public String getKeyWords() {
		return keyWords;
	}

	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}

	public Integer getKeyWordsFlag() {
		return keyWordsFlag;
	}

	public void setKeyWordsFlag(Integer keyWordsFlag) {
		this.keyWordsFlag = keyWordsFlag;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}


	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public Integer getPredictorStatus() {
		return predictorStatus;
	}

	public void setPredictorStatus(Integer predictorStatus) {
		this.predictorStatus = predictorStatus;
	}
	
	
	
}
