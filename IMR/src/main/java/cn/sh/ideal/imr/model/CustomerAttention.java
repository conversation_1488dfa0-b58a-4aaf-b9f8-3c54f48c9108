package cn.sh.ideal.imr.model;

import java.util.Date;

public class CustomerAttention {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_CUSTOMER_ATTENTION.ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_CUSTOMER_ATTENTION.OPEN_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    private String openId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_CUSTOMER_ATTENTION.STATUS
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_CUSTOMER_ATTENTION.UPDATE_DATE
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    private Date updateDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_CUSTOMER_ATTENTION.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    private String channelId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_CUSTOMER_ATTENTION.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    private String tenantId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_CUSTOMER_ATTENTION.ID
     *
     * @return the value of IMR_CUSTOMER_ATTENTION.ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_CUSTOMER_ATTENTION.ID
     *
     * @param id the value for IMR_CUSTOMER_ATTENTION.ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_CUSTOMER_ATTENTION.OPEN_ID
     *
     * @return the value of IMR_CUSTOMER_ATTENTION.OPEN_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public String getOpenId() {
        return openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_CUSTOMER_ATTENTION.OPEN_ID
     *
     * @param openId the value for IMR_CUSTOMER_ATTENTION.OPEN_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_CUSTOMER_ATTENTION.STATUS
     *
     * @return the value of IMR_CUSTOMER_ATTENTION.STATUS
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_CUSTOMER_ATTENTION.STATUS
     *
     * @param status the value for IMR_CUSTOMER_ATTENTION.STATUS
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_CUSTOMER_ATTENTION.UPDATE_DATE
     *
     * @return the value of IMR_CUSTOMER_ATTENTION.UPDATE_DATE
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_CUSTOMER_ATTENTION.UPDATE_DATE
     *
     * @param updateDate the value for IMR_CUSTOMER_ATTENTION.UPDATE_DATE
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_CUSTOMER_ATTENTION.CHANNEL_ID
     *
     * @return the value of IMR_CUSTOMER_ATTENTION.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_CUSTOMER_ATTENTION.CHANNEL_ID
     *
     * @param channelId the value for IMR_CUSTOMER_ATTENTION.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_CUSTOMER_ATTENTION.TENANT_ID
     *
     * @return the value of IMR_CUSTOMER_ATTENTION.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_CUSTOMER_ATTENTION.TENANT_ID
     *
     * @param tenantId the value for IMR_CUSTOMER_ATTENTION.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 16:33:17 CST 2015
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}