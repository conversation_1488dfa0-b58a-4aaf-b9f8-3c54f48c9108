package cn.sh.ideal.imr.model;

import java.math.BigDecimal;

public class MenuBusi {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_MENU_BUSI.AUTO_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    private BigDecimal autoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_MENU_BUSI.MENU_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    private BigDecimal menuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_MENU_BUSI.BUSI_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    private BigDecimal busiId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CMS_MENU_BUSI.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    private String tenantId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_MENU_BUSI.AUTO_ID
     *
     * @return the value of CMS_MENU_BUSI.AUTO_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public BigDecimal getAutoId() {
        return autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_MENU_BUSI.AUTO_ID
     *
     * @param autoId the value for CMS_MENU_BUSI.AUTO_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public void setAutoId(BigDecimal autoId) {
        this.autoId = autoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_MENU_BUSI.MENU_ID
     *
     * @return the value of CMS_MENU_BUSI.MENU_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public BigDecimal getMenuId() {
        return menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_MENU_BUSI.MENU_ID
     *
     * @param menuId the value for CMS_MENU_BUSI.MENU_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public void setMenuId(BigDecimal menuId) {
        this.menuId = menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_MENU_BUSI.BUSI_ID
     *
     * @return the value of CMS_MENU_BUSI.BUSI_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public BigDecimal getBusiId() {
        return busiId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_MENU_BUSI.BUSI_ID
     *
     * @param busiId the value for CMS_MENU_BUSI.BUSI_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public void setBusiId(BigDecimal busiId) {
        this.busiId = busiId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CMS_MENU_BUSI.TENANT_ID
     *
     * @return the value of CMS_MENU_BUSI.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CMS_MENU_BUSI.TENANT_ID
     *
     * @param tenantId the value for CMS_MENU_BUSI.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 16:07:54 CST 2015
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}