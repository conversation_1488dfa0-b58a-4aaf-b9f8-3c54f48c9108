/**
 * 
 */
package cn.sh.ideal.imr.service.message;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.model.SessionInfoExt;

/**
 * 语音消息
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月31日
 */
@Service
public class VoiceMessageService extends MessageService {
	@PostConstruct
	public void init() {
		register("voice", this);
	}

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.message.MessageService#handle(cn.sh.ideal.imr.imr.entity.PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception {
		return super.handle(message, session);
	}
}
