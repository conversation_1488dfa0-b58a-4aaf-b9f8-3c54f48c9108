package cn.sh.ideal.imr.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import cn.sh.ideal.imr.model.Flow;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.servcie.param.BaseParamModel;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.SessionInfo;

/**
 * 扩展会话
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月29日
 */
public class SessionInfoExt extends SessionInfo implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	/*imr 用的*/
    private Flow currentFlow;                                                       //当前流程
    private Menu currentMenu;                               	                    //当前菜单
    private int errorNum;                                                           //错误次数
	private List <BaseParamModel> userParam = new ArrayList<BaseParamModel>();		//提交参数
	private List <BaseParamModel> parameter = new ArrayList<BaseParamModel>();		//保留用户参数
	private List <BaseParamModel> flowParam = new ArrayList<BaseParamModel>();		//后台保存参数
	private List <BaseParamModel> flowParamTemp = new ArrayList<BaseParamModel>();	//临时保存参数

	private String contextMenu;														//标准问
	private List<Menu> contextMenuList;												//标准问列表
	private List<Menu> allMenuList;													//全局的menu列表
	private String agentText;
	private String callbackUrl;														//回调坐席地址
	
	private transient ChannelConfig channel;
	private transient SemanticConfig semanticConfig;
	
	private String followData;
	
	public void invalidate() {
		setFlowParam(new ArrayList<BaseParamModel>());
		setUserParam(new ArrayList<BaseParamModel>());
		currentFlow = null;
		currentMenu = null;
		errorNum = 0;
	}
	
	/**
	 * @return the currentFlow
	 */
	public Flow getCurrentFlow() {
		return currentFlow;
	}
	/**
	 * @param currentFlow the currentFlow to set
	 */
	public void setCurrentFlow(Flow currentFlow) {
		this.currentFlow = currentFlow;
	}
	/**
	 * @return the currentMenu
	 */
	public Menu getCurrentMenu() {
		return currentMenu;
	}
	/**
	 * @param currentMenu the currentMenu to set
	 */
	public void setCurrentMenu(Menu currentMenu) {
		this.currentMenu = currentMenu;
	}
	/**
	 * @return the userParam
	 */
	public List<BaseParamModel> getUserParam() {
		return userParam;
	}
	/**
	 * @param userParam the userParam to set
	 */
	public void setUserParam(List<BaseParamModel> userParam) {
		this.userParam = userParam;
	}
	/**
	 * @return the parameter
	 */
	public List<BaseParamModel> getParameter() {
		return parameter;
	}
	/**
	 * @param parameter the parameter to set
	 */
	public void setParameter(List<BaseParamModel> parameter) {
		this.parameter = parameter;
	}
	/**
	 * @return the flowParam
	 */
	public List<BaseParamModel> getFlowParam() {
		return flowParam;
	}
	/**
	 * @param flowParam the flowParam to set
	 */
	public void setFlowParam(List<BaseParamModel> flowParam) {
		this.flowParam = flowParam;
	}
	/**
	 * @return the flowParamTemp
	 */
	public List<BaseParamModel> getFlowParamTemp() {
		return flowParamTemp;
	}
	/**
	 * @param flowParamTemp the flowParamTemp to set
	 */
	public void setFlowParamTemp(List<BaseParamModel> flowParamTemp) {
		this.flowParamTemp = flowParamTemp;
	}
	/**
	 * @return the errorNum
	 */
	public int getErrorNum() {
		return errorNum;
	}
	/**
	 * @param errorNum the errorNum to set
	 */
	public void setErrorNum(int errorNum) {
		this.errorNum = errorNum;
	}
	/**
	 * @return the contextMenu
	 */
	public String getContextMenu() {
		return contextMenu;
	}
	/**
	 * @param contextMenu the contextMenu to set
	 */
	public void setContextMenu(String contextMenu) {
		this.contextMenu = contextMenu;
	}
	/**
	 * @return the contextMenuList
	 */
	public List<Menu> getContextMenuList() {
		return contextMenuList;
	}
	/**
	 * @param contextMenuList the contextMenuList to set
	 */
	public void setContextMenuList(List<Menu> contextMenuList) {
		this.contextMenuList = contextMenuList;
	}
	/**
	 * @return the agentText
	 */
	public String getAgentText() {
		return agentText;
	}
	/**
	 * @param agentText the agentText to set
	 */
	public void setAgentText(String agentText) {
		this.agentText = agentText;
	}
	/**
	 * @return the callbackUrl
	 */
	public String getCallbackUrl() {
		return callbackUrl;
	}
	/**
	 * @param callbackUrl the callbackUrl to set
	 */
	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}
	/**
	 * @return the allMenuList
	 */
	public List<Menu> getAllMenuList() {
		return allMenuList;
	}
	/**
	 * @param allMenuList the allMenuList to set
	 */
	public void setAllMenuList(List<Menu> allMenuList) {
		this.allMenuList = allMenuList;
	}
	/**
	 * @return the channel
	 */
	public ChannelConfig getChannel() {
		return channel;
	}
	/**
	 * @param channel the channel to set
	 */
	public void setChannel(ChannelConfig channel) {
		this.channel = channel;
	}
	/**
	 * @return the semanticConfig
	 */
	public SemanticConfig getSemanticConfig() {
		return semanticConfig;
	}
	/**
	 * @param semanticConfig the semanticConfig to set
	 */
	public void setSemanticConfig(SemanticConfig semanticConfig) {
		this.semanticConfig = semanticConfig;
	}

	public String getFollowData() {
		return followData;
	}

	public void setFollowData(String followData) {
		this.followData = followData;
	}
    
}
