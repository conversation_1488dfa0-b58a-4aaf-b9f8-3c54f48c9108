package cn.sh.ideal.imr.model;

import java.math.BigDecimal;
import java.util.Date;

public class NewsInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.ID
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private BigDecimal id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.TITLE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String title;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.PICURL
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String picurl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.CONTENT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.CREATE_TIME
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.STATUS
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.E_STATUS
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String eStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.PAGE_CONTENT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String pageContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.EXTEND_URL
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String extendUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.URL_TYPE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String urlType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.CHANNEL_CODE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String channelCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_NEWS_INFO.TENANT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    private String tenant;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.ID
     *
     * @return the value of IMR_NEWS_INFO.ID
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    
    private String filename;
    
    private String openId;
    
    public BigDecimal getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.ID
     *
     * @param id the value for IMR_NEWS_INFO.ID
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setId(BigDecimal id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.TITLE
     *
     * @return the value of IMR_NEWS_INFO.TITLE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.TITLE
     *
     * @param title the value for IMR_NEWS_INFO.TITLE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.PICURL
     *
     * @return the value of IMR_NEWS_INFO.PICURL
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getPicurl() {
        return picurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.PICURL
     *
     * @param picurl the value for IMR_NEWS_INFO.PICURL
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setPicurl(String picurl) {
        this.picurl = picurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.CONTENT
     *
     * @return the value of IMR_NEWS_INFO.CONTENT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.CONTENT
     *
     * @param content the value for IMR_NEWS_INFO.CONTENT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.CREATE_TIME
     *
     * @return the value of IMR_NEWS_INFO.CREATE_TIME
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.CREATE_TIME
     *
     * @param createTime the value for IMR_NEWS_INFO.CREATE_TIME
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.STATUS
     *
     * @return the value of IMR_NEWS_INFO.STATUS
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.STATUS
     *
     * @param status the value for IMR_NEWS_INFO.STATUS
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.E_STATUS
     *
     * @return the value of IMR_NEWS_INFO.E_STATUS
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String geteStatus() {
        return eStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.E_STATUS
     *
     * @param eStatus the value for IMR_NEWS_INFO.E_STATUS
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void seteStatus(String eStatus) {
        this.eStatus = eStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.PAGE_CONTENT
     *
     * @return the value of IMR_NEWS_INFO.PAGE_CONTENT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getPageContent() {
        return pageContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.PAGE_CONTENT
     *
     * @param pageContent the value for IMR_NEWS_INFO.PAGE_CONTENT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setPageContent(String pageContent) {
        this.pageContent = pageContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.EXTEND_URL
     *
     * @return the value of IMR_NEWS_INFO.EXTEND_URL
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getExtendUrl() {
        return extendUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.EXTEND_URL
     *
     * @param extendUrl the value for IMR_NEWS_INFO.EXTEND_URL
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setExtendUrl(String extendUrl) {
        this.extendUrl = extendUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.URL_TYPE
     *
     * @return the value of IMR_NEWS_INFO.URL_TYPE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getUrlType() {
        return urlType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.URL_TYPE
     *
     * @param urlType the value for IMR_NEWS_INFO.URL_TYPE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setUrlType(String urlType) {
        this.urlType = urlType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.CHANNEL_CODE
     *
     * @return the value of IMR_NEWS_INFO.CHANNEL_CODE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getChannelCode() {
        return channelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.CHANNEL_CODE
     *
     * @param channelCode the value for IMR_NEWS_INFO.CHANNEL_CODE
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_NEWS_INFO.TENANT
     *
     * @return the value of IMR_NEWS_INFO.TENANT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public String getTenant() {
        return tenant;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_NEWS_INFO.TENANT
     *
     * @param tenant the value for IMR_NEWS_INFO.TENANT
     *
     * @mbggenerated Wed Apr 08 11:23:33 CST 2015
     */
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

	/**
	 * @return the filename
	 */
	public String getFilename() {
		return filename;
	}

	/**
	 * @param filename the filename to set
	 */
	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
    
	
	
    
}