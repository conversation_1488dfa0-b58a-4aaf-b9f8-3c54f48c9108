package cn.sh.ideal.imr.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class Menu implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private String no;
	private String noType;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.MENU_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String menuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.MENU_NAME
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String menuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.PARENT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String parentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.ORDER_TYPE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String orderType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.MENU_DESCRIPTION
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String menuDescription;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.MENU_STATUS
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String menuStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.OPERATION_TIME
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private Date operationTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.EVENT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String eventId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.IS_PLATFORM_MENU
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String isPlatformMenu;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.KEYWORDS
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String keywords;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String channelId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String flowId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.MENU_TYPE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String menuType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.VIEW_URL
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String viewUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.MENU_CODE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String menuCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.SKILL_GROUP
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String skillGroup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column IMR_MENU.IS_INITIATIVE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    private String isInitiative;
    
    private String businessType;
    
    private List<Menu> submenu;
    
    private Date startDate;

    private Date endDate;
    

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.MENU_ID
     *
     * @return the value of IMR_MENU.MENU_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getMenuId() {
        return menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.MENU_ID
     *
     * @param menuId the value for IMR_MENU.MENU_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.MENU_NAME
     *
     * @return the value of IMR_MENU.MENU_NAME
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getMenuName() {
        return menuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.MENU_NAME
     *
     * @param menuName the value for IMR_MENU.MENU_NAME
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.PARENT_ID
     *
     * @return the value of IMR_MENU.PARENT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.PARENT_ID
     *
     * @param parentId the value for IMR_MENU.PARENT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.ORDER_TYPE
     *
     * @return the value of IMR_MENU.ORDER_TYPE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.ORDER_TYPE
     *
     * @param orderType the value for IMR_MENU.ORDER_TYPE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.MENU_DESCRIPTION
     *
     * @return the value of IMR_MENU.MENU_DESCRIPTION
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getMenuDescription() {
        return menuDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.MENU_DESCRIPTION
     *
     * @param menuDescription the value for IMR_MENU.MENU_DESCRIPTION
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setMenuDescription(String menuDescription) {
        this.menuDescription = menuDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.MENU_STATUS
     *
     * @return the value of IMR_MENU.MENU_STATUS
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getMenuStatus() {
        return menuStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.MENU_STATUS
     *
     * @param menuStatus the value for IMR_MENU.MENU_STATUS
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setMenuStatus(String menuStatus) {
        this.menuStatus = menuStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.OPERATION_TIME
     *
     * @return the value of IMR_MENU.OPERATION_TIME
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public Date getOperationTime() {
        return operationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.OPERATION_TIME
     *
     * @param operationTime the value for IMR_MENU.OPERATION_TIME
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.EVENT_ID
     *
     * @return the value of IMR_MENU.EVENT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getEventId() {
        return eventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.EVENT_ID
     *
     * @param eventId the value for IMR_MENU.EVENT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.IS_PLATFORM_MENU
     *
     * @return the value of IMR_MENU.IS_PLATFORM_MENU
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getIsPlatformMenu() {
        return isPlatformMenu;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.IS_PLATFORM_MENU
     *
     * @param isPlatformMenu the value for IMR_MENU.IS_PLATFORM_MENU
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setIsPlatformMenu(String isPlatformMenu) {
        this.isPlatformMenu = isPlatformMenu;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.TENANT_ID
     *
     * @return the value of IMR_MENU.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.TENANT_ID
     *
     * @param tenantId the value for IMR_MENU.TENANT_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.KEYWORDS
     *
     * @return the value of IMR_MENU.KEYWORDS
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getKeywords() {
        return keywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.KEYWORDS
     *
     * @param keywords the value for IMR_MENU.KEYWORDS
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.CHANNEL_ID
     *
     * @return the value of IMR_MENU.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.CHANNEL_ID
     *
     * @param channelId the value for IMR_MENU.CHANNEL_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.FLOW_ID
     *
     * @return the value of IMR_MENU.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getFlowId() {
        return flowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.FLOW_ID
     *
     * @param flowId the value for IMR_MENU.FLOW_ID
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.MENU_TYPE
     *
     * @return the value of IMR_MENU.MENU_TYPE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getMenuType() {
        return menuType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.MENU_TYPE
     *
     * @param menuType the value for IMR_MENU.MENU_TYPE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.VIEW_URL
     *
     * @return the value of IMR_MENU.VIEW_URL
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getViewUrl() {
        return viewUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.VIEW_URL
     *
     * @param viewUrl the value for IMR_MENU.VIEW_URL
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setViewUrl(String viewUrl) {
        this.viewUrl = viewUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.MENU_CODE
     *
     * @return the value of IMR_MENU.MENU_CODE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getMenuCode() {
        return menuCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.MENU_CODE
     *
     * @param menuCode the value for IMR_MENU.MENU_CODE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.SKILL_GROUP
     *
     * @return the value of IMR_MENU.SKILL_GROUP
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getSkillGroup() {
        return skillGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.SKILL_GROUP
     *
     * @param skillGroup the value for IMR_MENU.SKILL_GROUP
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setSkillGroup(String skillGroup) {
        this.skillGroup = skillGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column IMR_MENU.IS_INITIATIVE
     *
     * @return the value of IMR_MENU.IS_INITIATIVE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public String getIsInitiative() {
        return isInitiative;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column IMR_MENU.IS_INITIATIVE
     *
     * @param isInitiative the value for IMR_MENU.IS_INITIATIVE
     *
     * @mbggenerated Wed Apr 08 13:39:19 CST 2015
     */
    public void setIsInitiative(String isInitiative) {
        this.isInitiative = isInitiative;
    }

	/**
	 * @return the businessType
	 */
	public String getBusinessType() {
		return businessType;
	}

	/**
	 * @param businessType the businessType to set
	 */
	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	/**
	 * @return the submenu
	 */
	public List<Menu> getSubmenu() {
		return submenu;
	}

	/**
	 * @param submenu the submenu to set
	 */
	public void setSubmenu(List<Menu> submenu) {
		this.submenu = submenu;
	}

	/**
	 * @return the no
	 */
	public String getNo() {
		return no;
	}

	/**
	 * @param no the no to set
	 */
	public void setNo(String no) {
		this.no = no;
	}

	/**
	 * @return the noType
	 */
	public String getNoType() {
		return noType;
	}

	/**
	 * @param noType the noType to set
	 */
	public void setNoType(String noType) {
		this.noType = noType;
	}
	
	

	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "Menu [no=" + no + ", noType=" + noType + ", menuId=" + menuId
				+ ", menuName=" + menuName + ", parentId=" + parentId
				+ ", isPlatformMenu=" + isPlatformMenu + ", tenantId="
				+ tenantId + ", keywords=" + keywords + ", channelId="
				+ channelId + ", flowId=" + flowId + "]";
	}
    
    
}