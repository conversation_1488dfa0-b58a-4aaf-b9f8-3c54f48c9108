package cn.sh.ideal.imr.service.message;

import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.Menu;
import cn.sh.ideal.imr.model.SemanticConfig;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.model.servcie.ReqServiceModel;
import cn.sh.ideal.imr.model.servcie.RespServiceModel;
import cn.sh.ideal.imr.model.servcie.param.BaseParamModel;
import cn.sh.ideal.imr.model.servcie.param.TextParamModel;
import cn.sh.ideal.imr.service.SessionService;
import cn.sh.ideal.imr.service.impl.DataInitService;
import cn.sh.ideal.imr.util.ServiceClient;
import cn.sh.ideal.model.ChannelConfig;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.PostConstruct;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class TextMessageService extends MessageService
{
	private static Logger log = Logger.getLogger(TextMessageService.class);

	@Value("${msg.defaultReply}")
	private String defaultReplyMsg;

	@PostConstruct
	public void init() { register("text", this);
	}

	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session)
			throws Exception
	{
		message.setContent(StringUtils.upperCase(message.getContent()));

		ChannelConfig channel = session.getChannel();
		String keyword = message.getContent();

		if (session.getCurrentFlow() != null) {
			return executeFlow(session, message, session.getCurrentFlow());
		}

		String menuId = this.dataInitService.getMenuIdByKeyword(
				channel.getTenantCode(), String.valueOf(channel.getId()), keyword);
		message.setEventId(menuId);

		log.info("get menuId [" + menuId + "]");

		if (StringUtils.isNotEmpty(menuId)) {
			return menuFlow(session, menuId, message);
		}

		if ((StringUtils.isNumeric(keyword)) && (session.getAllMenuList() != null)) {
			for (Menu menu : session.getAllMenuList()) {
				if (!keyword.equals(menu.getNo()))
					continue;
				if ("0".equals(menu
						.getNoType())) {
					menu = (Menu)this.dataInitService.get("IMR_MENU", menu.getMenuId());
					return menuFlow(session, menu.getMenuId(), message);
				}
				keyword = menu.getMenuName();
			}

		}

		if ((session.getSemanticConfig() != null) &&
				(!"0".equals(
						session.getSemanticConfig().getSemanticType())))
		{
			return semanticFlow(session,
					requestParamBean(session, new TextParamModel(message.getContent())), Boolean.valueOf(true), session.getSemanticConfig(), message);
		}

		return new ResponseTextMessage(this.defaultReplyMsg);
	}

	public ResponseMessage semanticFlow(SessionInfoExt session, ReqServiceModel reqModel, Boolean isReturn, SemanticConfig config, PlatformMessage message)
			throws Exception
	{
		if (config == null) {
			return null;
		}
		if ((session.getContextMenuList() != null) &&
				(session.getContextMenuList().size() > 0)) {
			List userParamList = reqModel.getUserParam();

			if ((userParamList.get(userParamList.size() - 1) instanceof TextParamModel)) {
				String content =
						((TextParamModel)userParamList
								.get(userParamList.size() - 1)).getContent();
				List<Menu> contentList = session.getContextMenuList();
				for (Menu menu : contentList) {
					if (menu.getNo().equals(content)) {
						userParamList.remove(userParamList.size() - 1);
						userParamList
								.add(new TextParamModel(menu.getMenuName()));
						reqModel.setUserParam(userParamList);
					}
				}
			}
			if (!StringUtils.isEmpty(message.getFollowData())) {
				TextParamModel textparam = new TextParamModel(message.getFollowData());
				textparam.setParamName("followData");
				List flowList = session.getFlowParam();
				flowList.add(textparam);
				session.setFlowParam(flowList);
			}
			session.setContextMenuList(null);

			this.sessionService.update(session, null, null);
		}

		RespServiceModel respModel = new RespServiceModel();

		if ("0".equals(config.getInterfaceType()))
			respModel = ServiceClient.clientImpl(config.getSemanticWebserviceUrl(),
					config.getSemanticWebserviceFunction(), reqModel);
		else if ("1".equals(config.getInterfaceType())) {
			respModel = ServiceClient.httpClientImpl(config.getSemanticWebserviceUrl(),
					reqModel);
		}

		Menu menu = (Menu)this.dataInitService.get("IMR_MENU", respModel.getMenu());
		log.info("flag:" + respModel.getFlag());

		if ((menu != null) && (menu.getMenuName().equals("IN_AGENT"))) {
			List params = reqModel.getUserParam();
			if ((params != null) && (params.size() > 0) &&
					((params.get(0) instanceof TextParamModel))) {
				TextParamModel text = (TextParamModel)params.get(0);
				session.setAgentText(text.getContent());
			}

		}

		respModel.setCode("");
		if ((StringUtils.isBlank(respModel.getType())) ||
				("0".equals(respModel
						.getType()))) {
			if (StringUtils.isNotBlank(respModel.getContextMenu())) {
				setContextMenu(session, respModel.getContextMenu(),
						"0");
				if (isReturn.booleanValue())
					return getContextMenuText(session,
							returnResponseMessage(session, respModel),
							"输入序号查看相关问题  :");
			}
			else {
				return returnResponseMessage(session, respModel);
			}
		} else if (("1".equals(respModel.getType())) ||
				("23".equals(respModel
						.getType()))) {
			if (("23".equals(respModel.getType())) &&
					(StringUtils.isNotBlank(respModel.getContextMenu()))) {
				setContextMenu(session, respModel.getContextMenu(),
						"1");
			}

			session = setFlowParamTemp(session,
					respModel.getFlowParam());
			if (isReturn.booleanValue()) {
				session.setBusinessType(respModel.getFlag());
				return menuFlow(session, respModel.getMenu(), message);
			}
		}
		else if ("2".equals(respModel
				.getType())) {
			if (StringUtils.isNotBlank(respModel.getContextMenu())) {
				setContextMenu(session, respModel.getContextMenu(),
						"2");
			}

			session = setFlowParamTemp(session,
					respModel.getFlowParam());
			if (isReturn.booleanValue()) {
				return menuFlow(session, respModel.getMenu(), message);
			}
		}
		return null;
	}

	public SessionInfoExt setFlowParamTemp(SessionInfoExt session, List<BaseParamModel> flowParam) throws Exception
	{
		if ((flowParam != null) && (flowParam.size() > 0)) {
			session.setFlowParamTemp(flowParam);
			this.sessionService.update(session, null, null);
		}
		return session;
	}

	public ResponseMessage getContextMenuText(SessionInfoExt sessionFlow, ResponseMessage baseMessage, String info)
	{
		StringBuffer str = new StringBuffer();

		if (baseMessage.getClass() == ResponseTextMessage.class) {
			String content = ((ResponseTextMessage)baseMessage).getContent();
			if (StringUtils.isNotBlank(content)) {
				str.append(content + "\n\n");
			}
			if (StringUtils.isNotBlank(info)) {
				str.append(info + "\n");
			}
			List list = sessionFlow.getContextMenuList();
			if ((list != null) && (list.size() > 0)) {
				for (int i = 0; i < list.size(); i++) {
					Menu menu = (Menu)list.get(i);
					str.append(menu.getNo() + ") " + menu.getMenuName() + "\n");
				}
			}
			return new ResponseTextMessage(str.toString());
		}
		return null;
	}

	public void setContextMenu(SessionInfoExt session, String contextMenu, String contentMenuType)
			throws Exception
	{
		Menu menu = null;
		List menuList = new ArrayList();
		String[] cMenus = contextMenu.split(",");
		List allList = session.getAllMenuList();
		if (allList == null) {
			allList = new ArrayList();
		}
		int size = allList.size();
		if (cMenus.length > 0)
		{
			for (int i = 0; i < cMenus.length; i++) {
				menu = new Menu();

				if (contentMenuType
						.equals("2")) {
					menu = (Menu)this.dataInitService.get("IMR_MENU", cMenus[i]);
					menuList.add(menu);
				} else {
					menu.setNo(String.valueOf(size + i + 1));
					menu.setMenuName(cMenus[i]);
					menu.setNoType("1");
					menuList.add(menu);
					allList.add(menu);
				}
			}

		}

		session.setContextMenuList(menuList);
		session.setAllMenuList(allList);

		this.sessionService.update(session, null, null);
	}

	public ResponseMessage returnResponseMessage(SessionInfoExt session, RespServiceModel respModel)
	{
		if ((respModel.getMsg() instanceof ResponseTextMessage))
		{
			if ((session.getSemanticConfig() != null) && ("1".equals(session.getSemanticConfig().getSemanticType())) &&
					("END".equals(respModel.getCode())) &&
					(session.getContextMenuList() != null) &&
					(session.getContextMenuList().size() > 0)) {
				return getContextMenuText(session,
						(ResponseTextMessage)respModel.getMsg(), "输入序号查看相关问题  :");
			}
			return (ResponseTextMessage)respModel.getMsg();
		}

		return (ResponseMessage)respModel.getMsg();
	}
}