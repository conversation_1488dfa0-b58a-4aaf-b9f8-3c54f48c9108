/**
 * 
 */
package cn.sh.ideal.imr.service.message.event;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.imr.base.Constants;
import cn.sh.ideal.imr.dao.QrcodeDAO;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.Qrcode;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.service.impl.DataInitService;
import cn.sh.ideal.imr.service.message.MessageService;
import cn.sh.ideal.init.FrequentWordInitService;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.util.NetUtil;

/**
 * 扫描事件
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月31日
 */
@Service
public class ScanEventMessageService extends MessageService {

	@Autowired
	private QrcodeDAO qrcodeDAO;
	
	@Value("${toolbar.url}")
	private String toolbarUrl;
	
	@Autowired
	private cn.sh.ideal.mgw.service.MessageService mgwMessageService;
	
	@Value("${msg.channel.channge}")
	private String msgChannelChannge;
	@Autowired
	private FrequentWordInitService frequentWordInitService;
	@Value("${msg.sorting}")
	private String sortingMsg;

	@PostConstruct
	public void init() {
		MessageService.register("scan", this);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * cn.sh.ideal.imr.service.message.MessageService#handle(cn.sh.ideal.imr.imr.entity.
	 * PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception {

		String openId = message.getOpenId();
		ChannelConfig channel = session.getChannel();
		
		if (StringUtils.isNotEmpty(message.getEventId())) {
			// 扫码关注
			// 判断是否切换渠道
			String sceneId = message.getEventId();

			Qrcode qrcode = new Qrcode();
			qrcode.setSceneId(sceneId);

			List<Qrcode> qrcodes = qrcodeDAO.queryForList(qrcode);

			if (qrcodes != null && !qrcodes.isEmpty()) {
				// 是切换渠道或绑定用户
				qrcode = qrcodes.get(0);

				/** 获取微信易信昵称 */
				//TODO 获取昵称
				String nickname = ""/* getNickname(channel.getChannelCode(), message.getToUser(),
						openId)*/;

				if ("0".equals(qrcode.getUsageType())) {
					ChannelConfig channelConfig = (ChannelConfig)this.dataInitService.get(DataInitService.KEY_CHANNEL, 
				              qrcode.getFromChannelAccount());
					System.out.println("获取渠道信息："+JSONObject.toJSONString(channelConfig));
					if(channelConfig == null)
						throw new Exception("channel config not found by" + qrcode.getFromChannelAccount());

		            SessionInfoExt sessionInfo = this.sessionService.get(qrcode.getPlatformUser(), channelConfig.getChannelAccount(), 
		              channelConfig.getChannelCode(), channelConfig.getTenantCode());
					System.out.println("获取原会话信息："+JSONObject.toJSONString(sessionInfo));
		            if (sessionInfo == null) {
		            	throw new NullPointerException("session not found.");
		            }
					// 切换渠道
					Map<String, Object> updateFields = new HashMap<String, Object>();
					updateFields.put("sendAccount", openId);
					updateFields.put("acceptedAccount", message.getToUser());
					updateFields.put("channelCode", channel.getChannelCode());
					updateFields.put("nickname", nickname);

					sessionService.update(sessionInfo, null, updateFields);

					System.out.println("会话切换成功");

					if (SessionStatus.SELF.getCode().equals(sessionInfo.getStatus())) {
						return null;
					} else if (SessionStatus.MANUAL.getCode().equals(sessionInfo.getStatus())) {
						System.out.println("开始推送成功提示");
						sendChannelChangeEvent(sessionInfo);
						String msgChannelChannge = frequentWordInitService.getFreWordByParamCode("msg.channel.channge").getParamValue();
						return new ResponseTextMessage(
								String.format(msgChannelChannge, sessionInfo.getWorkNos()));
					} else {
						String sortingMsg = frequentWordInitService.getFreWordByParamCode("msg.sorting").getParamValue();
						return new ResponseTextMessage(sortingMsg);
					}
				} else if ("1".equals(qrcode.getUsageType())) {
					// 绑定用户
					JSONObject requestObj = new JSONObject();
					requestObj.put("nickname", nickname);
					requestObj.put("openId", openId);
					requestObj.put("clientId", qrcode.getPlatformUser());
					requestObj.put("channelCode", channel.getChannelCode());

					NetUtil.send(toolbarUrl, NetUtil.POST, requestObj.toString());

				}

			}
		}

		return menuFlow(session, channel.getId() + "_" + Constants.SCAN, message);
	}
	
	public void sendChannelChangeEvent(SessionInfoExt info){
		MessageInfo messageInfo = new MessageInfo();
		messageInfo.setChannelCode(info.getChannelCode());

		messageInfo.setTenantCode(info.getTenantCode());
		messageInfo.setSkillQueue(info.getSkillQueue());
		messageInfo.setNickname(info.getNickname());


		messageInfo.setSendAccount(info.getAcceptedAccount());
		messageInfo.setAcceptedAccount(info.getSendAccount());
		messageInfo.setBusinessType(info.getBusinessType());
		messageInfo.setMsgType("text");
		messageInfo.setMessageSource("3");
		messageInfo.setContent("渠道切换成功！");
		messageInfo.setFollowData("{\"eventType\":\"changeChannel\"}");
		mgwMessageService.newSend(messageInfo);

	}
}
