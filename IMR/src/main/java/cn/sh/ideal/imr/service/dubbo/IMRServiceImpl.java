package cn.sh.ideal.imr.service.dubbo;

import cn.sh.ideal.imr.dao.MessageInfoDAO;
import cn.sh.ideal.imr.dao.PlatformMessageDAO;
import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.entity.ResponseTextMessage;
import cn.sh.ideal.imr.model.SemanticConfig;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.req.MessageHandleRequest;
import cn.sh.ideal.imr.resp.MessageHandleResponse;
import cn.sh.ideal.imr.service.IMRService;
import cn.sh.ideal.imr.service.SessionService;
import cn.sh.ideal.imr.service.impl.DataInitService;
import cn.sh.ideal.imr.service.message.MessageService;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("IMRService")
public class IMRServiceImpl
		implements IMRService
{
	@Autowired
	private PlatformMessageDAO platformMessageDAO;
	@Autowired
	private SessionService sessionService;
	@Autowired
	private DataInitService dataInitService;
	@Value("${msg.error}")
	private String msgError;
	@Autowired
	private MessageService messageService;
	@Autowired
	private MessageInfoDAO messageInfoDAO;
	Logger log = Logger.getLogger(getClass());

	public MessageHandleResponse messageHandle(MessageHandleRequest request)
	{
		try
		{
			this.log.info("Request IMRService.messageHandle[" + JSONObject.toJSONString(request.getPlatformMessage()) + "]");

			PlatformMessage message = request.getPlatformMessage();
			if (message == null) {
				return null;
			}
			this.platformMessageDAO.insert(message);


			SessionInfoExt session = getSession(message);
			if(StringUtils.isNotEmpty(message.getContent()) && message.getContent().indexOf("人工") == -1){
				addSelfMessage(message.getContent(),session,"1");
			}
			ResponseMessage responseMessage = this.messageService.handle(message, session);
			if (responseMessage != null)
			{
				responseMessage.setFromUserName(message.getToUser());
				responseMessage.setToUserName(message.getOpenId());
				responseMessage.setCreateTime(System.currentTimeMillis());
				if(responseMessage instanceof ResponseTextMessage){
					ResponseTextMessage rtm = (ResponseTextMessage) responseMessage;
					addSelfMessage(rtm.getContent(),session,"2");
				}

			}
			this.log.info("Response IMRService.messageHandle[" + JSONObject.toJSONString(responseMessage) + "]");

			return new MessageHandleResponse(responseMessage);
		}
		catch (Exception e)
		{
			this.log.error("", e);

			ResponseMessage errorMessage = new ResponseTextMessage(this.msgError);
			errorMessage.setFromUserName(request.getPlatformMessage().getToUser());
			errorMessage.setToUserName(request.getPlatformMessage().getOpenId());
			errorMessage.setCreateTime(System.currentTimeMillis());

			return new MessageHandleResponse(errorMessage);
		}
	}

	private void addSelfMessage(String content, SessionInfoExt session,String type){
		MessageInfo messageInfo = new MessageInfo();
		messageInfo.setTenantCode(session.getTenantCode());
		messageInfo.setChannelCode(session.getChannelCode());
		messageInfo.setSessionId(session.getSessionId());
		messageInfo.setSendAccount(session.getSendAccount());
		messageInfo.setAcceptedAccount(session.getAcceptedAccount());
		messageInfo.setMsgType("text");
		if("1".equals(type)){
			messageInfo.setSource("1");
			messageInfo.setMessageSource("3");
			messageInfo.setSendAccount(session.getSendAccount());
			messageInfo.setAcceptedAccount(session.getAcceptedAccount());
		}else{
			messageInfo.setSource("2");
			messageInfo.setMessageSource("4");
			messageInfo.setSendAccount(session.getAcceptedAccount());
			messageInfo.setAcceptedAccount(session.getSendAccount());
		}

		messageInfo.setContent(content);
		messageInfoDAO.insert(messageInfo);
	}

	private SessionInfoExt getSession(PlatformMessage message)
			throws Exception
	{
		ChannelConfig channel = (ChannelConfig)this.dataInitService.get("IMR_CHANNEL", message.getToUser());
		if (channel == null) {
			throw new Exception("channel not found by " + message.getToUser());
		}
		SessionInfoExt session = this.sessionService.get(message.getOpenId(), message.getToUser(), channel.getChannelCode(), channel.getTenantCode());
		if (session == null) {
			throw new Exception("session not found");
		}
		this.log.info("Get Session[" + JSONObject.toJSONString(session) + "]");

		SemanticConfig semanticConfig = (SemanticConfig)this.dataInitService.get("IMR_SEMANTIC_CONFIG", String.valueOf(channel.getId()));
		session.setChannel(channel);
		session.setSemanticConfig(semanticConfig);

		return session;
	}

	public void reinit()
	{
		this.dataInitService.init();
	}
}
