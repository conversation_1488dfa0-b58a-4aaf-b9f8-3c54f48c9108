/**
 * 
 */
package cn.sh.ideal.imr.service.message.event;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.model.SessionInfoExt;
import cn.sh.ideal.imr.service.message.MessageService;

/**
 * 进入事件
 * <AUTHOR>
 * @version 1.0
 * @since 2016年3月31日
 */
@Service
public class EnterEventMessageService extends MessageService {


	@PostConstruct
	public void init() {
		MessageService.register("enter", this);
	}

	/* (non-Javadoc)
	 * @see cn.sh.ideal.imr.service.message.MessageService#handle(cn.sh.ideal.imr.imr.entity.PlatformMessage, cn.sh.ideal.imr.model.SessionInfoExt)
	 */
	@Override
	public ResponseMessage handle(PlatformMessage message, SessionInfoExt session) throws Exception {
		return null;
	}
	
	
}
