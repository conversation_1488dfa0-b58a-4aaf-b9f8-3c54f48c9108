/**
 * 
 */
package cn.sh.ideal.imr.service.systemflow;

import java.util.HashMap;

import cn.sh.ideal.imr.dao.FlowLogDAO;
import cn.sh.ideal.imr.model.FlowLog;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.imr.entity.PlatformMessage;
import cn.sh.ideal.imr.entity.ResponseMessage;
import cn.sh.ideal.imr.model.SessionInfoExt;

/**
 * 系统流程服务
 * <AUTHOR>
 * @version 1.0
 * @since 2016年4月12日
 */
@Service
public class SystemFlowService {

	private static HashMap<String, SystemFlowService> services;
	
	static {
		services = new HashMap<String, SystemFlowService>();
	}
	@Autowired
	protected FlowLogDAO flowLogDAO;
	/**
	 * 注册Service
	 */
	public final static void register(String key, SystemFlowService service) {
		if(services.containsKey(key))
			return;
		
		services.put(key, service);
	}
	
	/**
	 * 工厂
	 * @param messageType
	 * @return
	 */
	public final static SystemFlowService getFlowService(String code) {
		return services.get(code);
	}

	/**
	 * 初始化
	 */
	protected void init() {}

	
	public ResponseMessage service(PlatformMessage message, SessionInfoExt session) throws Exception{
		return null;
	}

	/**
	 * 记录日志
	 *
	 * @param flowLog
	 */
	public void flowLog(SessionInfoExt session, String menuCode) {

		FlowLog flowLog = new FlowLog();
		String menuId = session.getCurrentMenu().getMenuId();
		String flowId = null;
		if(session.getCurrentFlow() != null){
			flowId = session.getCurrentFlow().getFlowId();
		}
		String userParam = null;
		String flowParam = null;
		if(CollectionUtils.isNotEmpty(session.getUserParam())) {
			userParam = JSONObject.toJSONString(session.getUserParam());
		}
		if(CollectionUtils.isNotEmpty(session.getFlowParam())) {
			flowParam = JSONObject.toJSONString(session.getFlowParam());
		}
		if(flowId != null) {
			flowLog.setFlowId(flowId.matches("\\d*") ? flowId : "");
		}
		if(menuId != null) {
			flowLog.setMenuId(menuId.matches("\\d*") ? menuId : "");
		}
		flowLog.setFlowParameter(flowParam);
		flowLog.setUserParameter(userParam);
		flowLog.setOpenId(session.getSendAccount());
		flowLog.setsId(session.getSessionId());
		flowLog.setPlatform(session.getChannelCode());
		flowLogDAO.insert(flowLog);
	}
}
