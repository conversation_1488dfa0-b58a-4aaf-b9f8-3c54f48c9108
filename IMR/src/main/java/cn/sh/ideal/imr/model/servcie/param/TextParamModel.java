/**
 * 
 */
package cn.sh.ideal.imr.model.servcie.param;

import cn.sh.ideal.imr.base.Constants;

/**
 * <AUTHOR>
 *
 */
public class TextParamModel extends BaseParamModel {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = -826257623108511680L;
	private String content;

	
	public TextParamModel() {
		super();
	}
	

	public TextParamModel(String content) {
		super();
		super.setMsgType(Constants.REQ_MESSAGE_TYPE_TEXT);
		this.content = content;
	}


	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Override
	public String toString() {
		return "TextParamModel [content=" + content + ", toString()="
				+ super.toString() + "]";
	}


	
	
}
