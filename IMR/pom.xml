<?xml version="1.0" encoding="UTF-8"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>multi_media</groupId>
		<artifactId>multi_media</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>

	<artifactId>IMR</artifactId>
	<name>IMR</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>MGW_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>IMR_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>SM_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>MIR_API</artifactId>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>CM</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
		</dependency>
		<dependency>
			<groupId>org.codehaus.xfire</groupId>
			<artifactId>xfire-core</artifactId>
			<version>1.2.6</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
		</dependency>
		<dependency>
			<groupId>oracle.jdbc</groupId>
			<artifactId>ojdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-lang3</artifactId>
		  <version>3.3.2</version>
		</dependency>
		<dependency>
		  <groupId>com.thoughtworks.xstream</groupId>
		  <artifactId>xstream</artifactId>
		  <version>1.3.1</version>
		</dependency>
	</dependencies>
	
	<build>
		<finalName>IMR</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<filters>
			<filter>src/main/resources/conf/${env}/app-${env}.properties</filter>
			<filter>src/main/resources/conf/${env}/jdbc-${db}-${env}.properties</filter>
		</filters>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<transformers>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>cn.sh.ideal.imr.boot.Bootstrap</mainClass>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.handlers</resource>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.schemas</resource>
								</transformer>
							</transformers>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!--  <plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-jar</id>
						<phase>package</phase>
						<configuration>
							<tasks>
								<delete dir="F:/alljar/IMR.jar" />
								<copy todir="F:/alljar/">
									<fileset dir="target/">
										<include name="IMR.jar" />
									</fileset>
								</copy>
							</tasks>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->
		</plugins>
	</build>


	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<env>dev</env>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<env>prod</env>
			</properties>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env>test</env>
			</properties>
		</profile>
		<profile>
			<id>mysql</id>
			<properties>
				<db>mysql</db>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>oracle</id>
			<!-- <activation>
				<activeByDefault>true</activeByDefault>
			</activation> -->
			<properties>
				<db>oracle</db>
			</properties>
		</profile>
	</profiles>
</project>
