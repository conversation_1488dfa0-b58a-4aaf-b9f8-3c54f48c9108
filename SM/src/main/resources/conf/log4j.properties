 ### set log levels ###
#log4j.rootLogger = error , stdout , D , E
log4j.rootLogger = error , stdout , D

###  output to the console ###
log4j.appender.stdout = org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target = System.out
log4j.appender.stdout.layout = org.apache.log4j.PatternLayout
#log4j.appender.stdout.layout.ConversionPattern = %d{ABSOLUTE} %5p %c{ 1 }:%L - %m%n
log4j.appender.stdout.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss} [%c]-[%p] %m%n

### Output to the log file ###
log4j.appender.D = org.apache.log4j.DailyRollingFileAppender
log4j.appender.D.File = ../logs/SM/error.log
log4j.appender.D.Threshold = error
log4j.appender.D.layout = org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss} [ %t:%r ] - [ %p ] %m%n
log4j.appender.D.DatePattern='.'yyyy-MM-dd_HH'.log'

#mybatis
log4j.logger.org.apache.ibatis=error
log4j.logger.org.mybatis=error
log4j.logger.java.sql.ResultSet=error
log4j.logger.java.sql.Connection=error
log4j.logger.java.sql.Statement=error
log4j.logger.java.sql.PreparedStatement=error
log4j.logger.org.mybatis.spring=error

#sessionManager
log4j.logger.cn.sh.ideal.sm=error