pushMGWurl=/nsend.do
pushAgTipUrl =/message/stip.do
pushAgMsgUrl = /message/pmi.do
mirTransferUrl=/baseOperateController/transferTaskSkillQueueAuto.do
mirDecreaseSessionCountUrl =/baseOperateController/decreaseSessionCount.do
customerUrl=/customer/custDisti.do
quitSessionSort=/allocate/session/remove
selfTimeout=60000

#used to sessionTimeoutListener
expireKey=expire_
#getSession is or not reload from database
needReloadSession = false


#wae
wae.providerId=avaya.com
wae.endWork=/WARPRestService/rp/rprest/providers/%s/resources/%s/workItems/%s

#\uFFFD\u0176\u04F3\uFFFD\u02B1
session.isSortTimeout = fasle
session.sortTimeout = 1000
session.sortTimeoutUrl=http://localhost:8080/test

zookeeper.address=127.0.0.1:2181
zookeeper.port=20080
dubbo.shutdown.timeout=10000
dubbo.consumer.timeout=15000