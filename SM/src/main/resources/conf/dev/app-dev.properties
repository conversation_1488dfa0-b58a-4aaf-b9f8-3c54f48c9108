pushMGWurl=/nsend.do
pushAgTipUrl =/message/stip.do
pushAgMsgUrl = /message/pmi.do
mirTransferUrl=/baseOperateController/transferTaskSkillQueueAuto.do
mirDecreaseSessionCountUrl =/baseOperateController/decreaseSessionCount.do
customerUrl=/customer/custDisti.do
quitSessionSort=/allocate/session/remove
selfTimeout=60000
#used to sessionTimeoutListener
expireKey=expire_
#getSession is or not reload from database
needReloadSession = false

#wae
wae.providerId=avaya.com
wae.endWork=/WARPRestService/rp/rprest/providers/%s/resources/%s/workItems/%s

#\ufffd\u0176\u04f3\ufffd\u02b1
session.isSortTimeout = true
session.sortTimeout = 360000
session.sortTimeoutUrl=http://localhost:8080/test



session.timeoutMeg = \u6392\u961F\u8D85\u65F6\uFF0C\u7CFB\u7EDF\u81EA\u52A8\u9000\u51FA\u6392\u961F\uFF01
session.isSortTimeoutSystem = true
session.sortTimeoutSystem = 1000
session.sortTimeoutUrlSystem=http://localhost:8080/test



zookeeper.address=home.immortalbo.win:2181
# ?backup=***********:2182,***********:2183
zookeeper.port=20084
dubbo.shutdown.timeout=10000
dubbo.consumer.timeout=15000
