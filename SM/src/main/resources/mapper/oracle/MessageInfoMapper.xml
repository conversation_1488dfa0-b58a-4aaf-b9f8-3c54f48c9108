<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.MessageInfoDao">
   <!--旧的消息格式不支持新的消息格式-->
	<resultMap id="sessionResultMap" type="cn.sh.ideal.model.Session">
		<id column="SESSION_ID" property="sessionId" />
		<collection property="messages"
					ofType="cn.sh.ideal.model.Message" column="SESSION_ID">
			<id column="ID" property="id" />
			<result column="MESSAGE_ID" property="messageId" jdbcType="VARCHAR" />
			<result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
			<result column="CHANNEL_CODE" property="messageChannel"
					jdbcType="VARCHAR" />
			<result column="SEND_ACCOUNT" property="sendAccout" jdbcType="VARCHAR" />
			<result column="ACCEPTED_ACCOUNT" property="acceptedAccount"
					jdbcType="VARCHAR" />
			<result column="EXT_DATA" property="extData" jdbcType="VARCHAR" />
			<result column="TOOLBAR_ID" property="toolbarId" jdbcType="VARCHAR" />
			<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
			<result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
			<result column="TYPE" property="status" jdbcType="VARCHAR" />
			<result column="SKILL_QUEUE" property="skillQueue" jdbcType="VARCHAR" />
		</collection>

	</resultMap>
	
    <!-- 旧接口不支持新的消息格式-->
	<select id="getUnfinishSessionMessages" parameterType="hashMap"
			resultMap="sessionResultMap">
		SELECT
		s.SESSION_ID,m.ID,m.MESSAGE_ID,m.TOOLBAR_ID,s.CHANNEL_CODE,m.SEND_ACCOUNT,m.ACCEPTED_ACCOUNT,m.CREATE_TIME,s.SKILL_QUEUE,w.WORK_NO,m.TYPE,m.EXT_DATA
		FROM MGW_SESSION_INFO s,(
		<include refid="Messages" />
		) m ,MGW_SESSION_WORKNO w
		WHERE s.SESSION_ID = m.SESSION_ID
		and
		s.SESSION_ID = w.SESSION_ID
		and s.TENANT_CODE = #{tenantCode}
		and
		w.WORK_NO = #{workNo}

		<if test="sessionId != null and sessionId != ''">
			and s.SESSION_ID = #{sessionId}
		</if>
		<!-- 正在处理 -->
		<if test="sessionStatus == 0">
			and (s.status = '5' or s.status = '6' or s.status = '8')
		</if>
		<!-- 已关闭 -->
		<if test="sessionStatus == 1">
			and (s.status = '7' or s.status = '10' or s.status = '9')
		</if>
		<!-- 以延迟 -->
		<if test="sessionStatus == 2">
			and (s.status = '11' )
		</if>
	</select>
	<sql id="Messages">
		SELECT T3.*
		FROM (
		<include refid="From_Message" />
		UNION ALL
		<include refid="To_Message" />
		) T3
		ORDER BY T3.CREATE_TIME

	</sql>

	<sql id="From_Message">
		SELECT T2.ID,
		T2.SESSION_ID,
		T2.MESSAGE_ID,
		T2.EXT_DATA,
		S.SEND_ACCOUNT,
		S.ACCEPTED_ACCOUNT,
		T2.CREATE_TIME,
		'FROM' TYPE,
		T2.TOOLBAR_ID
		FROM MGW_MESSAGE T2,MGW_SESSION_INFO S
		WHERE T2.SESSION_ID
		= S.SESSION_ID
	</sql>
	<sql id="To_Message">
		SELECT T2.ID,
		T2.SESSIONID,
		T2.MESSAGE_ID,
		DECODE(T1.CHANNEL_CODE,
		'1002',
		'{\"attachment\":\"' || T2.FILE_PATH ||
		'\",\"contentHtml\":\"' || '\",\"content\":\"' ||
		T2.MEDIA_CONTENT ||
		'\",\"fromuser\":\"' ||
		T2.MEDIA_SENDER || '\",\"subject\":\"' ||
		T2.MEDIA_TITLE || '\",\"touser\":\"' ||
		T2.MEDIA_RECEIVER || '\"}',
		T2.MEDIA_CONTENT) AS EXT_DATA,
		T1.SEND_ACCOUNT,
		T1.ACCEPTED_ACCOUNT,
		T2.SENDTIME,
		'TO' TYPE,
		T2.TOOLBAR_ID
		FROM MGW_SESSION_INFO T1,
		MGW_MEDIA_SEND T2
		WHERE 1=1

		AND T1.SESSION_ID = T2.SESSIONID

	</sql>
	<insert id="insert" parameterType="cn.sh.ideal.model.MessageInfo">
		<selectKey resultType="java.lang.String" order="BEFORE" keyProperty="messageId">  
			 SELECT SEQ_MGW_MESSAGE_INFO.NEXTVAL FROM DUAL
	    </selectKey>
	insert into MGW_MESSAGE_INFO (MESSAGE_ID, CHANNEL_CODE, BUSINESS_TYPE,
	TENANT_CODE, USER_ID, SOURCE,
	NICKNAME, SEND_ACCOUNT, ACCEPTED_ACCOUNT,
	REPLY_ACCOUNT, CREATE_TIME, SEND_TIME,
	SESSION_ID, SKILL_QUEUE,
	WORK_NO,
	STATUS, MSG_TYPE, CONTENT,
	FOLLOW_DATA, REMARK,SKILL_TYPE,MESSAGE_SOURCE,send_type)
	values
	(#{messageId,jdbcType=VARCHAR},#{channelCode,jdbcType=VARCHAR},
	#{businessType,jdbcType=VARCHAR},
	#{tenantCode,jdbcType=VARCHAR},
	#{userId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
	#{nickname,jdbcType=VARCHAR}, #{sendAccount,jdbcType=VARCHAR},
	#{acceptedAccount,jdbcType=VARCHAR},
	#{replyAccount,jdbcType=VARCHAR},
	#{createTime,jdbcType=TIMESTAMP}, sysdate,
	#{sessionId,jdbcType=VARCHAR}, #{skillQueue,jdbcType=VARCHAR},
	#{workNo,jdbcType=VARCHAR},
	#{status,jdbcType=VARCHAR},
	#{msgType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
	#{followData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
	#{skillType,jdbcType=VARCHAR},#{messageSource,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR})
	</insert>
	<insert id="add" parameterType="cn.sh.ideal.model.MessageInfo">
		<selectKey resultType="java.lang.String" order="BEFORE" keyProperty="messageId">  
			 SELECT SEQ_MGW_MESSAGE_INFO.NEXTVAL FROM DUAL
	    </selectKey>
	insert into MGW_MESSAGE_INFO (MESSAGE_ID,CHANNEL_CODE, BUSINESS_TYPE,
	TENANT_CODE, USER_ID, SOURCE,
	NICKNAME, SEND_ACCOUNT, ACCEPTED_ACCOUNT,
	REPLY_ACCOUNT, CREATE_TIME, SEND_TIME,
	SESSION_ID, SKILL_QUEUE,
	WORK_NO,
	STATUS, MSG_TYPE, CONTENT,
	FOLLOW_DATA, REMARK,SKILL_TYPE,MESSAGE_SOURCE,send_type)
	values
	(#{messageId,jdbcType=VARCHAR},#{channelCode,jdbcType=VARCHAR},
	#{businessType,jdbcType=VARCHAR},
	#{tenantCode,jdbcType=VARCHAR},
	#{userId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
	#{nickname,jdbcType=VARCHAR}, #{sendAccount,jdbcType=VARCHAR},
	#{acceptedAccount,jdbcType=VARCHAR},
	#{replyAccount,jdbcType=VARCHAR},
	#{createTime,jdbcType=TIMESTAMP}, sysdate,
	#{sessionId,jdbcType=VARCHAR}, #{skillQueue,jdbcType=VARCHAR},
	#{workNo,jdbcType=VARCHAR},
	#{status,jdbcType=VARCHAR},
	#{msgType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
	#{followData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
	#{skillType,jdbcType=VARCHAR},#{messageSource,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR})
	</insert>
	 <select id="getMessages" parameterType="String" resultMap="sessionResultMap">
	 	select * from mgw_message_info where session_id = #{sessionId,jdbcType=VARCHAR}
	 </select>
</mapper>
