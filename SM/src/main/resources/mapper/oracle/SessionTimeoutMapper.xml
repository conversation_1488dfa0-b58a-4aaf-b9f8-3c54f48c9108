<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.SessionTimeoutDao">
	<resultMap id="baseResultMap" type="sessionTimeout">
		<id column="ID" property="id" />
		<result column="TENANT_CODE" property="tenantCode" />
		<result column="AG_SESSION_TIMEOUT" property="agSessionTimeout" />
		<result column="AG_SESSION_TIMEOUT2" property="agSessionTimeout2" />
		<result column="USER_SESSION_TIMEOUT" property="userSessionTimeout" />
		<result column="USER_SESSION_TIMEOUT2" property="userSessionTimeout2" />
		<result column="AG_SESSION_TIMEOUT_ACTION" property="agSessionTimeoutAction" />
		<result column="AG_SESSION_TIMEOUT_ACTION2" property="agSessionTimeoutAction2" />
		<result column="USER_SESSION_TIMEOUT_ACTION" property="userSessionTimeoutAction" />
		<result column="USER_SESSION_TIMEOUT_ACTION2" property="userSessionTimeoutAction2" />
		<result column="skillQueue" property="skillQueue" />
		<result column="acceptedAccount" property="acceptedAccount" />
		<result column="sorttimeout" property="sorttimeout" />
		<result column="sorttimeout_holiday" property="sorttimeoutHoliday" />
	</resultMap>
	<resultMap id="defaultResultMap" type="sessionTimeout">
		<id column="ID" property="id" />
		<result column="TENANT_CODE" property="tenantCode" />
		<result column="AG_SESSION_TIMEOUT" property="agSessionTimeout" />
		<result column="AG_SESSION_TIMEOUT2" property="agSessionTimeout2" />
		<result column="USER_SESSION_TIMEOUT" property="userSessionTimeout" />
		<result column="USER_SESSION_TIMEOUT2" property="userSessionTimeout2" />
		<result column="AG_SESSION_TIMEOUT_ACTION" property="agSessionTimeoutAction" />
		<result column="AG_SESSION_TIMEOUT_ACTION2" property="agSessionTimeoutAction2" />
		<result column="USER_SESSION_TIMEOUT_ACTION" property="userSessionTimeoutAction" />
		<result column="USER_SESSION_TIMEOUT_ACTION2" property="userSessionTimeoutAction2" />
		<result column="sorttimeout" property="sorttimeout" />
		<result column="sorttimeout_holiday" property="sorttimeoutHoliday" />
	</resultMap>
	<!-- 超时设置信息 -->
	<select id="getAllList" parameterType="sessionTimeout" resultMap="baseResultMap">
		select *
		from MGW_SESSION_TIMEOUT
		where 1 = 1
		<if test="tenantCode != null and tenantCode != ''">
			TENANT_CODE = #{tenantCode}
		</if>
	</select>
	<select id="getDefaultAllList" parameterType="sessionTimeout" resultMap="defaultResultMap">
		select *
		from mgw_session_timeout_default
		where 1 = 1
		<if test="tenantCode != null and tenantCode != ''">
			TENANT_CODE = #{tenantCode}
		</if>
	</select>
</mapper>
