<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.SessionEventInfoDao">
	<resultMap id="sessionEventInfoResultMap" type="sessionEventInfo">
		<id column="ID" property="id" />
		<result column="TENANT_CODE" property="tenantCode" />
		<result column="EVENT" property="event" />
		<result column="SESSION_STATUS" property="sessionStatus"/>
		<result column="URL" property="url"/>
		<result column="IS_IGNORE" property="isIgnore"/>
		<result column="CURRENT_STATUS" property="currentStatus"/>
	</resultMap>
	<!-- 获取系统参数 -->
	<select id="getAllList" parameterType="sessionEventInfo" resultMap="sessionEventInfoResultMap">
		select ID,TENANT_CODE,EVENT,SESSION_STATUS,URL,IS_IGNORE,CURRENT_STATUS
		from MGW_SESSION_EVENT_INFO order by EVENT_PRIORITY desc
	</select>
</mapper>
