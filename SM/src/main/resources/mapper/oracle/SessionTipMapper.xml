<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.SessionTipDao">
	<resultMap id="baseResultMap" type="cn.sh.ideal.model.SessionTip">
		<id column="ID" property="id" />
		<result column="TENANT_CODE" property="tenantCode" />
		<result column="AG_TIMEOUT_TIP" property="agTimeoutTip" />
		<result column="AG_TIMEOUT_CLOSE_TIP" property="agTimeoutCloseTip" />
		<result column="USER_TIMEOUT_TIP" property="userTimeoutTip" />
		<result column="USER_CLOSE_SESSION_TIP" property="userCloseSessionTip" />
		<result column="AG_CLOSE_SESSION_TIP" property="agCloseSessionTip" />
		<result column="AG_CLOSE_MUTUAL_TIP" property="agCloseMutualTip" />
		<result column="USER_TIMEOUT_CLOSE_TIP" property="userTimeoutCloseTip" />
		<result column="skillQueue" property="skillQueue" />
		<result column="acceptAccount" property="acceptedAccount" />
		<result column="AG_TIMEOUT_TOUSER_TIP" property="agTimeOutToUserTip" />
		<result column="AG_TIMEOUT_CLOSE_TOUSER_TIP" property="agTimeOutCloseToUserTip" />
		<result column="AG_CLOSE_SESSION_TOUSER_TIP" property="agCloseSessionToUserTip" />
		<result column="USER_TIMEOUT_TOAG_TIP" property="userTimeOutToAGTip" />
		<result column="USER_CLOSE_SESSION_TOAG_TIP" property="userCloseSessionToAGTip" />
		<result column="USER_TIMEOUT_CLOSE_TOAG_TIP" property="userTimeOutCloseToAGTip" />
		<result column="WORK_TIME" property="workTime" />
		<result column="WELCOME_MESSAGE" property="welcomeMessage" />
		<result column="CONNECT_MESSAGE" property="connectMessage" />
		<result column="SORT_MESSAGE" property="sortMessage" />
		<result column="IS_SHOW_QUEUECOUNT" property="isShowQueuecount" />
		<result column="WORK_TIMEOUT" property="workTimeOut" />
		<result column="IS_SORTTIMEOUT" property="isSortTimeOut" />
		<result column="SORTTIMEOUTTIP" property="sortTimeOutTip" />
		<result column="SORT_QUEUECOUNTTIP" property="sortQueueCountTip" />
		<result column="SORTING_MESSAGE_TIP" property="sortingMessageTip" />
		<result column="WORK_TIME_holiday" property="workTimeHoliday" />
		<result column="WELCOME_MESSAGE_holiday" property="welcomeMessageHoliday" />
		<result column="WORK_TIMEOUT_holiday" property="workTimeOutHoliday" />
		<result column="CONNECT_MESSAGE_holiday" property="connectMessageHoliday" />
		<result column="SORT_MESSAGE_holiday" property="sortMessageHoliday" />
		<result column="IS_SHOW_QUEUECOUNT_holiday" property="isShowQueuecountHoliday" />
		<result column="IS_SORTTIMEOUT_holiday" property="isSortTimeoutHoliday" />
		<result column="SORTTIMEOUTTIP_holiday" property="sortTimeoutTipHoliday" />
		<result column="SORT_QUEUECOUNTTIP_holiday" property="sortQueuecountTipHoliday" />
		<result column="SORTING_MESSAGE_TIP_holiday" property="sortingMessageTipHoliday" />
		<result column="holiday" property="holiday" />
	</resultMap>
	
	<resultMap id="defaultResultMap" type="cn.sh.ideal.model.SessionTip">
		<id column="ID" property="id" />
		<result column="TENANT_CODE" property="tenantCode" />
		<result column="AG_TIMEOUT_TIP" property="agTimeoutTip" />
		<result column="AG_TIMEOUT_CLOSE_TIP" property="agTimeoutCloseTip" />
		<result column="USER_TIMEOUT_TIP" property="userTimeoutTip" />
		<result column="USER_TIMEOUT_CLOSE_TIP" property="userTimeoutCloseTip" />
		<result column="USER_CLOSE_SESSION_TIP" property="userCloseSessionTip" />
		<result column="AG_CLOSE_SESSION_TIP" property="agCloseSessionTip" />
		<result column="AG_CLOSE_MUTUAL_TIP" property="agCloseMutualTip" />
		<result column="AG_TIMEOUT_TOUSER_TIP" property="agTimeOutToUserTip" />
		<result column="AG_TIMEOUT_CLOSE_TOUSER_TIP" property="agTimeOutCloseToUserTip" />
		<result column="AG_CLOSE_SESSION_TOUSER_TIP" property="agCloseSessionToUserTip" />
		<result column="USER_TIMEOUT_TOAG_TIP" property="userTimeOutToAGTip" />
		<result column="USER_CLOSE_SESSION_TOAG_TIP" property="userCloseSessionToAGTip" />
		<result column="USER_TIMEOUT_CLOSE_TOAG_TIP" property="userTimeOutCloseToAGTip" />
		<result column="WORK_TIME" property="workTime" />
		<result column="WELCOME_MESSAGE" property="welcomeMessage" />
		<result column="CONNECT_MESSAGE" property="connectMessage" />
		<result column="SORT_MESSAGE" property="sortMessage" />
		<result column="IS_SHOW_QUEUECOUNT" property="isShowQueuecount" />
		<result column="WORK_TIMEOUT" property="workTimeOut" />
		<result column="IS_SORTTIMEOUT" property="isSortTimeOut" />
		<result column="SORTTIMEOUTTIP" property="sortTimeOutTip" />
		<result column="SORT_QUEUECOUNTTIP" property="sortQueueCountTip" />
		<result column="SORTING_MESSAGE_TIP" property="sortingMessageTip" />
		<result column="WORK_TIME_holiday" property="workTimeHoliday" />
		<result column="WELCOME_MESSAGE_holiday" property="welcomeMessageHoliday" />
		<result column="WORK_TIMEOUT_holiday" property="workTimeOutHoliday" />
		<result column="CONNECT_MESSAGE_holiday" property="connectMessageHoliday" />
		<result column="SORT_MESSAGE_holiday" property="sortMessageHoliday" />
		<result column="IS_SHOW_QUEUECOUNT_holiday" property="isShowQueuecountHoliday" />
		<result column="IS_SORTTIMEOUT_holiday" property="isSortTimeoutHoliday" />
		<result column="SORTTIMEOUTTIP_holiday" property="sortTimeoutTipHoliday" />
		<result column="SORT_QUEUECOUNTTIP_holiday" property="sortQueuecountTipHoliday" />
		<result column="SORTING_MESSAGE_TIP_holiday" property="sortingMessageTipHoliday" />
		<result column="holiday" property="holiday" />
	</resultMap>
	<!-- 获取系统参数 -->
	<select id="getAllList" parameterType="cn.sh.ideal.model.SessionTip" resultMap="baseResultMap">
		select *
		from MGW_SESSION_TIP
		where 1 = 1 and status ='1'
		<if test="tenantCode != null and tenantCode != ''">
			TENANT_CODE = #{tenantCode}
		</if>
	</select>
	
	<select id="getDefaultList" parameterType="cn.sh.ideal.model.SessionTip" resultMap="defaultResultMap">
		select *
		from mgw_session_tip_default
		where 1 = 1
		<if test="tenantCode != null and tenantCode != ''">
			TENANT_CODE = #{tenantCode}
		</if>
	</select>
	
	<select id="getCurDate" resultType="string">
	  SELECT TO_CHAR(sysdate, 'MM-DD') FROM DUAL
	</select>
</mapper>
