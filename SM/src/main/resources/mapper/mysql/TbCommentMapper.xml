<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.TbCommentDao">
 
 	<select id="queryCommentById" resultType="cn.sh.ideal.sm.model.TbComment" parameterType="cn.sh.ideal.sm.model.TbComment">
 		
 		SELECT * 
 		FROM tb_good_comment
 		WHERE ID = #{id,jdbcType=VARCHAR}
 	
 	</select>
 	
 
 
 	<insert id="insert" parameterType="cn.sh.ideal.sm.model.TbComment">
 		INSERT INTO
		TB_NEUTRAL_BAD_COMMENT
		(
		TID,
		OID,
		ROLE,
		NICK,
		RATED_NICK,
		RESULT,
		TITLE,
		PRICE,
		CONTENT,
		REPLY,
		NUM_IID,
		VALID_SCORE,
		CREATED,
		IMPORT_TIME,
		REPLY_STATUS,
		MARK,
		GOOD_COMMENT_MARK
		)VALUES(
		#{tid,jdbcType=VARCHAR},
		#{oid,jdbcType=VARCHAR},
		#{role,jdbcType=VARCHAR},
		#{nick,jdbcType=VARCHAR},
		#{ratedNick,jdbcType=VARCHAR},
		#{result,jdbcType=VARCHAR},
		#{title,jdbcType=VARCHAR},
		#{price,jdbcType=VARCHAR},
		#{content,jdbcType=VARCHAR},
		#{reply,jdbcType=VARCHAR},
		#{numIid,jdbcType=VARCHAR},
		#{validScore,jdbcType=VARCHAR},
		#{created,jdbcType=VARCHAR},
		NOW(),
		#{replyStatus,jdbcType=VARCHAR},
		#{mark,jdbcType=VARCHAR},
		'1'
		)
 	
 	</insert>
 	
 	
 	<select id="isExist" resultType="int" parameterType="cn.sh.ideal.sm.model.TbComment">
	
		SELECT COUNT(1)
		FROM TB_NEUTRAL_BAD_COMMENT
		WHERE ID = #{id,jdbcType=VARCHAR}
	</select>
 	
 	
</mapper>
