<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.SessionLogDao">
     <!-- 插入 -->
    <insert id="add"  parameterType="sessionStatusLog">
        insert into MGW_SESSION_STATUS_LOG
        (
        SESSION_ID,
        TENANT_CODE,
        <if test="sessionStatus != null and sessionStatus != ''">
            SESSION_STATUS,
        </if>

        <if test="sessionSymbol != null and sessionSymbol != ''">
            SESSION_SYMBOL,
        </if>
        <if test="skillQueue != null and skillQueue != ''">
            SKILL_QUEUE,
        </if>
        <if test="businessType != null and businessType !=''">
            BUSINESS_TYPE,
        </if>
        <if test="workNo != null and workNo !=''">
            WORK_NO,
        </if>
        <if test="channelCode != null and channelCode != ''">
            CHANNEL_CODE,
        </if>
        <if test="sendAccount != null and sendAccount != ''">
            SEND_ACCOUNT,
        </if>
        <if test="acceptedAccount != null and acceptedAccount != ''">
            ACCEPTED_ACCOUNT,
        </if>
        <if test="extTime != null">
            EXT_TIME,
        </if>
        <if test="changeStatus != null and changeStatus != ''">
            CHANGE_STATUS,
        </if>

        CREATE_TIME
        )
        values
        (
        #{sessionId},
        #{tenantCode},
        <if test="sessionStatus != null and sessionStatus != ''">
            #{sessionStatus},
        </if>

        <if test="sessionSymbol != null and sessionSymbol != ''">
            #{sessionSymbol},
        </if>
        <if test="skillQueue != null and skillQueue != ''">
            #{skillQueue},
        </if>
        <if test="businessType != null and businessType !=''">
            #{businessType},
        </if>
        <if test="workNo != null and workNo !=''">
            #{workNo},
        </if>
        <if test="channelCode != null and channelCode != ''">
            #{channelCode},
        </if>
        <if test="sendAccount != null and sendAccount != ''">
            #{sendAccount},
        </if>
        <if test="acceptedAccount != null and acceptedAccount != ''">
            #{acceptedAccount},
        </if>
        <if test="extTime != null">
            #{extTime},
        </if>
        <if test="changeStatus != null and changeStatus != ''">
            #{changeStatus},
        </if>
        <if test="createTime !=null">
            CURRENT_TIMESTAMP(6)
        </if>

        )
    </insert>
   

</mapper>
