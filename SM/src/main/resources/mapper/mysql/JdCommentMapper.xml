<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.JdCommentDao">

	<select id="queryCommentById" resultType="cn.sh.ideal.sm.model.JdComment"  parameterType="cn.sh.ideal.sm.model.JdComment">
	
		SELECT * 
		FROM JD_GOOD_COMMENT 
		WHERE ID = #{id,jdbcType=VARCHAR}
	</select>
	
		<insert id="insert" parameterType="cn.sh.ideal.sm.model.JdComment">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. This element was generated on Mon Jun 20 
			15:07:13 CST 2016. -->
		insert into JD_NEUTRAL_BAD_COMMENT
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				ID,
			</if>
			<if test="guid != null">
				GUID,
			</if>
			<if test="content != null">
				CONTENT,
			</if>
			<if test="creationTime != null">
				CREATION_TIME,
			</if>
			<if test="isTop != null">
				IS_TOP,
			</if>
			<if test="referenceId != null">
				REFERENCE_ID,
			</if>
			<if test="referenceImage != null">
				REFERENCE_IMAGE,
			</if>
			<if test="referenceName != null">
				REFERENCE_NAME,
			</if>
			<if test="referenceTime != null">
				REFERENCE_TIME,
			</if>
			<if test="referenceType != null">
				REFERENCE_TYPE,
			</if>
			<if test="referenceTypeId != null">
				REFERENCE_TYPE_ID,
			</if>
			<if test="firstCategory != null">
				FIRST_CATEGORY,
			</if>
			<if test="secondCategory != null">
				SECOND_CATEGORY,
			</if>
			<if test="thirdCategory != null">
				THIRD_CATEGORY,
			</if>
			<if test="replyCount != null">
				REPLY_COUNT,
			</if>
			<if test="score != null">
				SCORE,
			</if>
			<if test="status != null">
				STATUS,
			</if>
			<if test="title != null">
				TITLE,
			</if>
			<if test="usefulVoteCount != null">
				USEFUL_VOTE_COUNT,
			</if>
			<if test="uselessVoteCount != null">
				USELESS_VOTE_COUNT,
			</if>
			<if test="userImage != null">
				USER_IMAGE,
			</if>
			<if test="userImageUrl != null">
				USER_IMAGE_URL,
			</if>
			<if test="userLevelId != null">
				USER_LEVEL_ID,
			</if>
			<if test="userProvince != null">
				USER_PROVINCE,
			</if>
			<if test="userRegisterTime != null">
				USER_REGISTER_TIME,
			</if>
			<if test="viewCount != null">
				VIEW_COUNT,
			</if>
			<if test="orderId != null">
				ORDER_ID,
			</if>
			<if test="isReplyGrade != null">
				IS_REPLY_GRADE,
			</if>
			<if test="nickname != null">
				NICKNAME,
			</if>
			<if test="userClient != null">
				USER_CLIENT,
			</if>
			<if test="mergeOrderStatus != null">
				MERGE_ORDER_STATUS,
			</if>
			<if test="discussionId != null">
				DISCUSSION_ID,
			</if>
			<if test="productColor != null">
				PRODUCT_COLOR,
			</if>
			<if test="productSize != null">
				PRODUCT_SIZE,
			</if>
			<if test="imageCount != null">
				IMAGE_COUNT,
			</if>
			<if test="integral != null">
				INTEGRAL,
			</if>
			<if test="anonymousFlag != null">
				ANONYMOUS_FLAG,
			</if>
			<if test="userLevelName != null">
				USER_LEVEL_NAME,
			</if>
			<if test="recommend != null">
				RECOMMEND,
			</if>
			<if test="userLevelColor != null">
				USER_LEVEL_COLOR,
			</if>
			<if test="userClientShow != null">
				USER_CLIENT_SHOW,
			</if>
			<if test="isMobile != null">
				IS_MOBILE,
			</if>
			<if test="days != null">
				DAYS,
			</if>
			<if test="importTime != null">
				IMPORT_TIME,
			</if>
			<if test="isAnswer != null">
				IS_ANSWER,
			</if>
			<if test="isPush != null">
				IS_PUSH,
			</if>
			<if test="replyWorkNo != null">
				REPLY_WORKNO,
			</if>
			<if test="replyContent != null">
				REPLY_CONTENT,
			</if>
			<if test="goodCommentMark != null">
				GOOD_COMMENT_MARK,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=VARCHAR},
			</if>
			<if test="guid != null">
				#{guid,jdbcType=VARCHAR},
			</if>
			<if test="content != null">
				#{content,jdbcType=VARCHAR},
			</if>
			<if test="creationTime != null">
				#{creationTime,jdbcType=VARCHAR},
			</if>
			<if test="isTop != null">
				#{isTop,jdbcType=DECIMAL},
			</if>
			<if test="referenceId != null">
				#{referenceId,jdbcType=VARCHAR},
			</if>
			<if test="referenceImage != null">
				#{referenceImage,jdbcType=VARCHAR},
			</if>
			<if test="referenceName != null">
				#{referenceName,jdbcType=VARCHAR},
			</if>
			<if test="referenceTime != null">
				#{referenceTime,jdbcType=VARCHAR},
			</if>
			<if test="referenceType != null">
				#{referenceType,jdbcType=VARCHAR},
			</if>
			<if test="referenceTypeId != null">
				#{referenceTypeId,jdbcType=VARCHAR},
			</if>
			<if test="firstCategory != null">
				#{firstCategory,jdbcType=VARCHAR},
			</if>
			<if test="secondCategory != null">
				#{secondCategory,jdbcType=VARCHAR},
			</if>
			<if test="thirdCategory != null">
				#{thirdCategory,jdbcType=VARCHAR},
			</if>
			<if test="replyCount != null">
				#{replyCount,jdbcType=DECIMAL},
			</if>
			<if test="score != null">
				#{score,jdbcType=DECIMAL},
			</if>
			<if test="status != null">
				#{status,jdbcType=DECIMAL},
			</if>
			<if test="title != null">
				#{title,jdbcType=VARCHAR},
			</if>
			<if test="usefulVoteCount != null">
				#{usefulVoteCount,jdbcType=VARCHAR},
			</if>
			<if test="uselessVoteCount != null">
				#{uselessVoteCount,jdbcType=VARCHAR},
			</if>
			<if test="userImage != null">
				#{userImage,jdbcType=VARCHAR},
			</if>
			<if test="userImageUrl != null">
				#{userImageUrl,jdbcType=VARCHAR},
			</if>
			<if test="userLevelId != null">
				#{userLevelId,jdbcType=VARCHAR},
			</if>
			<if test="userProvince != null">
				#{userProvince,jdbcType=VARCHAR},
			</if>
			<if test="userRegisterTime != null">
				#{userRegisterTime,jdbcType=VARCHAR},
			</if>
			<if test="viewCount != null">
				#{viewCount,jdbcType=DECIMAL},
			</if>
			<if test="orderId != null">
				#{orderId,jdbcType=DECIMAL},
			</if>
			<if test="isReplyGrade != null">
				#{isReplyGrade,jdbcType=VARCHAR},
			</if>
			<if test="nickname != null">
				#{nickname,jdbcType=VARCHAR},
			</if>
			<if test="userClient != null">
				#{userClient,jdbcType=DECIMAL},
			</if>
			<if test="mergeOrderStatus != null">
				#{mergeOrderStatus,jdbcType=DECIMAL},
			</if>
			<if test="discussionId != null">
				#{discussionId,jdbcType=DECIMAL},
			</if>
			<if test="productColor != null">
				#{productColor,jdbcType=VARCHAR},
			</if>
			<if test="productSize != null">
				#{productSize,jdbcType=VARCHAR},
			</if>
			<if test="imageCount != null">
				#{imageCount,jdbcType=VARCHAR},
			</if>
			<if test="integral != null">
				#{integral,jdbcType=DECIMAL},
			</if>
			<if test="anonymousFlag != null">
				#{anonymousFlag,jdbcType=DECIMAL},
			</if>
			<if test="userLevelName != null">
				#{userLevelName,jdbcType=VARCHAR},
			</if>
			<if test="recommend != null">
				#{recommend,jdbcType=VARCHAR},
			</if>
			<if test="userLevelColor != null">
				#{userLevelColor,jdbcType=VARCHAR},
			</if>
			<if test="userClientShow != null">
				#{userClientShow,jdbcType=VARCHAR},
			</if>
			<if test="isMobile != null">
				#{isMobile,jdbcType=VARCHAR},
			</if>
			<if test="days != null">
				#{days,jdbcType=DECIMAL},
			</if>
			<if test="importTime != null">
				#{importTime,jdbcType=TIMESTAMP},
			</if>
			<if test="isAnswer != null">
				#{isAnswer,jdbcType=VARCHAR},
			</if>
			<if test="isPush != null">
				#{isPush,jdbcType=VARCHAR},
			</if>
			<if test="replyWorkNo != null">
				#{replyWorkNo,jdbcType=VARCHAR},
			</if>
			<if test="replyContent != null">
				#{replyContent,jdbcType=VARCHAR}
			</if>
			<if test="goodCommentMark != null">
				#{goodCommentMark,jdbcType=VARCHAR}
			</if>
		</trim>
	</insert>
	
	<select id="isExist" resultType="int" parameterType="cn.sh.ideal.sm.model.JdComment">
	
		SELECT COUNT(1)
		FROM JD_NEUTRAL_BAD_COMMENT
		WHERE ID = #{id,jdbcType=VARCHAR}
	</select>
	
</mapper>
