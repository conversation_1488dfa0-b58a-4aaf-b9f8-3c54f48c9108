<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.SessionTimeoutLogDao">

	<resultMap type="cn.sh.ideal.model.TimeOutSession" id="checkTimeOutActiveLogResultMap">
		<id column="ID" property="id" jdbcType="INTEGER"/>
		<result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR"/>
        <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="LAST_ACTIVE_TIME" property="lastActiveTime" jdbcType="TIMESTAMP"/>
        <result column="LAST_ACTIVE_USER" property="lastActiveUser" jdbcType="VARCHAR"/>
        <result column="TIMEOUT_COUNT" property="timeoutCount" jdbcType="INTEGER"/>
        <result column="TIMEOUT_TYPE" property="timeoutType" jdbcType="VARCHAR"/>
        <result column="OPT_TIME" property="optTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
    <!-- 查询技能组分类会话数 -->
    <select id="querySkillTypeNum" resultType="cn.sh.ideal.sm.resp.SkillTypeNum" parameterType="cn.sh.ideal.sm.req.TenantData">
		select o.skill_type skillType, b.work_no workNo, count(1) skillNum  from mgw_session_info o,
		 (select n.work_no, n.session_id, count(1)  t_num  from mgw_session_workno n 
		   where 1=1 and n.tenant_code=#{tenantCode} 
		 <if test="startTime != null and startTime !=''">
			<![CDATA[  and  DATE_FORMAT(n.create_time,'%Y-%m-%d') >= #{startTime}]]>
		</if>
		 <if test="endTime != null and endTime !=''">
			<![CDATA[  and  DATE_FORMAT(n.create_time,'%Y-%m-%d') <=#{endTime} ]]>
		</if>
		 <if test="workNo != null and workNo !=''">
			and n.work_no=#{workNo}
		</if>
		 group by  n.work_no,n.session_id) b where o.session_id=b.session_id
		 group by o.skill_type, b.work_no
		
	</select>
	
	<!-- 查询呼入类型会话数 -->
	<select id="queryCallStatuNum" resultType="cn.sh.ideal.sm.resp.CallStatuNum" parameterType="cn.sh.ideal.sm.req.TenantData">
		select o.call_status callStatus, b.work_no workNo,count(1) statuNum from mgw_session_info o,
		(select n.work_no,n.session_id,count(1) t_num from mgw_session_workno n 
		  where 1=1 and n.tenant_code=#{tenantCode} 
		    <if test="startTime != null and startTime !=''">
				<![CDATA[ and DATE_FORMAT(n.create_time,'%Y-%m-%d') >= #{startTime}]]>
			</if>
			 <if test="endTime != null and endTime !=''">
				<![CDATA[ and DATE_FORMAT(n.create_time,'%Y-%m-%d') <= #{endTime}]]>
			</if>
			 <if test="workNo != null and workNo !=''">
				 and  n.work_no=#{workNo}
			</if>
		  group by  n.work_no, n.session_id
		) b where o.session_id=b.session_id
		group by o.call_status, b.work_no
	</select>
	
	<!-- 查询最后一次小结内容 -->
	<select id="querySummarySection" resultType="hashmap"  parameterType="cn.sh.ideal.sm.req.TenantData">
		select work_no workNo, qes_desc qesDesc ,deal_scheme dealScheme from cuv_summary_section where start_time=(
		select max(n.start_time) from cuv_summary_section n where 1=1 and n.tenant_code=#{tenantCode} 
		     <if test="startTime != null and startTime !=''">
				<![CDATA[ and substring(start_time,1,10)>=#{startTime}]]>
			</if>
			 <if test="endTime != null and endTime !=''">
				<![CDATA[ and substring(start_time,1,10) <= #{endTime}]]>
			</if>
			 <if test="workNo != null and workNo !=''">
				 and   n.work_no=#{workNo}
			</if>
		)
	</select>
	<!-- 查询会话总数量  -->
	<select id="queryHisData" resultType="cn.sh.ideal.sm.resp.AgentHisData"  parameterType="cn.sh.ideal.sm.req.TenantData">
	
		select n.work_no workNo, count(1) sessionNum from MGW_SESSION_WORKNO n 
		where 1=1 and n.tenant_code=#{tenantCode} 
	     <if test="startTime != null and startTime !=''">
			<![CDATA[ and  date_format(n.create_time,'%Y-%m-%d') >= #{startTime} ]]>
		</if>
		 <if test="endTime != null and endTime !=''">
			<![CDATA[ and  date_format(n.create_time,'%Y-%m-%d') <= #{endTime}]]>
		</if>
		 <if test="workNo != null and workNo !=''">
			 and   n.work_no=#{workNo}
		</if>
		  group by  n.work_no
	</select>
	
   <insert id="checkTimeOutActiveLog" parameterType="cn.sh.ideal.model.TimeOutSession">
		insert into MGW_SESSION_TIMEOUT_LOG_ACTIVE(SESSION_ID,TENANT_CODE,LAST_ACTIVE_TIME,LAST_ACTIVE_USER,TIMEOUT_COUNT,TIMEOUT_TYPE,OPT_TIME) 
		values(#{sessionId,jdbcType=VARCHAR},#{tenantCode,jdbcType=VARCHAR},#{lastActiveTime,jdbcType=TIMESTAMP},#{lastActiveUser,jdbcType=VARCHAR},
		#{timeoutCount,jdbcType=INTEGER},#{timeoutType,jdbcType=VARCHAR},now())
	</insert>

</mapper>
