<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.MessageInfoDao">
   <!--旧的消息格式不支持新的消息格式-->
	<resultMap id="sessionResultMap" type="cn.sh.ideal.model.Session">
		<id column="SESSION_ID" property="sessionId" />
		<collection property="messages"
					ofType="cn.sh.ideal.model.Message" column="SESSION_ID">
			<id column="ID" property="id" />
			<result column="MESSAGE_ID" property="messageId" jdbcType="VARCHAR" />
			<result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
			<result column="CHANNEL_CODE" property="messageChannel"
					jdbcType="VARCHAR" />
			<result column="SEND_ACCOUNT" property="sendAccout" jdbcType="VARCHAR" />
			<result column="ACCEPTED_ACCOUNT" property="acceptedAccount"
					jdbcType="VARCHAR" />
			<result column="EXT_DATA" property="extData" jdbcType="VARCHAR" />
			<result column="TOOLBAR_ID" property="toolbarId" jdbcType="VARCHAR" />
			<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
			<result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
			<result column="TYPE" property="status" jdbcType="VARCHAR" />
			<result column="SKILL_QUEUE" property="skillQueue" jdbcType="VARCHAR" />
		</collection>

	</resultMap>
	
	<resultMap id="BaseResultMapMes"
		type="cn.sh.ideal.model.MessageInfo">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
		<result column="MESSAGE_ID" property="messageId" jdbcType="INTEGER" javaType="java.lang.String"/>
		<result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType"
			jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="USER_ID" property="userId" jdbcType="VARCHAR" />
		<result column="SOURCE" property="source" jdbcType="VARCHAR" />
		<result column="NICKNAME" property="nickname" jdbcType="VARCHAR" />
		<result column="SEND_ACCOUNT" property="sendAccount" jdbcType="VARCHAR" />
		<result column="ACCEPTED_ACCOUNT" property="acceptedAccount"
			jdbcType="VARCHAR" />
		<result column="REPLY_ACCOUNT" property="replyAccount"
			jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="DATE" />
		<result column="SEND_TIME" property="sendTime" jdbcType="DATE" />
		<result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
		<result column="SKILL_QUEUE" property="skillQueue" jdbcType="VARCHAR" />
		<result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="MSG_TYPE" property="msgType" jdbcType="VARCHAR" />
		<result column="CONTENT" property="content" jdbcType="VARCHAR" />
		<result column="FOLLOW_DATA" property="followData" jdbcType="VARCHAR" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
		<result column="SKILL_TYPE" property="skillType" jdbcType="VARCHAR" />
	</resultMap>
    <!-- 旧接口不支持新的消息格式-->
	<select id="getUnfinishSessionMessages" parameterType="hashMap"
			resultMap="sessionResultMap">
		SELECT
		s.SESSION_ID,m.ID,m.MESSAGE_ID,m.TOOLBAR_ID,s.CHANNEL_CODE,m.SEND_ACCOUNT,m.ACCEPTED_ACCOUNT,m.CREATE_TIME,s.SKILL_QUEUE,w.WORK_NO,m.TYPE,m.EXT_DATA
		FROM MGW_SESSION_INFO s,(
		<include refid="Messages" />
		) m ,MGW_SESSION_WORKNO w
		WHERE s.SESSION_ID = m.SESSION_ID
		and
		s.SESSION_ID = w.SESSION_ID
		and s.TENANT_CODE = #{tenantCode}
		and
		w.WORK_NO = #{workNo}

		<if test="sessionId != null and sessionId != ''">
			and s.SESSION_ID = #{sessionId}
		</if>
		<!-- 正在处理 -->
		<if test="sessionStatus == 0">
			and (s.status = '5' or s.status = '6' or s.status = '8')
		</if>
		<!-- 已关闭 -->
		<if test="sessionStatus == 1">
			and (s.status = '7' or s.status = '10' or s.status = '9')
		</if>
		<!-- 以延迟 -->
		<if test="sessionStatus == 2">
			and (s.status = '11' )
		</if>
	</select>
	<sql id="Messages">
		SELECT T3.*
		FROM (
		<include refid="From_Message" />
		UNION ALL
		<include refid="To_Message" />
		) T3
		ORDER BY T3.CREATE_TIME

	</sql>

	<sql id="From_Message">
		SELECT T2.ID,
		T2.SESSION_ID,
		T2.MESSAGE_ID,
		T2.EXT_DATA,
		S.SEND_ACCOUNT,
		S.ACCEPTED_ACCOUNT,
		T2.CREATE_TIME,
		'FROM' TYPE,
		T2.TOOLBAR_ID
		FROM MGW_MESSAGE T2,MGW_SESSION_INFO S
		WHERE T2.SESSION_ID
		= S.SESSION_ID
	</sql>
	<sql id="To_Message">
		SELECT T2.ID,
		T2.SESSIONID,
		T2.MESSAGE_ID,
		DECODE(T1.CHANNEL_CODE,
		'1002',
		'{\"attachment\":\"' || T2.FILE_PATH ||
		'\",\"contentHtml\":\"' || '\",\"content\":\"' ||
		T2.MEDIA_CONTENT ||
		'\",\"fromuser\":\"' ||
		T2.MEDIA_SENDER || '\",\"subject\":\"' ||
		T2.MEDIA_TITLE || '\",\"touser\":\"' ||
		T2.MEDIA_RECEIVER || '\"}',
		T2.MEDIA_CONTENT) AS EXT_DATA,
		T1.SEND_ACCOUNT,
		T1.ACCEPTED_ACCOUNT,
		T2.SENDTIME,
		'TO' TYPE,
		T2.TOOLBAR_ID
		FROM MGW_SESSION_INFO T1,
		MGW_MEDIA_SEND T2
		WHERE 1=1

		AND T1.SESSION_ID = T2.SESSIONID

	</sql>
	<select id="query" resultMap="BaseResultMapMes"
		parameterType="cn.sh.ideal.sm.model.MessageN">
		SELECT * FROM MGW_MESSAGE_INFO
		<include refid="where" />
	</select>
	
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- Where -->
	<!-- This element was generated on Thu Jun 05 16:38:50 CST 2014. -->
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<sql id="where">
		<where>
			<if test="channelCode != null and channelCode !=''">
				and
				CHANNEL_CODE = #{channelCode ,jdbcType=VARCHAR}
			</if>
			<if test="businessType != null and businessType !=''">
				and
				BUSINESS_TYPE = #{businessType ,jdbcType=VARCHAR}
			</if>
			<if test="tenantCode != null and tenantCode !=''">
				and
				TENANT_CODE = #{tenantCode ,jdbcType=VARCHAR}
			</if>
			<if test="userId != null and userId !=''">
				and
				USER_ID = #{userId ,jdbcType=VARCHAR}
			</if>
			<if test="source != null and source !=''">
				and
				SOURCE = #{source ,jdbcType=VARCHAR}
			</if>
			<if test="nickname != null and nickname !=''">
				and
				NICKNAME = #{nickname ,jdbcType=VARCHAR}
			</if>
			<if test="sendAccount != null and sendAccount !=''">
				and
				SEND_ACCOUNT = #{sendAccount ,jdbcType=VARCHAR}
			</if>
			<if test="acceptedAccount != null and acceptedAccount !=''">
				and
				ACCEPTED_ACCOUNT = #{acceptedAccount
				,jdbcType=VARCHAR}
			</if>
			<if test="replyAccount != null and replyAccount !=''">
				and
				REPLY_ACCOUNT = #{replyAccount ,jdbcType=VARCHAR}
			</if>
			<if test="createTime != null and createTime !=''">
				and
				CREATE_TIME = #{createTime ,jdbcType="DATE"}
			</if>
			<if test="sendTime != null and sendTime !=''">
				and
				SEND_TIME = #{sendTime ,jdbcType="DATE"}
			</if>
			<if test="sessionId != null and sessionId !=''">
				and
				SESSION_ID = #{sessionId ,jdbcType=VARCHAR}
			</if>
			<if test="skillQueue != null and skillQueue !=''">
				and
				SKILL_QUEUE = #{skillQueue ,jdbcType=VARCHAR}
			</if>
			<if test="workNo != null and workNo !=''">
				and
				WORK_NO = #{workNo ,jdbcType=VARCHAR}
			</if>
			<if test="status != null and status !=''">
				and
				STATUS = #{status ,jdbcType=VARCHAR}
			</if>
			<if test="msgType != null and msgType !=''">
				and
				MSG_TYPE = #{msgType ,jdbcType=VARCHAR}
			</if>
			<if test="content != null and content !=''">
				and
				CONTENT = #{content ,jdbcType=VARCHAR}
			</if>
			<if test="followData != null and followData !=''">
				and
				FOLLOW_DATA = #{followData ,jdbcType=VARCHAR}
			</if>
			<if test="remark != null and remark !=''">
				and
				REMARK = #{remark ,jdbcType=VARCHAR}
			</if>
			<if test="skillType != null and skillType !=''">
				and
				SKILL_TYPE = #{skillType ,jdbcType=VARCHAR}
			</if>

		</where>
	</sql>
	<insert id="insert" parameterType="cn.sh.ideal.model.MessageInfo" useGeneratedKeys="true" keyProperty="id">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
	insert into MGW_MESSAGE_INFO (CHANNEL_CODE, BUSINESS_TYPE,
	TENANT_CODE, USER_ID, SOURCE,
	NICKNAME, SEND_ACCOUNT, ACCEPTED_ACCOUNT,
	REPLY_ACCOUNT, CREATE_TIME, SEND_TIME,
	SESSION_ID, SKILL_QUEUE,
	WORK_NO,
	STATUS, MSG_TYPE, CONTENT,
	FOLLOW_DATA, REMARK,SKILL_TYPE,MESSAGE_SOURCE,send_type)
	values
	(#{channelCode,jdbcType=VARCHAR},
	#{businessType,jdbcType=VARCHAR},
	#{tenantCode,jdbcType=VARCHAR},
	#{userId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
	#{nickname,jdbcType=VARCHAR}, #{sendAccount,jdbcType=VARCHAR},
	#{acceptedAccount,jdbcType=VARCHAR},
	#{replyAccount,jdbcType=VARCHAR},
	#{createTime,jdbcType=TIMESTAMP}, sysdate(),
	#{sessionId,jdbcType=VARCHAR}, #{skillQueue,jdbcType=VARCHAR},
	#{workNo,jdbcType=VARCHAR},
	#{status,jdbcType=VARCHAR},
	#{msgType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
	#{followData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},#{skillType,jdbcType=VARCHAR},
	#{messageSource,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR})
	</insert>
	<insert id="add" parameterType="cn.sh.ideal.model.MessageInfo" useGeneratedKeys="true" keyProperty="id">
		<!-- WARNING - @mbggenerated This element is automatically generated by 
			MyBatis Generator, do not modify. -->
	insert into MGW_MESSAGE_INFO (CHANNEL_CODE, BUSINESS_TYPE,
	TENANT_CODE, USER_ID, SOURCE,
	NICKNAME, SEND_ACCOUNT, ACCEPTED_ACCOUNT,
	REPLY_ACCOUNT, CREATE_TIME, SEND_TIME,
	SESSION_ID, SKILL_QUEUE,
	WORK_NO,
	STATUS, MSG_TYPE, CONTENT,
	FOLLOW_DATA, REMARK,SKILL_TYPE,MESSAGE_SOURCE,send_type)
	values
	(#{channelCode,jdbcType=VARCHAR},
	#{businessType,jdbcType=VARCHAR},
	#{tenantCode,jdbcType=VARCHAR},
	#{userId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
	#{nickname,jdbcType=VARCHAR}, #{sendAccount,jdbcType=VARCHAR},
	#{acceptedAccount,jdbcType=VARCHAR},
	#{replyAccount,jdbcType=VARCHAR},
	#{createTime,jdbcType=TIMESTAMP}, sysdate(),
	#{sessionId,jdbcType=VARCHAR}, #{skillQueue,jdbcType=VARCHAR},
	#{workNo,jdbcType=VARCHAR},
	#{status,jdbcType=VARCHAR},
	#{msgType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
	#{followData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
	#{skillType,jdbcType=VARCHAR},#{messageSource,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR})
	</insert>
	 <select id="getMessages" parameterType="String" resultMap="sessionResultMap">
	 	select * from mgw_message_info where session_id = #{sessionId,jdbcType=VARCHAR}
	 </select>
</mapper>
