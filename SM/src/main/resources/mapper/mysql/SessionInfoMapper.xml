<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sh.ideal.sm.dao.SessionInfoDao">
    <!-- 会话信息 resultMap -->
    <resultMap id="BaseResultMap" type="sessionInfo">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR"/>
        <result column="SESSION_MODEL" property="sessionModel" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="STATUS_SYMBOL" property="statusSymbol" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="OPT_TIME" property="optTime" jdbcType="TIMESTAMP"/>
        <result column="SEND_ACCOUNT" property="sendAccount" jdbcType="VARCHAR"/>
        <result column="ACCEPTED_ACCOUNT" property="acceptedAccount" jdbcType="VARCHAR"/>
        <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="SKILL_QUEUE" property="skillQueue" jdbcType="VARCHAR"/>
        <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="WORKNO" property="workNos" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="SESSION_TYPE" property="sessionType" jdbcType="VARCHAR"/>
        <result column="SKILL_TYPE" property="skillType" jdbcType="VARCHAR"/>
        <result column="NICK_NAME" property="nickname" jdbcType="VARCHAR"/>
        <result column="CHANGE_STATUS" property="changeStatus" jdbcType="VARCHAR"/>
        <result column="CALL_STATUS" property="callStatus" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"/>
    </resultMap>
    <!--从mir转移过来旧的sessionInfo 定义   用于获取坐席关联的会话信息  -->
    <resultMap id="BaseAgSessionResultMap" type="agSession">
        <result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="WORKNO" property="workNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="SESSION_TYPE" property="sessionType" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseMessageResultMap" type="messageModel">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="MESSAGE_ID" property="messageId" jdbcType="VARCHAR"/>
        <result column="WORK_NO" property="workNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"/>
        <result column="SEND_ACCOUNT" property="sendAccount" jdbcType="VARCHAR"/>
        <result column="ACCEPTED_ACCOUNT" property="acceptedAccount" jdbcType="VARCHAR"/>
        <result column="EXT_DATA" property="extData" jdbcType="VARCHAR"/>
        <result column="TOOLBAR_ID" property="toolbarId" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="BEFORE_SKILL_QUEUE" property="beforeSkillQueue" jdbcType="VARCHAR"/>
        <result column="BEFORE_WORK_NO" property="beforeWorkNo" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 旧的消息格式  resultMap -->
    <resultMap id="HisMessageMap" type="hisMessage">
        <result column="WORKNO" property="workNo" jdbcType="VARCHAR"/>
        <result column="TIMEMSG" property="timemsg" jdbcType="TIMESTAMP"/>
        <result column="SESSIONID" property="sessionId" jdbcType="VARCHAR"/>
        <result column="CONTENTMSG" property="contentmsg" jdbcType="VARCHAR"/>
        <result column="CHANNEL" property="channel" jdbcType="VARCHAR"/>
        <result column="SENDACCOUNT" property="sendAccout" jdbcType="VARCHAR"/>
        <result column="ACCEPTEDACCOUNT" property="acceptAccout" jdbcType="VARCHAR"/>
        <result column="MESSAGEBUSINESS" property="messageBusiness" jdbcType="VARCHAR"/>
        <result column="RESULTTYPE" property="resultType" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 排队数据 resultMap -->
    <resultMap id="SessionSortItemMap" type="sessionSortItem">
        <result column="SKILLQUEUE" property="skillQueue" jdbcType="VARCHAR"/>
        <result column="QUEUENAME" property="queueName" jdbcType="TIMESTAMP"/>
        <result column="CHANNELCODE" property="channelCode" jdbcType="VARCHAR"/>
        <result column="CHANNELNAME" property="channelName" jdbcType="VARCHAR"/>
        <result column="SORTCOUNT" property="sortCount" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="SkillQueueWorkNoMap" type="skillQueueWorkNo">
        <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="SKILL_QUEUE" property="skillQueue" jdbcType="TIMESTAMP"/>
        <result column="WORK_NO" property="workNo" jdbcType="VARCHAR"/>
    </resultMap>
	<resultMap type="cn.sh.ideal.sm.resp.AgentStatistics"
		id="agentStatisticsResult">
		<result column="WORK_NO" property="workNo" jdbcType="VARCHAR" />
		<result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
		<result column="SESSION_ID" property="sessionId" jdbcType="VARCHAR" />
		<result column="SATIFIED" property="satisfied" jdbcType="DECIMAL" />
		<result column="SERVICE_TIME" property="serviceTime" jdbcType="DECIMAL"/>
		<result column="BEGIN_TIME" property="beginTime" jdbcType="TIMESTAMP"/>
		<result column="MIN_TIME" property="minTime" jdbcType="TIMESTAMP"/>
	</resultMap>
    <!-- 插入会话信息 -->
    <insert id="add" useGeneratedKeys="true" keyProperty="id" parameterType="sessionInfo">
        insert into
        MGW_SESSION_INFO (
        SESSION_ID,
        SESSION_MODEL,
        STATUS,
        STATUS_SYMBOL,
        CREATE_TIME,
        OPT_TIME,
        SEND_ACCOUNT,
        ACCEPTED_ACCOUNT,
        CHANNEL_CODE,
        BUSINESS_TYPE,
        TENANT_CODE,
        WORKNO,
        START_TIME,
        END_TIME,
        SESSION_TYPE,
        SKILL_TYPE,
        CHANGE_STATUS,
        CALL_STATUS,
        CUSTOMER_ID,
        CALL_ID,
        TASK_ID,
        NICK_NAME
        )
        values (
        #{sessionId,jdbcType=VARCHAR},
        #{sessionModel,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{statusSymbol,jdbcType=VARCHAR},
        CURRENT_TIMESTAMP(6),
        CURRENT_TIMESTAMP(6),
        #{sendAccount,jdbcType=VARCHAR},
        #{acceptedAccount,jdbcType=VARCHAR},
        #{channelCode,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{tenantCode,jdbcType=VARCHAR},
        #{workNos,jdbcType=VARCHAR},
        CURRENT_TIMESTAMP(6),
        CURRENT_TIMESTAMP(6),
        #{sessionType,jdbcType=VARCHAR},
        #{skillType,jdbcType=VARCHAR},
        #{changeStatus,jdbcType=VARCHAR},
        #{callStatus,jdbcType=VARCHAR},
        #{customerId,jdbcType=VARCHAR},
        #{callId,jdbcType=VARCHAR},
        #{taskId,jdbcType=VARCHAR},
        #{nickname,jdbcType=VARCHAR}
        )

    </insert>
    <!-- 获取未结束的会话信息 -->
    <select id="getUnfinishSession" parameterType="sessionInfo"
            resultMap="BaseResultMap">
        SELECT
       <include refid="SessionInfoSql"></include>
        WHERE
        1=1
        <if test="sessionModel != null and sessionModel !=''">
            AND e.SESSION_MODEL = #{sessionModel}
        </if>
        <if test="sessionId != null and sessionId !=''">
            AND e.SESSION_ID = #{sessionId}
        </if>
        <if test="tenantCode != null and tenantCode !=''">
            AND e.TENANT_CODE = #{tenantCode}
        </if>

        <if test="workNos != null and workNos !=''">
            AND e.WORKNO like '%'||#{workNos}||'%'
        </if>
        AND e.STATUS in ('1','2','3','5','6','8')
    </select>

    <!-- 获取未结束的会话信息 -->
    <select id="getMutualSession" parameterType="sessionInfo"
            resultMap="BaseResultMap">
		SELECT
		<include refid="SessionInfoSql"></include>
		where
	    e.TENANT_CODE = #{tenantCode}
	    AND e.SKILL_QUEUE = #{skillQueue}
		AND e.WORKNO like '%'||#{workNos}||'%'
		AND e.STATUS in('5','6','8','11')
	</select>
	
	<!-- 优化sql -->
	<select id="getSysTime" resultType="Date">
		SELECT CURRENT_TIMESTAMP(6)
	</select>
	
	<!-- 优化sql -->
	<select id="diffTime" parameterType="string" resultType="int">
		select sign(CAST(s.start_time AS DATE) - CAST(s.end_time AS DATE))
           from mgw_session_info s 
        	where s.session_id = #{sessionId}
	</select>
	
    <!-- 修改会话信息 -->
    <update id="edit" parameterType="sessionInfo">
        update MGW_SESSION_INFO
        <set>
            <if test="businessType != null and businessType != ''">
                BUSINESS_TYPE = #{businessType},
            </if>
            <if test="skillQueue != null and skillQueue != ''">
                SKILL_QUEUE = #{skillQueue},
            </if>
            <if test="workNos != null and workNos != ''">
                WORKNO = #{workNos},
            </if>
            <!-- <if test="status == 5">
               START_TIME = (case when (SELECT b.mi_time FROM ( SELECT
                (
                CAST(s.start_time AS DATE) - CAST(s.end_time AS DATE)
                ) mi_time
                FROM
                mgw_session_info s
                WHERE s.session_id = #{sessionId} ) b ) &gt;0
                then (SELECT a.start_time FROM (SELECT
                s.start_time
                FROM
                mgw_session_info s
                WHERE s.session_id = #{sessionId}) a )
                else CURRENT_TIMESTAMP(6) end),
            </if> -->
            <if test="status == 5">
            	START_TIME = #{startTime,jdbcType=TIMESTAMP},
            </if>
            
            <if test="status == 7 or status == 9 or status == 10">
                END_TIME = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != ''">
                STATUS = #{status},
            </if>
            <if test="statusSymbol != null and statusSymbol != ''">
                STATUS_SYMBOL = #{statusSymbol},
            </if>
            <if test="sendAccount != null and sendAccount !=''">
                SEND_ACCOUNT = #{sendAccount},
            </if>
            <if test="acceptedAccount != null and acceptedAccount !=''">
                ACCEPTED_ACCOUNT = #{acceptedAccount},
            </if>
            <if test="channelCode != null and channelCode !=''">
                CHANNEL_CODE = #{channelCode},
            </if>
            <if test="sessionModel != null and sessionModel !=''">
                SESSION_MODEL = #{sessionModel},
            </if>
            <if test="optTime != null and optTime != ''">
                OPT_TIME = CURRENT_TIMESTAMP(6),
            </if>
            <if test="sessionType != null and sessionType != ''">
                SESSION_TYPE = #{sessionType},
            </if>
            <if test="skillType != null and skillType != ''">
                SKILL_TYPE = #{skillType},
            </if>
            <if test="changeStatus != null and changeStatus != ''">
                CHANGE_STATUS = #{changeStatus,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null and taskId != ''">
                TASK_ID = #{taskId,jdbcType = VARCHAR},
            </if>
            <if test="callId != null and callId != ''">
                CALL_ID = #{callId,jdbcType = VARCHAR},
            </if>
             <if test="nickname != null and nickname != ''">
                NICK_NAME = #{nickname,jdbcType = VARCHAR}
            </if>
        </set>
        where SESSION_ID = #{sessionId}
    </update>

    <!-- 通过会话ID删除一个会话 -->
    <delete id="deleteById" parameterType="String">
		delete from
		MGW_SESSION_INFO
		where
		SESSION_ID = #{sessionId}
	</delete>

  
    <!-- 添加会话中的工号 -->
    <insert id="addSessionWorkNo" parameterType="sessionWorkNo">
		insert into
		MGW_SESSION_WORKNO (
		SESSION_ID,
		WORK_NO,
		TENANT_CODE,
		CREATE_TIME,
		START_TIME,
		END_TIME,
		JOIN_TYPE
		)
		values (
		#{sessionId,jdbcType=VARCHAR},
		#{workNo,jdbcType=VARCHAR},
		#{tenantCode},
		CURRENT_TIMESTAMP(6),
		CURRENT_TIMESTAMP(6),
		CURRENT_TIMESTAMP(6),
		#{joinType}
		)
	</insert>

    <!-- 删除会话中的工号 -->
    <update id="deleteSessionWorkNo" parameterType="hashmap">
        UPDATE
        MGW_SESSION_WORKNO
        <set>
         END_TIME = CURRENT_TIMESTAMP(),
        <if test="joinType != null and joinType != ''">
         JOIN_TYPE = #{joinType},
        </if>
        </set>
        where SESSION_ID = #{sessionId}
        and
        WORK_NO = #{workNo}
        and start_time = end_time
    </update>

    <!-- 坐席会话需要字段 -->
    <sql id="baseAgSessionSql">
		select
		t.start_time,
		w.work_no,
		t.session_id,
		t.status,
		t.tenant_code,
		t.end_time,
		c.channel_name,
		t.session_type,
		w.start_time service_start_time,
		w.end_time service_end_time
		FROM 
		MGW_SESSION_INFO t,Mgw_Session_Workno w,mgw_channel_info c 
		where t.session_id = w.session_id
		and t.channel_code = c.channel_code
		order by
		start_time 
		desc
	</sql>
	
	<sql id="SessionInfoSql">
		e.ID,e.SESSION_ID,e.SESSION_MODEL,e.STATUS,e.STATUS_SYMBOL,e.CREATE_TIME,e.OPT_TIME,e.SEND_ACCOUNT,e.ACCEPTED_ACCOUNT,e.CHANNEL_CODE,
        e.BUSINESS_TYPE,e.SKILL_QUEUE,e.TENANT_CODE,e.WORKNO,e.START_TIME,e.END_TIME,e.SESSION_TYPE,e.SKILL_TYPE,e.CHANGE_STATUS,e.CALL_STATUS,
        e.CUSTOMER_ID,e.NICK_NAME from MGW_SESSION_INFO e
	</sql>
	
  
    <!--旧的  获取会话数量  从mir转移过来 旧的  息  后面建议使用 getSessionCount -->
    <select id="getAgSessionCount" parameterType="hashMap"
            resultType="int">
        select count(1)
        FROM (
        <include refid="baseAgSessionSql"/>
        ) t WHERE 1=1
        <if test="sessionId !=null and sessionId!=''">
            AND t.SESSION_ID=#{sessionId}
        </if>
        <if test="workNo != null and workNo !=''">
            AND t.WORK_NO = #{workNo}
        </if>
        <if test="tenantCode != null and tenantCode !=''">
            AND t.TENANT_CODE = #{tenantCode}
        </if>
        <if test="sessionStatus == null or sessionStatus == ''">
            and (
            t.status &lt;&gt; '1' and t.status &lt;&gt; '2'
            and
            t.status &lt;&gt; '3' and t.status &lt;&gt; '4')
        </if>
        <if test="sessionStatus == 0 ">
            and (t.status = '5' or t.status = '6' or t.status = '8')
            and t.service_end_time &gt; t.service_start_time
        </if>
        <if test="sessionStatus == 1">
            and (t.status = '7' or t.status = '10' or t.status = '9')
        </if>
        <if test="sessionStatus == 2">
            and (t.status = '11' )
            and t.service_end_time &gt; t.service_start_time
        </if>
    </select>

    <select id="getSessionCount" parameterType="sessionData"
            resultType="int">
        select count(1) from
        (
        select distinct t.session_id
        FROM
        MGW_SESSION_INFO t
        left join
        MGW_SESSION_WORKNO o
        on
        t.session_id = o.session_id
        WHERE 1=1
        <if test="tenantCode != null and tenantCode != ''">
            AND
            <foreach collection="tenantCodes" item="t" open="("
                     separator="or" close=")">
                t.tenant_code = #{t}
            </foreach>
        </if>
        <if test="status != null and status !=''">
            AND
            <foreach collection="statuses" item="s" open="("
                     separator="or" close=")">
                <if test="status == 5 or status == 6 or status == 8">
                    ( t.STATUS = #{status} and o.start_time = o.end_time )
                </if>
                <if test="status == 7 or status == 9 or status == 10 or status == 11">
                    ( t.STATUS = #{status} and o.join_type = '1' )
                </if>
                <if test="status &lt; 5 ">
                    t.STATUS = #{status}
                </if>
            </foreach>
        </if>
        <!-- <if test="status != null and status !=''">
            AND t.STATUS = #{status}
        </if> -->
        <if test="skillQueue != null and skillQueue !=''">
            AND t.SKILL_QUEUE = #{skillQueue}
        </if>
        <if test="workNo != null and workNo !=''">
            AND o.WORK_NO = #{workNo}
        </if>
        <if test="beginDate != null and beginDate != ''">
            AND
            t.START_TIME &gt;=
            str_to_date(#{beginDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        <if test="endDate != null and endDate != ''">
            AND
            t.START_TIME &lt;=
            str_to_date(#{endDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        ) res
    </select>

    <!-- 获取一个未关闭的会话的技能组ID -->
    <select id="getSessionSkillQueue" parameterType="cn.sh.ideal.model.SessionInfo"
            resultType="String">
        SELECT
        t.SKILL_QUEUE
        FROM MGW_SESSION_INFO t WHERE 1=1
        <if test="tenantCode != null and tenantCode !=''">
            AND t.TENANT_CODE = #{tenantCode}
        </if>
        <if test="channelCode != null and channelCode !=''">
            AND t.CHANNEL_CODE = #{channelCode}
        </if>
        <if test="sendAccount != null and sendAccount !=''">
            AND t.SEND_ACCOUNT = #{sendAccount}
        </if>
        AND t.STATUS in ('1','2','3','5','6','8')
    </select>

    <!-- 旧的message表  获取最后一条消息 -->
    <select id="getMessage" parameterType="hashMap" resultMap="BaseMessageResultMap">

        select res.*
        from
        (
        SELECT T3.*
        FROM
        (
        <include refid="From_Message"/>
        UNION ALL
        <include refid="To_Message"/>
        ) T3
        ORDER BY T3.CREATE_TIME DESC
        ) res LIMIT 1


    </select>
    <!--旧的message表  分页获取消息数量 -->
    <select id="getMessageCount" parameterType="hashMap" resultType="int">
        SELECT count(1)
        FROM (
        <include refid="From_Message"/>
        UNION ALL
        <include refid="To_Message"/>
        )
    </select>

    <sql id="From_Message">
        SELECT T2.ID,T1.SESSION_ID,
        T2.MESSAGE_ID,
        W.WORK_NO,
        T1.CHANNEL_CODE,
        T1.SEND_ACCOUNT,
        T2.EXT_DATA,
        T1.ACCEPTED_ACCOUNT,
        T2.CREATE_TIME,
        'FROM'
        TYPE,
        T2.TOOLBAR_ID
        FROM MGW_SESSION_INFO T1, MGW_MESSAGE
        T2,MGW_SESSION_WORKNO W
        WHERE 1=1
        <if test="status != null and status !=''">
            AND T1.STATUS = #{status}
        </if>
        <if test="workNo != null and workNo !=''">
            AND W.WORK_NO = #{workNo}
        </if>

        <if test="sessionId != null and sessionId !=''">
            AND T1.SESSION_ID = #{sessionId}
        </if>
        AND T1.SESSION_ID = T2.SESSION_ID
        AND T1.SESSION_ID = W.SESSION_ID
        AND
        T2.WORK_NO = W.WORK_NO
    </sql>
    <sql id="To_Message">
        SELECT T2.ID,T1.SESSION_ID,
        T2.MESSAGE_ID,
        W.WORK_NO,
        T1.CHANNEL_CODE,
        T1.ACCEPTED_ACCOUNT,
        DECODE(T1.CHANNEL_CODE,
        '1002',
        '{\"attachment\":\"' || T2.FILE_PATH ||
        '\",\"contentHtml\":\"' ||
        '\",\"content\":\"' ||
        T2.MEDIA_CONTENT || '\",\"fromuser\":\"' ||
        T2.MEDIA_SENDER || '\",\"subject\":\"' ||
        T2.MEDIA_TITLE ||
        '\",\"touser\":\"' ||
        T2.MEDIA_RECEIVER || '\"}',
        T2.MEDIA_CONTENT) AS
        EXT_DATA,
        T1.SEND_ACCOUNT,
        T2.SENDTIME,
        'TO' TYPE,
        T2.TOOLBAR_ID
        FROM
        MGW_SESSION_INFO T1, MGW_MEDIA_SEND T2,MGW_SESSION_WORKNO W
        WHERE 1=1
        <if test="status != null and status !=''">
            AND T1.STATUS = #{status}
        </if>
        <if test="workNo != null and workNo !=''">
            AND W.WORK_NO = #{workNo}
        </if>
        <if test="sessionId != null and sessionId !=''">
            AND T1.SESSION_ID = #{sessionId}
        </if>
        AND T1.SESSION_ID = T2.SESSIONID
        AND T1.SESSION_ID = W.SESSION_ID
        AND
        T2.WORKNO = W.WORK_NO
    </sql>
    <!-- 获取会话信息 从mir转移过来旧的会话信息 -->
    <select id="getAgSession" parameterType="hashMap" resultMap="BaseResultMap">
        select DISTINCT * from
        MGW_SESSION_INFO t ,MGW_SESSION_WORKNO o
        where
        t.SESSION_ID = o.SESSION_ID
        <if test="skillType != null and skillType != ''">
            AND t.SKILL_TYPE in
            <foreach item="item" index="index" collection="skillTypes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 正在处理 -->
        <if test="sessionStatus == 0">
            and ((t.status = '5' or t.status = '6' or t.status = '8') and o.start_time = o.end_time)
        </if>
        <!-- 已关闭 -->
        <if test="sessionStatus == 1">
            and ((t.status = '7' or t.status = '10' or t.status = '9')  and o.join_type = '1')

        </if>
        <!-- 以延迟 -->
        <if test="sessionStatus == 2">
            and (t.status = '11' and  o.join_type = '1')
        </if>
        and o.work_no = #{workNo}
        and t.tenant_code = #{tenantCode}
    </select>


    <sql id="From_HisMsgs">
        SELECT
        T3.WORK_NO WORKNO,
        T1.CREATE_TIME TIMEMSG,
        T2.SESSION_ID SESSIONID,
        T1.EXT_DATA CONTENTMSG,
        T2.Channel_Code CHANNEL,
        T2.SEND_ACCOUNT SENDACCOUNT,
        T2.ACCEPTED_ACCOUNT ACCEPTEDACCOUNT,
        T2.BUSINESS_TYPE MESSAGEBUSINESS,
        'FROM' RESULTTYPE
        FROM
        MGW_MESSAGE T1,MGW_SESSION_INFO T2,MGW_SESSION_WORKNO T3
        WHERE 1=1
        and
        T1.Session_Id = T2.Session_Id
        and
        T2.Session_Id = T3.Session_Id
        AND
        T1.WORK_NO = T3.WORK_NO
        <if test="channelCode != null and channelCode != ''">
            AND T2.CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
        </if>
        <if test="sessionId != null and sessionId != ''">
            AND T2.SESSION_ID = #{sessionId,jdbcType=VARCHAR}
        </if>
        <if test="contentMsg != null and contentMsg != ''">
            AND
            T1.EXT_DATA LIKE '%' || #{contentMsg,jdbcType=VARCHAR}||'%'
        </if>
        <if test="account != null and account != ''">
            AND
            T2.SEND_ACCOUNT LIKE '%' ||#{account,jdbcType=VARCHAR} ||'%'
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND T1.CREATE_TIME &gt;=
            str_to_date(#{beginTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        <if test="endTime != null and endTime != ''">
            AND T1.CREATE_TIME &lt;=
            str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
    </sql>
    <sql id="To_HisMsgs">
        SELECT
        T3.WORK_NO WORKNO,
        T1.SENDTIME TIMEMSG,
        T2.SESSION_ID SESSIONID,
        T1.MEDIA_CONTENT CONTENTMSG,
        T2.Channel_Code CHANNEL,
        T2.ACCEPTED_ACCOUNT SENDACCOUNT,
        T2.Send_Account ACCEPTEDACCOUNT,
        T2.BUSINESS_TYPE MESSAGEBUSINESS,
        'TO' RESULTTYPE
        FROM
        MGW_MEDIA_SEND T1,MGW_SESSION_INFO T2,MGW_SESSION_WORKNO T3
        WHERE
        1=1
        and T1.SESSIONID = T2.Session_Id
        and T2.Session_Id = T3.Session_Id
        AND T1.WORKNO = T3.WORK_NO
        <if test="channelCode != null and channelCode != ''">
            AND T2.CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
        </if>
        <if test="sessionId != null and sessionId != ''">
            AND T2.SESSION_ID = #{sessionId,jdbcType=VARCHAR}
        </if>
        <if test="contentMsg != null and contentMsg != ''">
            AND T1.MEDIA_CONTENT LIKE '%' ||
            #{contentMsg,jdbcType=VARCHAR} || '%'
        </if>
        <if test="account != null and account != ''">
            AND T2.SEND_ACCOUNT LIKE '%' ||
            #{account,jdbcType=VARCHAR}
            || '%'
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND T1.SENDTIME &gt;=
            str_to_date(#{beginTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        <if test="endTime != null and endTime != ''">
            AND T1.SENDTIME &lt;=
            str_to_date(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
    </sql>

    <!-- 获取排队数据 -->
    <select id="getSessionSortItemCount" parameterType="sessionInfo"
            resultMap="SessionSortItemMap">
        select t.skill_queue skillQueue,
        a.queue_name, queueName,
        t.channel_code channelCode,
        e.channel_name channelName,
        count(t.session_id) sortCount
        from
        (
        select <include refid="SessionInfoSql"></include>
        where
        1=1
        <if test="status != null and status !=''">
            AND e.STATUS = #{status}
        </if>
        <if test="skillQueue != null and skillQueue !=''">
            AND e.SKILL_QUEUE = #{skillQueue}
        </if>
        )t
         LEFT JOIN mgw_tenant_skill_queue a ON t.SKILL_QUEUE = a.id
        LEFT JOIN mgw_channel_info e ON e.channel_code = t.channel_code
        group by t.skill_queue,t.channel_code

    </select>
<!-- 
	<select id="getnickname" parameterType="String" resultType="String">
		select m2.nickname from (
        select MESSAGE_ID,CHANNEL_CODE,BUSINESS_TYPE,TENANT_CODE,USER_ID,SOURCE,
        nickname,SEND_ACCOUNT,ACCEPTED_ACCOUNT,REPLY_ACCOUNT,
        CREATE_TIME,SEND_TIME,SESSION_ID,SKILL_QUEUE,WORK_NO,STATUS,MSG_TYPE,CONTENT,FOLLOW_DATA,REMARK,
        SKILL_TYPE,MESSAGE_SOURCE
         from mgw_message_info m1 
         where m1.source = '1' and m1.message_source = '1' order by m1.send_time desc
        ) m2 where m2.session_id = #{sessionId,jdbcType=VARCHAR} LIMIT 1 
	</select> -->
	
    <!-- 获取会话信息 列表 -->
    <select id="getSessionList" parameterType="hashmap" resultMap="BaseResultMap">
        select ID,SESSION_ID,SESSION_MODEL,STATUS,STATUS_SYMBOL,CREATE_TIME,OPT_TIME,SEND_ACCOUNT,ACCEPTED_ACCOUNT,CHANNEL_CODE,
        BUSINESS_TYPE,SKILL_QUEUE,TENANT_CODE,WORKNO,START_TIME,END_TIME,SESSION_TYPE,SKILL_TYPE,CHANGE_STATUS,CALL_STATUS,
        CUSTOMER_ID,NICK_NAME from ( 
        select res.ID,res.SESSION_ID,res.SESSION_MODEL,res.STATUS,res.STATUS_SYMBOL,res.CREATE_TIME,res.OPT_TIME,res.SEND_ACCOUNT,res.ACCEPTED_ACCOUNT,res.CHANNEL_CODE,
        res.BUSINESS_TYPE,res.SKILL_QUEUE,res.TENANT_CODE,res.WORKNO,res.START_TIME,res.END_TIME,res.SESSION_TYPE,res.SKILL_TYPE,res.CHANGE_STATUS,res.CALL_STATUS,
        res.CUSTOMER_ID,res.NICK_NAME
        from (
        select t.ID,t.SESSION_ID,t.SESSION_MODEL,t.STATUS,t.STATUS_SYMBOL,t.CREATE_TIME,t.OPT_TIME,t.SEND_ACCOUNT,t.ACCEPTED_ACCOUNT,t.CHANNEL_CODE,
        t.BUSINESS_TYPE,t.SKILL_QUEUE,t.TENANT_CODE,t.WORKNO,t.START_TIME,t.END_TIME,t.SESSION_TYPE,t.SKILL_TYPE,t.CHANGE_STATUS,t.CALL_STATUS,
        t.CUSTOMER_ID,t.NICK_NAME
        FROM MGW_SESSION_INFO t
        left join MGW_SESSION_WORKNO w
        on t.SESSION_ID = w.SESSION_ID
        WHERE 1=1
        <if test="id !=null and id!=''">
            AND t.ID=#{id}
        </if>
        <if test="sessionId != null and sessionId !=''">
            AND t.SESSION_ID = #{sessionId}
        </if>
        <if test="sendAccount != null and sendAccount !=''">
            AND t.SEND_ACCOUNT = #{sendAccount}
        </if>
        <if test="sessionModel != null and sessionModel !=''">
            AND t.SESSION_MODEL = #{sessionModel}
        </if>
        <if test="statuses != null and statuses !=''">
            AND
            <foreach collection="statuses" item="status" open="("
                     separator="or" close=")">
                <if test="status == 5 or status == 6 or status == 8">
                    ( t.STATUS = #{status} and w.start_time = w.end_time )
                </if>
                <if test="status == 7 or status == 9 or status == 10 or status == 11">
                    ( t.STATUS = #{status} and w.join_type = '1' )
                </if>
                <if test="status &lt; 5 ">
                    t.STATUS = #{status}
                </if>
            </foreach>
        </if>
        <if test="skillTypes != null">
            AND
            <foreach collection="skillTypes" item="skillType" open="("
                     separator="or" close=")">
                    t.SKILL_TYPE = #{skillType}
            </foreach>
        </if>
        <if test="statusSymbol != null and statusSymbol !=''">
            AND t.STATUS_SYMBOL = #{statusSymbol}
        </if>
        <if test="workNo != null and workNo !=''">
            AND w.WORK_NO = #{workNo}
        </if>
        <if test="tenantCode != null and tenantCode !=''">
            AND t.TENANT_CODE = #{tenantCode}
        </if>
        <if test="channelCode != null and channelCode !=''">
            AND t.CHANNEL_CODE = #{channelCode}
        </if>
        <if test="businessType != null and businessType !=''">
            AND t.BUSINESS_TYPE = #{businessType}
        </if>
        <if test="beginDate != null and beginDate != ''">
            AND t.START_TIME &gt;=
            DATE_FORMAT(#{beginDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        <if test="endDate != null and endDate != ''">
            AND t.START_TIME &lt;=
            DATE_FORMAT(#{endDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        order by t.create_time desc
        ) res)a
        where 1=1
        <if test="pageSize >0 and pageNum >0">
            LIMIT ${(pageNum - 1) * pageSize},${pageSize}
        </if>

    </select>
    <!-- 获取会话信息总数 -->
    <select id="getSessionListCount" parameterType="hashmap"
            resultType="int">
        select count(1) from (
       select t.ID,t.SESSION_ID,t.SESSION_MODEL,t.STATUS,t.STATUS_SYMBOL,t.CREATE_TIME,t.OPT_TIME,t.SEND_ACCOUNT,t.ACCEPTED_ACCOUNT,t.CHANNEL_CODE,
        t.BUSINESS_TYPE,t.SKILL_QUEUE,t.TENANT_CODE,t.WORKNO,t.START_TIME,t.END_TIME,t.SESSION_TYPE,t.SKILL_TYPE,t.CHANGE_STATUS,t.CALL_STATUS,
        t.CUSTOMER_ID
        FROM MGW_SESSION_INFO t
        left join MGW_SESSION_WORKNO w
        on t.SESSION_ID = w.SESSION_ID
        WHERE 1=1
        <if test="id !=null and id!=''">
            AND t.ID=#{id}
        </if>
        <if test="sessionId != null and sessionId !=''">
            AND t.SESSION_ID = #{sessionId}
        </if>
        <if test="sendAccount != null and sendAccount !=''">
            AND t.SEND_ACCOUNT = #{sendAccount}
        </if>
        <if test="sessionModel != null and sessionModel !=''">
            AND t.SESSION_MODEL = #{sessionModel}
        </if>
        <if test="statuses != null and statuses !=''">
            AND
            <foreach collection="statuses" item="status" open="("
                     separator="or" close=")">
                <if test="status == 5 or status == 6 or status == 8">
                    ( t.STATUS = #{status} and w.start_time = w.end_time )
                </if>
                <if test="status == 7 or status == 9 or status == 10 or status == 11">
                    ( t.STATUS = #{status} and w.join_type = '1' )
                </if>
                <if test="status &lt; 5 ">
                    t.STATUS = #{status}
                </if>
            </foreach>
        </if>
        <if test="skillTypes != null">
            AND
            <foreach collection="skillTypes" item="skillType" open="("
                     separator="or" close=")">
                t.SKILL_TYPE = #{skillType}
            </foreach>
        </if>
        <if test="statusSymbol != null and statusSymbol !=''">
            AND t.STATUS_SYMBOL = #{statusSymbol}
        </if>
        <if test="workNo != null and workNo !=''">
            AND w.WORK_NO = #{workNo}
        </if>
        <if test="tenantCode != null and tenantCode !=''">
            AND t.TENANT_CODE = #{tenantCode}
        </if>
        <if test="channelCode != null and channelCode !=''">
            AND t.CHANNEL_CODE = #{channelCode}
        </if>
        <if test="businessType != null and businessType !=''">
            AND t.BUSINESS_TYPE = #{businessType}
        </if>
        <if test="beginDate != null and beginDate != ''">
            AND t.START_TIME &gt;=
            str_to_date(#{beginDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        <if test="endDate != null and endDate != ''">
            AND t.START_TIME &lt;=
            str_to_date(#{endDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%I')
        </if>
        ) res
    </select>

    <!--分渠道 业务 技能组 技能组分类统一 时间段 会话数 -->
    <select id="queryServicePeak" resultType="hashmap"
            parameterType="hashmap">
        select dt as TIME,
        <if test="skillTypeFlag == 'true'">
            t.skill_type,
            (select skill_type_name from MGW_TENANT_SKILL_TYPE where skill_type_code =
            t.skill_type) skill_type_name,
        </if>
        <if test="skillQueueFlag == 'true'">
            t.skill_queue,
            (select queue_name from MGW_TENANT_SKILL_QUEUE where id = t.skill_queue)
            queue_name,
        </if>
        <if test="businessTypeFlag == 'true'">
            t.business_type,
            (select business_name from MGW_TENANT_BUSINESS_TYPE where id =
            t.business_type) business_name,
        </if>
        <if test="channelFlag == 'true'">
            t.channel_code,
            (select channel_name from MGW_CHANNEL_INFO where channel_code =
            t.channel_code) channel_name,
        </if>
        <if test="status != null and status != ''">
            status,
        </if>
        count(t.id) as COUNT
        from mgw_session_info t
        right join (select
		TIMESTAMPADD(hour,
		        ((t1.n - 1) *
		        (timestampdiff(hour,str_to_date(#{startTime, jdbcType=VARCHAR}, '%Y-%m-%d %H'),
				str_to_date(#{endTime, jdbcType=VARCHAR}, '%Y-%m-%d %H')) / #{scale, jdbcType=INTEGER})),
				str_to_date(#{startTime, jdbcType=VARCHAR}, '%Y-%m-%d %H')
			) dt, t1.n from 
			
			 <foreach collection="emptyList" open="(" close=")" index="i" separator=" union ">
			 	select ${i + 1} <if test="i == 0"> n</if>
			 </foreach>
			 t1)t2 on (t.start_time &lt; TIMESTAMPADD(hour, (timestampdiff(hour,str_to_date(#{startTime, jdbcType=VARCHAR}, '%Y-%m-%d %H'),
		str_to_date(#{endTime, jdbcType=VARCHAR}, '%Y-%m-%d %H')) / #{scale, jdbcType=INTEGER}), t2.dt))
       and t.start_time &gt;=
        t2.dt  and t.workno is not null
        <if test="tenantCodes != null and tenantCodes != ''">
            AND t.tenant_code in
            <foreach item="item" index="index" collection="tenantCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="tenantCode != null and tenantCode != ''">
            AND t.tenant_code = #{tenantCode, jdbcType=VARCHAR}
        </if>
         <if test="workNo != null and workNo != ''">
            AND t.workno = #{workNo}
        </if>
        <if test="status != null and status != ''">
            AND status in
            <foreach item="item" index="index" collection="statuses"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="skillQueue != null and skillQueue != ''">
            AND SKILL_QUEUE in
            <foreach item="item" index="index" collection="skillQueues"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="skillType != null and skillType != ''">
            AND SKILL_TYPE in
            <foreach item="item" index="index" collection="skillTypes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by t2.dt
        <if test="skillTypeFlag == 'true'">
            ,t.skill_type
        </if>
        <if test="skillQueueFlag == 'true'">
            ,t.skill_queue
        </if>
        <if test="businessTypeFlag == 'true'">
            ,t.business_type
        </if>
        <if test="channelFlag == 'true'">
            ,t.channel_code
        </if>
        <if test="status != null and status != ''">
            ,status
        </if>
        order by t2.dt
    </select>

    <!-- -->
    <select id="queryAgentCount" resultType="hashmap" parameterType="hashmap">
		select dt as time, count(distinct(t.workno)) as count
		from mgw_session_info t
		right join (select str_to_date(#{startTime, jdbcType=VARCHAR}, 'yyyy-mm-dd
		hh24') +
		(level) *
		((str_to_date(#{endTime, jdbcType=VARCHAR}, '%Y-%m-%d %H') -
        str_to_date(#{startTime, jdbcType=VARCHAR}, '%Y-%m-%d %H')) / #{scale, jdbcType=INTEGER})
		dt
		from dual
		connect by level &lt;= #{scale, jdbcType=INTEGER}) d on
		(t.start_time &gt; d.dt -((str_to_date(#{endTime, jdbcType=VARCHAR}, '%Y-%m-%d %H')
		-
        str_to_date(#{startTime, jdbcType=VARCHAR}, '%Y-%m-%d %H')) / #{scale, jdbcType=INTEGER}))
		and t.start_time &lt;
		d.dt
		<if test="tenantCode != null and tenantCode != ''">
            and t.tenant_code in
            <foreach item="item" index="index" collection="tenantCode"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
		group by d.dt order by d.dt
	</select>

    <insert id="insertSatified" parameterType="hashmap">
	

	
		INSERT INTO IMR_SCORE (SESSION_ID,
		WORK_NO,SATIFIED,GETSCORETIME,GETREQUESTTIME,SUGGEST,TENANT_CODE
		
		)
		VALUES(
		#{sessionId,jdbcType=VARCHAR},
		#{workNo,jdbcType=VARCHAR},
		#{satified,jdbcType=INTEGER},
		#{createTime,jdbcType=VARCHAR},
		#{requestTime,jdbcType=VARCHAR},
		#{suggest,jdbcType=VARCHAR},
		(select t.tenant_code from mgw_session_info t
		where t.SESSION_ID=#{sessionId,jdbcType=VARCHAR})
		
		)
	</insert>
    <select id="querySkillWaitAvgTime" parameterType="hashmap"
            resultType="hashmap">
        <![CDATA[


        SELECT
            T2.QUEUE_NAME,
            T1. TIME
        FROM
            (
                SELECT
                    SKILL_QUEUE,
                    AVG (
                        TO_NUMBER (
                            (
                                CAST (START_TIME AS DATE) - CAST (CREATE_TIME AS DATE)
                            ) * 24 * 60 * 60
                        )
                    ) AS TIME
                FROM
                    MGW_SESSION_INFO
                WHERE
                    TENANT_CODE=#{tenantCode,jdbcType=VARCHAR} AND
                    SKILL_QUEUE IS NOT NULL
                AND CREATE_TIME >= date_format (
                    #{startTime,jdbcType=VARCHAR},
                    '%Y-%m-%d %H:%i:%S'
                )
                AND CREATE_TIME <= date_format (
                    #{endTime,jdbcType=VARCHAR},
                    '%Y-%m-%d %H:%i:%S'
                )
                GROUP BY
                    SKILL_QUEUE
            ) T1,
            MGW_TENANT_SKILL_QUEUE T2
        WHERE
            T1.SKILL_QUEUE = T2. ID


        ]]>
	</select>

    <select id="queryChannelServiceCount" parameterType="hashmap"
            resultType="hashmap">
		SELECT
		T2.CHANNEL_NAME,
		T1.*
		FROM
		(
		SELECT
		CHANNEL_CODE,
		SUCCESS,
		TOTAL - SUCCESS FAILURE,
		TOTAL,
		ROUND (SUCCESS / TOTAL, 2) PER
		FROM
		(
		SELECT
		CHANNEL_CODE,
		COUNT (WORKNO) SUCCESS,
		COUNT (*) TOTAL
		FROM
		MGW_SESSION_INFO
		WHERE
		TENANT_CODE=#{tenantCode,jdbcType=VARCHAR}
		GROUP BY
		CHANNEL_CODE
		)
		) T1,
		MGW_CHANNEL_INFO T2
		WHERE
		T1.CHANNEL_CODE = T2.CHANNEL_CODE
	</select>
    <!-- 获取工号数量 -->
    <select id="getSessionWorkNoCount" parameterType="cn.sh.ideal.sm.model.SessionWorkNo"
            resultType="int">
        select count(1) from MGW_SESSION_WORKNO t
        where 1=1
        <if test="sessionId != null and sessionId != ''">
            and t.SESSION_ID = #{sessionId}
        </if>
        <if test="tenantCode != null and tenantCode != ''">
            and t.TENANT_CODE = #{tenantCode}
        </if>
        <if test="workNo != null and workNo != ''">
            and t.WORK_NO = #{workNo}
            and t.start_time = t.end_time
        </if>

    </select>
    <select id="getSkillWorkNo" parameterType="hashmap" resultMap="SkillQueueWorkNoMap">
        SELECT a.`TENANT_ID` tenant_code,
        a.`work_no`,
        SUBSTRING_INDEX(
        SUBSTRING_INDEX(
        a.skill_group,
        ',',
        b.help_topic_id + 1
        ),
        ',',
        - 1
        ) AS skill_queue FROM cms_agent_info a
        JOIN help_topic b
        ON b.help_topic_id &lt; (
        LENGTH(a.skill_group) - LENGTH(REPLACE(a.skill_group, ',', '')) + 1
        )
        AND  a.status = '1'
    </select>
    <select id="getSkillWorkNoSessionCount" parameterType="skillQueueWorkNo" resultType="int">
        SELECT
 COUNT(1)
FROM
  mgw_session_info s,
  mgw_tenant_skill_type tst
WHERE
s.skill_type = tst.skill_type_code
  AND s.tenant_code = tst.tenant_id
  AND tst.is_record = 'Y'
  AND s.status IN (5, 6, 8)
   and s.tenant_code  = #{tenantCode,jdbcType=VARCHAR}
   and s.skill_queue  = #{skillQueue,jdbcType=VARCHAR}
   and s.workno  = #{workNo,jdbcType=VARCHAR}
    </select>
    
    <select id="queryNotClosedTask" resultMap="BaseResultMap" parameterType="string">
		select * from mgw_session_info where status in ('5','6','8','11') and find_in_set(#{workNo,jdbcType=VARCHAR},workno, ',') > 0
    </select>
    
    <select id="queryNotConclusionTask" resultMap="BaseResultMap" parameterType="string">
    	select * from mgw_session_info where status in ('9', '10') and find_in_set(#{workNo,jdbcType=VARCHAR},workno, ',') > 0 and session_id not in (select session_id from cuv_summary_section)
    </select>
    
     <select id="getPendingTask" resultType="java.util.HashMap" parameterType="string">
		select
		    SUM(CASE t2.channel_code WHEN '1001' THEN t2.count ELSE 0 END ) as '1001',
		    SUM(CASE t2.channel_code WHEN '1002' THEN t2.count ELSE 0 END ) as '1002',
		    SUM(CASE t2.channel_code WHEN '1003' THEN t2.count ELSE 0 END ) as '1003',
		    SUM(CASE t2.channel_code WHEN '1004' THEN t2.count ELSE 0 END ) as '1004',
		    SUM(CASE t2.channel_code WHEN '1005' THEN t2.count ELSE 0 END ) as '1005',
		    SUM(CASE t2.channel_code WHEN '1006' THEN t2.count ELSE 0 END ) as '1006',
		    SUM(CASE t2.channel_code WHEN '1007' THEN t2.count ELSE 0 END ) as '1007',
		    SUM(CASE t2.channel_code WHEN '1008' THEN t2.count ELSE 0 END ) as '1008',
		    SUM(CASE t2.channel_code WHEN '1009' THEN t2.count ELSE 0 END ) as '1009',
		    SUM(CASE t2.channel_code WHEN '1010' THEN t2.count ELSE 0 END ) as '1010',
		    SUM(CASE t2.channel_code WHEN '1011' THEN t2.count ELSE 0 END ) as '1011',
		    SUM(CASE t2.channel_code WHEN '1012' THEN t2.count ELSE 0 END ) as '1012',
		    from (
		select channel_code, count(*) count from 
		    ((select * from mgw_session_info where find_in_set(#{workNo,jdbcType=VARCHAR},workno, ',') &gt; 0 and status in ('5','6','8','11')) t1) group by channel_code) t2
    </select>
    
 <select id="querySatisfied" resultMap="agentStatisticsResult" parameterType="cn.sh.ideal.sm.resp.AgentStatistics">
select m.session_id, o.satified, m.work_no, m.tenant_code
  from (select t.session_id, t.work_no, p.tenant_code, p.create_time
          from (select k.session_id,b.work_no,k.create_time
            from (select e.session_id, min(e.create_time) create_time
                    from mgw_session_workno e
                   group by e.session_id) k
            left join mgw_session_workno b
              on k.session_id = b.session_id
             and k.create_time = b.create_time) t
                    inner join mgw_session_info p
                      on t.session_id = p.session_id
                   where p.workno is not null
                     and p.status in ('7', '9', '10')
                     and t.work_no = #{workNo, jdbcType = VARCHAR}
                     and p.tenant_code = #{tenantCode,jdbcType = VARCHAR}
                     <if test="endTime != null and endTime != ''">
                     and cast(t.create_time as date) &lt; date_format(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
                     </if>
                     <if test="startTime !=null and startTime !=''">
                     and cast(t.create_time as date) > date_format(#{startTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
                     </if>
                     ) m
            left join imr_score o
              on m.session_id = o.session_id 
</select>   

<select id="queryServiceTime" resultMap="agentStatisticsResult" parameterType="cn.sh.ideal.sm.resp.AgentStatistics" >
select t.session_id, t.work_no, p.tenant_code,(cast(p.end_time as date) - cast(p.start_time as date))*86400 service_time 
            from (select k.session_id, b.work_no,k.create_time
                    from (select e.session_id, min(e.create_time) create_time
                            from mgw_session_workno e
                           group by e.session_id) k
                    left join mgw_session_workno b
                      on k.session_id = b.session_id
                     and k.create_time = b.create_time) t
           inner join mgw_session_info p
              on t.session_id = p.session_id
           where p.workno is not null
             and p.status in ('7', '9', '10')
             and t.work_no = #{workNo, jdbcType = VARCHAR}
             and p.tenant_code = #{tenantCode,jdbcType = VARCHAR}
             <if test="endTime != null and endTime != ''">
				and cast(t.create_time as date) &lt; date_format(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
			</if>
			<if test="startTime !=null and startTime !=''">
				and cast(t.create_time as date) &gt; date_format(#{startTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
			</if>
</select>

<select id="queryAgentResponse" resultMap="agentStatisticsResult" parameterType="cn.sh.ideal.sm.resp.AgentStatistics">

select w.session_id,
       w.work_no,
       w.tenant_code,
       w.start_time  begin_time,
       c.min_time  min_time
  from (
  	(select t.session_id, t.work_no, p.tenant_code, p.start_time
           from (select k.session_id, b.work_no,k.create_time
                   from (select e.session_id, min(e.create_time) create_time
                           from mgw_session_workno e
                          group by e.session_id) k
                   left join mgw_session_workno b
                     on k.session_id = b.session_id
                    and k.create_time = b.create_time) t
          inner join mgw_session_info p
             on t.session_id = p.session_id
          where p.workno is not null
            and p.status in ('7', '9', '10')
             and t.work_no = #{workNo, jdbcType = VARCHAR}
             and p.tenant_code = #{tenantCode,jdbcType = VARCHAR}
             <if test="endTime != null and endTime != ''">
				and cast(t.create_time as date) &lt; date_format(#{endTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
			</if>
			<if test="startTime !=null and startTime !=''">
				and cast(t.create_time as date) &gt; date_format(#{startTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
			</if>
            ) w
        left join (select session_id, min(send_time) min_time
                     from mgw_message_info
                    where session_id is not null
                      and message_source = '2'
                    group by session_id) c 
        on w.session_id = c.session_id)
</select>

</mapper>
