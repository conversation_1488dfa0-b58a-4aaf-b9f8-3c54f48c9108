<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!--提供方应用信息 -->
    <dubbo:application name="sm-provider"/>

    <dubbo:provider>
        <!-- 停止服务超时设置，单位毫秒 -->
        <dubbo:parameter key="shutdown.timeout" value="${dubbo.shutdown.timeout}" />
    </dubbo:provider>

    <!-- 注册中心地址 -->
    <dubbo:registry address="zookeeper://${zookeeper.address}"/>
 	<!-- <dubbo:registry address="127.0.0.1:2181"/>  -->
    <!-- 通过dubbo协议，在指定端口上暴露服务 dubbo协议、rmi远程tcp协议、hessian基于http协议 -->
    <dubbo:protocol name="dubbo" port="${zookeeper.port}" threadpool="fixed" threads="200"/>

    <!-- 声明需要暴露的服务 -->

    <!-- sm 暴露服务-->
    <dubbo:service interface="cn.sh.ideal.sm.service.ReportService" ref="reportServiceImpl"   version="1.0.0"/>
    <dubbo:service interface="cn.sh.ideal.sm.service.SessionService" ref="sessionService"  version="1.0.0"/>
	

    <dubbo:monitor protocol="registry"/>
</beans>