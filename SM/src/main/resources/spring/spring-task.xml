<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd">
    <task:annotation-driven/>
    <context:annotation-config/>
    <bean class="org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor"/>
    <context:component-scan base-package="cn.sh.ideal"/>
    <task:scheduled-tasks scheduler="checkSessionTimeoutScheduler">
        <task:scheduled ref="taskService" method="checkSessionTimeout" cron="0/5 * *  * * ?"/>
    </task:scheduled-tasks>
    <task:scheduled-tasks scheduler="checkSessionCountScheduler">
        <task:scheduled ref="taskService" method="checkSessionCount" cron="0/5 * *  * * ?"/>
    </task:scheduled-tasks>
    <task:scheduler id="checkSessionTimeoutScheduler" pool-size="1"/>
    <task:scheduler id="checkSessionCountScheduler" pool-size="2"/>

</beans>
