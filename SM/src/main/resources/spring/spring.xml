<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd">

	<!-- 启动aop对注解的支持 -->
	<aop:aspectj-autoproxy proxy-target-class="true" expose-proxy="true" />

	<bean id="context" class="cn.sh.ideal.util.SpringContextUtil" />

	<!-- 注解扫描包路径 -->
	<context:component-scan base-package="cn.sh.ideal.sm">
		<context:exclude-filter type="annotation"
			expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<!-- 引入配置文件 -->
	<util:properties id="config"
		location="classpath:conf/${env}/app-${env}.properties" />
	<context:property-placeholder
		location="classpath:conf/${env}/jdbc-${db}-${env}.properties" />

	<!-- 注解式事务 -->
	<tx:annotation-driven transaction-manager="transactionManager" />

	<!-- 拦截器方式配置事物 -->
	<tx:advice id="transactionAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="add*" propagation="REQUIRED" rollback-for="java.lang.Exception" />
			<tx:method name="create*" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="edit*" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="update*" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="remove*" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="batch*" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="delete*" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="*Delete" propagation="REQUIRED"
				rollback-for="java.lang.Exception" />
			<tx:method name="get*" propagation="REQUIRED" read-only="true" />
			<tx:method name="query*" propagation="REQUIRED" read-only="true" />
		</tx:attributes>
	</tx:advice>
	<aop:config>
		<aop:pointcut id="transactionPointcut"
			expression="execution(* cn.sh.ideal.sm.service.impl.*.*(..))" />

		<aop:advisor pointcut-ref="transactionPointcut"
			advice-ref="transactionAdvice" />
	</aop:config>
	<aop:config>
		<aop:aspect id="sessoinLogPointcut" ref="sessionLogAspect">
			<aop:pointcut id="target"
				expression="execution(* cn.sh.ideal.sm.service.SessionInfoService.updateSessionStatus(..))||execution(* cn.sh.ideal.sm.service.SessionInfoService.createSession(..))||execution(* cn.sh.ideal.sm.service.SessionInfoService.updateSessionTimeout(..))" />
			<aop:around method="addSessionLog" pointcut-ref="target" />
		</aop:aspect>
	</aop:config>

	<!-- mybatis -->
	<import resource="spring-mybatis.xml" />

	<!-- task -->
	<import resource="spring-task.xml" />
	<import resource="dubbo-sm-consumer.xml" />
	<import resource="dubbo-sm-provider.xml" />
</beans>