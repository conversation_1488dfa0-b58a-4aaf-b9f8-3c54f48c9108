<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
		
	
     <dubbo:registry address="zookeeper://${zookeeper.address}"/>
     <!--  <dubbo:registry address="127.0.0.1:2181"/>  -->
     
    <!-- 所有消费服务的默认超时设置 -->
    <dubbo:consumer timeout="${dubbo.consumer.timeout}">
        <!-- 停止服务超时设置，单位毫秒 -->
        <dubbo:parameter  key="shutdown.timeout" value="${dubbo.shutdown.timeout}" />
    </dubbo:consumer>
    <!--生成远程服务代理,timeout=10000超时为10秒,retries=0表示不重试-->
    <dubbo:reference id="messageService" interface="cn.sh.ideal.si.service.MessageService" check="false" version="1.0.0" retries="0"/>
    <dubbo:reference id="mgwMessageService" interface="cn.sh.ideal.mgw.service.MessageService" check="false" version="1.0.0" retries="0"/>
    <dubbo:reference id="allocateService" interface="cn.sh.ideal.mir.service.AllocateService" check="false" version="1.0.0" retries="0"/>
    <dubbo:reference id="agentService" interface="cn.sh.ideal.as.service.AgentService" check="false" version="1.0.0" retries="0"/>
    
    

</beans>