#Version 1.5.2 （ghc）
*获取会话性能优化 可通过修改配置选择是否检查从数据还原遗失会话。配置参数为：needReloadSession = false  参数为 true/false
 true 为需要 false为不需要。
*优化超时任务检查  采用redis自身key过期通知机制，redis版本支持2.8.2 以上，系统环境linux , redis配置说明：
    在 redis.conf 里面配置以下属性 ：notify-keyspace-events Ex
      重启redis.
     若不满足上述条件，需采用原始的超时过期机制,在spring-task.xml 配置以下内容
      <task:scheduled ref="taskService" method="checkSessionTimeout" cron="0/5 * *  * * ?" /> 

