package cn.sh.ideal.sm.utils;

public class Constants {

	public static String RESPONSE_OK = "0";
	public static String RESPONSE_ERROR = "1";
	public static String RESPONSE_RESUBMIT = "2";
	public static String NO = "0";
	public static String YES = "1";
    public final static String DATA = "data";
	public final static String RESULT= "result";
	public final static Integer CUSTOMER = 1;
	public final static Integer AGENT = 2;
	public final static String RES = "res";
	public final static String PARAMINVALID="查询上次历史消息参数不合法！";
	public final static String LASTMES = "lastMes";
	public final static String SESSION_OUTIME = "SESSION_TIMEOUT";
	public final static Integer PAGEFIRST = 1;
	public final static Integer PAGESIZETWO = 2; 
	public final static String DESC = "desc";
	public final static String SUCCESS_MSG="success!";
	public final static String SUCCESS="0";
	public final static String OTHER ="2";
	public final static String FAILED_MSG="failed!";
	public final static String FAILED="-1";
	
	

	/**redis中存放签入租户信息*/
	public final static String SIGNIN_TENANT = "SIGNIN_TENANT#";

	public final static String COUNT_TENANT = "COUNT_TENANT:";
	/*点对点消息可调用的会话key*/
	public final static String DUE_SESSION = "DUE_";
	




	/**redis中存放工号信息*/
	public final static String SIGNIN_WORKNO = ":SIGNIN_WORKNO#";
	/*标示任务执行*/
	public final static String TIMEOUT_EXE_FALG =":TIMEOUT_EXE_FALG:";
    /*可用的session*/
	public final static String AVAILSESSION_ = "AVAILSESSION_";




	/*会话活动状态 user用户  ag坐席  用于会话超时 */
	public final static String USER_ACTIVE = "user";
	public final static String AG_ACTIVE = "ag";

	/* 超时类型  tip提醒  close 关闭会话 transfer 转发会话  */
	public final static String TIMEOUT_TYPE_TIP = "tip";
	public final static String TIMEOUT_TYPE_CLOSE = "close";
	public final static String TIMEOUT_TYPE_TRANSFER = "transfer";

	/*其他工程地址参数*/
	public final static String AG_ADDRESS = "AG_ADDRESS";
	public final static String MIR_ADDRESS = "MIR_ADDRESS";
	public final static String MGW_ADDRESS = "MGW_ADDRESS";
	public static final String CMS_ADDRESS="CMS_ADDRESS";
	public static final String SERVICE_INVOKER_ADDRESS = "SERVICE_INVOKER_ADDRESS";
	/*超时时间*/
	public final static String AG_SESSION_TIMEOUT = "AG_SESSION_TIMEOUT";
	public final static String AG_SESSION_TIMEOUT2 = "AG_SESSION_TIMEOUT2";
	public final static String USER_SESSION_TIMEOUT = "USER_SESSION_TIMEOUT";
	public final static String USER_SESSION_TIMEOUT2 = "USER_SESSION_TIMEOUT2";
	/*超时时间动作配置 tip close transfer*/
	public final static String AG_SESSION_TIMEOUT_ACTION = "AG_SESSION_TIMEOUT_ACTION";
	public final static String AG_SESSION_TIMEOUT_ACTION2 = "AG_SESSION_TIMEOUT_ACTION2";
	public final static String USER_SESSION_TIMEOUT_ACTION = "USER_SESSION_TIMEOUT_ACTION";
	public final static String USER_SESSION_TIMEOUT_ACTION2 = "USER_SESSION_TIMEOUT_ACTION2";

	/*提示*/
	public final static String AG_TIMEOUT_TIP = "AG_TIMEOUT_TIP";
	public final static String AG_TIMEOUT_CLOSE_TIP = "AG_TIMEOUT_CLOSE_TIP";
	public final static String AG_TIMEOUT_TRANS_TIP = "AG_TIMEOUT_TRANS_TIP";
	public final static String USER_TIMEOUT_TIP = "USER_TIMEOUT_TIP";
	public final static String USER_CLOSE_SESSION_TIP = "USER_CLOSE_SESSION_TIP";
	public final static String AG_CLOSE_SESSION_TIP = "AG_CLOSE_SESSION_TIP";
	public final static String AG_CLOSE_MUTUAL_TIP = "AG_CLOSE_MUTUAL_TIP";
	public final static String USER_TIMEOUT_CLOSE_TIP = "USER_TIMEOUT_CLOSE_TIP";

}
