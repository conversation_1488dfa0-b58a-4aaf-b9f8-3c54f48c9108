/**
 * Project Name:SM Maven Webapp
 * File Name:CmsSysParamServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2015年2月3日下午2:14:53
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service.impl;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.model.SessionTip;
import cn.sh.ideal.sm.dao.SessionTipDao;
import cn.sh.ideal.sm.service.SessionTipService;


@Service("sessionTipService")
public class SessionTipServiceImpl extends BaseServiceImpl<SessionTip> implements SessionTipService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "redisDao")
    private RedisDao<String, Serializable> redisDao;
    @Autowired
    private SessionTipDao sessionTipDao;

    public static String TIP_ ="TIP_";

    @Override
    @PostConstruct
    public void init() throws Exception {
    	SessionTip sessionTip = new SessionTip();
    	Set<String> okeys = redisDao.getKeysByPattern(TIP_.concat("*"));
        for(String okey : okeys){
			redisDao.deleteValue(okey);
		}
        
        List<SessionTip> tips = this.getList(sessionTip);
        for (SessionTip tip : tips){
        	String tenantCode = tip.getTenantCode();
        	String skillQueue = tip.getSkillQueue();
            String acceptedAccount = tip.getAcceptedAccount();
            logger.info("=========================:"+TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
            redisDao.saveValue(TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount,tip);
        }
        
        List<SessionTip> defaultTips = sessionTipDao.getDefaultList(sessionTip);
        for (SessionTip tip : defaultTips){
            String tenantCode = tip.getTenantCode();
            String skillQueue = "";
            String acceptedAccount = "";
            logger.info("default=========================:"+TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
            redisDao.saveValue(TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount,tip);
        }
    }

    @Override
    public List<SessionTip> getList(SessionTip sessionTimeout) throws Exception {
        return sessionTipDao.getAllList(sessionTimeout);
    }

    @Override
    public SessionTip get(String tenantCode,String skillQueue,String acceptedAccount) throws Exception {
    	logger.info("get:"+TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
        return (SessionTip) redisDao.readValue(TIP_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
    }
    
    public String getCurDate(){
    	return this.sessionTipDao.getCurDate();
    }
}
