/**
 * Project Name:SM Maven Webapp
 * File Name:Utils.java
 * Package Name:cn.sh.ideal.mir.session.util
 * Date:2014年12月12日下午1:52:20
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang.StringUtils;

import cn.sh.ideal.model.SessionData;


/**
 * ClassName:Utils <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2014年12月12日 下午1:52:20 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class Utils {
    public static boolean check(SessionData data) throws Exception{
        if(data == null ) throw new Exception("参数格式不合法！");
        String sessionId = data.getSessionId();
        String tenantCode = data.getTenantCode();
        if(StringUtils.isEmpty(sessionId)||StringUtils.isEmpty(tenantCode)){
            throw new Exception("sessionId or tenantCode is null");
        }
        return true;
    }
    /**
     * 
     * checkSessionDate:(这里用一句话描述这个方法的作用). <br/>
     * TODO(这里描述这个方法适用条件 – 可选).<br/>
     * TODO(这里描述这个方法的执行流程 – 可选).<br/>
     * TODO(这里描述这个方法的使用方法 – 可选).<br/>
     * TODO(这里描述这个方法的注意事项 – 可选).<br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception boolean
     * @since JDK 1.6
     */
    public static boolean checkSessionDate(SessionData data) throws Exception{
        if(data == null ) throw new Exception("参数格式不合法！");
        String sessionId = data.getSessionId();
        String tenantCode = data.getTenantCode();
        String sendAccount = data.getSendAccount();
        String acceptedAccount = data.getAcceptedAccount();
        String channelCode = data.getChannelCode();
        
        boolean flag1 = (StringUtils.isEmpty(sessionId)||StringUtils.isEmpty(tenantCode));
        boolean flag2 = ((StringUtils.isEmpty(sendAccount)&&StringUtils.isEmpty(acceptedAccount)&&StringUtils.isEmpty(channelCode)))||StringUtils.isEmpty(tenantCode);
   
        if(flag1&&flag2){
            throw new Exception("参数格式不合法！");
        }
        return true;
    }
    
    public static String date2String(Date date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(date);
        return dateString;
    }
    public static String getAgentKey(String tenantCode,String queueId,String workNo){
    	return cn.sh.ideal.util.Constants.TENANTCODE + tenantCode + cn.sh.ideal.util.Constants.AGENT + workNo + cn.sh.ideal.util.Constants.QUEUE_BASE;
//         cn.sh.ideal.util.Constants.TENANTCODE + tenantCode + cn.sh.ideal.util.Constants.SKILLQUEUE + queueId + cn.sh.ideal.util.Constants.AGENT + workNo;
    }

    public static String getSessionCountMapKey() {
        return "currSessionCount";
    }

    public static boolean isHoliday(String curdate,String holiday){
    	if(holiday == null || "".equals(holiday)){
    		return false;
    	}
    	String holidays[] = holiday.split(",");
    	for(String holi:holidays){
    		if(curdate.equals(holi)){
    			return true;
    		}
    	}
    	return false;
    }
}
