/**
 * Project Name:SM Maven Webapp
 * File Name:CmsSysParamServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2015年2月3日下午2:14:53
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service.impl;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.sm.dao.SessionTimeoutDao;
import cn.sh.ideal.sm.model.SessionTimeout;
import cn.sh.ideal.sm.service.SessionTimeoutService;


@Service("sessionTimeoutService")
public class SessionTimeoutServiceImpl extends BaseServiceImpl<SessionTimeout> implements SessionTimeoutService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "redisDao")
    private RedisDao<String, Serializable> redisDao;
    @Autowired
    private SessionTimeoutDao sessionTimeoutDao;

    public static String TIMEOUT_ = "TIMEOUT_";

    @Override
    @PostConstruct
    public void init() throws Exception {
        SessionTimeout sessionTimeout = new SessionTimeout();
        Set<String> okeys = redisDao.getKeysByPattern(TIMEOUT_.concat("*"));
        for(String okey : okeys){
        	logger.info("remove okey=========================:"+okey);
			redisDao.deleteValue(okey);
		}
        List<SessionTimeout> timeouts = this.getList(sessionTimeout);
        for(SessionTimeout timeout : timeouts){
        	String tenantCode = timeout.getTenantCode();
            String skillQueue = timeout.getSkillQueue();
            String acceptedAccount = timeout.getAcceptedAccount();
            logger.info("TIMEOUTs :"+TIMEOUT_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
            redisDao.saveValue(TIMEOUT_+tenantCode+"_"+skillQueue+"_"+acceptedAccount,timeout);
        }
        
        List<SessionTimeout> defaultTimeouts = sessionTimeoutDao.getDefaultAllList(sessionTimeout);
        for(SessionTimeout timeout : defaultTimeouts){
            String tenantCode = timeout.getTenantCode();
            String skillQueue = "";
            String acceptedAccount = "";
            logger.info("default TIMEOUTs :"+TIMEOUT_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
            redisDao.saveValue(TIMEOUT_+tenantCode+"_"+skillQueue+"_"+acceptedAccount,timeout);
        }

    }

    @Override
    public List<SessionTimeout> getList(SessionTimeout sessionTimeout) throws Exception {
        return sessionTimeoutDao.getAllList(sessionTimeout);
    }

    @Override
    public SessionTimeout get(String tenantCode,String skillQueue,String acceptedAccount) throws Exception {
    	logger.info("get:"+TIMEOUT_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
        return (SessionTimeout) redisDao.readValue(TIMEOUT_+tenantCode+"_"+skillQueue+"_"+acceptedAccount);
    }
}
