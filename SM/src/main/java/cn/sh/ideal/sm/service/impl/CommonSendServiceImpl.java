package cn.sh.ideal.sm.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mgw.model.response.BaseResponseDTO;
import cn.sh.ideal.mir.req.PushTipRequest;
import cn.sh.ideal.mir.resp.PushTipResponse;
import cn.sh.ideal.mir.service.AllocateService;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.si.service.MessageService;
import cn.sh.ideal.sm.service.CommonSendService;
import cn.sh.ideal.sm.utils.Constants;
import cn.sh.ideal.util.NetUtil;

@Service("commonSendService")
public class CommonSendServiceImpl implements CommonSendService {
	
	private static final Logger logger = LoggerFactory.getLogger(CommonSendServiceImpl.class);
	@Value("#config[customerUrl]")
	private String customerUrl="";
    @Value("#{config['pushAgTipUrl']}")
    private String pushAgTipUrl = "";
    @Autowired
    private MessageService messageService; 
    @Autowired
    private cn.sh.ideal.mgw.service.MessageService mgwMessageService;
    @Autowired
    private AllocateService allocateService;
    
	@Autowired
	SysInitService sysInitService; 

	@Override
	public String sendCustomerInfo(String requestBody, PARAMTYPE paramType) {
		// TODO 
		try {
			SysParam sysParam = sysInitService.getSysParam(Constants.SERVICE_INVOKER_ADDRESS);
			String siAddress = sysParam.getParamValue();
			String pushUrl = siAddress + customerUrl;
			String result = "";
			logger.info("send Address:{}" + pushUrl);
			logger.info("send Param:" + requestBody);
			if(PARAMTYPE.GET.equals(paramType)){
				//result = getPushMessage(pushUrl);
			}
			if(PARAMTYPE.POST.equals(paramType)){
				//result = postPushMessage(pushUrl, requestBody);
			}
			logger.info("发送消息返回结果为：" + result);
			return result;
		} catch (Exception e) {
			 logger.error("推送网关消息失败", e);
		}
		 return null;
		
	}
	
	 /**
     * 推送消息 Post 方式.
     * 
     * @param pushMessageUrl
     *            the push message url
     * @param msg
     *            the msg
     * @return the string
     */
    private String postPushMessage(SessionInfo info) {
        List<MessageInfo> list = new ArrayList<MessageInfo>();
        MessageInfo messageinfo = new MessageInfo();
        messageinfo.setSessionId(info.getSessionId());
        messageinfo.setSkillQueue(info.getSkillQueue());
        list.add(messageinfo);
        BaseResponse resp = messageService.pushMessageInfos(list);
        return JSONObject.toJSONString(resp).toString();
    }
    

    public MessageInfo tranNText(SessionInfo info,String msg,String workNo,String sendType){
        JSONObject textJson = new JSONObject();
        textJson.put("source", "");
        textJson.put("workNo", workNo);
        textJson.put("channelCode", info.getChannelCode());
     
        textJson.put("tenantCode", info.getTenantCode());
        textJson.put("skillQueue", info.getSkillQueue());
        textJson.put("nickname", info.getNickname());
        //超时提醒，不能填写sessionId
        //textJson.put("sessionId", info.getSessionId());
    
        textJson.put("sendAccount", info.getAcceptedAccount());
        textJson.put("acceptedAccount", info.getSendAccount());
        textJson.put("businessType", info.getBusinessType());
        textJson.put("msgType", "text");
        textJson.put("messageSource","4");
        textJson.put("content", msg);
        textJson.put("sendType", sendType);
       
        MessageInfo messageInfo = JSONObject.toJavaObject(textJson, MessageInfo.class);
        return messageInfo;
    }

	@Override
	public MessageInfo tranText(SessionInfo info, String msg, String workNo,String sendType) {
	    return this.tranNText(info, msg,workNo,sendType);
	}

	@Override
	public String sendMsgMGW(MessageInfo info, PARAMTYPE paramType) {
        try {
            String result = "";
            if (paramType.equals(PARAMTYPE.POST)) {
                BaseResponseDTO resp = mgwMessageService.newSend(info);
                result = JSONObject.toJSONString(resp);
            }
            logger.info("发送消息返回结果为：" + result);
            return result;
        } catch (Exception e) {
            logger.error("推送网关消息失败", e);
        }
        return null;
	}

	@Override
	public String sendMsgAg(String messageBody, PARAMTYPE paramType, String tenantCode, String workNo) {
		
		try {
			JSONObject json = JSONObject.parseObject(messageBody);
//			json.put("workNo", workNo);
//            json.put("tenantCode", tenantCode);
//            json.put("sessionId", sessionId);
//            json.put("skillType", info.getSkillType());
//            json.put("type", type);
//            json.put("content", agCloseMutualTip);
//            json.put("descr", "");
            
			PushTipRequest stip = new PushTipRequest();
			stip.setTenantCode(tenantCode);
			stip.setWorkNo(workNo);
			stip.setContent(json.getString("content"));
			stip.setSessionId(json.getString("sessionId"));
			stip.setType(json.getString("type"));
            stip.setChannelCode(json.getString("channelCode"));
            stip.setLastActiveUser(json.getString("lastActiveUser"));
			stip.setSendType(json.getString("sendType"));
			stip.setTimeOutAction(json.getString("timeOutAction"));
			PushTipResponse result = allocateService.pushTip(stip);
			
            return JSONObject.toJSONString(result).toString();
        } catch (Exception e) {
           logger.error("接口调用失败", e);
        }
		return null;
	}
	
	/*
     * (non-Javadoc)
     * 
     * @see
     * cn.sh.ideal.imr.interfaces.service.CommonSendService#sendMsg(java.lang
     * .String, java.lang.String, java.lang.String,
     * cn.sh.ideal.imr.interfaces.service.CommonSendService.PARAMTYPE)
     */
    public String sendMsg(String allPath, String messageBody,
            PARAMTYPE paramType) {
        String result = "";
        logger.info("发送总地址信息为：{}", allPath);
        logger.info("发送消息信息为：{}", messageBody);
        
        result =  NetUtil.send(allPath, "POST", messageBody);
        
        return result;
    }


}
