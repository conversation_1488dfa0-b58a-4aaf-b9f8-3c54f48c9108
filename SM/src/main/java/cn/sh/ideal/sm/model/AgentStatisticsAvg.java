package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class AgentStatisticsAvg implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String workNo;
	private HashMap<String,Object> satisfied;
	private Map<String,Object> serviceTime;
	private Map<String,Object> avgAgentResponse;
	private String tenantCode;
	public String getWorkNo() {
		return workNo;
	}
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}
	
	public HashMap<String, Object> getSatisfied() {
		return satisfied;
	}
	public void setSatisfied(HashMap<String, Object> satisfied) {
		this.satisfied = satisfied;
	}
	
	public Map<String, Object> getServiceTime() {
		return serviceTime;
	}
	public void setServiceTime(Map<String, Object> serviceTime) {
		this.serviceTime = serviceTime;
	}
	
	public Map<String, Object> getAvgAgentResponse() {
		return avgAgentResponse;
	}
	public void setAvgAgentResponse(Map<String, Object> avgAgentResponse) {
		this.avgAgentResponse = avgAgentResponse;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	
}
