package cn.sh.ideal.sm.model;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

// TODO: Auto-generated Javadoc
/**
 * The Class HisMessage.
 */
public class CallStatuNum implements Serializable{

	/** The Constant serialVersionUID. */
	private static final long serialVersionUID = 1L;
	
	/**  工号. */
	private String workNo;
	
	/** 技能组分类. */
	private String callStatus;

	/** 技能组的数量. */
	private  Integer statuNum;

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public Integer getStatuNum() {
		return statuNum;
	}

	public void setStatuNum(Integer statuNum) {
		this.statuNum = statuNum;
	}

	public String getCallStatus() {
		return callStatus;
	}

	public void setCallStatus(String callStatus) {
		this.callStatus = callStatus;
	}


}
