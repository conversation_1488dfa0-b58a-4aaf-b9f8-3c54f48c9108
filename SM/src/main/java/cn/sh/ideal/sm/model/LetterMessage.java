/**
 * Project Name:SM Maven Webapp
 * File Name:LetterMessage.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2015年1月12日下午9:53:04
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;



import java.io.Serializable;

import cn.sh.ideal.model.BaseModel;

/**
 * ClassName:LetterMessage <br/>
 * Function: 私信消息实体类
 * Date:     2015年1月12日 下午9:53:04 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class LetterMessage extends BaseModel implements Serializable{

    private static final long serialVersionUID = 5260643662548092207L;
    private String id; //主键
    private String fromWorkNo; //发送工号
    private String toWorkNo;  //接收工号
    private String tenantCode; //租户code
    private String content;  //私信消息内容
    private String type;
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getFromWorkNo() {
        return fromWorkNo;
    }
    public void setFromWorkNo(String fromWorkNo) {
        this.fromWorkNo = fromWorkNo;
    }
    public String getToWorkNo() {
        return toWorkNo;
    }
    public void setToWorkNo(String toWorkNo) {
        this.toWorkNo = toWorkNo;
    }
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    public String getContent() {
        return content;
    }
    public void setContent(String content) {
        this.content = content;
    }
    
    
    
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public LetterMessage(String fromWorkNo, String toWorkNo, String tenantCode,
            String content,String type) {
        super();
        this.fromWorkNo = fromWorkNo;
        this.toWorkNo = toWorkNo;
        this.tenantCode = tenantCode;
        this.content = content;
        this.type = type;
    }
    @Override
    public String toString() {
        return "LetterMessage [id=" + id + ", fromWorkNo=" + fromWorkNo
                + ", toWorkNo=" + toWorkNo + ", tenantCode=" + tenantCode
                + ", content=" + content + "]";
    }
    
    
    public boolean equals(Object o)
    {
        if (this == o) {
            return true;
        }
        if ((o == null) || (getClass() != o.getClass())) {
            return false;
        }
        LetterMessage letterMessage = (LetterMessage)o;
        if (this.id != null ? !this.id.equals(letterMessage.id) : letterMessage.id != null) {
            return false;
        }
        return true;
    }

    public int hashCode()
    {
        return this.id != null ? this.id.hashCode() : 0;
    }
    
    
    

}
