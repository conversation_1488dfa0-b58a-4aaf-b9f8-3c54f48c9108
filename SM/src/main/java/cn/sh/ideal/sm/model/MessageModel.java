/**
 * Project Name:SM Maven Webapp
 * File Name:MessageModel.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月15日下午6:39:50
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.Date;

/**
 * ClassName:MessageModel <br/>
 * Function: 返回消息格式 <br/>
 * Date:     2014年12月15日 下午6:39:50 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class MessageModel implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/** 主键 */
    private String id;

    /** 消息ID */
    private String messageId;
    /**功能条用户id*/
    private String toolbarId;

    /** 消息渠道 */
    private String channelCode;

    /** 业务类型 */
    private String messageBusiness;

    /** 所属租户 */
    private String tenantCode;

    /** 全息用户视图ID */
   // private String userId;
    /** 全息用户等级 */
   // private String userLevel;

    /** 发送账号  openId*/
    private String sendAccount;

    /** 接受账号  commonId*/
    private String acceptedAccount;

    /** 创建时间 */
    private Date createTime;

    /** 会话ID */
    private String sessionId;

    /** 转发前技能组 */
    private String beforeSkillQueue;

    /** 分配技能组 */
    private String skillQueue;

    /** 转发前工号 */
    private String beforeWorkNo;

    /** 客服工号 */
    private String workNo;

    /** 1:未分配 2:已分配 */
    private String status;

    /** 备注 */
    private String remark;

    /** 不同渠道扩展信息JSON格式 */
    private String extData;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getToolbarId() {
        return toolbarId;
    }

    public void setToolbarId(String toolbarId) {
        this.toolbarId = toolbarId;
    }

 
    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getMessageBusiness() {
        return messageBusiness;
    }

    public void setMessageBusiness(String messageBusiness) {
        this.messageBusiness = messageBusiness;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

 
    public String getSendAccount() {
        return sendAccount;
    }

    public void setSendAccount(String sendAccount) {
        this.sendAccount = sendAccount;
    }

    public String getAcceptedAccount() {
        return acceptedAccount;
    }

    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getBeforeSkillQueue() {
        return beforeSkillQueue;
    }

    public void setBeforeSkillQueue(String beforeSkillQueue) {
        this.beforeSkillQueue = beforeSkillQueue;
    }

    public String getSkillQueue() {
        return skillQueue;
    }

    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }

    public String getBeforeWorkNo() {
        return beforeWorkNo;
    }

    public void setBeforeWorkNo(String beforeWorkNo) {
        this.beforeWorkNo = beforeWorkNo;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }
    

}
