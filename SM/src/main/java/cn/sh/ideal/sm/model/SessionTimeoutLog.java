/**
 * Project Name:SM Maven Webapp
 * File Name:SessionTimeoutLog.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月11日下午2:48:12
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.util.Date;

import cn.sh.ideal.model.BaseModel;

/**
 * ClassName:SessionTimeoutLog <br/>
 * Function: 会话超时日志实体类
 * Date:     2014年12月11日 下午2:48:12 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionTimeoutLog extends BaseModel{

    private static final long serialVersionUID = 1656894127416888932L;
    private String id;
    private String sessionId;
    private String tenantCode;
    private String timeouter;
    private Date createTime;
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    public String getTimeouter() {
        return timeouter;
    }
    public void setTimeouter(String timeouter) {
        this.timeouter = timeouter;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    

}
