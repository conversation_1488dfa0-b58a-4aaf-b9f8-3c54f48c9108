package cn.sh.ideal.sm.dao;

import java.util.HashMap;
import java.util.List;

import cn.sh.ideal.model.TimeOutSession;
import cn.sh.ideal.sm.model.SessionTimeoutLog;
import cn.sh.ideal.sm.req.TenantData;
import cn.sh.ideal.sm.resp.AgentHisData;
import cn.sh.ideal.sm.resp.CallStatuNum;
import cn.sh.ideal.sm.resp.SkillTypeNum;

public interface SessionTimeoutLogDao extends BaseDao<SessionTimeoutLog>{
	
	AgentHisData queryHisData(TenantData data);

	List<SkillTypeNum> querySkillTypeNum(TenantData data);

	List<CallStatuNum> queryCallStatuNum(TenantData data);

	HashMap<String, Object> querySummarySection(TenantData data);
    
	public void checkTimeOutActiveLog(TimeOutSession tos) throws Exception;
}