/**
 * Project Name:<PERSON> Maven Webapp
 * File Name:SessionMessage.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2015年3月10日下午3:24:31
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.List;

import cn.sh.ideal.model.MessageInfo;

/**
 * ClassName:SessionMessage <br/>
 * Function: 新定义消息格式  会话 -- 消息 一对多关联实体类
 * Date:     2015年3月10日 下午3:24:31 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionMessage implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/*会话id*/
    private String sessionId;
    /*消息*/
    private List<MessageInfo> messageInfos;
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public List<MessageInfo> getMessageInfos() {
        return messageInfos;
    }
    public void setMessageInfos(List<MessageInfo> messageInfos) {
        this.messageInfos = messageInfos;
    }
    

}
