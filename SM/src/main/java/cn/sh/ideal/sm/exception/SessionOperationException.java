/**
 * Project Name:SM Maven Webapp
 * File Name:SessionOperateException.java
 * Package Name:cn.sh.ideal.mir.session.exception
 * Date:2015年1月19日上午2:49:20
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.exception;
/**
 * ClassName:SessionOperateException <br/>
 * Function: 会话操作异常   
 * Date:     2015年1月19日 上午2:49:20 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionOperationException extends Exception{
    private static final long serialVersionUID = 1L;
    
    public SessionOperationException() {
        super();
    }

    public SessionOperationException(String message) {
        super(message);
    }

    public SessionOperationException(Throwable cause) {
        super(cause);
    }

    public SessionOperationException(String message, Throwable cause) {
        super(message, cause);
    }

}
