package cn.sh.ideal.sm.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class JdComment {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.GUID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String guid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.CONTENT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String content;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.CREATION_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IS_TOP
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private boolean isTop;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REFERENCE_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String referenceId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REFERENCE_IMAGE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String referenceImage;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REFERENCE_NAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String referenceName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REFERENCE_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String referenceTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REFERENCE_TYPE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String referenceType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REFERENCE_TYPE_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String referenceTypeId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.FIRST_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String firstCategory;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.SECOND_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String secondCategory;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.THIRD_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String thirdCategory;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.REPLY_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal replyCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.SCORE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal score;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.STATUS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal status;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.TITLE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USEFUL_VOTE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String usefulVoteCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USELESS_VOTE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String uselessVoteCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_IMAGE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userImage;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_IMAGE_URL
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userImageUrl;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_LEVEL_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userLevelId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_PROVINCE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userProvince;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_REGISTER_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userRegisterTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.VIEW_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal viewCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.ORDER_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal orderId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IS_REPLY_GRADE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String isReplyGrade;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.NICKNAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String nickname;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_CLIENT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal userClient;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.MERGE_ORDER_STATUS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal mergeOrderStatus;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.DISCUSSION_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal discussionId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.PRODUCT_COLOR
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String productColor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.PRODUCT_SIZE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String productSize;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IMAGE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String imageCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.INTEGRAL
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal integral;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.ANONYMOUS_FLAG
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal anonymousFlag;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_LEVEL_NAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userLevelName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.RECOMMEND
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String recommend;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_LEVEL_COLOR
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userLevelColor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.USER_CLIENT_SHOW
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String userClientShow;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IS_MOBILE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String isMobile;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.DAYS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private BigDecimal days;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IMPORT_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private Date importTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IS_ANSWER
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String isAnswer;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column JD_COMMENT.IS_PUSH
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	private String isPush;
	
	private String sessionId;
	
	private Date replyTime;
	
	private int count;
	
	private String shop;
	
	private String shopUrl;
	
	private String replyWorkNo;
	
	private String replyContent;
	
	private String customerId;
	
	private List scoreList;
	
	private String goodCommentMark;
	
	public String getGoodCommentMark() {
		return goodCommentMark;
	}

	public void setGoodCommentMark(String goodCommentMark) {
		this.goodCommentMark = goodCommentMark;
	}

	public List getScoreList() {
		return scoreList;
	}

	public void setScoreList(List scoreList) {
		this.scoreList = scoreList;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.ID
	 * @return  the value of JD_COMMENT.ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.ID
	 * @param id  the value for JD_COMMENT.ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.GUID
	 * @return  the value of JD_COMMENT.GUID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getGuid() {
		return guid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.GUID
	 * @param guid  the value for JD_COMMENT.GUID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setGuid(String guid) {
		this.guid = guid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.CONTENT
	 * @return  the value of JD_COMMENT.CONTENT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getContent() {
		return content;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.CONTENT
	 * @param content  the value for JD_COMMENT.CONTENT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setContent(String content) {
		this.content = content;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.CREATION_TIME
	 * @return  the value of JD_COMMENT.CREATION_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.CREATION_TIME
	 * @param creationTime  the value for JD_COMMENT.CREATION_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setCreationTime(String creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IS_TOP
	 * @return  the value of JD_COMMENT.IS_TOP
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public boolean getIsTop() {
		return isTop;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IS_TOP
	 * @param isTop  the value for JD_COMMENT.IS_TOP
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setIsTop(boolean isTop) {
		this.isTop = isTop;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REFERENCE_ID
	 * @return  the value of JD_COMMENT.REFERENCE_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getReferenceId() {
		return referenceId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REFERENCE_ID
	 * @param referenceId  the value for JD_COMMENT.REFERENCE_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReferenceId(String referenceId) {
		this.referenceId = referenceId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REFERENCE_IMAGE
	 * @return  the value of JD_COMMENT.REFERENCE_IMAGE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getReferenceImage() {
		return referenceImage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REFERENCE_IMAGE
	 * @param referenceImage  the value for JD_COMMENT.REFERENCE_IMAGE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReferenceImage(String referenceImage) {
		this.referenceImage = referenceImage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REFERENCE_NAME
	 * @return  the value of JD_COMMENT.REFERENCE_NAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getReferenceName() {
		return referenceName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REFERENCE_NAME
	 * @param referenceName  the value for JD_COMMENT.REFERENCE_NAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReferenceName(String referenceName) {
		this.referenceName = referenceName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REFERENCE_TIME
	 * @return  the value of JD_COMMENT.REFERENCE_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getReferenceTime() {
		return referenceTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REFERENCE_TIME
	 * @param referenceTime  the value for JD_COMMENT.REFERENCE_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReferenceTime(String referenceTime) {
		this.referenceTime = referenceTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REFERENCE_TYPE
	 * @return  the value of JD_COMMENT.REFERENCE_TYPE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getReferenceType() {
		return referenceType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REFERENCE_TYPE
	 * @param referenceType  the value for JD_COMMENT.REFERENCE_TYPE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReferenceType(String referenceType) {
		this.referenceType = referenceType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REFERENCE_TYPE_ID
	 * @return  the value of JD_COMMENT.REFERENCE_TYPE_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getReferenceTypeId() {
		return referenceTypeId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REFERENCE_TYPE_ID
	 * @param referenceTypeId  the value for JD_COMMENT.REFERENCE_TYPE_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReferenceTypeId(String referenceTypeId) {
		this.referenceTypeId = referenceTypeId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.FIRST_CATEGORY
	 * @return  the value of JD_COMMENT.FIRST_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getFirstCategory() {
		return firstCategory;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.FIRST_CATEGORY
	 * @param firstCategory  the value for JD_COMMENT.FIRST_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setFirstCategory(String firstCategory) {
		this.firstCategory = firstCategory;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.SECOND_CATEGORY
	 * @return  the value of JD_COMMENT.SECOND_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getSecondCategory() {
		return secondCategory;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.SECOND_CATEGORY
	 * @param secondCategory  the value for JD_COMMENT.SECOND_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setSecondCategory(String secondCategory) {
		this.secondCategory = secondCategory;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.THIRD_CATEGORY
	 * @return  the value of JD_COMMENT.THIRD_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getThirdCategory() {
		return thirdCategory;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.THIRD_CATEGORY
	 * @param thirdCategory  the value for JD_COMMENT.THIRD_CATEGORY
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setThirdCategory(String thirdCategory) {
		this.thirdCategory = thirdCategory;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.REPLY_COUNT
	 * @return  the value of JD_COMMENT.REPLY_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getReplyCount() {
		return replyCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.REPLY_COUNT
	 * @param replyCount  the value for JD_COMMENT.REPLY_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setReplyCount(BigDecimal replyCount) {
		this.replyCount = replyCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.SCORE
	 * @return  the value of JD_COMMENT.SCORE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getScore() {
		return score;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.SCORE
	 * @param score  the value for JD_COMMENT.SCORE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setScore(BigDecimal score) {
		this.score = score;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.STATUS
	 * @return  the value of JD_COMMENT.STATUS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getStatus() {
		return status;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.STATUS
	 * @param status  the value for JD_COMMENT.STATUS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setStatus(BigDecimal status) {
		this.status = status;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.TITLE
	 * @return  the value of JD_COMMENT.TITLE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.TITLE
	 * @param title  the value for JD_COMMENT.TITLE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USEFUL_VOTE_COUNT
	 * @return  the value of JD_COMMENT.USEFUL_VOTE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUsefulVoteCount() {
		return usefulVoteCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USEFUL_VOTE_COUNT
	 * @param usefulVoteCount  the value for JD_COMMENT.USEFUL_VOTE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUsefulVoteCount(String usefulVoteCount) {
		this.usefulVoteCount = usefulVoteCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USELESS_VOTE_COUNT
	 * @return  the value of JD_COMMENT.USELESS_VOTE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUselessVoteCount() {
		return uselessVoteCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USELESS_VOTE_COUNT
	 * @param uselessVoteCount  the value for JD_COMMENT.USELESS_VOTE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUselessVoteCount(String uselessVoteCount) {
		this.uselessVoteCount = uselessVoteCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_IMAGE
	 * @return  the value of JD_COMMENT.USER_IMAGE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserImage() {
		return userImage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_IMAGE
	 * @param userImage  the value for JD_COMMENT.USER_IMAGE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserImage(String userImage) {
		this.userImage = userImage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_IMAGE_URL
	 * @return  the value of JD_COMMENT.USER_IMAGE_URL
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserImageUrl() {
		return userImageUrl;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_IMAGE_URL
	 * @param userImageUrl  the value for JD_COMMENT.USER_IMAGE_URL
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserImageUrl(String userImageUrl) {
		this.userImageUrl = userImageUrl;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_LEVEL_ID
	 * @return  the value of JD_COMMENT.USER_LEVEL_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserLevelId() {
		return userLevelId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_LEVEL_ID
	 * @param userLevelId  the value for JD_COMMENT.USER_LEVEL_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserLevelId(String userLevelId) {
		this.userLevelId = userLevelId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_PROVINCE
	 * @return  the value of JD_COMMENT.USER_PROVINCE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserProvince() {
		return userProvince;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_PROVINCE
	 * @param userProvince  the value for JD_COMMENT.USER_PROVINCE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserProvince(String userProvince) {
		this.userProvince = userProvince;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_REGISTER_TIME
	 * @return  the value of JD_COMMENT.USER_REGISTER_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserRegisterTime() {
		return userRegisterTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_REGISTER_TIME
	 * @param userRegisterTime  the value for JD_COMMENT.USER_REGISTER_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserRegisterTime(String userRegisterTime) {
		this.userRegisterTime = userRegisterTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.VIEW_COUNT
	 * @return  the value of JD_COMMENT.VIEW_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getViewCount() {
		return viewCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.VIEW_COUNT
	 * @param viewCount  the value for JD_COMMENT.VIEW_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setViewCount(BigDecimal viewCount) {
		this.viewCount = viewCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.ORDER_ID
	 * @return  the value of JD_COMMENT.ORDER_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getOrderId() {
		return orderId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.ORDER_ID
	 * @param orderId  the value for JD_COMMENT.ORDER_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setOrderId(BigDecimal orderId) {
		this.orderId = orderId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IS_REPLY_GRADE
	 * @return  the value of JD_COMMENT.IS_REPLY_GRADE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getIsReplyGrade() {
		return isReplyGrade;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IS_REPLY_GRADE
	 * @param isReplyGrade  the value for JD_COMMENT.IS_REPLY_GRADE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setIsReplyGrade(String isReplyGrade) {
		this.isReplyGrade = isReplyGrade;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.NICKNAME
	 * @return  the value of JD_COMMENT.NICKNAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getNickname() {
		return nickname;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.NICKNAME
	 * @param nickname  the value for JD_COMMENT.NICKNAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_CLIENT
	 * @return  the value of JD_COMMENT.USER_CLIENT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getUserClient() {
		return userClient;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_CLIENT
	 * @param userClient  the value for JD_COMMENT.USER_CLIENT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserClient(BigDecimal userClient) {
		this.userClient = userClient;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.MERGE_ORDER_STATUS
	 * @return  the value of JD_COMMENT.MERGE_ORDER_STATUS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getMergeOrderStatus() {
		return mergeOrderStatus;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.MERGE_ORDER_STATUS
	 * @param mergeOrderStatus  the value for JD_COMMENT.MERGE_ORDER_STATUS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setMergeOrderStatus(BigDecimal mergeOrderStatus) {
		this.mergeOrderStatus = mergeOrderStatus;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.DISCUSSION_ID
	 * @return  the value of JD_COMMENT.DISCUSSION_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getDiscussionId() {
		return discussionId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.DISCUSSION_ID
	 * @param discussionId  the value for JD_COMMENT.DISCUSSION_ID
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setDiscussionId(BigDecimal discussionId) {
		this.discussionId = discussionId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.PRODUCT_COLOR
	 * @return  the value of JD_COMMENT.PRODUCT_COLOR
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getProductColor() {
		return productColor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.PRODUCT_COLOR
	 * @param productColor  the value for JD_COMMENT.PRODUCT_COLOR
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setProductColor(String productColor) {
		this.productColor = productColor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.PRODUCT_SIZE
	 * @return  the value of JD_COMMENT.PRODUCT_SIZE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getProductSize() {
		return productSize;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.PRODUCT_SIZE
	 * @param productSize  the value for JD_COMMENT.PRODUCT_SIZE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setProductSize(String productSize) {
		this.productSize = productSize;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IMAGE_COUNT
	 * @return  the value of JD_COMMENT.IMAGE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getImageCount() {
		return imageCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IMAGE_COUNT
	 * @param imageCount  the value for JD_COMMENT.IMAGE_COUNT
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setImageCount(String imageCount) {
		this.imageCount = imageCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.INTEGRAL
	 * @return  the value of JD_COMMENT.INTEGRAL
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getIntegral() {
		return integral;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.INTEGRAL
	 * @param integral  the value for JD_COMMENT.INTEGRAL
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setIntegral(BigDecimal integral) {
		this.integral = integral;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.ANONYMOUS_FLAG
	 * @return  the value of JD_COMMENT.ANONYMOUS_FLAG
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getAnonymousFlag() {
		return anonymousFlag;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.ANONYMOUS_FLAG
	 * @param anonymousFlag  the value for JD_COMMENT.ANONYMOUS_FLAG
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setAnonymousFlag(BigDecimal anonymousFlag) {
		this.anonymousFlag = anonymousFlag;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_LEVEL_NAME
	 * @return  the value of JD_COMMENT.USER_LEVEL_NAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserLevelName() {
		return userLevelName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_LEVEL_NAME
	 * @param userLevelName  the value for JD_COMMENT.USER_LEVEL_NAME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserLevelName(String userLevelName) {
		this.userLevelName = userLevelName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.RECOMMEND
	 * @return  the value of JD_COMMENT.RECOMMEND
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getRecommend() {
		return recommend;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.RECOMMEND
	 * @param recommend  the value for JD_COMMENT.RECOMMEND
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setRecommend(String recommend) {
		this.recommend = recommend;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_LEVEL_COLOR
	 * @return  the value of JD_COMMENT.USER_LEVEL_COLOR
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserLevelColor() {
		return userLevelColor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_LEVEL_COLOR
	 * @param userLevelColor  the value for JD_COMMENT.USER_LEVEL_COLOR
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserLevelColor(String userLevelColor) {
		this.userLevelColor = userLevelColor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.USER_CLIENT_SHOW
	 * @return  the value of JD_COMMENT.USER_CLIENT_SHOW
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getUserClientShow() {
		return userClientShow;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.USER_CLIENT_SHOW
	 * @param userClientShow  the value for JD_COMMENT.USER_CLIENT_SHOW
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setUserClientShow(String userClientShow) {
		this.userClientShow = userClientShow;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IS_MOBILE
	 * @return  the value of JD_COMMENT.IS_MOBILE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getIsMobile() {
		return isMobile;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IS_MOBILE
	 * @param isMobile  the value for JD_COMMENT.IS_MOBILE
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setIsMobile(String isMobile) {
		this.isMobile = isMobile;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.DAYS
	 * @return  the value of JD_COMMENT.DAYS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public BigDecimal getDays() {
		return days;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.DAYS
	 * @param days  the value for JD_COMMENT.DAYS
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setDays(BigDecimal days) {
		this.days = days;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IMPORT_TIME
	 * @return  the value of JD_COMMENT.IMPORT_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public Date getImportTime() {
		return importTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IMPORT_TIME
	 * @param importTime  the value for JD_COMMENT.IMPORT_TIME
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setImportTime(Date importTime) {
		this.importTime = importTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IS_ANSWER
	 * @return  the value of JD_COMMENT.IS_ANSWER
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getIsAnswer() {
		return isAnswer;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IS_ANSWER
	 * @param isAnswer  the value for JD_COMMENT.IS_ANSWER
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setIsAnswer(String isAnswer) {
		this.isAnswer = isAnswer;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column JD_COMMENT.IS_PUSH
	 * @return  the value of JD_COMMENT.IS_PUSH
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public String getIsPush() {
		return isPush;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column JD_COMMENT.IS_PUSH
	 * @param isPush  the value for JD_COMMENT.IS_PUSH
	 * @mbggenerated  Mon Jun 20 15:07:13 CST 2016
	 */
	public void setIsPush(String isPush) {
		this.isPush = isPush;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public Date getReplyTime() {
		return replyTime;
	}

	public void setReplyTime(Date replyTime) {
		this.replyTime = replyTime;
	}

	public String getShop() {
		return shop;
	}

	public void setShop(String shop) {
		this.shop = shop;
	}

	public String getShopUrl() {
		return shopUrl;
	}

	public void setShopUrl(String shopUrl) {
		this.shopUrl = shopUrl;
	}

	public String getReplyWorkNo() {
		return replyWorkNo;
	}

	public void setReplyWorkNo(String replyWorkNo) {
		this.replyWorkNo = replyWorkNo;
	}

	public String getReplyContent() {
		return replyContent;
	}

	public void setReplyContent(String replyContent) {
		this.replyContent = replyContent;
	}
	
	
	
}