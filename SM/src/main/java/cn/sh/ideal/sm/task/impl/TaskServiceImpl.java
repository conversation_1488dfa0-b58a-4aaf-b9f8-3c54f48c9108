/**
 * Project Name:SM Maven Webapp
 * File Name:TaskServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.task.impl
 * Date:2014年12月11日上午10:25:26
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 */

package cn.sh.ideal.sm.task.impl;

import java.io.Serializable;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.listener.MessageListenerHandler;
import cn.sh.ideal.mir.req.RemoveQueueSessionRequest;
import cn.sh.ideal.mir.resp.RemoveQueueSessionResponse;
import cn.sh.ideal.mir.service.AllocateService;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.model.SessionTip;
import cn.sh.ideal.model.SkillType;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.model.TimeOutSession;
import cn.sh.ideal.sm.dubbo.SessionServiceImpl;
import cn.sh.ideal.sm.model.SessionEventInfo;
import cn.sh.ideal.sm.model.SessionTimeout;
import cn.sh.ideal.sm.model.SkillQueueWorkNo;
import cn.sh.ideal.sm.service.CommonSendService;
import cn.sh.ideal.sm.service.CommonSendService.PARAMTYPE;
import cn.sh.ideal.sm.service.MessageInfoService;
import cn.sh.ideal.sm.service.SessionEventService;
import cn.sh.ideal.sm.service.SessionInfoService;
import cn.sh.ideal.sm.service.SessionTimeoutService;
import cn.sh.ideal.sm.service.SessionTipService;
import cn.sh.ideal.sm.task.TaskService;
import cn.sh.ideal.sm.utils.Constants;
import cn.sh.ideal.sm.utils.Utils;
import cn.sh.ideal.util.NetUtil;
import cn.sh.ideal.util.RedisLock;
import cn.sh.ideal.util.TipTypes;

import com.alibaba.fastjson.JSONObject;

/**
 * ClassName:TaskServiceImpl <br/>
 * Function: 定时任务实现类 Date: 2014年12月11日 上午10:25:26 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.6
 */
@Scope("prototype")
@Service("taskService")
public class TaskServiceImpl implements TaskService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	private SessionInfoService sessionInfoService;

	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;

	@Resource
	private CommonSendService commonSendService;
	
	@Resource
	private  SessionServiceImpl sessionServiceImpl;
	@Autowired
	private AllocateService allocateService;

	/**
	 * 超时发送地址
	 */
	@Value("#{config['pushAgTipUrl']}")
	private String pushAgTipUrl = "";
	
//	//排队超时
//	
//	@Value("#{config['session.timeoutMeg']}")
//	private String timeoutMeg = "";
	
	/**
	 * mir 会话转发接口Url
	 */
	@Value("#{config['mirTransferUrl']}")
	private String mirTransferUrl = "";
//	@Value("#{config['session.isSortTimeout']}")
//	private String isSortTimeout = "";
	
	
	@Value("#{config['session.isSortTimeoutSystem']}")
	private String isSortTimeoutSystem = "";
	
	
	@Value("#{config['session.sortTimeoutUrl']}")
	private String sortTimeoutUrl = "";
	@Value("#{config['untimeoutSkillTypes']}")
	private String untimeoutSkillTypes = "";
	@Value("#{config['expireKey']}")
	private String expirekey;
	@Value("#{config['actualKey']}")
	private String actualkey;
	@Autowired
	private SysInitService sysInitService;
	@Autowired
	private SessionEventService sessionEventService;
	@Autowired
	private SessionTimeoutService sessionTimeoutService;
	@Autowired
	private SessionTipService sessionTipService;
	@Autowired
	private MessageListenerHandler hanlder;
	@Autowired
	private MessageInfoService messageInfoService;
	
	private String[] needTimeoutStatus = { SessionStatus.MANUAL.getCode(),
			SessionStatus.FORWORD.getCode(), SessionStatus.TEMP_SELF.getCode() };

	private String[] needSortTimeoutStatus = { SessionStatus.SORT.getCode(),
			SessionStatus.ROUTE.getCode() };



	@PostConstruct
	public void init() {
		hanlder.addListenerByPattern(new SessionTimeOutMessageListener(redisDao,this),"__keyevent@*__:expired");
	}

	@Override
	public void checkSessionTimeout() {
		logger.debug("check session outtime start");
		// 获取当前时间

		RedisLock lock = RedisLock.getRedisLock(Constants.TIMEOUT_EXE_FALG);
		if (lock.lock()) {
			try {
				// add 2015 5 28 超时数据单放
				Set<String> sessionIds = redisDao.getKeysByPattern("SESSION_*");
				// add 2015 5 28 超时数据单放
				if (sessionIds == null) {
					logger.info("获取redis中超时对象为:null");

				} else {

					for (String sessionIdKey : sessionIds) {
						RedisLock sessionLock = RedisLock
								.getRedisLock(sessionIdKey);
						if (sessionLock.lock()) {
							try {
								checkTimeout(sessionIdKey);
							} catch (Exception e) {
								logger.info("进行超时操作超时：" + e.getMessage(), e);
							} finally {
								sessionLock.unlock();
							}
						}
					}
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			} finally {
				lock.unlock();
			}
			logger.debug("check session outtime end");

		} else {
			lock.unlock();// 释放锁
		}

	}

	@Override
	public void checkSessionCount() {
		List<SkillQueueWorkNo> skillQueueWorkNos = sessionInfoService
				.getSkillWorkNo();
		for (SkillQueueWorkNo skillQueueWorkNo : skillQueueWorkNos) {
			String tenantCode = skillQueueWorkNo.getTenantCode();
			String skillQueue = skillQueueWorkNo.getSkillQueue();
			if (StringUtils.isEmpty(skillQueue) || "null".equals(skillQueue))
				continue;
			String workNo = skillQueueWorkNo.getWorkNo();
			String key = Utils.getAgentKey(tenantCode, skillQueue, workNo);
			if (!redisDao.exist(key))
				continue;
			String mapKey = "allocate:" + tenantCode + skillQueue + workNo;
			RedisLock lock = RedisLock.getRedisLock(mapKey);
			if (lock.lock()) {
				try {
					logger.debug(
							"sync session count tenantCode:{} skillQueue:{} workNo:{}",
							tenantCode, skillQueue, workNo);
					int currSessionCount = sessionInfoService
							.getSkillWorkNoSessionCount(skillQueueWorkNo);
					redisDao.mapPut(key, "currSessionCount", currSessionCount);
				} finally {
					lock.unlock();
				}

			}
		}
	}

	@Override
	public void checkTimeout(String sessionIdKey) {
		SessionInfo sessionInfo = sessionInfoService.readCacheSessionBySessionKey(sessionIdKey);
		if (sessionInfo == null)
			return;
		String skillType = sessionInfo.getSkillType();
		String tenantCode = sessionInfo.getTenantCode();
		String isReplay = null;
		if (StringUtils.isNotEmpty(skillType)) {
			SkillType skillTypeModel;
			try {
				skillTypeModel = sysInitService.getSkillType(tenantCode,
						skillType);
			} catch (Exception e) {
				logger.error("tenantCode:" + tenantCode + " skillType:"
						+ skillType + " getSkillType error", e.getMessage());
				return;
			}
			logger.debug("{} get setIsRecord {}", skillType,
					skillTypeModel.getIsRecord());
			logger.debug("{} get setIsReplay {}", skillType,
					skillTypeModel.getIsReplay());
			isReplay = skillTypeModel.getIsReplay();
			// 不等于Y表示会话计入坐席会话数
			if (!"Y".equals(skillTypeModel.getIsRecord()))
				return;
		}
		TimeOutSession tos = sessionInfo.getTimeOutSession();
		if (tos == null)
			return;

		long oldTime = tos.getLastActiveTime().getTime();
		long nowTime = new Date().getTime();
		long poor = nowTime - oldTime;
		// 会话已超时 进行后续操作
		if (poor > 0) {
			try {
				if (!Arrays.asList(needTimeoutStatus).contains(
						sessionInfo.getStatus())) {
					// add 2015-5-18添加会话自助超时
					if (SessionStatus.SELF.getCode().equals(
							sessionInfo.getStatus())) {
						logger.info("会话：{}自助服务超时，关闭该会话",
								sessionInfo.getSessionId());
						updateSessionTimeout(sessionInfo);
					} else if (Arrays.asList(needSortTimeoutStatus).contains(
							sessionInfo.getStatus())) {
						SessionTip sessionTip = sessionTipService.get(sessionInfo.getTenantCode(),sessionInfo.getSkillQueue(),sessionInfo.getAcceptedAccount());
						if(sessionTip == null){
							sessionTip = sessionTipService.get(sessionInfo.getTenantCode(),"","");
						}
						boolean isHoliday = Utils.isHoliday(sessionTipService.getCurDate(), sessionTip.getHoliday());
						String sortTimeOutTip = sessionTip.getSortTimeOutTip();
						String isSortTimeOut = sessionTip.getIsSortTimeOut();
						if(isHoliday){
							if(sessionTip.getSortTimeoutTipHoliday() != null && !"".equals(sessionTip.getSortTimeoutTipHoliday())){
								sortTimeOutTip = sessionTip.getSortTimeoutTipHoliday();
							}
							
							if(sessionTip.getIsSortTimeoutHoliday() != null && !"".equals(sessionTip.getIsSortTimeoutHoliday())){
								isSortTimeOut = sessionTip.getIsSortTimeoutHoliday();
							}
							 
						}
						if ("true".equals(isSortTimeOut)) {
							SessionEventInfo eventInfo = new SessionEventInfo();
							eventInfo.setUrl(sortTimeoutUrl);
							MessageInfo messageInfo = new MessageInfo();
							messageInfo.setChannelCode(sessionInfo.getChannelCode());
							messageInfo.setBusinessType(sessionInfo.getBusinessType());
							messageInfo.setTenantCode(sessionInfo.getTenantCode());
							messageInfo.setUserId(sessionInfo.getCustomerId());
							messageInfo.setNickname(sessionInfo.getNickname());
							messageInfo.setSendAccount(sessionInfo.getSendAccount());
							messageInfo.setAcceptedAccount(sessionInfo.getAcceptedAccount());
							messageInfo.setCreateTime(sessionInfo.getCreateTime());
							messageInfo.setSessionId(sessionInfo.getSessionId());
							messageInfo.setSkillQueue(sessionInfo.getSkillQueue());
							messageInfo.setWorkNo(sessionInfo.getWorkNos());
							messageInfo.setStatus(sessionInfo.getStatus());
							messageInfo.setMsgType("text");
							messageInfo.setSkillType(sessionInfo.getSkillType());
							logger.info("SortTimeOutTip:"+sortTimeOutTip);
							if(sortTimeOutTip != null && !"".equals(sortTimeOutTip)){
								MessageInfo messageBody = commonSendService.tranText(sessionInfo,
										sortTimeOutTip, sessionInfo.getWorkNos(),"2");
								commonSendService.sendMsgMGW(messageBody, PARAMTYPE.POST);
								messageInfo.setContent(sortTimeOutTip);
								messageInfo.setMessageSource("4");
								messageInfo.setSource("2");
//								this.messageInfoService.add(messageInfo);
							}
							sessionEventService.doSyncUrl(eventInfo,sessionInfo);
							updateSessionTimeout(sessionInfo);
							RemoveQueueSessionRequest rqsr = new RemoveQueueSessionRequest();
							rqsr.setSessionId(sessionInfo.getSessionId());
							rqsr.setTenantCode(sessionInfo.getTenantCode());
							rqsr.setQueueId(sessionInfo.getSkillQueue());
							RemoveQueueSessionResponse reqsr = allocateService
									.removeSession(rqsr);
//							sessionInfoService.sessionOutTimeSave(
//									sessionInfo.getSessionId(), null);
						}
//						else if("true".equals(isSortTimeoutSystem.trim())) {
//							//系统提示语 关闭会话前提示语
//							//如果是系统提示语，就走这个方法
//							SessionData sessionData = new SessionData();
//							sessionData.setSessionId(sessionInfo.getSessionId());
//							sessionData.setWorkNo(sessionInfo.getWorkNos());
//							sessionData.setChannelCode(sessionInfo.getChannelCode());
//							sessionData.setTenantCode(sessionInfo.getTenantCode());
//							sessionData.setStatus(SessionStatus.TIMEOUT.getCode());
//							try {
//								// 向用户推送超时信息
//								MessageInfo messageBody = commonSendService.tranText(sessionInfo,
//										timeoutMeg, sessionInfo.getWorkNos());
//								commonSendService.sendMsgMGW(messageBody, PARAMTYPE.POST);
//								sessionServiceImpl.updateSession(sessionData);
//								logger.info("提示语成功和关闭也成功，关闭排队。。。。。。。。。。。。");
//							} catch (Exception e) {
//								logger.info("关闭会话异常:"+e);
//							}
//						}
					}
					return;
				} else {
					String timeoutType = tos.getTimeoutType();
					// add 2016-06-12 by handSomeWu 添加在人工超时日志记录
					checkTimeOutActiveLog(tos);
					// 推送提示
					if (Constants.TIMEOUT_TYPE_TIP.equals(timeoutType)) {
						if ("Y".equals(isReplay)) {
							sendTimeoutTip(sessionInfo);
						}

						// 关闭会话
					} else if (Constants.TIMEOUT_TYPE_CLOSE.equals(timeoutType)) {
						updateSessionTimeout(sessionInfo);
						return ;
						// 转发会话
					} else if (Constants.TIMEOUT_TYPE_TRANSFER
							.equals(timeoutType)) {
						transferSession(sessionInfo);
					}
					updateTimeoutModel(sessionInfo);

				}

			} catch (Exception e) {
				logger.error(sessionInfo.getSessionId() + "超时处理异常!", e);
			}

		}

	}

	/**
	 * 添加在人工超时日志记录 2016-06-12 by handSomeWu
	 */
	public void checkTimeOutActiveLog(TimeOutSession tos) {
		sessionInfoService.addCheckTimeOutActiveLog(tos);
	}

	/**
	 * 更新会话超时时间
	 *
	 * @param sessionInfo
	 */
	public void updateTimeoutModel(SessionInfo sessionInfo) throws Exception {
		TimeOutSession tos = sessionInfo.getTimeOutSession();
		String timeOutUser = tos.getLastActiveUser();
		String sessionId = sessionInfo.getSessionId();
		SessionTimeout sessionTimeout = sessionTimeoutService.get(sessionInfo.getTenantCode(),sessionInfo.getSkillQueue(),sessionInfo.getAcceptedAccount());
		if(sessionTimeout == null){
			sessionTimeout = sessionTimeoutService.get(sessionInfo.getTenantCode(),"","");
		}
		long time;
		String timeoutType = "";
		if (Constants.AG_ACTIVE.equals(timeOutUser)) {// 用户超时
			/*
			 * 会话超时关闭时间
			 */
			int userSessionTimeout2 = 600000;
			if (sessionTimeout != null) {
				userSessionTimeout2 = sessionTimeout.getUserSessionTimeout2();
				timeoutType = sessionTimeout.getUserSessionTimeoutAction2();
				//TODO
				redisDao.saveValue(expirekey+sessionInfo.getSessionId(), "", userSessionTimeout2, TimeUnit.MILLISECONDS);
			}
			time = new Date().getTime() + userSessionTimeout2;
			//TODO
			redisDao.saveValue(actualkey+sessionInfo.getSessionId(), sessionInfo.getSessionId());
		} else {// 坐席超时
			/*
			 * 会话超时关闭时间
			 */
			int agSessionTimeout2 = 600000;
			if (sessionTimeout != null) {
				agSessionTimeout2 = sessionTimeout.getAgSessionTimeout2();
				timeoutType = sessionTimeout.getAgSessionTimeoutAction2();
				//TODO
				redisDao.saveValue(expirekey+sessionInfo.getSessionId(), "", agSessionTimeout2, TimeUnit.MILLISECONDS);

			}
			time = new Date().getTime() + agSessionTimeout2;
			//TODO
			redisDao.saveValue(actualkey+sessionInfo.getSessionId(), sessionInfo.getSessionId());
		}
		tos.setLastActiveTime(new Date(time));
		if (StringUtils.isEmpty(timeoutType)) {
			logger.warn(
					"sessionId:{} sessionTimeoutAction is null use default action tip",
					sessionId);
			tos.setTimeoutType(Constants.TIMEOUT_TYPE_TIP);
		} else {
			tos.setTimeoutType(timeoutType);
		}

		tos.setTimeoutCount(tos.getTimeoutCount() + 1);
		logger.info("**********会话id:" + sessionId + "下次超时时间为:" + new Date(time));
		sessionInfoService.sessionOutTimeSave(sessionId, tos);

	}

	/**
	 * 推送超时提示信息 用户超时 用户和坐席收到提示信息 坐席超时 坐席收到提示信息
	 *
	 * @param sessionInfo
	 */
	public void sendTimeoutTip(SessionInfo sessionInfo) throws Exception {
		String randomWorkno = "";
		String content;
		String timeoutUser = sessionInfo.getTimeOutSession()
				.getLastActiveUser();
		TimeOutSession timeOutSession = sessionInfo.getTimeOutSession();
		int timeoutCount = timeOutSession.getTimeoutCount();
		String workNos = sessionInfo.getWorkNos();
		SessionTip sessionTip = sessionTipService.get(sessionInfo.getTenantCode(),sessionInfo.getSkillQueue(),sessionInfo.getAcceptedAccount());
		if(sessionTip == null){
			sessionTip = sessionTipService.get(sessionInfo.getTenantCode(),"","");
		}
		String contentTipToUser = "";
		String contentTipToAg = "";
		String sendType = TipTypes.DEFAULT.getCode();
		if (StringUtils.isNotEmpty(workNos)) {
			String[] works = workNos.split(",");
			randomWorkno = works[0];
			if (timeoutCount == 0){
				if (Constants.AG_ACTIVE.equals(timeoutUser)) {
					if (sessionTip != null) {
						contentTipToUser = sessionTip.getUserTimeoutTip();
						contentTipToAg = sessionTip.getUserTimeOutToAGTip();
						sendType = TipTypes.USERTIMEOUT.getCode();
					}
				}else{
					if (sessionTip != null) {
						contentTipToUser = sessionTip.getAgTimeOutToUserTip();
						contentTipToAg = sessionTip.getAgTimeoutTip();
						sendType = TipTypes.AGTIMEOUT.getCode();
					}
				}
			}else if(timeoutCount == 1){
				if (Constants.AG_ACTIVE.equals(timeoutUser)) {
					if (sessionTip != null) {
						contentTipToUser = sessionTip.getUserTimeoutCloseTip();
						contentTipToAg = sessionTip.getUserTimeOutCloseToAGTip();
						sendType = TipTypes.USERTIMEOUTCLOSE.getCode();
					}
				}else{
					if (sessionTip != null) {
						contentTipToUser = sessionTip.getAgTimeOutCloseToUserTip();
						contentTipToAg = sessionTip.getAgTimeoutCloseTip();
						sendType = TipTypes.AGTIMEOUTCLOSE.getCode();
					}
				}
			}
			
			MessageInfo messageInfo = new MessageInfo();
			messageInfo.setChannelCode(sessionInfo.getChannelCode());
			messageInfo.setBusinessType(sessionInfo.getBusinessType());
			messageInfo.setTenantCode(sessionInfo.getTenantCode());
			messageInfo.setUserId(sessionInfo.getCustomerId());
			messageInfo.setNickname(sessionInfo.getNickname());
			messageInfo.setSendAccount(sessionInfo.getSendAccount());
			messageInfo.setAcceptedAccount(sessionInfo.getAcceptedAccount());
			messageInfo.setCreateTime(sessionInfo.getCreateTime());
			messageInfo.setSessionId(sessionInfo.getSessionId());
			messageInfo.setSkillQueue(sessionInfo.getSkillQueue());
			messageInfo.setWorkNo(sessionInfo.getWorkNos());
			messageInfo.setStatus(sessionInfo.getStatus());
			messageInfo.setMsgType("text");
			messageInfo.setSkillType(sessionInfo.getSkillType());
			
			// 坐席超时提示
			if(contentTipToUser != null && !"".equals(contentTipToUser)){
				// 向用户推送超时信息
				MessageInfo mInfo = commonSendService.tranText(
						sessionInfo, contentTipToUser, randomWorkno,"0");
				mInfo.setSendType(sendType);
				mInfo.setTimeOutAction(timeOutSession.getTimeoutType());
				commonSendService.sendMsgMGW(mInfo, PARAMTYPE.POST);
				messageInfo.setContent(contentTipToUser);
				messageInfo.setMessageSource("6");
				messageInfo.setSendType(sendType);
				messageInfo.setSource("2");
				this.messageInfoService.add(messageInfo);
			}

			if(contentTipToAg != null && !"".equals(contentTipToAg)){
				String tipUser;
				if (StringUtils.isNotEmpty(sessionInfo.getNickname())) {
					tipUser = URLDecoder.decode(sessionInfo.getNickname(), "UTF-8");

				} else {
					tipUser = sessionInfo.getSendAccount();

				}
				contentTipToAg = String.format(contentTipToAg, tipUser);
				TimeOutSession tos = sessionInfo.getTimeOutSession();
				sessionInfoService.sendAgTip(sessionInfo,TipStatus.TIME_OUT.getName(), contentTipToAg,tos.getLastActiveUser(),sendType,tos.getTimeoutType());
				messageInfo.setContent(contentTipToAg);
				messageInfo.setMessageSource("5");
				messageInfo.setSource("1");
				messageInfo.setSendType(sendType);
				this.messageInfoService.add(messageInfo);
			}

		}

	}

	/**
	 * 超时关闭会话
	 *
	 * @param info
	 * @throws Exception
	 */
	public void updateSessionTimeout(SessionInfo info) throws Exception {
		logger.info("会话：{} 自助服务超时，关闭该会话", info.getSessionId());
		SessionData data = new SessionData();
		data.setSessionId(info.getSessionId());
		data.setTenantCode(info.getTenantCode());
		data.setStatus(SessionStatus.TIMEOUT.getCode());

		sessionInfoService.updateSessionStatus(data);

	}

	/**
	 * 转发会话
	 *
	 * @param info
	 */
	public void transferSession(SessionInfo info) {
		if ("MULTI".equals(info.getSessionType()))
			return;
		// 推送转发请求
		pushMirTransfer(info);

	}

	/**
	 * 向mir推送转发请求
	 *
	 * @param session
	 * @return
	 */
	private boolean pushMirTransfer(SessionInfo session) {
		try {
			if (!(StringUtils.isEmpty(session.getWorkNos()) || session
					.getWorkNos().split(",").length > 1)) {
				String[] works = session.getWorkNos().split(",");
				JSONObject json = new JSONObject();
				json.put("sessionId", session.getSessionId());
				json.put("beforeWorkNo", works[0]);
				json.put("beforeSkillQueue", session.getSkillQueue());
				json.put("forwardSkillQueue", session.getSkillQueue());
				json.put("tenantCode", session.getTenantCode());
				json.put("remark", "超时自动转发");
				SysParam param = sysInitService
						.getSysParam(Constants.MIR_ADDRESS);
				String mirAddress = param.getParamValue();
				String transferUrl = mirAddress + mirTransferUrl;
				logger.info("mir转发url:{}", transferUrl);
				// TODO

				String result = NetUtil.send(transferUrl, NetUtil.POST,
						json.toJSONString(), "application/json");
				logger.info("超时转发到MIR返回的结果信息为：" + result);
				JSONObject object = JSONObject.parseObject(result);
				if (object.getString("result").equals("0")) {
					return true;
				}
			}
		} catch (Exception e) {
			logger.error("超时转发 异常!", e);
		}
		return false;
	}

	/* 坐席提示类型 */
	public enum TipStatus {
		/* 超时提醒 */
		TIME_OUT,

		/* 超时转发 */
		TIME_OUT_RELAY,

		/* 用户关闭会话 */
		CUSTOMER_QUIT;

		public String getName() {
			return this.name();
		}
	}

}
