/**
 * Project Name:SM Maven Webapp
 * File Name:AgSession.java
 * Package Name:cn.sh.ideal.mir.model
 * Date:2014年12月15日上午11:17:30
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.Arrays;

import cn.sh.ideal.model.SessionStatus;

/**
 * ClassName:AgSession <br/>
 * Function: 坐席会话需要字段. <br/>
 * Date:     2014年12月15日 上午11:17:30 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class AgSession implements Serializable{
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/*会话ID*/
    private String sessionId;
    
    /*工号*/
    private String workNo;
    
    /*会话状态*/
    private String status;
    
    /*渠道名称*/
    private String channelName;
    
    /*会话开始时间*/
    private String startTime;
    
    /*会话结束时间*/
    private String endTime;
    /*会话类型*/
    private String sessionType;
    
    String[] runtimeStatus = {SessionStatus.MANUAL.getCode(),SessionStatus.TEMP_SELF.getCode(),SessionStatus.FORWORD.getCode()};
    String[] closeStatus = {SessionStatus.USER_CLOSE.getCode(),SessionStatus.AG_CLOSE.getCode(),SessionStatus.TIMEOUT.getCode()};
    String[] delayStatus= {SessionStatus.DELAY.getCode()};
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getWorkNo() {
        return workNo;
    }
    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }
    public String getStatus() {
        if (Arrays.asList(runtimeStatus).contains(status)) return "0";
        if (Arrays.asList(closeStatus).contains(status)) return "1";
        if (Arrays.asList(delayStatus).contains(status)) return "2";
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public String getChannelName() {
        return channelName;
    }
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
    public String getStartTime() {
        return startTime;
    }
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    public String getEndTime() {
        return endTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    public String getSessionType() {
        return sessionType;
    }
    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }
    
    
    
    

}
