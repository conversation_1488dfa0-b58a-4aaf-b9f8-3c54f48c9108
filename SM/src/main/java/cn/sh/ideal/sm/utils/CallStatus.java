/**
 * Project Name:SM Maven Webapp
 * File Name:SessionType.java
 * Package Name:cn.sh.ideal.mir.session.util
 * Date:2015年3月6日下午1:55:39
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.utils;
/**
 * ClassName:SessionType <br/>
 * Function: 会话类型
 * Date:     2015年3月6日 下午1:55:39 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public enum CallStatus {
    /*被动接入*/
    IN("0"),
    /*主动外呼*/
    OUT("1");

    private String code;
    private CallStatus(String code){
        this.code = code;
    }
    public String getCode(){
        return this.code;
    }
    

}
