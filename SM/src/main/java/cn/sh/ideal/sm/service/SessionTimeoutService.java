/**
 * Project Name:SM Maven Webapp
 * File Name:CmsSysParamService.java
 * Package Name:cn.sh.ideal.mir.session.service
 * Date:2015年2月3日下午2:13:31
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service;


import java.util.List;

import cn.sh.ideal.sm.model.SessionTimeout;


/**
 * 超时设置service
 */
public interface SessionTimeoutService extends BaseService<SessionTimeout>{
    /*初始化*/
    void init() throws Exception;
    /**
     * 获取超时设置列表
     * @param sessionTimeout
     * @return
     * @throws Exception
     */
    List<SessionTimeout> getList(SessionTimeout sessionTimeout) throws Exception;

    /**
     * 获取租户下的超时设置
     * @param tenantCode
     * @return
     * @throws Exception
     */
    SessionTimeout get(String tenantCode,String skillQueue,String acceptedAccount) throws Exception;

}
