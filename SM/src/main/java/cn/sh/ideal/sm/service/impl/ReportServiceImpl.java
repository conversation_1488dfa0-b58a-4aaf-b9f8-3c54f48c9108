package cn.sh.ideal.sm.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.sm.dao.SessionInfoDao;
import cn.sh.ideal.sm.dao.SessionTimeoutLogDao;
import cn.sh.ideal.sm.req.ReportData;
import cn.sh.ideal.sm.req.SatisfyData;
import cn.sh.ideal.sm.req.TenantData;
import cn.sh.ideal.sm.resp.AgentHisData;
import cn.sh.ideal.sm.resp.AgentStatistics;
import cn.sh.ideal.sm.resp.AgentStatisticsAvg;
import cn.sh.ideal.sm.resp.CallStatuNum;
import cn.sh.ideal.sm.resp.RespAgentCountReport;
import cn.sh.ideal.sm.resp.RespAgentHisDataReport;
import cn.sh.ideal.sm.resp.RespAgentStatistics;
import cn.sh.ideal.sm.resp.RespPeakReport;
import cn.sh.ideal.sm.resp.RespPutSatisfied;
import cn.sh.ideal.sm.resp.RespQueryChannelServiceCount;
import cn.sh.ideal.sm.resp.RespQuerySkillWaitAvgTime;
import cn.sh.ideal.sm.resp.SkillTypeNum;
import cn.sh.ideal.sm.service.ReportService;
import cn.sh.ideal.sm.utils.Constants;

@Service("reportService")
public class ReportServiceImpl implements ReportService {
	@Autowired
	private SessionInfoDao sessionInfoDao;
	
	@Autowired
    private SessionTimeoutLogDao sessionTimeoutLogDao;
	

	private static final Logger logger = LoggerFactory
            .getLogger(ReportServiceImpl.class);


	@Override
	public RespPeakReport peakReport(ReportData reportData) {
		RespPeakReport result = null;
		//logger.info("服务峰谷分析报表-------请求参数：" + reportData.toString());
		try {
			fillEmptyList(reportData);
			
			 if (reportData.getStatus() != null && StringUtils.isNotEmpty((String) reportData.getStatus())) {
		            String status = reportData.getStatus();
		            String[] statuses = status.split(",");
		            reportData.setStatuses(statuses);
		        }
		        if (reportData.getSkillQueue() != null && StringUtils.isNotEmpty((String)reportData.getSkillQueue())) {
		            String skillQueue = reportData.getSkillQueue();
		            String[] skillQueues = skillQueue.split(",");
		            reportData.setSkillQueues(skillQueues);
		        }
		        if (reportData.getSkillType() != null && StringUtils.isNotEmpty((String) reportData.getSkillType())) {
		            String skillType = reportData.getSkillType();
		            String[] skillTypes = skillType.split(",");
		            reportData.setSkillTypes(skillTypes);
		        }
		        List<HashMap<String, Object>> list = sessionInfoDao.queryServicePeak(reportData);
		        result = new RespPeakReport(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, list);
			
		} catch (Exception e) {
			logger.error("服务峰谷分析报表错误", e);
			result = new RespPeakReport(Constants.RESPONSE_ERROR, e.getMessage(), null);
		}
		//logger.info("服务峰谷分析报表-------返回参数：" + result);
		return result;
	}
	
	private void fillEmptyList(ReportData reportData) {
		if(reportData == null)
			return;
		
		if(reportData.getScale()!=null)
			return;
		
		int scale = reportData.getScale();
		
		List<Object> objs = new ArrayList<Object>();
		for (int i = 0; i < scale; i++) {
			objs.add(new Object());
		}
		
		reportData.setEmptyList(objs);
	}


	@Override
	public RespAgentCountReport agentCountReport(ReportData reportData) {
		RespAgentCountReport result = null;
		//logger.info("服务峰谷分析报表所需坐席数-------请求参数：" + data.toString());
		try {
			
			List<Object> objs = sessionInfoDao.queryAgentCount(reportData);
			result = new RespAgentCountReport(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, objs);
			
		} catch (Exception e) {
			logger.error("服务峰谷分析报表所需坐席数", e);
			result = new RespAgentCountReport(Constants.RESPONSE_ERROR, e.getMessage(), null);
		}
		//logger.info("服务峰谷分析报表所需坐席数-------返回参数：" + result);
		return result;
	}


	@Override
	public RespPutSatisfied putSatisfied(SatisfyData data) {
		RespPutSatisfied result = null;
    	if(data!=null){
    		if(!StringUtils.isEmpty(data.getSessionId())
    				&&!StringUtils.isEmpty(data.getWorkNo())
    				&&!StringUtils.isEmpty(data.getSatified()+"")
    				){
    			try {
    				data.setCreateTime(System.currentTimeMillis());
    				data.setRequestTime(System.currentTimeMillis());
					int i=sessionInfoDao.insertSatified(data);
					if(i==1){
						result = new RespPutSatisfied(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, null);
					}else{
						result = new RespPutSatisfied(Constants.RESPONSE_ERROR, Constants.FAILED_MSG, null);
					}
				} catch (Exception e) {
					logger.error(e.getMessage(),e);
				}
				//result = new RespPutSatisfied("201", Constants.FAILED_MSG, null);
				return result;
    		}
    	}
    	return null;
	}


	@Override
	public RespQuerySkillWaitAvgTime querySkillWaitAvgTime(TenantData data) {
		RespQuerySkillWaitAvgTime result = null;
    	if(data!=null){
    		if(data.getStartTime()!=null&&data.getEndTime()!=null){
    			try {
    				List<HashMap<String, Object>> list = sessionInfoDao.querySkillWaitAvgTime(data);
    				result = new RespQuerySkillWaitAvgTime(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, list);
				} catch (Exception e) {
					e.printStackTrace();
				}
    		}else{
    			result = new RespQuerySkillWaitAvgTime(Constants.RESPONSE_ERROR, Constants.FAILED_MSG, null);
    		}
    	}
    	return result;
	}


	@Override
	public RespQueryChannelServiceCount queryChannelServiceCount(
			String tenant) {
		RespQueryChannelServiceCount result = null;
    	try {
    		List<HashMap<String, Object>> list = sessionInfoDao.queryChannelServiceCount(tenant);
			result = new RespQueryChannelServiceCount(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, list);
		} catch (Exception e) {
			result = new RespQueryChannelServiceCount(Constants.RESPONSE_ERROR, Constants.FAILED_MSG, null);
		}
		return result;
	}


	@Override
	public RespAgentHisDataReport agentHisDataReport(TenantData data) {
		RespAgentHisDataReport result = null;
		//logger.info("坐席历史交互数据-------请求参数：" + data.toString());
		try {
			
			 AgentHisData hisData = sessionTimeoutLogDao.queryHisData(data);
		        if (hisData != null) {
		            List<SkillTypeNum> skillTypeList = sessionTimeoutLogDao.querySkillTypeNum(data);
		            List<CallStatuNum> callStatuList = sessionTimeoutLogDao.queryCallStatuNum(data);
		            HashMap<String, Object> summary = sessionTimeoutLogDao.querySummarySection(data);
		            if (skillTypeList.size() > 0)
		                hisData.setSkillNumList(skillTypeList);
		            if (callStatuList.size() > 0)
		                hisData.setCallNumList(callStatuList);
		            if (summary != null) {
		                hisData.setQesDesc(summary.get("QESDESC") == null ? "" : summary.get("QESDESC").toString());
		                hisData.setDealScheme(summary.get("DEALSCHEME") == null ? "" : summary.get("DEALSCHEME").toString());
		            }
		        }
			
			result = new RespAgentHisDataReport(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, hisData);
			
		} catch (Exception e) {
			logger.error("坐席历史交互数据错误", e);
			result = new RespAgentHisDataReport(Constants.RESPONSE_ERROR, Constants.FAILED_MSG, null);
		}
		//logger.info("坐席历史交互数据-------返回参数：" + result);
		return result;
	}


	@Override
	public RespAgentStatistics agentStatistics(AgentStatistics data) {
		RespAgentStatistics result = null;
		try {
			//logger.info("=========请求坐席个人绩效统计=========【"+jsonTemp+"】");
	        if (data == null) {
	            throw new RuntimeException("request object is null!");
	        }
	        if (StringUtils.isEmpty(data.getTenantCode()) || StringUtils.isEmpty(data.getWorkNo())) {
	            throw new RuntimeException("tenantCode or workNo is empty!");
	        }
	//求出绩效
	        AgentStatisticsAvg aag = calculateAgentAvg(data);

			result = new RespAgentStatistics(Constants.RESPONSE_OK, Constants.SUCCESS_MSG, aag);
		} catch (Exception e) {
			logger.error("坐席个人绩效统计错误", e);
			result = new RespAgentStatistics(Constants.RESPONSE_ERROR, Constants.FAILED_MSG, null);
		}
		return result;
	}
	
	/**
     * 个人绩效数据统计（满意度、交互时长、响应速度）
     */
    private AgentStatisticsAvg calculateAgentAvg(AgentStatistics as) {
        AgentStatisticsAvg aag = new AgentStatisticsAvg();
        Map<String, Object> mapSatified = new HashMap<String, Object>();
        Map<String, Object> avgServiceTime = new HashMap<String, Object>();
        Map<String, Object> avgAgentResponse = new HashMap<String, Object>();
//满意度统计
        mapSatified = getAgentSatisfied(as);
        aag.setSatisfied((HashMap<String, Object>) mapSatified);
//交互时长
        avgServiceTime = getAgentServiceTime(as);
        aag.setServiceTime(avgServiceTime);
//响应速度
        avgAgentResponse = getAgentResponse(as);
        aag.setAvgAgentResponse(avgAgentResponse);
//带上参数
        aag.setTenantCode(as.getTenantCode());
        aag.setWorkNo(as.getWorkNo());
        return aag;

    }
    
    /**
     * 获取满意度统计
     */
    private Map<String, Object> getAgentSatisfied(AgentStatistics as) {
        try {
//1.获取未评价的数量
            int unFinishCount = 0;
            int finishCount = 0;
            int satifiedScore = 0;
            int avgSatified = 0;
            Map<String, Object> mapSatified = new HashMap<String, Object>();
            Map<String, Object> mapFinishSatified = new HashMap<String, Object>();
            List<AgentStatistics> asSatifiedList = sessionInfoDao.querySatisfied(as);
//2.循环list的数据，取出满意度
            for (AgentStatistics asSatified : asSatifiedList) {
                if (asSatified.getSatisfied() == null) {
                    unFinishCount++;
                } else {
                    satifiedScore += asSatified.getSatisfied();
                    finishCount++;
                }
            }
//装配参数
            mapSatified.put("unFinishCount", unFinishCount);

            mapFinishSatified.put("finishCount", finishCount);
            mapFinishSatified.put("satifiedScore", satifiedScore);
            if (finishCount != 0) {
                avgSatified = satifiedScore / finishCount;
            }
            mapFinishSatified.put("avgSatified", avgSatified);

            mapSatified.put("finishSatified", mapFinishSatified);
            return mapSatified;

        } catch (Exception e) {
            logger.info("满意度统计失败" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取交互时长
     */
    private Map<String, Object> getAgentServiceTime(AgentStatistics as) {
        int Service = 0;
        Integer serviceTimeOne = 0;
        Integer totalNum = 0;
        Map<String, Object> mapServiceTime = new HashMap<String, Object>();
        try {
            List<AgentStatistics> asServiceTimeList = sessionInfoDao.queryServiceTime(as);
            for (AgentStatistics asServiceTime : asServiceTimeList) {
                serviceTimeOne += asServiceTime.getServiceTime();
            }
            totalNum = asServiceTimeList.size();
            mapServiceTime.put("totalServiceTime", serviceTimeOne);
            mapServiceTime.put("totalServiceNum", totalNum);

            if (totalNum != 0) {
                Service = serviceTimeOne / totalNum;
            }
            mapServiceTime.put("avgServiceTime", Service);

            return mapServiceTime;
        } catch (Exception e) {
            logger.error("获取交互时长失败" + e);
            return mapServiceTime;
        }
    }

    /**
     * 获取响应速度
     */
    private Map<String, Object> getAgentResponse(AgentStatistics as) {
        long agentResponse = 0;
        long agentResponseTotal = 0;
        long avgAgentResponse = 0;
        Integer validNum = 0;
        Integer invalidNum = 0;
        Map<String, Object> mapAgentResponse = new HashMap<String, Object>();
        Map<String, Object> mapCorrectAgentResponse = new HashMap<String, Object>();

        try {
            List<AgentStatistics> asAgentResponseList = sessionInfoDao.queryAgentResponse(as);
            for (AgentStatistics asAgentResponse : asAgentResponseList) {
//不合法参数过滤
                if (asAgentResponse.getMinTime() == null) {
                    invalidNum++;
                    continue;
                } else {
//求出坐席单次响应的时间
                    agentResponse = (asAgentResponse.getMinTime().getTime() - asAgentResponse.getBeginTime().getTime()) / 1000;
                    if (agentResponse <= 0) {
                        invalidNum++;
                    }
                    agentResponseTotal += agentResponse;
                    validNum++;
                }
            }

            mapAgentResponse.put("invalidNum", invalidNum);
            mapCorrectAgentResponse.put("validNum", validNum);
            mapCorrectAgentResponse.put("agentResponseTotalTime", agentResponseTotal);
            if (validNum != 0) {
                avgAgentResponse = agentResponseTotal / validNum;
            }
            mapCorrectAgentResponse.put("avgAgentResponseTime", avgAgentResponse);


            mapAgentResponse.put("validData", mapCorrectAgentResponse);

            return mapAgentResponse;
        } catch (Exception e) {
            logger.error("获取响应速度失败" + e);
            return null;
        }


    }

}
