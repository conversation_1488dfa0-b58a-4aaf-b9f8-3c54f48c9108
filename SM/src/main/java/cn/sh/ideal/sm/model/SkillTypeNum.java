package cn.sh.ideal.sm.model;

import java.io.Serializable;

// TODO: Auto-generated Javadoc
/**
 * The Class HisMessage.
 */
public class SkillTypeNum implements Serializable{

	/** The Constant serialVersionUID. */
	private static final long serialVersionUID = 1L;
	
	/**  工号. */
	private String workNo;
	
	/** 技能组分类. */
	private String skillType;

	/** 技能组的数量. */
	private  Integer skillNum;

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public String getSkillType() {
		return skillType;
	}

	public void setSkillType(String skillType) {
		this.skillType = skillType;
	}

	public Integer getSkillNum() {
		return skillNum;
	}

	public void setSkillNum(Integer skillNum) {
		this.skillNum = skillNum;
	}
	
}
