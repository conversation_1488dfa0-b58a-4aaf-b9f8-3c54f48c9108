/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionInfoServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service
 * Date:2014年12月8日上午9:50:02
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.sh.ideal.model.Session;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.TimeOutSession;
import cn.sh.ideal.sm.model.AgSession;
import cn.sh.ideal.sm.model.MessageData;
import cn.sh.ideal.sm.model.SessionTimeoutLog;
import cn.sh.ideal.sm.model.SessionWorkNo;
import cn.sh.ideal.sm.model.SkillQueueWorkNo;
import cn.sh.ideal.sm.resp.FlagModel;
import cn.sh.ideal.sm.resp.SessionSortItem;

/**
 * ClassName:SessionInfoServiceImpl <br/>
 * Function: 会话信息Service <br/>
 * Date:     2014年12月8日 上午9:50:02 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface SessionInfoService extends BaseService<SessionInfo> {
    /**
     * 初始化系统配置
     */
    public void init() throws Exception;
    /**
     *
     * cacheSession:(&#x7f13;&#x5b58;&#x4f1a;&#x8bdd;&#x5230;&#x7f13;&#x5b58;). <br/>
     *
     * <AUTHOR>
     * @param sessionId
     * @param info
     * @return void
     * @since JDK 1.6
     */
    public void cacheSession(String sessionId,SessionInfo info);
    /**
     * 缓存数据
     * 
     * @param key
     * @param value
     * @param timeout
     *            超时时间
     * @param timeUnit
     *            单位
     */
    public void cacheData(String key, Serializable value, int timeout, TimeUnit timeUnit);
    
    /**
     * 
     * readCacheData:(读取缓存中数据). <br/>
     *
     * <AUTHOR>
     * @param key
     * @return Object
     * @since JDK 1.6
     */
    public Object readCacheData(String key);
    /**
     * 
     * createSession:(创建会话id). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return String 会话id
     * @since JDK 1.6
     */
    public SessionInfo createSession(SessionData data) throws Exception;

    /**
     * 批量创建主动发起会话
     * @param data
     * @param failAccounts
     * @return
     * @throws Exception
     */
    public List<SessionInfo> batchCreateInitiativeSession(SessionData data,List<String> failAccounts) throws Exception;

    /**
     * 创建主动发起会话
     * @param data
     * @return
     * @throws Exception
     */
    public SessionInfo createInitiativeSession(SessionData data) throws Exception;
    /**
     * 
     * getSession:(获取会话信息). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception SessionInfo
     * @since JDK 1.6
     */
    public SessionInfo getSession(SessionData data) throws Exception;

    /**
     * getSessionActiveFlag(获取会话可发送状态 网关点对点消息)
     * @param data
     * @return
     * @throws Exception
     */
    public int getSessionActiveFlag(SessionData data) throws Exception;
    /**
     * 
     * getSessionList:(获取会话列表). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception 
     * @return List<SessionInfo>
     * @since JDK 1.6
     */
    public List<SessionInfo> getSessionList(SessionData data) throws Exception;
    /**
     * 
     * getSessionListCount:(获取会话列表数量). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception 
     * @return int
     * @since JDK 1.6
     */
    public int getSessionListCount(SessionData data) throws Exception;
    /**
     * 
     * updateSession:(更新会话状态). <br/>
     *
     * <AUTHOR>
     * @param info
     * @return
     * @throws Exception SessionInfo
     * @since JDK 1.6
     */
    public void updateSession(SessionInfo info) throws Exception;
    /**
     * 
     * updateSessionSelf:(自助状态更新接口). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionSelf(SessionData data) throws Exception;
    /**
     * 
     * updateSessionRoute:(更新会话已接入至路由). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionRoute(SessionData data) throws Exception;
    /**
     * 
     * updateSessionSort:(更新会话进入排序). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionSort(SessionData data) throws Exception;
    /**
     * 
     * updateSessionAllocat:(更新会话开始分配). 
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionAllocat(SessionData data) throws Exception;
    /**
     * 
     * updateSessionForword:(更新会话被转发). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionForword(SessionData data) throws Exception;
    /**
     * 
     * updateSessionManual:(更新会话进入到人工坐席). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionManual(SessionData data) throws Exception;
    /**
     * 
     * updateSessionTempSelf:(更新会话进入临时自助状态). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionTempSelf(SessionData data) throws Exception;
    /**
     * 
     * sessionOutTimeSave:(更新最后一次会话活动时间，可用于判断会话超时). <br/>
     *
     * <AUTHOR>
     * @param sessionId
     * @param session void
     * @since JDK 1.6
     */
    public void sessionOutTimeSave(String sessionId,TimeOutSession session);
    /**
     * 
     * updateSessionAgActive:(更新坐席最后一次会话活动时间). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionAgActive(SessionData data) throws Exception;
    /**
     * 
     * updateSessionUserActive:(更新用户最后一次会话活动时间). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionUserActive(SessionData data) throws Exception;
    /**
     * 
     * updateSessionUserClose:(更新会话状态为用户关闭). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionUserClose(SessionData data) throws Exception;
    /**
     * 
     * updateSessionAgClose:(更新会话为坐席关闭). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionAgClose(SessionData data) throws Exception;
    /**
     * 
     * updateSessionTimeout:(更新会话超时). <br/>
     *
     * <AUTHOR>
     * @param sessionId
     * @param tenantCode
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionTimeout(String sessionId,String tenantCode) throws Exception;

    /**
     *  更新会话
     * @param data
     * @throws Exception
     */
    public void updateSessionTimeout(SessionData data) throws Exception;
    
    /**
     * 
     * updateSessionTimeout:(更新会话状态为超时). <br/>
     *
     * <AUTHOR>
     * @param sessionInfo
     * @return
     * @throws Exception 
     * @return SessionInfo
     * @since JDK 1.6
     */
    public SessionInfo updateSessionTimeout(SessionInfo sessionInfo) throws Exception;
    /**
     * 
     * updateSessionDelay:(更新会话为延迟). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionDelay(SessionData data) throws Exception;
    /**
     * 
     * updateSessionAgFinish:(更新会话为已完结). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionFinish(SessionData data) throws Exception;
    /**
     * 
     * addMessage:(添加消息). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addMessage(SessionData data) throws Exception;
    /**
     * 
     * addWorkNo:(添加坐席工号). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addWorkNo(SessionData data) throws Exception;
    /**
     * 
     * removeWorkNo:(移除工号). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception 
     * @return void
     * @since JDK 1.6
     */
    public void removeWorkNo(SessionData data) throws Exception;
    /**
     * 
     * updateSessionRedis:(更新缓存里面数据). <br/>
     *
     * <AUTHOR>
     * @param info
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionRedis(SessionInfo info) throws Exception;
    /**
     * 
     * sessionCacheDeleteCascade:(删除会话中所有与缓存相关的数据). <br/>
     *
     * <AUTHOR>
     * @param info void
     * @since JDK 1.6
     */
    public void sessionCacheDeleteCascade(SessionInfo info);
    /**
     * 
     * updateSessionStatus:(更新会话状态). <br/>
     *
     * <AUTHOR>
     * @param data
     * @throws Exception void
     * @since JDK 1.6
     */
    public void updateSessionStatus(SessionData data) throws Exception;
    /**
     * 
     * addSessionTimeoutLog:(添加超时日志). <br/>
     *
     * <AUTHOR>
     * @param log
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addSessionTimeoutLog(SessionTimeoutLog log) throws Exception;
    /**
     * 
     * addSessionWorkNo:(添加会话工号). <br/>
     *
     * <AUTHOR>
     * @param sessionWorkNo
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addSessionWorkNo(SessionWorkNo sessionWorkNo) throws Exception;
    /**
     * 
     * addSeesionCount:(增加该坐席的会话数量). <br/>
     *
     * <AUTHOR>
     * @param tenantCode
     * @param skillQueue
     * @param workNo
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addSeesionCount(String tenantCode,String skillQueue,String workNo,String skillType) throws Exception;
    /**
     * 
     * reduceSeesionCount:(减少坐席的会话数量). <br/>
     *
     * <AUTHOR>
     * @param tenantCode
     * @param skillType
     * @param skillQueue
     * @param workNo
     * @throws Exception void
     * @since JDK 1.6
     */
    public void reduceSeesionCount(String tenantCode,String skillQueue,String workNo,String skillType) throws Exception;

    /**
     * 获取坐席当前会话数
     * @param tenantCode
     * @param skillType
     * @param skillQueue
     * @param workNo
     * @throws Exception
     */
    public int getWorkNoSessionCount(String tenantCode,String skillQueue,String workNo) throws Exception;
    /**
     * 
     * getAgSession:(根据状态获取坐席会话). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception List<AgSession>
     * @since JDK 1.6
     */
    public List<AgSession> getAgSessionAll(Map<String, Object> sessionData) throws Exception;
    /**
     * 
     * getAgSessionCount:(根据状态获取坐席会话数量). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getAgSessionCount(Map<String, Object> sessionData) throws Exception;
    /**
     * 
     * getSessionCount:(获取各个状态下的会话数量). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getSessionCount(SessionData sessionData) throws Exception;
    /**
     * 
     * getSessionSortCount:(获取发送账号对应下的排队数量). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getSessionSortCount(SessionData sessionData,FlagModel flag) throws Exception;

    /**
     * 获取当前会话实际的排队顺序
     * @param sessionData
     * @return
     * @throws Exception
     */
    public int getSessionActualSortCount(SessionData sessionData) throws Exception;
    /**
     * 
     * getSessionSortItemCount:(获取排队详细信息  技能组下 渠道下的排队数). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception 
     * @return Map<String,Object>
     * @since JDK 1.6
     */
    public List<SessionSortItem> getSessionSortItemCount(SessionData sessionData) throws Exception;
    /**
     * 
     * getSortItem:(获取详细的排队数据). <br/>
     *
     * <AUTHOR>
     * @param items
     * @return
     * @throws Exception 
     * @return Map<String,Object>
     * @since JDK 1.6
     */
    public Map<String, Object> getSortItem (List<SessionSortItem> items) throws Exception;
    /**
     * 
     * getSessionSortTime:(获取技能组排队时间). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getSessionSortTime(SessionData sessionData) throws Exception;
    /**
     * 
     * getAgSessionContent:(分状态查询会话内容). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception Map<String,Object>
     * @since JDK 1.6
     */
    public List<Map<String, Object>> getAgSessionContent(SessionData sessionData) throws Exception;
    /**
     * 
     * getSessionContent:(分状态查询会话内容 新). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception 
     * @return List<Map<String,Object>>
     * @since JDK 1.6
     */
    public List<Map<String, Object>> getSessionContent(SessionData sessionData) throws Exception;
    /**
     * 
     * getAgSession:(分状态查询工号下的会话  0，正在处理 1，已结束 2延迟待办). <br/>
     *
     * <AUTHOR>
     * @param agSession
     * @return
     * @throws Exception List<SessionInfo>
     * @since JDK 1.6
     */
    public List<SessionInfo> getAgSession(Map<String, Object> agSession) throws Exception;
    /**
     * 
     * getHistoryMessage:(获取会话历史记录). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception Map<String,Object>
     * @since JDK 1.6
     */
    public Map<String, Object> getHistoryMessage(MessageData data) ;
    /**
     * 
     * removeSessionCache:(在缓存中将会话移除). <br/>
     *
     * <AUTHOR>
     * @param sessionId
     * @return
     * @throws Exception String
     * @since JDK 1.6
     */
    public String removeSessionCache(String sessionId,String tenantCode) throws Exception;
    /**
     * 
     * getUnfinishSessionMsgs:(获取未完成会话消息内容). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception 
     * @return List<Session>
     * @since JDK 1.6
     */
    public List<Session> getUnfinishSessionMsgs(SessionData data) throws Exception;
    /**
     * 
     * unfinishSessionMsgToAg:(将消息格式转化为坐席需要格式). <br/>
     *
     * <AUTHOR>
     * @param sessions
     * @return
     * @throws Exception 
     * @return List<Map<String,Object>>
     * @since JDK 1.6
     */
    public Map<String, Object> unfinishSessionMsgToAg(List<Session> sessions) throws Exception;
    /**
     * 
     * createSessionSelf:(创建会话，并将会话状态改为自助). <br/>
     *
     * <AUTHOR>
     * @param info
     * @return
     * @throws Exception 
     * @return SessionInfo
     * @since JDK 1.6
     */
    public SessionInfo createSessionSelf(SessionInfo info) throws Exception;
    
    /**
     * 根据会话id查询最近的一条消息id
     * 
     * @param sessionId
     * @param type
     * @return
     * @throws Exception
     */
    public String getMessageId(String sessionId,String type)throws Exception;

    /**
     * 向坐席推送提醒
     * @param info 会话
     * @param type 提醒类型
     * @param contentTip 提醒内容
     */
    public void  sendAgTip(SessionInfo info,String type,String contentTip,String lastActiveUser,String sendType,String timeOutAction);

    /**
     * 获取session通过sessionKey
     * @param sessionKey
     * @return
     */
    public SessionInfo readCacheSessionBySessionKey(String sessionKey);

    /**
     * 退出排队
     * @param data
     */
    public void quitSessionSort(SessionData data) throws Exception;
    
    /**
     * 获取所有技能组工号
     * @return
     */
    public List<SkillQueueWorkNo> getSkillWorkNo();

    /**
     * 获取技能组工号的当前会话数
     * @param skillQueueWorkNo
     * @return
     */
    public int getSkillWorkNoSessionCount(SkillQueueWorkNo skillQueueWorkNo);
    
	public List<SessionInfo> queryNotClosedTask(String workno);

	public List<SessionInfo> queryNotConclusionTask(String workno);
	public Map<String, String> getPendingTask(String workNo);
	//超时日志动作记录
    public void addCheckTimeOutActiveLog(TimeOutSession tos);
}
