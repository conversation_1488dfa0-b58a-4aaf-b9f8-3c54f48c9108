package cn.sh.ideal.sm.service.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.sm.dao.SessionEventInfoDao;
import cn.sh.ideal.sm.model.SessionEventInfo;
import cn.sh.ideal.sm.service.CommonSendService;
import cn.sh.ideal.sm.service.SessionEventService;
import cn.sh.ideal.sm.utils.Constants;

/**
 * SessionEventServiceImpl
 *
 * <AUTHOR>
 * @date 2015/8/5
 */
@Service("sessionEventService")
public class SessionEventServiceImpl extends BaseServiceImpl<SessionEventInfo> implements SessionEventService {
    /**
     * The logger.
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static String KEY_SESSION_EVENT = "EVENT";
    private static String KEY_ID = "ID";
    private static String KEY_URL = "URL";
    private static String KEY_IS_IGNORE = "IS_IGNORE";
    private static String KEY_CURRENT_STATUS = "CURRENT_STATUS";
    @Autowired
    private SessionEventInfoDao sessionEventInfoDao;

    @Autowired
    private CommonSendService commonSendService;
    @Autowired
    private RedisDao<String, Serializable> redisDao;

    /**
     * 初始化
     *
     * @throws Exception
     */
   // @PostConstruct
    public void init() throws Exception {
        List<SessionEventInfo> infos = sessionEventInfoDao.getAllList(null);
        removeAllEventData();
        for (SessionEventInfo info : infos) {
            String key = getCacheKey(info.getTenantCode(), info.getSessionStatus(), info.getEvent());
            List<Serializable> infos2 = redisDao.listRangeAll(key);
            if(infos2 == null) infos2 = new ArrayList<Serializable>();
            infos2.add(info);
            redisDao.listlPush(key,infos2);
            /*logger.debug("event info key:{},mapKey:{},value:{}",key,KEY_ID,info.getId());
            redisDao.mapPut(key, KEY_ID, info.getId());
            logger.debug("event info key:{},mapKey:{},value:{}", key, KEY_URL, info.getUrl());
            redisDao.mapPut(key, KEY_URL, info.getUrl());
            logger.debug("event info key:{},mapKey:{},value:{}", key, KEY_IS_IGNORE, info.getIsIgnore());
            redisDao.mapPut(key, KEY_IS_IGNORE, info.getIsIgnore());
            logger.debug("event info key:{},mapKey:{},value:{}", key, KEY_CURRENT_STATUS, info.getCurrentStatus());
            redisDao.mapPut(key, KEY_CURRENT_STATUS, info.getCurrentStatus());*/

        }
    }
    public void removeAllEventData(){
        Set<String> keys = redisDao.getKeysByPattern(KEY_SESSION_EVENT + "*");
        for(String key : keys){
            redisDao.deleteValue(key);
        }
    }


    /**
     * 获取事件详情
     *
     * @param tenantCode
     * @param sessionStatus
     * @return
     */
   /* public SessionEventInfo get(String tenantCode, String sessionStatus, String event) {
        String key = getCacheKey(tenantCode, sessionStatus, event);
        Object o = redisDao.mapGet(key);
        if (o != null) {
            Map<String, String> map = (Map<String, String>) o;
            if(map.size() == 0)  return null;
            String url = map.get(KEY_URL);
            String isIgnore = map.get(KEY_IS_IGNORE);
            String id = map.get(KEY_ID);
            String currentStatus = map.get(KEY_CURRENT_STATUS);
            SessionEventInfo info = new SessionEventInfo(id, tenantCode, event, sessionStatus, url, isIgnore, currentStatus);
            return info;
        }
        return null;
    }*/

    /**
     * 获取事件详情列表
     * @param tenantCode
     * @param sessionStatus
     * @param event
     * @return
     */
    public List<Serializable> getList(String tenantCode, String sessionStatus, String event) {
        String key = getCacheKey(tenantCode, sessionStatus, event);
        List<Serializable> infos2 = redisDao.listRangeAll(key);
        return infos2;

    }


    /**
     * 通过租户和状态返回缓存key
     *
     * @param tenantCode
     * @param sessionStatus
     * @return
     */
    public String getCacheKey(String tenantCode, String sessionStatus, String event) {
        return KEY_SESSION_EVENT + ":" + tenantCode + ":" + sessionStatus + ":" + event;

    }

    @Override
    public void doEvent(SessionData data,SessionInfo info, SessionEventInfo.SessionEvent event) throws Exception {
        //SessionInfo info = sessionInfoService.getSession(data);
        if (info != null) {
            String status = data.getStatus();
            if(StringUtils.isEmpty(status)) {
               if(data.getData() != null && data.getData().get("status") != null){
                   status = (String)data.getData().get("status");
               }else{
                   status = SessionStatus.SELF.getCode();
               }
            }
           // SessionEventInfo eventInfo = get(info.getTenantCode(), data.getStatus(), event.getName());
            List<Serializable> infos = getList(info.getTenantCode(), data.getStatus(), event.getName());
            for(Serializable value : infos){
                SessionEventInfo eventInfo = (SessionEventInfo) value;
                if (eventInfo != null) {
                    logger.info("event Status sessionId:{},status:{},url:{},isIgnore:{}",data.getSessionId(),data.getStatus(),eventInfo.getUrl(),eventInfo.getIsIgnore());
                    String isIgnore = eventInfo.getIsIgnore();
                    String currentStatus = eventInfo.getCurrentStatus();
                    if(StringUtils.isEmpty(currentStatus) || Arrays.asList(currentStatus.split(",")).contains(info.getStatus()))
                        //如果忽略执行结果，采用异步调用的方式，忽略调用第三方的执行结果
                        if (Constants.YES.equals(isIgnore)) {
                            ((SessionEventService) AopContext.currentProxy()).doSyncUrl(eventInfo, info);
                        } else {
                            doUrl(eventInfo, info);
                        }

                }
            }

        }


    }

    @Async
    public void doSyncUrl(SessionEventInfo eventInfo, SessionInfo sessionInfo) {
        try {
            doUrl(eventInfo, sessionInfo);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    public void doUrl(SessionEventInfo eventInfo, SessionInfo sessionInfo) throws Exception {
        String url = eventInfo.getUrl();
        String json = JSON.toJSONString(sessionInfo);
        String result = commonSendService.sendMsg(url, json, CommonSendService.PARAMTYPE.POST);
        if (StringUtils.isNotEmpty(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            String resultCode = jsonObject.getString("resultCode");
            if (!Constants.RESPONSE_OK.equals(resultCode)) {
                throw new Exception("statusEventError:result code is error! url:{" + url + "}");
            }

        } else {
            throw new Exception("statusEventError:result msg is null! url:{" + url + "}");
        }


    }


}
