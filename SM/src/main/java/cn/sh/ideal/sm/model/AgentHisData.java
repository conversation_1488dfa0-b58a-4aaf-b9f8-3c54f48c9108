package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.List;

// TODO: Auto-generated Javadoc
/**
 * The Class HisMessage.
 */
public class AgentHisData implements Serializable{

	/** The Constant serialVersionUID. */
	private static final long serialVersionUID = 1L;
	
	/**  工号. */
	private String workNo;
	
	private Integer sessionNum; 
	
	/** 技能组分类数量 */
	private List<SkillTypeNum> skillNumList;
	
	/**主叫/被叫分类数量 */
	private List<CallStatuNum> callNumList;
	
	/**问题*/
	private String qesDesc;
	
	/**处理*/
	private String dealScheme;

	public String getWorkNo() {
		return workNo;
	}

	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	public List<SkillTypeNum> getSkillNumList() {
		return skillNumList;
	}

	public void setSkillNumList(List<SkillTypeNum> skillNumList) {
		this.skillNumList = skillNumList;
	}

	public List<CallStatuNum> getCallNumList() {
		return callNumList;
	}

	public void setCallNumList(List<CallStatuNum> callNumList) {
		this.callNumList = callNumList;
	}

	public String getQesDesc() {
		return qesDesc;
	}

	public void setQesDesc(String qesDesc) {
		this.qesDesc = qesDesc;
	}

	public String getDealScheme() {
		return dealScheme;
	}

	public void setDealScheme(String dealScheme) {
		this.dealScheme = dealScheme;
	}

	public Integer getSessionNum() {
		return sessionNum;
	}

	public void setSessionNum(Integer sessionNum) {
		this.sessionNum = sessionNum;
	}
	
}
