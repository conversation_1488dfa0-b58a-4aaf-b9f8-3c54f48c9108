/**
 * Project Name:SM Maven Webapp
 * File Name:BaseService2.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2015年4月27日下午3:10:41
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import cn.sh.ideal.model.BaseModel;
import cn.sh.ideal.sm.dao.BaseDao;
import cn.sh.ideal.sm.service.BaseService;


/**
 * ClassName:BaseService2 <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2015年4月27日 下午3:10:41 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public abstract class BaseServiceImpl<T extends BaseModel> implements BaseService<T>{
    @Autowired
    private BaseDao<T> baseDao;
    /**
     * 增加
     * @param entity 封装数据的实体
     * @return 
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void add(T entity) throws Exception{
         baseDao.add(entity);
    };
    
    /**
     * 修改
     * @param entity 封装数据的实体
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void edit(T entity) throws Exception{
         baseDao.edit(entity);
    };

    /**
     * 删除
     * @param entity 封装数据的实体
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void delete(T entity) throws Exception{
        baseDao.delete(entity);
    };
    
    /**
     * 删除
     * @param entity 封装数据的实体
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void deleteById(String id) throws Exception{
        baseDao.deleteById(id);
    };
    
    
    /**
     * 批量删除
     * @param list 要删除的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchDelete(List<String> list) throws Exception{
        baseDao.batchDelete(list);
    };
    
    
    /**
     * 批量增加
     * @param list 要增加的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchAdd(List<T> list) throws Exception{
        baseDao.batchAdd(list);
    };

    /**
     * 以id为条件查找对象
     * @param entity 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public T get(T entity) throws Exception{
        return baseDao.get(entity);
    };
    /**
     * 查询
     * @param entity 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public List<T> getAllList(T entity) throws Exception{
        return baseDao.getAllList(entity);
    };
    /**
     * 查询数量
     * @param entity 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public int getCount(T entity) throws Exception{
        return baseDao.getCount(entity);
    };   
    /**
     * 批量删除
     * @param list 要删除的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchDeleteT(List<T> list) throws Exception{
        baseDao.batchDeleteT(list);
    };

    /**
     * 获取单个值 如sum、count等
     * @param entity 查询条件实体
     * @return
     * @throws Exception
     */
    public String getOneResult(T entity) throws Exception{
        return baseDao.getOneResult(entity);
    };
    
    /**
     * 获取map集合
     * @param obj 查询条件map
     * @return
     * @throws Exception
     */
    public Map<String, Object> getMap(Map<String, Object> obj) throws Exception{
        return baseDao.getMap(obj);
    };
    
    /**
     * 查询 -单个String 查询所有 如in ids 
     * @param params 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public List<T> getAllListByIds(List<String> params) throws Exception{
        return baseDao.getAllListByIds(params);
    };
    
    /**
     * 获取对象 
     * @param params String
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     *//*
    public T get(String param) throws Exception{
        return baseDao2.get(param);
    };
*/    
    /**
     * 批量更新
     * @param Map 要更新的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchEdit(Map<String,Object> maps) throws Exception{
        baseDao.batchEdit(maps);
    };

}
