/**
 * 
 */
package cn.sh.ideal.sm.dao;

import cn.sh.ideal.sm.model.TbComment;

/**
 * @project SM
 * @Package cn.sh.ideal.sm.dao
 * @typeName TbCommentDao
 * <AUTHOR>
 * @Description:  
 * @date 2016年7月26日 下午5:52:21
 * @version 
 */
public interface TbCommentDao {

	TbComment queryCommentById(String string);

	void insert(TbComment comment);

	/** @param comment 
	 * @Description 
	 */
	int isExist(TbComment comment);

}
