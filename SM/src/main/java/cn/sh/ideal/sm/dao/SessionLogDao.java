/**
 * Project Name:SM Maven Webapp
 * File Name:SessionLogDao.java
 * Package Name:cn.sh.ideal.mir.session.dao
 * Date:2014年12月20日下午8:01:27
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.dao;

import cn.sh.ideal.sm.model.SessionStatusLog;

/**
 * ClassName:SessionLogDao <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2014年12月20日 下午8:01:27 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface SessionLogDao extends BaseDao<SessionStatusLog>{

}
