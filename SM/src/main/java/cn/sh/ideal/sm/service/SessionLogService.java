/**
 * Project Name:SM Maven Webapp
 * File Name:SessionLogService.java
 * Package Name:cn.sh.ideal.mir.session.service
 * Date:2014年12月20日下午8:03:26
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service;

import cn.sh.ideal.sm.model.SessionStatusLog;

/**
 * ClassName:SessionLogService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2014年12月20日 下午8:03:26 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface SessionLogService extends BaseService<SessionStatusLog>{

}
