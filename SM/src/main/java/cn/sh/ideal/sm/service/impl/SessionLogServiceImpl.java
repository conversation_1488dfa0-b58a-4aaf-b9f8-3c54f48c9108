/**
 * Project Name:SM Maven Webapp
 * File Name:SessionLogServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2014年12月20日下午8:04:07
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service.impl;

import org.springframework.stereotype.Service;

import cn.sh.ideal.sm.model.SessionStatusLog;
import cn.sh.ideal.sm.service.SessionLogService;

/**
 * ClassName:SessionLogServiceImpl <br/>
 * Function: 日志添加service
 * Date:     2014年12月20日 下午8:04:07 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Service("sessionLogService")
public class SessionLogServiceImpl extends BaseServiceImpl<SessionStatusLog> implements SessionLogService{

}
