/**
 * Project Name:SM Maven Webapp
 * File Name:SessionStatusLog.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月20日下午4:04:30
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.Date;

import cn.sh.ideal.model.BaseModel;

/**
 * ClassName:SessionStatusLog <br/>
 * Function: 会话更新日志表 
 * Date:     2014年12月20日 下午4:04:30 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionStatusLog extends BaseModel implements Serializable{
    private static final long serialVersionUID = 82537164088082744L;
    
    /*主键*/
    private String id;
    
    /*会话ID*/
    private String sessionId;
    
    /*租户CODE*/
    private String tenantCode;
    
    /*会话状态*/
    private String sessionStatus;
    
    /*会话标识*/
    private String sessionSymbol;
    
    /*技能队列*/
    private String skillQueue;
    
    /*业务类型*/
    private String businessType;
    
    /*工号*/
    private String workNo;
    
    /*渠道code*/
    private String channelCode;
    
    /*发送账号*/
    private String sendAccount;
    
    /*接收账号*/
    private String acceptedAccount;
    
    /*超时/延时时间*/
    private Date extTime;
    
    /*创建时间*/
    private Date createTime;
    
    private String changeStatus;
    
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    public String getSessionStatus() {
        return sessionStatus;
    }
    public void setSessionStatus(String sessionStatus) {
        this.sessionStatus = sessionStatus;
    }
    public String getSessionSymbol() {
        return sessionSymbol;
    }
    public void setSessionSymbol(String sessionSymbol) {
        this.sessionSymbol = sessionSymbol;
    }
    public String getSkillQueue() {
        return skillQueue;
    }
    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }
    public String getBusinessType() {
        return businessType;
    }
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getWorkNo() {
        return workNo;
    }
    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }
    public String getChannelCode() {
        return channelCode;
    }
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
    public String getSendAccount() {
        return sendAccount;
    }
    public void setSendAccount(String sendAccount) {
        this.sendAccount = sendAccount;
    }
    public String getAcceptedAccount() {
        return acceptedAccount;
    }
    public void setAcceptedAccount(String acceptedAccount) {
        this.acceptedAccount = acceptedAccount;
    }
    public Date getExtTime() {
        return extTime;
    }
    public void setExtTime(Date extTime) {
        this.extTime = extTime;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getChangeStatus() {
        return changeStatus;
    }
    public void setChangeStatus(String changeStatus) {
        this.changeStatus = changeStatus;
    }
    
    
    
    

}
