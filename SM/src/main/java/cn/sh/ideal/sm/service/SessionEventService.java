package cn.sh.ideal.sm.service;

import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.sm.model.SessionEventInfo;

/**
 * SessionEventService
 * 会话状态事件触发service
 * <AUTHOR>
 * @date 2015/8/5
 */
public interface SessionEventService extends BaseService<SessionEventInfo> {
    /**
     * 初始化系统配置
     */
    public void init() throws Exception;
    /**
     * 状态改变触发业务逻辑实现 （分异步调用和同步两种，根据用户配置是否忽略执行结果而定）
     * @param data
     * @throws Exception
     */
    public void doEvent(SessionData data,SessionInfo info,SessionEventInfo.SessionEvent event) throws Exception;

    /**
     * 异步调用url
     * @param eventInfo
     * @param sessionInfo
     */
    public void doSyncUrl(SessionEventInfo eventInfo, SessionInfo sessionInfo);

}
