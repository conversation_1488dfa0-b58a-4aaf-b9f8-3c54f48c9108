/**
 * Project Name:MIR Maven Webapp
 * File Name:SessionInfoDao.java
 * Package Name:cn.sh.ideal.mir.session.dao
 * Date:2014年12月8日下午1:21:07
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.dao;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.sh.ideal.model.Session;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.sm.model.AgSession;
import cn.sh.ideal.sm.model.HisMessage;
import cn.sh.ideal.sm.model.MessageData;
import cn.sh.ideal.sm.model.MessageModel;
import cn.sh.ideal.sm.model.SessionTimeoutLog;
import cn.sh.ideal.sm.model.SessionWorkNo;
import cn.sh.ideal.sm.model.SkillQueueWorkNo;
import cn.sh.ideal.sm.req.ReportData;
import cn.sh.ideal.sm.req.SatisfyData;
import cn.sh.ideal.sm.req.TenantData;
import cn.sh.ideal.sm.resp.AgentStatistics;
import cn.sh.ideal.sm.resp.SessionSortItem;

/**
 * ClassName:SessionInfoDao <br/>
 * Function: 会话管理DAO
 * Date:     2014年12月8日 下午1:21:07 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface SessionInfoDao extends BaseDao<SessionInfo>{
    /**
     * 
     * getUnfinishSession:(获取未结束会话). <br/>
     *
     * <AUTHOR>
     * @param sessionInfo
     * @return
     * @throws Exception 
     * @return SessionInfo
     * @since JDK 1.6
     */
    public List<SessionInfo> getUnfinishSession(SessionInfo sessionInfo) throws Exception;
    /**
     * 
     * getSessionSkillQueue:(获取会话的技能组). <br/>
     *
     * <AUTHOR>
     * @param sessionInfo
     * @return
     * @throws Exception 
     * @return String
     * @since JDK 1.6
     */
    public String getSessionSkillQueue(SessionInfo sessionInfo) throws Exception;
    /**
     * 
     * getSynergySession:(获取协同会话的id). <br/>
     *
     * <AUTHOR>
     * @param synergyAccount
     * @return
     * @throws Exception String
     * @since JDK 1.6
     */
    public String getSynergySession(String synergyAccount) throws Exception;
    /**
     * 
     * addSessionTimeoutLog:(添加超时会话日志). <br/>
     *
     * <AUTHOR>
     * @param log
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addSessionTimeoutLog(SessionTimeoutLog log) throws Exception;
    /**
     * 
     * addSessionWorkNo:(添加会话工号). <br/>
     *
     * <AUTHOR>
     * @param sessionWorkNo
     * @throws Exception void
     * @since JDK 1.6
     */
    public void addSessionWorkNo(SessionWorkNo sessionWorkNo) throws Exception;
    /**
     * 
     * getSessionWorkNoCount:(获取会话工号数量). <br/>
     *
     * <AUTHOR>
     * @param sessionWorkNo
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getSessionWorkNoCount(SessionWorkNo sessionWorkNo) throws Exception;
    /**
     * 
     * getAgSession:(根据状态获取坐席会话). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception List<AgSession>
     * @since JDK 1.6
     */
    public List<AgSession> getAgSessionAll(Map<String, Object> sessionData) throws Exception;
    /**
     * 
     * getAgSessionCount:(根据状态获取坐席会话数量). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getAgSessionCount(Map<String, Object> sessionData) throws Exception;
    /**
     * 
     * getSessionCount:(获取各个状态下的会话数量). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getSessionCount(SessionData sessionData) throws Exception;
    /**
     * 
     * getSessionSortItemCount:(获取技能组下渠道下的排队数). <br/>
     *
     * <AUTHOR>
     * @param sessionData
     * @return
     * @throws Exception 
     * @return int
     * @since JDK 1.6
     */
    public List<SessionSortItem> getSessionSortItemCount(SessionData sessionData) throws Exception;
    /**
     * 
     * getAgSession:(这里用一句话描述这个方法的作用). <br/>
     *
     * <AUTHOR>
     * @param agSession
     * @return
     * @throws Exception List<SessionInfo>
     * @since JDK 1.6
     */
    public List<SessionInfo> getAgSession(Map<String, Object> agSession) throws Exception;
    /**
     * 
     * getMessageCount:(获取消息数量). <br/>
     *
     * <AUTHOR>
     * @param map
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getMessageCount(Map<String, Object> map) throws Exception;
    /**
     * 
     * getMessage:(获取会话下的一条消息). <br/>
     *
     * <AUTHOR>
     * @param map
     * @return
     * @throws Exception MessageData
     * @since JDK 1.6
     */
    public MessageModel getMessage(Map<String, Object> map) throws Exception;
    
    /**
     * 
     * getHistoryMsgs:(获取历史会话内容). <br/>
     *
     * <AUTHOR>
     * @param map
     * @return
     * @throws Exception List<MessageModel>
     * @since JDK 1.6
     */
    public List<HisMessage> getHistoryMsgs(MessageData data) throws Exception;
    /**
     * 
     * getHistoryMsgCount:(获取历史会话数量). <br/>
     *
     * <AUTHOR>
     * @param map
     * @return
     * @throws Exception int
     * @since JDK 1.6
     */
    public int getHistoryMsgCount(MessageData data) throws Exception;
    /**
     * 
     * deleteBySessionId:(根据sessionId 删除一个会话). <br/>
     *
     * <AUTHOR>
     * @param sessionId
     * @throws Exception void
     * @since JDK 1.6
     */
    public void deleteBySessionId(String sessionId) throws Exception;
    
    public List<Session> getSessionMsgs(Map<String, Object> session) throws Exception;
    
    public String getMessageId(String sql,String sessionId)throws Exception;
    /**
     * 
     * getSessionList:(分状态获取会话列表). <br/>
     *
     * <AUTHOR>
     * @param session
     * @return
     * @throws Exception 
     * @return List<SessionInfo>
     * @since JDK 1.6
     */
    public List<SessionInfo> getSessionList(Map<String, Object> session) throws Exception;
    /**
     * 
     * getSessionListCount:(获取会话总数). <br/>
     *
     * <AUTHOR>
     * @param session
     * @return
     * @throws Exception 
     * @return int
     * @since JDK 1.6
     */
    public int getSessionListCount(Map<String, Object> session) throws Exception;
    /**
     * 
     * deleteSessionWorkNo:(通过工号和会话id删除会话). <br/>
     *
     * <AUTHOR>
     * @param session
     * @throws Exception 
     * @return void
     * @since JDK 1.6
     */
    public void deleteSessionWorkNo(Map<String, Object> session) throws Exception;
    
    public int insertSatified(SatisfyData data)throws Exception;
    
    public List<HashMap<String,Object>> querySkillWaitAvgTime(TenantData data);
    
    public List<HashMap<String,Object>> queryChannelServiceCount(String tenant)throws Exception;
    
	/**
	 * 服务峰谷分析报表
	 * @param reportData
	 * @return
	 */
	public List<HashMap<String, Object>> queryServicePeak(ReportData reportData);
	
	public List<Object> queryAgentCount(ReportData reportData);
    
	public List<SessionInfo> getMutualSession(SessionInfo info);

    /**
     * 获取所有技能组工号
     * @return
     */
    public List<SkillQueueWorkNo> getSkillWorkNo();
    /**
     * 获取技能组工号的当前会话数
     * @param skillQueueWorkNo
     * @return
     */
    public int getSkillWorkNoSessionCount(SkillQueueWorkNo skillQueueWorkNo);
	public List<SessionInfo> queryNotClosedTask(String workno);
	public List<SessionInfo> queryNotConclusionTask(String workno);
	public Map<String, String> getPendingTask(String workNo);
    
    /**
     * 查询个人绩效统计list
     * @param as
     * @return
     */
    public List<AgentStatistics> queryAgentStatistics(AgentStatistics as);
    
    public List<AgentStatistics> querySatisfied(AgentStatistics as);
    
    public List<AgentStatistics> queryServiceTime(AgentStatistics as);
    
    public List<AgentStatistics> queryAgentResponse(AgentStatistics as);
    
    public Date getSysTime();
    
    public int diffTime(String sessionId);
    
}
