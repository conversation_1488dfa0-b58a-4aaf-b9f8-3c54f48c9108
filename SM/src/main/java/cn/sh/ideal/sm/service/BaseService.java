/**
 * Project Name:SM Maven Webapp
 * File Name:BaseService2.java
 * Package Name:cn.sh.ideal.mir.session.service
 * Date:2015年4月27日下午3:24:33
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service;

import java.util.List;
import java.util.Map;

/**
 * ClassName:BaseService <br/>
 * Function: Service基类. <br/>
 * Date:     2015年4月27日 下午3:24:33 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface BaseService<T> {
    /**
     * 增加
     * @param entity 封装数据的实体
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void add(T entity) throws Exception;
    
    /**
     * 修改
     * @param entity 封装数据的实体
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void edit(T entity) throws Exception;

    /**
     * 删除
     * @param entity 封装数据的实体
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void delete(T entity) throws Exception;
    
    public void deleteById(String id) throws Exception;
    
    /**
     * 批量删除
     * @param list 要删除的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchDelete(List<String> list) throws Exception;
    
    
    /**
     * 批量增加
     * @param list 要增加的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchAdd(List<T> list) throws Exception;

    /**
     * 以id为条件查找对象
     * @param entity 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public T get(T entity) throws Exception;
    /**
     * 查询
     * @param entity 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public List<T> getAllList(T entity) throws Exception;
    /**
     * 查询数量
     * @param entity 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public int getCount(T entity) throws Exception;   
    /**
     * 批量删除
     * @param list 要删除的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchDeleteT(List<T> list) throws Exception;

    /**
     * 获取单个值 如sum、count等
     * @param entity 查询条件实体
     * @return
     * @throws Exception
     */
    public String getOneResult(T entity) throws Exception;
    
    /**
     * 获取map集合
     * @param obj 查询条件map
     * @return
     * @throws Exception
     */
    public Map<String, Object> getMap(Map<String, Object> obj) throws Exception;
    
    /**
     * 查询 -单个String 查询所有 如in ids 
     * @param params 封装数据的实体
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
    public List<T> getAllListByIds(List<String> params) throws Exception;
    
    /**
     * 获取对象 
     * @param params String
     * @return 返回查询结果
     * @throws Exception 抛出所有异常
     */
   /* public T get(String param) throws Exception;*/
    
    /**
     * 批量更新
     * @param Map 要更新的数据集合
     * @return 返回操作结果
     * @throws Exception 抛出所有异常
     */
    public void batchEdit(Map<String,Object> maps) throws Exception;

}
