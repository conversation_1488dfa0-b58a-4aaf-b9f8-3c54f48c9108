/**
 * Project Name:SM Maven Webapp
 * File Name:MessageInfoService.java
 * Package Name:cn.sh.ideal.mir.session.service
 * Date:2015年3月9日上午10:28:16
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionData;

/**
 * ClassName:MessageInfoService <br/>
 * Function: 新的消息Service
 * Date:     2015年3月9日 上午10:28:16 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface MessageInfoService extends BaseService<MessageInfo>{
    /**
     * 
     * getHistoryMsgs:(获取历史会话service新). <br/>
     *
     * <AUTHOR>
     * @param info
     * @return 
     * @return Map<String,Object>
     * @since JDK 1.6
     */
    public Map<String, Object> getHistoryMsgs(MessageInfo info) throws Exception;
    /**
     * 
     * @param getLastHistoryMegsData(获取上一次的消息数据)
     * @return
     * @throws Exception
     */
    public List<MessageInfo> getLastHistoryMegsData(Map<String, Object> mapHistoryMsgs,Integer lastSize) throws Exception;
    /**
     * 
     * getLastMessage:(获取最新一条会话下的消息). <br/>
     *
     * <AUTHOR>
     * @param map
     * @return
     * @throws Exception 
     * @return MessageInfo
     * @since JDK 1.6
     */
   
   public MessageInfo getLastMessage(Map<String, Object> map) throws Exception;
    /**
     * 
     * getUnfinishSessionMsgs:(查询坐席未完成会话消息内容). <br/>
     *
     * <AUTHOR>
     * @param data
     * @return
     * @throws Exception 
     * @return Map<String,List<MessageInfo>>
     * @since JDK 1.6
     */
    public Map<String, List<MessageInfo>> getUnfinishSessionMsgs(SessionData data) throws Exception;
    /**
     * 获取上一次会话ID
     * @return
     * @throws Exception
     */
    public String getLastSessionId(MessageInfo lastMesInfo) throws Exception;
}
