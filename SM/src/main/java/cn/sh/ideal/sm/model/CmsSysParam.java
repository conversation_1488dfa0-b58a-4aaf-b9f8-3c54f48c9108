/**
 * Project Name:SM Maven Webapp
 * File Name:CmsSysParam.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2015年2月3日下午2:04:37
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.io.Serializable;

import cn.sh.ideal.model.BaseModel;

/**
 * ClassName:CmsSysParam <br/>
 * Function: 系统参数实体类
 * Date:     2015年2月3日 下午2:04:37 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class CmsSysParam extends BaseModel implements Serializable{

    private static final long serialVersionUID = 2999747266062035107L;
    /*主键*/
    private String autoId;
    /*参数code*/
    private String paramCode;
    /*参数值*/
    private String paramValue;
    public String getAutoId() {
        return autoId;
    }
    public void setAutoId(String autoId) {
        this.autoId = autoId;
    }
    public String getParamCode() {
        return paramCode;
    }
    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }
    public String getParamValue() {
        return paramValue;
    }
    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }
    
    public CmsSysParam() {
        super();
    }
    public CmsSysParam(String paramCode) {
        super();
        this.paramCode = paramCode;
    }
    
    
    
    

}
