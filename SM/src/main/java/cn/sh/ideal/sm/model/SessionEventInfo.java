package cn.sh.ideal.sm.model;

import java.io.Serializable;

import cn.sh.ideal.model.BaseModel;

/**
 * SessionEventInfo
 * 状态事件触发model
 *
 * <AUTHOR>
 * @date 2015/8/5
 */
public class SessionEventInfo extends BaseModel implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/*主键*/
    private String id;
    /*租户code*/
    private String tenantCode;
    /*触发时间*/
    private String event;
    /*会话状态*/
    private String sessionStatus;
    /*请求地址*/
    private String url;
    /*是否忽略执行结果*/
    private String isIgnore;
    /*当前会话状态 用以限制当前状态下执行配置触发地址*/
    private String currentStatus;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getSessionStatus() {
        return sessionStatus;
    }

    public void setSessionStatus(String sessionStatus) {
        this.sessionStatus = sessionStatus;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIsIgnore() {
        return isIgnore;
    }

    public void setIsIgnore(String isIgnore) {
        this.isIgnore = isIgnore;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public SessionEventInfo() {
    }

    public SessionEventInfo(String id, String currentStatus, String isIgnore, String url, String sessionStatus, String event, String tenantCode) {
        this.id = id;
        this.currentStatus = currentStatus;
        this.isIgnore = isIgnore;
        this.url = url;
        this.sessionStatus = sessionStatus;
        this.event = event;
        this.tenantCode = tenantCode;
    }


    public enum SessionEvent {
        BEFORE, AFTER;
        public String getName(){
            return this.name();
        }
    }
}
