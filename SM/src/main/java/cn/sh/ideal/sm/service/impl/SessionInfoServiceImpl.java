/**
 * b * Project Name:MIR Maven Webapp
 * File Name:SessionInfoService.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2014年12月8日上午10:03:58
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 */

package cn.sh.ideal.sm.service.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import cn.sh.ideal.as.req.TransferRequest;
import cn.sh.ideal.as.service.AgentService;
import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.mir.req.RemoveQueueSessionRequest;
import cn.sh.ideal.mir.resp.RemoveQueueSessionResponse;
import cn.sh.ideal.mir.service.AllocateService;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.ChannelConfig;
import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.Session;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionInfo.ChangeStatus;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.model.SessionTip;
import cn.sh.ideal.model.SessionType;
import cn.sh.ideal.model.SkillType;
import cn.sh.ideal.model.SysParam;
import cn.sh.ideal.model.TimeOutSession;
import cn.sh.ideal.sm.dao.JdCommentDao;
import cn.sh.ideal.sm.dao.MessageInfoDao;
import cn.sh.ideal.sm.dao.SessionInfoDao;
import cn.sh.ideal.sm.dao.SessionTimeoutLogDao;
import cn.sh.ideal.sm.dao.TbCommentDao;
import cn.sh.ideal.sm.exception.SessionOperationException;
import cn.sh.ideal.sm.model.AgSession;
import cn.sh.ideal.sm.model.HisMessage;
import cn.sh.ideal.sm.model.JdComment;
import cn.sh.ideal.sm.model.MessageData;
import cn.sh.ideal.sm.model.MessageModel;
import cn.sh.ideal.sm.model.SessionTimeout;
import cn.sh.ideal.sm.model.SessionTimeoutLog;
import cn.sh.ideal.sm.model.SessionWorkNo;
import cn.sh.ideal.sm.model.SkillQueueWorkNo;
import cn.sh.ideal.sm.model.TbComment;
import cn.sh.ideal.sm.resp.FlagModel;
import cn.sh.ideal.sm.resp.SessionSortItem;
import cn.sh.ideal.sm.service.CommonSendService;
import cn.sh.ideal.sm.service.CommonSendService.PARAMTYPE;
import cn.sh.ideal.sm.service.MessageInfoService;
import cn.sh.ideal.sm.service.SessionEventService;
import cn.sh.ideal.sm.service.SessionInfoService;
import cn.sh.ideal.sm.service.SessionLogService;
import cn.sh.ideal.sm.service.SessionTimeoutService;
import cn.sh.ideal.sm.service.SessionTipService;
import cn.sh.ideal.sm.task.impl.TaskServiceImpl.TipStatus;
import cn.sh.ideal.sm.utils.AgSessionStatus;
import cn.sh.ideal.sm.utils.Constants;
import cn.sh.ideal.sm.utils.Utils;
import cn.sh.ideal.util.RedisLock;
import cn.sh.ideal.util.TipTypes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * ClassName:SessionInfoService <br/>
 * Function: 会话信息service <br/>
 * Date: 2014年12月8日 上午10:03:58 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.6
 */
@Service("sessionInfoService")
public class SessionInfoServiceImpl extends BaseServiceImpl<SessionInfo>
		implements SessionInfoService {
	private static final Logger logger = LoggerFactory
			.getLogger(SessionInfoServiceImpl.class);
	@Resource(name = "redisDao")
	private RedisDao<String, Serializable> redisDao;
	@Resource
	private SessionInfoDao sessionInfoDao;
	@Autowired
	private SessionTimeoutLogDao sessionTimeoutLogDao;
	@Autowired
	private MessageInfoDao messageInfoDao;
	@Autowired
	private JdCommentDao jdCommentDao;
	@Autowired
	private TbCommentDao tbCommentDao;
	@Autowired
	private SessionLogService sessionLogService;
	@Autowired
	private CommonSendService commonSendService;
	@Autowired
	private SessionTimeoutService sessionTimeoutService;
	@Autowired
	private SessionTipService sessionTipService;
	@Autowired
	private SessionEventService sessionEventService;
	@Autowired
	private ApplicationContext applicationContext;
	// @Autowired
	// private CmsSysParamService cmsSysParamService;
	@Autowired
	private SysInitService sysInitService;
	@Autowired
	private MessageInfoService messageInfoService;
	@Autowired
	private AgentService agentService;
	private final static String ZSET = "ZSET";
	private final static String LIST = "LIST";
//	@Value("#{config['session.isSortTimeout']}")
//	private String isSortTimeout;
//	@Value("#{config['session.sortTimeout']}")
//	private int sortTimeout;


//	@Value("#{config['session.isSortTimeoutSystem']}")
//	private String isSortTimeoutSystem;
//	@Value("#{config['session.sortTimeoutSystem']}")
//	private int sortTimeoutSystem;

	@Value("#{config['pushAgMsgUrl']}")
	private String pushAgMsgUrl;

	@Value("#{config['quitSessionSort']}")
	private String quitSessionSort = "";

	@Autowired
	private AllocateService allocateService;

	/**
	 * 会话超时提醒时间
	 */
	private int agSessionTimeout = 3600000;

	private int userSessionTimeout = 3600000;

//	private String userCloseSessionTip = "用户已主动退出人工，请关闭会话 ！";
//
//	private String agCloseSessionTip = "坐席已关闭会话，您已退出人工服务，很高兴为您服务。";
	@Value("#{config['mirDecreaseSessionCountUrl']}")
	private String decreaseSessionCountUrl;

	@Value("#{config['selfTimeout']}")
	private int selfTimeout = 60000;

	@Value("#{config['expireKey']}")
	private String expirekey;

	@Value("#{config['needReloadSession']}")
	private String needReloadSession;


	private String[] manualStatus = { SessionStatus.MANUAL.getCode(),
			SessionStatus.FORWORD.getCode(), SessionStatus.TEMP_SELF.getCode() };

	/**
	 * 初始化操作
	 */
	@PostConstruct
	public void init() throws Exception {
		// 初始化系统参数表
		//sysInitService.init(true, true, false, true, true);
		sessionEventService.init();
		sessionTimeoutService.init();
		sessionTipService.init();

		// 对所有的会话解锁，防止重启后未解锁导致加锁死循环
		unLockAllSession();

	}

	/**
	 * 删除所有会话相关的锁
	 */
	public void unLockAllSession() {
		Set<String> lockKeys = redisDao.getKeysByPattern("lock_SESSION_*");
		for (String lockKey : lockKeys) {
			redisDao.deleteValue(lockKey);
		}
		redisDao.deleteValue("lock_" + Constants.TIMEOUT_EXE_FALG);
	}

	/*
	 * private String[] channelCodes = { Channel.WX.getCode(),
	 * Channel.YX.getCode(), Channel.WEBCHAT.getCode() };
	 */

	@Override
	public void cacheData(String key, Serializable value, int timeout,
			TimeUnit timeUnit) {
		redisDao.saveValue(key, value, timeout, timeUnit);

	}

	@Override
	public SessionInfo createSession(SessionData data) throws Exception {
		String sessionModel = data.getSessionModel();
		if (StringUtils.isEmpty(sessionModel))
			return null;
		RedisLock lock = RedisLock.getRedisLock(sessionModel);
		while (true) {
			if (lock.lock()) {
				try {
					/* 从数据库还原 */
					SessionInfo sessionInfo;
					sessionInfo = getSession(data);
					if (sessionInfo != null) {
						logger.error(
								"createSession Failure sessionData:[{}],exist SessionInfo:[{}]",
								data, sessionInfo);
						return sessionInfo;
					}
					String messageId = data.getMessageId();
					String tenantCode = data.getTenantCode();
					String nickname = data.getNickname();
					String sessionKey = sessionModel;
					String businessType = data.getBusinessType();
					String skillQueue = data.getSkillQueue();
					String skillType = StringUtils.isEmpty(data.getSkillType()) ? "1"
							: data.getSkillType();

					String workNo = data.getWorkNo();
					/* 数据库不存在，直接创建 */
					Date date = new Date();
					String sessionId = sessionModel + date.getTime();
					sessionInfo = new SessionInfo();
					sessionInfo.setSessionId(sessionId);
					sessionInfo.setSessionModel(sessionModel);
					sessionInfo.setStatus(SessionStatus.SELF.getCode());
					sessionInfo.setStatusSymbol(SessionStatus.SELF.name());
					sessionInfo.setCreateTime(date);
					sessionInfo.setOptTime(date);
					sessionInfo.setCallStatus(data.getCallStatus());
					sessionInfo.setSendAccount(data.getSendAccount());
					sessionInfo.setAcceptedAccount(data.getAcceptedAccount());
					sessionInfo.setChannelCode(data.getChannelCode());
					if (StringUtils.isNotEmpty(messageId)) {
						sessionInfo.addMessageId(messageId);
					}
					sessionInfo.setTenantCode(tenantCode);
					sessionInfo.setStartTime(date);
					sessionInfo.setEndTime(date);
					sessionInfo.setNickname(nickname);
					sessionInfo.setBusinessType(businessType);
					sessionInfo.setSkillQueue(skillQueue);
					sessionInfo.setWorkNos(workNo);
					sessionInfo.setSkillType(skillType);
					String customerId = data.getCustomerId();
					if (StringUtils.isNotEmpty(customerId)) {
						sessionInfo.setCustomerId(data.getCustomerId());
					}

					sessionInfo.setSessionType(SessionType.COMMON.getName());
					sessionInfo.setCallId(data.getCallId());
					String taskId = data.getTaskId();
					sessionInfo.setTaskId(taskId);

					/* 创建新的会话，并初始为自助状态 */
					createSessionSelf(sessionInfo);
					return sessionInfo;

				} finally {
					lock.unlock();
				}
			}
		}

	}

	public List<SessionInfo> batchCreateInitiativeSession(SessionData data,
			List<String> failAccounts) throws Exception {
		List<SessionInfo> sessionInfos = new ArrayList<SessionInfo>();
		String sendAccount = data.getSendAccount();
		String customerId = data.getCustomerId();
		String nickname = data.getNickname();
		if (StringUtils.isEmpty(sendAccount))
			throw new Exception("sendAccount is null");
		String skillType = data.getSkillType();
		if (StringUtils.isEmpty(skillType))
			throw new Exception("skillType is null");
		String skillQueue = data.getSkillQueue();
		if (StringUtils.isEmpty(skillQueue))
			throw new Exception("skillQueue is null");
		String workNo = data.getWorkNo();
		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is null");
		String[] sendAccounts = sendAccount.split(",");
		String[] customerIds = {};
		String[] nicknames = {};
		if (StringUtils.isNotEmpty(customerId))
			customerIds = customerId.split(",");
		if (StringUtils.isNotEmpty(nickname))
			nicknames = nickname.split(",");
		for (int i = 0; i < sendAccounts.length; i++) {
			String account = sendAccounts[i];
			String cId = null;
			if (customerIds.length == sendAccounts.length) {
				cId = customerIds[i];
			}
			String nname = null;
			if (nicknames.length == sendAccounts.length) {
				nname = nicknames[i];
			}
			// for(String account : sendAccounts){
			try {
				data.setSendAccount(account);
				data.setCustomerId(cId);
				data.setNickname(nname);
				data.setSessionId(null);
				if(data.getSkillType().equals("4"))
					data.setContent(data.getContent()+";"+i);
				// 捕捉异常，并使切面事务生效
				SessionInfo sessionInfo = this.createInitiativeSession(data);

				sessionInfos.add(sessionInfo);

			} catch (Exception e) {
				logger.error("sendAccount :{} 创建会话失败，原因:{}", account, e);
				failAccounts.add(account);
			}
		}
		return sessionInfos;
	}

	@Override
	public SessionInfo createInitiativeSession(SessionData data)
			throws Exception {
		SessionInfo session = getSession(data);
		if (session != null) {
			//评论类型的不报错
			if(!data.getSkillType().equals("4"))
				throw new Exception("the account in the self status");

		} else {
			// 会话创建处理逻辑
			session = createSession(data);

		}
		if (session == null)
			throw new Exception("create session fail!");
		if (!Arrays.asList(manualStatus).contains(session.getStatus())) {
			// 更新会话到人工
			data.setSessionId(session.getSessionId());
			data.setStatus(SessionStatus.MANUAL.getCode());
			// 使绑定的aop生效
			((SessionInfoService) AopContext.currentProxy()).updateSessionStatus(data);
			session = getSession(data);
			updateSessionAgActive(data);
		}
		String content = data.getContent();
		if (StringUtils.isNotEmpty(content)) {
			// 技能类型为4 -> 评论渠道 不走 网关nSend  调用SI 推送消息到坐席
			if(data.getSkillType().equals("4")){

				logger.info("评论转发参数 : "+JSON.toJSONString(data));

				// TODO  拿评论的ID去好评的库里查出来再 入到 中差评的 库里
				JSONObject commentData = JSON.parseObject(data.getContent().substring(0, data.getContent().indexOf(";")));

//				"content": "{"commentPlat":"京东,天猫,京东,京东"};1;2;3;4;5"

				int currentLoopNumber = Integer.parseInt(data.getContent().substring(data.getContent().lastIndexOf(";")+1, data.getContent().length()));

				String[] commentPlatform = commentData.getString("commentPlat").split(",");

				if(commentPlatform[currentLoopNumber].equals("京东")){

					JdComment comment = jdCommentDao.queryCommentById(data.getSendAccount());

					if(jdCommentDao.isExist(comment)==0)
						comment.setGoodCommentMark("1");
						//设置为已推送
						comment.setIsPush("1");
						jdCommentDao.insert(comment);

				}else if(commentPlatform[currentLoopNumber].equals("天猫")){

					TbComment comment = tbCommentDao.queryCommentById(data.getSendAccount());
					if(tbCommentDao.isExist(comment)==0)
						comment.setMark("1");
						tbCommentDao.insert(comment);
				}

				TransferRequest transferRequest = new TransferRequest();
				transferRequest.setForward(data.getWorkNo());
				transferRequest.setRemark(commentPlatform[currentLoopNumber]);
				transferRequest.setSessionId(session.getSessionId());
				transferRequest.setTenantCode(data.getTenantCode());
				logger.info("***********调用MIR 转发接口,参数:"+JSON.toJSONString(transferRequest));
				// TODO
				BaseResponse transferTaskUser = agentService.transferTaskUser(transferRequest);

				logger.info("@@@@@@@@@@@MIR 转发接口return : "+JSON.toJSONString(transferTaskUser));

				if(!transferTaskUser.getResultCode().equals("0"))
					session = null;

			}else{

				MessageInfo info = JSONObject.parseObject(content,MessageInfo.class);
				info.setSendAccount(data.getAcceptedAccount());
				info.setAcceptedAccount(data.getSendAccount());
				info.setSessionId(data.getSessionId());

				String result = commonSendService.sendMsgMGW(info, PARAMTYPE.POST);
				if (StringUtils.isNotEmpty(result)) {
					JSONObject resultJson = JSONObject.parseObject(result);
					if (!Constants.RESPONSE_OK.equals(resultJson
							.getString("resultCode"))) {
						throw new Exception("向网关发送消息失败");
					}
				}
			}


		}

		return session;
	}

	@Override
	public void updateSession(SessionInfo info) throws Exception {

		// sql优化
		// 获取数据库时间
		Date sysTime = sessionInfoDao.getSysTime();

		if ("5".equals(info.getStatus())
				&& sessionInfoDao.diffTime(info.getSessionId()) < 0) {
			info.setStartTime(sysTime);
		}

		if ("7".equals(info.getStatus()) || "9".equals(info.getStatus())
				|| "10".equals(info.getStatus())) {
			info.setEndTime(sysTime);
		}

		sessionInfoDao.edit(info);
	}

	@Override
	public void updateSessionRoute(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		Map<String, Object> dataMap = data.getData();
		SessionInfo info = this.getSession(data);
		if (dataMap != null) {
			if (dataMap.containsKey("skillQueue")) {
				info.setSkillQueue((String) dataMap.get("skillQueue"));
			}
			if (dataMap.containsKey("skillType")) {
				info.setSkillType((String) dataMap.get("skillType"));
			}
			if (dataMap.containsKey("businessType")) {
				info.setBusinessType((String) dataMap.get("businessType"));
			}
			if (dataMap.containsKey("taskId")) {
				info.setTaskId((String) dataMap.get("taskId"));
			}
		}
		info.setStatus(SessionStatus.ROUTE.getCode());
		info.setStatusSymbol(SessionStatus.ROUTE.name());

		updateSession(info);
		cacheSession(sessionId, info);
		SessionTip sessionTip = sessionTipService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
		if(sessionTip == null){
			sessionTip = sessionTipService.get(info.getTenantCode(),"","");
		}

		SessionTimeout sessionTimeOut = sessionTimeoutService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
		if(sessionTimeOut == null){
			sessionTimeOut = sessionTimeoutService.get(info.getTenantCode(),"","");
		}

		boolean isHoliday = Utils.isHoliday(sessionTipService.getCurDate(), sessionTip.getHoliday());
		String isSortTimeOut = sessionTip.getIsSortTimeOut();
		long sorttimeout = sessionTimeOut.getSorttimeout();
		if(isHoliday){
			if(sessionTip.getIsSortTimeoutHoliday() != null && !"".equals(sessionTip.getIsSortTimeoutHoliday())){
				isSortTimeOut = sessionTip.getIsSortTimeoutHoliday();
			}
			if(sessionTimeOut.getSorttimeoutHoliday() > 0){
				sorttimeout = sessionTimeOut.getSorttimeoutHoliday();
			}
		}

		logger.info("IsSortTimeOut:"+sessionTip.getIsSortTimeOut());
		if ("true".equals(isSortTimeOut)) {
			TimeOutSession tos = info.getTimeOutSession();
			if (tos != null) {
				long timeoutSecond = new Date().getTime() + sorttimeout;
				logger.info("超时时间为多少--------"+sorttimeout);
				tos.setLastActiveTime(new Date(timeoutSecond));
				sessionOutTimeSave(sessionId, tos);
			}
		}

	}

	@Override
	public void updateSessionSort(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		String skillQueue = (String) data.getData().get("skillQueue");
		SessionInfo info = this.getSession(data);

		String skillType = data.getSkillType();
		/* 验证参数中是否传递技能组分类 */
		/*
		 * if (StringUtils.isEmpty(skillType)) { if (data.getData() == null ||
		 * !data.getData().containsKey("skillType")) { if
		 * (StringUtils.isEmpty(info.getSkillType())) { throw new
		 * Exception("skillType技能组分类不能为空！"); } skillType = info.getSkillType();
		 *
		 * } else { skillType = (String) data.getData().get("skillType"); } }
		 */
		// info.setSkillType(skillType);
		if (StringUtils.isEmpty(skillQueue))
			skillQueue = data.getSkillQueue();
		if (StringUtils.isEmpty(skillQueue))
			skillQueue = info.getSkillQueue();
		if (StringUtils.isEmpty(skillQueue))
			throw new Exception("skillQueue技能队列不能为空！");

		info.setStatus(SessionStatus.SORT.getCode());
		info.setStatusSymbol(SessionStatus.SORT.name());
		info.setSkillQueue(skillQueue);
		info.setTaskId((String) data.getData().get("taskId"));

		updateSession(info);
		cacheSession(sessionId, info);
		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);

	}

	/**
	 * updateSessionModel:(更新会话model 切换渠道). <br/>
	 *
	 * @param data
	 * @return void
	 * @throws Exception
	 * <AUTHOR>
	 * @since JDK 1.6
	 */
	public void updateSessionModel(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		String sendAccount = data.getSendAccount();
		String acceptedAccount = data.getAcceptedAccount();
		String channelCode = data.getChannelCode();
		String nickname = data.getNickname();
		String callId = data.getCallId();
		SessionInfo info = this.getSession(data);
		/* 先清除原先旧会话 后面重新存放 */
		sessionCacheDeleteCascade(info);

		String newSessionModel = data.getSessionModel();

		info.setSendAccount(sendAccount);
		info.setAcceptedAccount(acceptedAccount);
		info.setChannelCode(channelCode);
		info.setSessionModel(newSessionModel);
		info.setNickname(nickname);
		info.setChangeStatus(ChangeStatus.YES.getCode());
		info.setCallId(callId);

		// String oldSessionModelKey = oldSessionModel;
		// String newSessionModelKey = newSessionModel;
		updateSession(info);
		// 推送渠道切换提醒
		sendChangeChannelTip(info);

		/* 保存新会话 */
		cacheSession(sessionId, info);

		// updateSessionBySessionModel(oldSessionModelKey, newSessionModelKey);

		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);

	}

	@Override
	public void updateSessionAllocat(SessionData data) throws Exception {
		String sessionId = data.getSessionId();

		SessionInfo info = this.getSession(data);
		info.setStatus(SessionStatus.ALLOCAT.getCode());
		info.setStatusSymbol(SessionStatus.ALLOCAT.name());

		updateSession(info);
		cacheSession(sessionId, info);
		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);

	}

	@Override
	public void updateSessionManual(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		String workNo = data.getWorkNo();
		String skillQueue = data.getSkillQueue();
		String callId = data.getCallId();

		if (StringUtils.isEmpty(workNo))
			workNo = (String) data.getData().get("workNo");
		if (StringUtils.isEmpty(callId) && data.getData() != null)
			callId = (String) data.getData().get("callId");
		if(StringUtils.isEmpty(skillQueue) && data.getData() != null)
			skillQueue = (String) data.getData().get("skillQueue");

		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is not null");


		SessionInfo info = this.getSession(data);
		String workNos = info.getWorkNos();
		if (info != null
				&& info.getStatus().equals(SessionStatus.MANUAL.getCode())) {
			// throw new SessionOperationException("status resubmit");
			logger.error("sessionId:{},错误原因:{}", sessionId, "status resubmit");
			return;
		}
		info.setStatus(SessionStatus.MANUAL.getCode());
		info.setStatusSymbol(SessionStatus.MANUAL.name());
		if (StringUtils.isNotEmpty(skillQueue))
			info.setSkillQueue(skillQueue);

		Date date = new Date();
		info.setStartTime(date);
		info.setEndTime(date);
		info.setWorkNos(workNo);
		info.setCallId(callId);

		updateSession(info);
		String[] wnos = workNo.split(",");
		for (String wno : wnos) {
			SessionWorkNo sessionWorkNo = new SessionWorkNo();
			sessionWorkNo.setSessionId(sessionId);
			sessionWorkNo.setTenantCode(data.getTenantCode());
			sessionWorkNo.setWorkNo(wno);
			sessionWorkNo.setJoinType(SessionWorkNo.JoinType.CREATER.code());
			addSessionWorkNo(sessionWorkNo);
		}

		/*
		 * SessionWorkNo sessionWorkNo = new SessionWorkNo();
		 * sessionWorkNo.setSessionId(sessionId);
		 * sessionWorkNo.setTenantCode(data.getTenantCode());
		 * sessionWorkNo.setWorkNo(workNo);
		 *
		 * addSessionWorkNo(sessionWorkNo);
		 */
		cacheSession(sessionId, info);

		if (!info.getWorkNos().equals(workNos)) {
			addSeesionCount(info.getTenantCode(), info.getSkillQueue(),
					wnos[0], info.getSkillType());
		}
		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);
		// 为工号增加会话数
		/*
		 * String tenantCode = info.getTenantCode(); String skillQueue =
		 * info.getSkillQueue();
		 */
		// addSeesionCount(tenantCode, skillQueue, workNo);

	}

	@Override
	public SessionInfo getSession(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		String tenantCode = data.getTenantCode();
		String sessionKey;
		SessionInfo info = null;
		if (StringUtils.isNotEmpty(sessionId)) {
			info = readCacheSessionBySessionId(sessionId);
			/* 缓存中不存在数据 从数据库中还原 */
			if("true".equals(needReloadSession)){
				if (info == null ) {
					info = new SessionInfo();
					info.setSessionId(sessionId);
					info.setTenantCode(tenantCode);

					List<SessionInfo> sessionInfos = sessionInfoDao
							.getUnfinishSession(info);
					if (sessionInfos != null && sessionInfos.size() > 0) {
						info = sessionInfos.get(0);
						cacheSession(info.getSessionId(), info);
					} else {
						info = null;
					}
				}
			}

		} else {
			sessionKey = data.getSessionModel();
			info = readCacheSessionBySessionKey(sessionKey);
			if("true".equals(needReloadSession)){
				if (info == null) {
					info = new SessionInfo();
					info.setSessionModel(sessionKey);
					info.setTenantCode(data.getTenantCode());
					List<SessionInfo> sessionInfos = sessionInfoDao
							.getUnfinishSession(info);
					if (sessionInfos != null && sessionInfos.size() > 0) {
						info = sessionInfos.get(0);
						cacheSession(info.getSessionId(), info);
					} else {
						info = null;
					}
				} else {
					String realSessionKey = info.getSessionModel();
					if (!sessionKey.equals(realSessionKey)) {
						logger.debug(
								"request sessionKey :{},realSessionKey:{},the sessionId:{} had changed channel",
								sessionKey, realSessionKey, info.getSessionId());
						info = null;
					}
				}

			}


		}
		if (info != null) {
			if (StringUtils.isNotEmpty(data.getSessionId())) {
				if (!data.getSessionId().equals(info.getSessionId())) {
					info = null;
				}

			}
		}
		return info;

	}

	@Override
	public int getSessionActiveFlag(SessionData data) throws Exception {
		String tenantCode = data.getTenantCode();
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is null");
		String sendAccount = data.getSendAccount();
		if (StringUtils.isEmpty(sendAccount))
			throw new Exception("sendAccount is null");
		String channelCode = data.getChannelCode();
		if (StringUtils.isEmpty(channelCode))
			throw new Exception("channelCode is null");
		String acceptedAccount = data.getAcceptedAccount();
		if (StringUtils.isEmpty(acceptedAccount))
			throw new Exception("acceptedAccount is null");
		String sessionKey = data.getSessionModel();
		String dueSessionKey = Constants.DUE_SESSION + sessionKey;
		if (redisDao.exist(dueSessionKey)) {
			return 1;
		}
		return 0;
	}

	@Override
	public void sessionOutTimeSave(String sessionId, TimeOutSession session) {
		SessionInfo sessionInfo = readCacheSessionBySessionId(sessionId);
		if (sessionInfo != null) {
			sessionInfo.setTimeOutSession(session);
			cacheSession(sessionId, sessionInfo);
		}
	}

	@Override
	public void updateSessionAgActive(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		RedisLock lock = RedisLock.getRedisLock("SESSION_" + sessionId);

		while (true) {
			if (lock.lock()) {
				try {
					String tenantCode = data.getTenantCode();
					TimeOutSession timeOutSession = new TimeOutSession();
					timeOutSession.setSessionId(sessionId);
					timeOutSession.setTenantCode(tenantCode);
					SessionInfo sessionInfo = getSession(data);
					SessionTimeout sessionTimeout = sessionTimeoutService.get(sessionInfo.getTenantCode(),sessionInfo.getSkillQueue(),sessionInfo.getAcceptedAccount());
					if(sessionTimeout == null){
						sessionTimeout = sessionTimeoutService.get(sessionInfo.getTenantCode(),"","");
					}

					long timeoutSecond = 0;
					int time = data.getTime();
					if (data.getTime() > 0) {
						timeoutSecond = new Date().getTime() + time;

						// TODO
						redisDao.saveValue(expirekey + data.getSessionId(), "",time, TimeUnit.MILLISECONDS);

					}else {
						if (sessionTimeout != null) {
							userSessionTimeout = sessionTimeout.getUserSessionTimeout();
							// TODO
							redisDao.saveValue(expirekey + data.getSessionId(), "",userSessionTimeout, TimeUnit.MILLISECONDS);
						}
						timeoutSecond = new Date().getTime()+ userSessionTimeout;
					}
					// 设置超时动作
					String action = null;
					if (sessionTimeout != null) {
						action = sessionTimeout.getUserSessionTimeoutAction();
					}

					if (StringUtils.isEmpty(action)) {
						logger.warn("sessionId:{} sessionTimeoutAction is null use default action tip",sessionId);
						timeOutSession
								.setTimeoutType(Constants.TIMEOUT_TYPE_TIP);
					} else {
						timeOutSession.setTimeoutType(action);
					}
					timeOutSession.setTimeoutCount(0);
					timeOutSession.setLastActiveTime(new Date(timeoutSecond));
					timeOutSession.setLastActiveUser(Constants.AG_ACTIVE);
					logger.info("updateSessionAgActive:TimeoutCount:{},LastActiveUser:{}",timeOutSession.getTimeoutCount(),timeOutSession.getLastActiveUser());
					sessionOutTimeSave(sessionId, timeOutSession);
				} finally {
					lock.unlock();
				}
				break;
			}

		}

	}

	@Override
	public void updateSessionUserActive(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		RedisLock lock = RedisLock.getRedisLock("SESSION_" + sessionId);
		while (true) {
			if (lock.lock()) {
				try {
					String tenantCode = data.getTenantCode();
					TimeOutSession timeOutSession = new TimeOutSession();
					timeOutSession.setSessionId(sessionId);
					timeOutSession.setTenantCode(tenantCode);
					long timeoutSecond = 0;
					int time = data.getTime();
					SessionInfo info = getSession(data);
					SessionTimeout sessionTimeout = sessionTimeoutService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
					if(sessionTimeout == null){
						sessionTimeout = sessionTimeoutService.get(info.getTenantCode(),"","");
					}

					if (data.getTime() > 0) {
						timeoutSecond = new Date().getTime() + time;

						// TODO
						redisDao.saveValue(expirekey + data.getSessionId(), "",time, TimeUnit.MILLISECONDS);
					} else {
						// add 2015-5-18 添加自助超时功能
						if (info != null && SessionStatus.SELF.getCode().equals(info.getStatus())) {
							logger.info("sessionId:" + sessionId + " 更新超时"
									+ selfTimeout);
							timeoutSecond = new Date().getTime() + selfTimeout;

							// TODO
							redisDao.saveValue(expirekey + data.getSessionId(), "", selfTimeout,TimeUnit.MILLISECONDS);

						} else if (SessionStatus.ROUTE.getCode().equals(info.getStatus())
								|| SessionStatus.SORT.getCode().equals(info.getStatus())) {
							return;
						} else {
							if (sessionTimeout != null) {
								agSessionTimeout = sessionTimeout.getAgSessionTimeout();

								// TODO
								redisDao.saveValue(expirekey + data.getSessionId(), "",agSessionTimeout, TimeUnit.MILLISECONDS);
							}
							timeoutSecond = new Date().getTime()+ agSessionTimeout;
						}
					}

					// 设置超时动作
					String action = null;
					if (sessionTimeout != null) {
						action = sessionTimeout.getAgSessionTimeoutAction();
					}

					if (StringUtils.isEmpty(action)) {
						logger.warn(
								"sessionId:{} sessionTimeoutAction is null use default action tip",
								sessionId);
						timeOutSession
								.setTimeoutType(Constants.TIMEOUT_TYPE_TIP);
					} else {
						timeOutSession.setTimeoutType(action);
					}
					timeOutSession.setTimeoutCount(0);
					timeOutSession.setLastActiveTime(new Date(timeoutSecond));
					timeOutSession.setLastActiveUser(Constants.USER_ACTIVE);
					logger.info("updateSessionUserActive:TimeoutCount:{},LastActiveUser:{}",timeOutSession.getTimeoutCount(),timeOutSession.getLastActiveUser());
					sessionOutTimeSave(sessionId, timeOutSession);
					updateSessionDueTime(info);
				} finally {
					lock.unlock();
				}
				break;
			}

		}

	}

	// 更新会话可调用点对点接口
	public void updateSessionDueTime(SessionInfo sessionInfo) {
		if (sessionInfo == null)
			return;
		// String channelCode = sessionInfo.getChannelCode();
		String acceptedAccount = sessionInfo.getAcceptedAccount();
		// String tenantCode = sessionInfo.getTenantCode();
		ChannelConfig config = sysInitService.getChannelInfo(acceptedAccount);
		int sessionDueDate = config.getSessionDueTime();
		if (sessionDueDate > 0)
			redisDao.saveNxValue(
					Constants.DUE_SESSION + sessionInfo.getSessionModel(),
					null, sessionDueDate, TimeUnit.SECONDS);

	}

	@Override
	public void updateSessionUserClose(SessionData data) throws Exception {
		SessionInfo info = null;
		try {
			info = this.getSession(data);
		} catch (Exception e) {
			logger.warn("该会话已关闭已被关闭。。。。。");
		}
		if (info != null) {
			String oldStatus = info.getStatus();

			if ("2".equals(oldStatus) || "3".equals(oldStatus)) {
				RemoveQueueSessionRequest rqsr = new RemoveQueueSessionRequest();
				rqsr.setSessionId(info.getSessionId());
				rqsr.setTenantCode(info.getTenantCode());
				rqsr.setQueueId(info.getSkillQueue());
				logger.info("[request]用户主动退出排队！ sessionId:"
						+ info.getSessionId() + ",TenantCode:"
						+ info.getTenantCode() + ",SkillQueue:"
						+ info.getSkillQueue());
				RemoveQueueSessionResponse reqsr = allocateService
						.removeSession(rqsr);
				logger.info("[response]用户主动退出排队："
						+ JSONObject.toJSONString(reqsr));
			}

			info.setStatus(SessionStatus.USER_CLOSE.getCode());
			info.setStatusSymbol(SessionStatus.USER_CLOSE.name());
			info.setEndTime(new Date());
			updateSession(info);
			/* 删除缓存中会话相关的数据 */
			sessionCacheDeleteCascade(info);

			String tenantCode = info.getTenantCode();
			String skillQueue = info.getSkillQueue();
			String skillType = info.getSkillType();
			String workNos = info.getWorkNos();
			if (StringUtils.isNotEmpty(workNos)) {
				String[] works = workNos.split(",");
				Map<String, Object> sessionMap = new HashMap<String, Object>();
				for (String wno : works) {
					sessionMap.put("sessionId", info.getSessionId());
					sessionMap.put("workNo", wno);
					sessionInfoDao.deleteSessionWorkNo(sessionMap);
				}
			}

			if (!SessionStatus.DELAY.getCode().equals(oldStatus)) {
				if (StringUtils.isNotEmpty(workNos)) {
					String[] works = workNos.split(",");
					// 只减发起方的会话数
					reduceSeesionCount(tenantCode, skillQueue, works[0],
							skillType);
					/*
					 * for (String workNo : works) {
					 * reduceSeesionCount(tenantCode, skillQueue,
					 * workNo,skillType); }
					 */
				}

			}
			// 是否要推送提示
			if (getIsReply(tenantCode, skillType)) {
				// 推送用户关闭会话提示
				sendUserCloseTip(info);
			}

		}

	}

	public boolean getIsReply(String tenantCode, String skillType) {
		SkillType skillTypeModel = null;
		try {
			skillTypeModel = sysInitService.getSkillType(tenantCode, skillType);
		} catch (Exception e) {
			logger.error("tenantCode:" + tenantCode + " skillType:" + skillType
					+ " getSkillType error", e.getMessage());
			return false;
		}
		if (skillTypeModel != null && "Y".equals(skillTypeModel.getIsReplay()))
			return true;
		return false;
	}

	public void updateSessionTimeout(SessionData data) throws Exception {
		SessionInfo info = null;
		try {
			info = this.getSession(data);
		} catch (Exception e) {
			logger.warn("该会话已经超时。。。。。");
		}
		if (info != null) {
			info.setStatus(SessionStatus.TIMEOUT.getCode());
			info.setStatusSymbol(SessionStatus.TIMEOUT.name());
			info.setEndTime(new Date());
			updateSession(info);
			/* 删除缓存中会话相关的数据 */
			sessionCacheDeleteCascade(info);

			String tenantCode = info.getTenantCode();
			String skillQueue = info.getSkillQueue();
			String skillType = info.getSkillType();
			String workNos = info.getWorkNos();
			String[] works = null;
			if (StringUtils.isNotEmpty(workNos)) {
				works = workNos.split(",");
				Map<String, Object> sessionMap = new HashMap<String, Object>();
				for (String wno : works) {
					sessionMap.put("sessionId", info.getSessionId());
					sessionMap.put("workNo", wno);
					sessionInfoDao.deleteSessionWorkNo(sessionMap);
				}
			}
			if (StringUtils.isNotEmpty(workNos)) {
				if (StringUtils.isNotEmpty(workNos)) {
					works = workNos.split(",");
					// 只减发起方的会话数
					reduceSeesionCount(tenantCode, skillQueue, works[0],
							skillType);
					/*
					 * for (String workNo : works) {
					 * reduceSeesionCount(tenantCode, skillQueue,
					 * workNo,skillType); }
					 */
				}

			}
			// 是否要推送提示
			if (getIsReply(tenantCode, skillType)) {
				/* 推送超时提醒 */
				sendTimeoutTip(info);
			}

		}

	}

	public void updateSessionAgClose(SessionData data) throws Exception {
		String workNo = data.getWorkNo();
		if (StringUtils.isEmpty(workNo) && data.getData() != null
				&& data.getData().get("workNo") != null)
			workNo = (String) data.getData().get("workNo");
		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is not null ");

		SessionInfo info = null;
		try {
			info = this.getSession(data);
		} catch (Exception e) {
			logger.warn("该会话已关闭已被关闭。。。。。");
		}
		if (info != null) {
			String workNos = info.getWorkNos();
			String[] works = workNos.split(",");

			if (!ArrayUtils.contains(works, workNo)) {
				throw new SessionOperationException(
						"the workNo is not exist this session");
			} else if (!workNo.equals(works[0])) {
				// 清除会话里面此工号
				info.removeWorkNo(workNo);
				String workNos2 = info.getWorkNos();
				if (workNos2.split(",").length == 1)
					info.setSessionType(SessionType.COMMON.getName());
				Map<String, Object> sessionMap = new HashMap<String, Object>();
				sessionMap.put("sessionId", info.getSessionId());
				sessionMap.put("workNo", workNo);
				sessionInfoDao.deleteSessionWorkNo(sessionMap);
				updateSession(info);
				cacheSession(info.getSessionId(), info);
				return;
				// throw new
				// SessionOperationException("the session  is not create by this workNo");
			}

			String oldStatus = info.getStatus();
			info.setStatus(SessionStatus.AG_CLOSE.getCode());
			info.setStatusSymbol(SessionStatus.AG_CLOSE.name());
			info.setEndTime(new Date());
			updateSession(info);
			if (StringUtils.isNotEmpty(workNos)) {
				works = workNos.split(",");
				Map<String, Object> sessionMap = new HashMap<String, Object>();
				for (String wno : works) {
					sessionMap.put("sessionId", info.getSessionId());
					sessionMap.put("workNo", wno);
					sessionInfoDao.deleteSessionWorkNo(sessionMap);
				}
			}
			/* 删除缓存中会话相关的数据 */
			sessionCacheDeleteCascade(info);

			String tenantCode = info.getTenantCode();
			String skillQueue = info.getSkillQueue();
			String skillType = info.getSkillType();

			logger.info("oldSessionStatus:" + oldStatus);
			if (!SessionStatus.DELAY.getCode().equals(oldStatus)) {
				// 只减发起方的会话数
				reduceSeesionCount(tenantCode, skillQueue, works[0], skillType);
				/*
				 * for (String workNo2 : works) { reduceSeesionCount(tenantCode,
				 * skillQueue, workNo2,skillType);
				 *
				 * }
				 */
			}
			// 是否要推送提示
			if (getIsReply(tenantCode, skillType)) {
				// 推送坐席关闭会话提示
				sendAgCloseTip(info, workNo);
			}

		}

	}

	/**
	 * 推送超时提示
	 *
	 * @param info
	 */
	public void sendTimeoutTip(SessionInfo info) throws Exception {
		TimeOutSession tos = info.getTimeOutSession();
		String timeoutUser = tos.getLastActiveUser();
		int timeoutCount = tos.getTimeoutCount();
		logger.info("sendTimeoutTip============================================SkillQueue:{},AcceptedAccount:{}",info.getSkillQueue(),info.getAcceptedAccount());
		SessionTip sessionTip = sessionTipService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
		if(sessionTip == null){
			sessionTip = sessionTipService.get(info.getTenantCode(),"","");
		}
		logger.info("sendTimeoutTip============================================timeoutCount:{},timeoutUser:{}",timeoutCount,timeoutUser);
		String contentTipToUser = "";
		String contentTipToAg = "";
		String sendType = TipTypes.DEFAULT.getCode();
		if (timeoutCount == 0){
			if (Constants.AG_ACTIVE.equals(timeoutUser)) {
				if (sessionTip != null) {
					contentTipToUser = sessionTip.getUserTimeoutTip();
					contentTipToAg = sessionTip.getUserTimeOutToAGTip();
					sendType = TipTypes.USERTIMEOUT.getCode();
				}
			}else{
				if (sessionTip != null) {
					contentTipToUser = sessionTip.getAgTimeOutToUserTip();
					contentTipToAg = sessionTip.getAgTimeoutTip();
					sendType = TipTypes.AGTIMEOUT.getCode();
				}
			}
		}else if(timeoutCount == 1){
			if (Constants.AG_ACTIVE.equals(timeoutUser)) {
				if (sessionTip != null) {
					contentTipToUser = sessionTip.getUserTimeoutCloseTip();
					contentTipToAg = sessionTip.getUserTimeOutCloseToAGTip();
					sendType = TipTypes.USERTIMEOUTCLOSE.getCode();
				}
			}else{
				if (sessionTip != null) {
					contentTipToUser = sessionTip.getAgTimeOutCloseToUserTip();
					contentTipToAg = sessionTip.getAgTimeoutCloseTip();
					sendType = TipTypes.AGTIMEOUTCLOSE.getCode();
				}
			}
		}
		MessageInfo messageInfo = new MessageInfo();
		messageInfo.setChannelCode(info.getChannelCode());
		messageInfo.setBusinessType(info.getBusinessType());
		messageInfo.setTenantCode(info.getTenantCode());
		messageInfo.setUserId(info.getCustomerId());
		messageInfo.setNickname(info.getNickname());
		messageInfo.setSendAccount(info.getSendAccount());
		messageInfo.setAcceptedAccount(info.getAcceptedAccount());
		messageInfo.setCreateTime(info.getCreateTime());
		messageInfo.setSessionId(info.getSessionId());
		messageInfo.setSkillQueue(info.getSkillQueue());
		messageInfo.setWorkNo(info.getWorkNos());
		messageInfo.setStatus(info.getStatus());
		messageInfo.setMsgType("text");
		messageInfo.setSkillType(info.getSkillType());

		try {
			String type = TipStatus.CUSTOMER_QUIT.name();

			if(contentTipToAg != null && !"".equals(contentTipToAg)){
				sendAgTip(info, type, contentTipToAg,tos.getLastActiveUser(),sendType,tos.getTimeoutType());
				messageInfo.setContent(contentTipToAg);
				messageInfo.setMessageSource("5");
				messageInfo.setSendType(sendType);
				messageInfo.setSource("1");
				this.messageInfoService.add(messageInfo);
			}

		} catch (Exception e) {
			logger.error("向坐席推送超时关闭会话提示失败：", e);
		}

		try {
			String workNos = info.getWorkNos();
			if (StringUtils.isNotEmpty(workNos)) {
				// 向用户推送超时信息
				if(contentTipToUser != null && !"".equals(contentTipToUser)){
					// 向用户推送超时信息
					MessageInfo messageBody = commonSendService.tranText(info,
						contentTipToUser, workNos.split(",")[0],"0");
					messageBody.setSendType(sendType);
					messageBody.setTimeOutAction(tos.getTimeoutType());
					commonSendService.sendMsgMGW(messageBody, PARAMTYPE.POST);
					messageInfo.setContent(contentTipToUser);
					messageInfo.setMessageSource("6");
					messageInfo.setSource("2");
					messageInfo.setSendType(sendType);
					this.messageInfoService.add(messageInfo);
				}
			}
		} catch (Exception e) {
			logger.error("向用户推送超时关闭会话提示失败：", e);
		}

	}

	/**
	 * sendUserClose:(推送用户关闭会话提示). <br/>
	 *
	 * @param info
	 * @return void
	 * <AUTHOR>
	 * @since JDK 1.6
	 */
	public void sendUserCloseTip(SessionInfo info) {
		try {
			TimeOutSession tos = info.getTimeOutSession();
			SessionTip sessionTip = sessionTipService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
			if(sessionTip == null){
				sessionTip = sessionTipService.get(info.getTenantCode(),"","");
			}

			String contentTipToUser = "";
			String contentTipToAg = "";

			String type = TipStatus.CUSTOMER_QUIT.name();
			if (sessionTip != null) {
				contentTipToUser = sessionTip.getUserCloseSessionTip();
				contentTipToAg = sessionTip.getUserCloseSessionToAGTip();
			}
			MessageInfo messageInfo = new MessageInfo();
			messageInfo.setChannelCode(info.getChannelCode());
			messageInfo.setBusinessType(info.getBusinessType());
			messageInfo.setTenantCode(info.getTenantCode());
			messageInfo.setUserId(info.getCustomerId());
			messageInfo.setNickname(info.getNickname());
			messageInfo.setSendAccount(info.getSendAccount());
			messageInfo.setAcceptedAccount(info.getAcceptedAccount());
			messageInfo.setCreateTime(info.getCreateTime());
			messageInfo.setSessionId(info.getSessionId());
			messageInfo.setSkillQueue(info.getSkillQueue());
			messageInfo.setWorkNo(info.getWorkNos());
			messageInfo.setStatus(info.getStatus());
			messageInfo.setMsgType("text");
			messageInfo.setSkillType(info.getSkillType());

			if(contentTipToAg != null && !"".equals(contentTipToAg)){

				sendAgTip(info, type, contentTipToAg,tos.getLastActiveUser(),TipTypes.USERCLOSE.getCode(),tos.getTimeoutType());
				messageInfo.setContent(contentTipToAg);
				messageInfo.setMessageSource("5");
				messageInfo.setSource("1");
				messageInfo.setSendType(TipTypes.USERCLOSE.getCode());
				this.messageInfoService.add(messageInfo);
			}

			String workNos = info.getWorkNos();
			if (StringUtils.isNotEmpty(workNos)) {
				if(contentTipToUser != null && !"".equals(contentTipToUser)){
					// 向用户推送超时信息
					MessageInfo messageBody = commonSendService.tranText(info,
						contentTipToUser, workNos.split(",")[0],"0");
					messageBody.setSendType(TipTypes.USERCLOSE.getCode());
					messageBody.setTimeOutAction(tos.getTimeoutType());
					commonSendService.sendMsgMGW(messageBody, PARAMTYPE.POST);
					messageInfo.setContent(contentTipToUser);
					messageInfo.setMessageSource("6");
					messageInfo.setSource("2");
					messageInfo.setSendType(TipTypes.USERCLOSE.getCode());
					this.messageInfoService.add(messageInfo);
				}
			}

		} catch (Exception e) {
			logger.error("推送用户关闭会话提示失败：", e);
		}

	}

	public void sendAgTip(SessionInfo info, String type, String contentTip,String lastActiveUser,String sendType,String timeOutAction) {
		try {
			String workNos = info.getWorkNos();
			String tenantCode = info.getTenantCode();
			String sessionId = info.getSessionId();
			String[] works = {};
			if (StringUtils.isNotEmpty(workNos))
				works = workNos.split(",");
			for (String workNo : works) {
				try {

					/*
					 * userCloseSessionTip = cmsSysParamService
					 * .getParamValue(Constants.USER_CLOSE_SESSION_TIP);
					 */
					JSONObject json = new JSONObject();
					json.put("workNo", workNo);
					json.put("tenantId", tenantCode);
					json.put("tenantCode", tenantCode);
					json.put("sessionId", sessionId);
					json.put("channelCode",info.getChannelCode());
					json.put("lastActiveUser", lastActiveUser);
					json.put("type", type);
					json.put("skillType", info.getSkillType());
					json.put("content", contentTip);
					json.put("descr", "");
					json.put("sendType", sendType);
					json.put("timeOutAction", timeOutAction);
					String messageBody = json.toJSONString();
					String result = commonSendService.sendMsgAg(messageBody,
							PARAMTYPE.POST, tenantCode, workNo);
					logger.info("提醒动作，推送消息到AG:" + workNo + "上返回的结果信息为："
							+ result);
				} catch (Exception e) {
					logger.error("提醒动作，推送消息到AG:" + workNo + "失败", e);
				}
			}
		} catch (Exception e) {
			logger.error("提醒动作，推送消息到AG：", e);
		}

	}

	/**
	 * sendAgCloseTip:(推送坐席关闭会话提示). <br/>
	 *
	 * @param info
	 * @return void
	 * <AUTHOR>
	 * @since JDK 1.6
	 */
	public void sendAgCloseTip(SessionInfo info, String work) {
		try {
			TimeOutSession tos = info.getTimeOutSession();
			SessionTip sessionTip = sessionTipService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
			if(sessionTip == null){
				sessionTip = sessionTipService.get(info.getTenantCode(),"","");
			}
			String contentTipToUser = "";
			String contentTipToAg = "";

			if (sessionTip != null) {
				contentTipToAg = sessionTip.getAgCloseSessionTip();
				contentTipToUser = sessionTip.getAgCloseSessionToUserTip();
			}

			MessageInfo messageInfo = new MessageInfo();
			messageInfo.setChannelCode(info.getChannelCode());
			messageInfo.setBusinessType(info.getBusinessType());
			messageInfo.setTenantCode(info.getTenantCode());
			messageInfo.setUserId(info.getCustomerId());
			messageInfo.setNickname(info.getNickname());
			messageInfo.setSendAccount(info.getSendAccount());
			messageInfo.setAcceptedAccount(info.getAcceptedAccount());
			messageInfo.setCreateTime(info.getCreateTime());
			messageInfo.setSessionId(info.getSessionId());
			messageInfo.setSkillQueue(info.getSkillQueue());
			messageInfo.setWorkNo(info.getWorkNos());
			messageInfo.setStatus(info.getStatus());
			messageInfo.setMsgType("text");
			messageInfo.setSkillType(info.getSkillType());
			if(contentTipToUser != null && !"".equals(contentTipToUser)){
				// 向用户推送超时信息
				MessageInfo messageBody = commonSendService.tranText(info,
						contentTipToUser, work,"0");
				messageBody.setSendType(TipTypes.AGCLOSE.getCode());
				messageBody.setTimeOutAction(tos.getTimeoutType());
				commonSendService.sendMsgMGW(messageBody, PARAMTYPE.POST);

				messageInfo.setContent(contentTipToUser);
				messageInfo.setMessageSource("6");
				messageInfo.setSource("2");
				messageInfo.setSendType(TipTypes.AGCLOSE.getCode());
				this.messageInfoService.add(messageInfo);
			}

			String type = TipStatus.CUSTOMER_QUIT.name();
			if(contentTipToAg != null && !"".equals(contentTipToAg)){

				sendAgTip(info, type, contentTipToAg,tos.getLastActiveUser(),TipTypes.AGCLOSE.getCode(),tos.getTimeoutType());
				messageInfo.setContent(contentTipToAg);
				messageInfo.setMessageSource("5");
				messageInfo.setSource("1");
				messageInfo.setSendType(TipTypes.AGCLOSE.getCode());
				this.messageInfoService.add(messageInfo);
			}
			sendAgCloseToAgTip(info, work);
			// }
		} catch (Exception e) {
			logger.error("推送坐席关闭会话提醒失败", e);
		}
	}

	public String tranMsg(SessionInfo info, String source, String msgSource,
			String content, String msgType, String workNo) {
		JSONObject textJson = new JSONObject();
		textJson.put("source", source);
		textJson.put("workNo", workNo);
		textJson.put("channelCode", info.getChannelCode());
		textJson.put("tenantCode", info.getTenantCode());
		textJson.put("skillQueue", info.getSkillQueue());
		textJson.put("nickname", info.getNickname());
		textJson.put("sessionId", info.getSessionId());
		textJson.put("sendAccount", info.getSendAccount());
		textJson.put("acceptedAccount", info.getAcceptedAccount());
		textJson.put("businessType", info.getBusinessType());
		textJson.put("msgType", msgType);
		textJson.put("messageSource", msgSource);
		textJson.put("content", content);
		return textJson.toJSONString();
	}

	public void sendChangeChannelTip(SessionInfo info) {
		String workNos = info.getWorkNos();
		if (StringUtils.isNotEmpty(workNos)) {
			String[] works = workNos.split(",");
			for (String workNo : works) {
				String msgBody = tranMsg(info, "1", "3", "用户渠道切换成功！",
						"eventChannelChange", workNo);
				MessageInfo megBody = JSONObject.parseObject(msgBody,
						MessageInfo.class);
				// msgBody = "[" + msgBody + "]";
				// String agAddress =
				// sysInitService.getServiceInvokerAddress(info.getTenantCode(),
				// workNo, pushAgMsgUrl);
				// commonSendService.sendMsg(agAddress, "", msgBody,
				// PARAMTYPE.POST);
				redisDao.listrPush(
						cn.sh.ideal.util.Constants.WAITING_REQUEST_MESSAGE,
						megBody);
			}
		}

	}

	public void sendAgCloseToAgTip(SessionInfo info, String closeWork) {
		String workNos = info.getWorkNos();
		String tenantCode = info.getTenantCode();
		String sessionId = info.getSessionId();
		String type = TipStatus.CUSTOMER_QUIT.name();
		String[] works = {};
		if (StringUtils.isNotEmpty(workNos))
			works = workNos.split(",");
		for (String workNo : works) {
			if (workNo.equals(closeWork))
				continue;
			try {
				SessionTip sessionTip = sessionTipService.get(info.getTenantCode(),info.getSkillQueue(),info.getAcceptedAccount());
				if(sessionTip == null){
					sessionTip = sessionTipService.get(info.getTenantCode(),"","");
				}
				String agCloseMutualTip = null;
				if (sessionTip != null) {
					agCloseMutualTip = sessionTip.getAgCloseMutualTip();
				}
				if(agCloseMutualTip != null && !"".equals(agCloseMutualTip)){
					MessageInfo messageInfo = new MessageInfo();
					messageInfo.setChannelCode(info.getChannelCode());
					messageInfo.setBusinessType(info.getBusinessType());
					messageInfo.setTenantCode(info.getTenantCode());
					messageInfo.setUserId(info.getCustomerId());
					messageInfo.setNickname(info.getNickname());
					messageInfo.setSendAccount(info.getSendAccount());
					messageInfo.setAcceptedAccount(info.getAcceptedAccount());
					messageInfo.setCreateTime(info.getCreateTime());
					messageInfo.setSessionId(info.getSessionId());
					messageInfo.setSkillQueue(info.getSkillQueue());
					messageInfo.setWorkNo(info.getWorkNos());
					messageInfo.setStatus(info.getStatus());
					messageInfo.setMsgType("text");
					messageInfo.setSkillType(info.getSkillType());

					/*
					 * String agCloseMutualTip = cmsSysParamService
					 * .getParamValue(Constants.AG_CLOSE_MUTUAL_TIP);
					 */

					agCloseMutualTip = String.format(agCloseMutualTip, closeWork);
				    JSONObject json = new JSONObject();
				    json.put("workNo", workNo);
				    json.put("tenantCode", tenantCode);
				    json.put("sessionId", sessionId);
				    json.put("skillType", info.getSkillType());
				    json.put("type", type);
				    json.put("content", agCloseMutualTip);
				    json.put("descr", "");
				    String messageBody = json.toJSONString();
				    String result = commonSendService.sendMsgAg(messageBody,
						PARAMTYPE.POST, tenantCode, workNo);
				    logger.info("坐席关闭三方提醒，推送消息到AG:" + workNo + "上返回的结果信息为："
						+ result);
				    messageInfo.setContent(agCloseMutualTip);
				    messageInfo.setMessageSource("5");
				    messageInfo.setSource("1");
				    this.messageInfoService.add(messageInfo);
				  }
			} catch (Exception e) {
				logger.error("坐席关闭三方提醒，推送消息到AG:" + workNo + "失败", e);
			}
		}
	}

	@Override
	public void updateSessionFinish(SessionData data) throws Exception {
		SessionInfo info = new SessionInfo();
		info.setSessionId(data.getSessionId());
		info.setTenantCode(data.getTenantCode());
		info.setStatus(SessionStatus.FINISH.getCode());
		info.setStatusSymbol(SessionStatus.FINISH.name());
		updateSession(info);

	}

	@Override
	public void addMessage(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		RedisLock lock = RedisLock.getRedisLock("SESSION_" + sessionId);
		while (true) {
			if (lock.lock()) {
				try {
					SessionInfo info = getSession(data);
					String messageId = data.getMessageId();
					if (StringUtils.isEmpty(messageId))
						throw new Exception("messageId is null");
					info.addMessageId(messageId);
					cacheSession(sessionId, info);
				} finally {
					lock.unlock();
				}
				break;
			}

		}

	}

	@Override
	public void updateSessionRedis(SessionInfo info) throws Exception {
		String sessionId = info.getSessionId();
		cacheSession(sessionId, info);
	}

	/**
	 * updateSessionBySessionModel:(更新会话的sessionKey). <br/>
	 *
	 * @param oldSessionKey
	 * @param newSessionKey
	 * @return void
	 * <AUTHOR>
	 * @since JDK 1.6
	 */
	public void updateSessionBySessionModel(String oldSessionKey,
			String newSessionKey) {
		SessionInfo sessionInfo = readCacheSessionBySessionKey(oldSessionKey);
		if (sessionInfo != null) {
			// sessionCacheDeleteCascade(sessionInfo);
			cacheSessionBySessionKey(newSessionKey, sessionInfo);
		}
		// redisDao.saveValue("SESSIONMODEL_" + newSessionKey, oldSessionKey);
		/*
		 * RedisLock lock = RedisLock.getRedisLock(Constants.SESSION_MODEL,
		 * applicationContext); while (true) { if (lock.lock()) { try {
		 * SessionModelArray sma = (SessionModelArray) redisDao
		 * .readValue(Constants.SESSION_MODEL); if (sma != null) { Map<String,
		 * String> sessionModel = sma.queryMaps(); String oldSessionId =
		 * sessionModel.get(oldSessionKey); String newSessionId =
		 * sessionModel.get(newSessionKey); if
		 * (StringUtils.isNotEmpty(oldSessionId)) {
		 * sessionModel.remove(oldSessionKey); try { //
		 * sessionInfoDao.deleteBySessionId(newSessionId);
		 * sessionInfoDao.deleteById(newSessionId); } catch (Exception e) {
		 * logger.error("删除渠道切换前的会话失败！", e); } sessionModel.put(newSessionKey,
		 * oldSessionId); } else { logger.error("原会话id丢失，渠道切换失败！");
		 *
		 * }
		 *
		 * } else { sma = new SessionModelArray();
		 *
		 * } redisDao.saveValue(Constants.SESSION_MODEL, sma); } catch
		 * (Exception e) { logger.error(e.getMessage(), e); } finally {
		 * lock.unlock(); } break; } }
		 */

	}

	@Override
	public void sessionCacheDeleteCascade(SessionInfo info) {
		logger.debug("开始级联删除缓存数据..................");
		String sessionId = info.getSessionId();
		// 清除渠道切换导致多余的session
		String oldSessionKey = sessionId.substring(0, sessionId.length() - 13);
		String sessionKey = info.getSessionModel();
		if (!sessionKey.equals(oldSessionKey)) {
			redisDao.deleteValue("SESSION_" + oldSessionKey);
		}
		// if(getSessionKey(sessionKey) != null)
		// redisDao.deleteValue("SESSIONMODEL_"+sessionKey);
		redisDao.deleteValue("SESSION_" + sessionKey);
		logger.debug("删除缓存会话：SESSION_" + sessionId + "..................");
		logger.debug("结束级联删除缓存数据..................");

	}

	@Override
	public void addWorkNo(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		RedisLock lock = RedisLock.getRedisLock("SESSION_" + sessionId);
		while (true) {
			if (lock.lock()) {
				try {
					SessionInfo info = getSession(data);
					String workNo = data.getWorkNo();
					if (StringUtils.isEmpty(workNo))
						throw new Exception("workNo is null");
					info.addWorkNo(workNo);
					String workNos = info.getWorkNos();
					if (workNos.split(",").length > 1)
						info.setSessionType(SessionType.MULTI.getName());
					updateSession(info);
					String[] wnos = workNo.split(",");
					SessionWorkNo sessionWorkNo = new SessionWorkNo();
					for (String wno : wnos) {
						sessionWorkNo.setSessionId(sessionId);
						sessionWorkNo.setTenantCode(data.getTenantCode());
						sessionWorkNo.setWorkNo(wno);
						sessionWorkNo.setJoinType(SessionWorkNo.JoinType.JOINER
								.code());
						addSessionWorkNo(sessionWorkNo);
					}

					cacheSession(sessionId, info);
					/*
					 * if(!info.getWorkNos().equals(oldWorkNo)){
					 * addSeesionCount(
					 * info.getTenantCode(),info.getSkillQueue(),
					 * data.getWorkNo(),info.getSkillType()); }
					 */
				} finally {
					lock.unlock();
				}
				break;
			}
		}
	}

	/* 此处原工号会话数减一 新工号会话数加一都由mir管理 */
	@Override
	public void updateSessionForword(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		String workNo = (String) data.getData().get("workNo");
		String skillQueue = (String) data.getData().get("skillQueue");
		if (StringUtils.isEmpty(workNo) || StringUtils.isEmpty(skillQueue))
			throw new Exception("workNo or skillQueue is not null");

		/*
		 * if(StringUtils.isEmpty(workNo)) workNo = data.getWorkNo();
		 *
		 * if(StringUtils.isEmpty(workNo)) throw new
		 * Exception("workNo is not null！");
		 */
		SessionInfo info = this.getSession(data);
		// String oldSkillQueue = info.getSkillQueue();
		String oldWorkNos = info.getWorkNos();
		String oldQueue = info.getSkillQueue();
		String[] oldWorkNo = oldWorkNos.split(",");
		if (oldWorkNo.length > 1)
			throw new Exception("多方回话不允许转发！");
		info.setStatus(SessionStatus.FORWORD.getCode());
		info.setStatusSymbol(SessionStatus.FORWORD.name());
		if (StringUtils.isNotEmpty(workNo)) {
			info.setWorkNos(workNo);
			// addSeesionCount(info.getTenantCode(), skillQueue, workNo);
		}
		/*
		 * if (oldWorkNo != null) { String oWorkNo = oldWorkNo[0];
		 * info.removeWorkNo(oWorkNo); }
		 */

		if (StringUtils.isNotEmpty(skillQueue))
			info.setSkillQueue(skillQueue);
		info.setSessionType(SessionType.FORWORD.getName());

		updateSession(info);

		if (StringUtils.isNotEmpty(oldWorkNo[0])) {
			Map<String, Object> sessionMap = new HashMap<String, Object>();
			sessionMap.put("sessionId", sessionId);
			sessionMap.put("workNo", oldWorkNo[0]);
			sessionMap.put("joinType", SessionWorkNo.JoinType.JOINER.code());
			sessionInfoDao.deleteSessionWorkNo(sessionMap);
			// reduceSeesionCount(info.getTenantCode(), oldSkillQueue,
			// oldWorkNos);
		}

		SessionWorkNo sessionWorkNo = new SessionWorkNo();
		sessionWorkNo.setSessionId(sessionId);
		sessionWorkNo.setTenantCode(data.getTenantCode());
		sessionWorkNo.setWorkNo(workNo);
		sessionWorkNo.setJoinType(SessionWorkNo.JoinType.CREATER.code());
		addSessionWorkNo(sessionWorkNo);
		cacheSession(sessionId, info);

		if (!info.getWorkNos().equals(oldWorkNos)) {
			reduceSeesionCount(info.getTenantCode(), oldQueue, oldWorkNos,
					info.getSkillType());
			addSeesionCount(info.getTenantCode(), skillQueue, workNo,
					info.getSkillType());
		}
		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);

	}

	@Override
	public void updateSessionTempSelf(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		String skillQueue = (String) data.getData().get("skillQueue");
		String businessType = (String) data.getData().get("businessType");
		SessionInfo info = this.getSession(data);
		info.setStatus(SessionStatus.TEMP_SELF.getCode());
		info.setStatusSymbol(SessionStatus.TEMP_SELF.name());
		if (StringUtils.isNotEmpty(skillQueue)) {
			info.setSkillQueue(skillQueue);
		}
		if (StringUtils.isNotEmpty(businessType)) {
			info.setBusinessType(businessType);
		}

		/* 更新数据库会话 */
		updateSession(info);
		/* 更新缓存 */
		cacheSession(sessionId, info);
		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);

	}

	@Override
	public Object readCacheData(String key) {
		return redisDao.readValue(key);
	}

	@Override
	public void updateSessionStatus(SessionData data) throws Exception {
		String lockKey = "";
		lockKey = data.getSessionId();
		if (StringUtils.isEmpty(lockKey))
			lockKey = data.getSessionModel();
		RedisLock lock = RedisLock.getRedisLock("SESSION_" + lockKey);
		while (true) {
			if (lock.lock()) {
				try {
					String status = StringUtils.defaultIfEmpty(
							data.getStatus(), "0");
					int st = Integer.valueOf(status);
					switch (st) {
					// 自助
					case 1:
						updateSessionSelf(data);
						break;
					// 路由
					case 2:
						updateSessionRoute(data);
						break;
					// 排队
					case 3:
						this.updateSessionSort(data);
						break;
					// 分配
					case 4:
						updateSessionAllocat(data);
						break;
					// 人工
					case 5:
						updateSessionManual(data);
						break;
					// 临时自助
					case 6:
						updateSessionTempSelf(data);
						break;
					// 会话超时
					case 7:
						updateSessionTimeout(data);
						break;
					// 会话转发
					case 8:
						updateSessionForword(data);
						break;
					// 用户关闭回话
					case 9:
						updateSessionUserClose(data);
						break;
					// 坐席关闭会话
					case 10:
						updateSessionAgClose(data);
						break;
					// 会话延迟
					case 11:
						updateSessionDelay(data);
						break;
					// 会话完结
					case 12:
						updateSessionFinish(data);
						break;
					// 其他情况只更新会话信息
					default:
						String sendAccount = data.getSendAccount();
						String acceptedAccount = data.getAcceptedAccount();
						String channelCode = data.getChannelCode();
						String nickname = data.getNickname();
						Map<String, Object> dataMap = data.getData();
						// 渠道协同
						if (dataMap != null) {
							if (StringUtils.isEmpty(sendAccount)
									&& dataMap.get("sendAccount") != null) {
								sendAccount = (String) dataMap
										.get("sendAccount");
								data.setSendAccount(sendAccount);
							}
							if (StringUtils.isEmpty(acceptedAccount)
									&& dataMap.get("acceptedAccount") != null) {
								acceptedAccount = (String) dataMap
										.get("acceptedAccount");
								data.setAcceptedAccount(acceptedAccount);
							}
							if (StringUtils.isEmpty(channelCode)
									&& dataMap.get("channelCode") != null) {
								channelCode = (String) dataMap
										.get("channelCode");
								data.setChannelCode(channelCode);
							}
							if (StringUtils.isEmpty(nickname)
									&& dataMap.get("nickname") != null) {
								nickname = (String) dataMap.get("nickname");
								data.setNickname(nickname);
							}
						}
						if (StringUtils.isNotEmpty(sendAccount)
								&& StringUtils.isNotEmpty(acceptedAccount)
								&& StringUtils.isNotEmpty(channelCode)) {
							updateSessionModel(data);
						} else {
							throw new Exception(
									"session status is not rightful ");
						}
					}
				} finally {
					lock.unlock();
				}
				break;
			}

		}
	}

	@Override
	public void addSessionTimeoutLog(SessionTimeoutLog log) throws Exception {

		sessionTimeoutLogDao.add(log);

	}

	@Override
	public void updateSessionTimeout(String sessionId, String tenantCode)
			throws Exception {
		SessionInfo info = new SessionInfo();
		info.setSessionId(sessionId);
		info.setTenantCode(tenantCode);
		info.setStatus(SessionStatus.TIMEOUT.getCode());
		info.setStatusSymbol(SessionStatus.TIMEOUT.name());
		info.setEndTime(new Date());
		this.updateSession(info);

	}

	@Override
	public void addSessionWorkNo(SessionWorkNo sessionWorkNo) throws Exception {
		if (sessionInfoDao.getSessionWorkNoCount(sessionWorkNo) == 0)
			sessionInfoDao.addSessionWorkNo(sessionWorkNo);

	}

	@Override
	public void updateSessionSelf(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		Map<String, Object> dataMap = data.getData();
		SessionInfo info = this.getSession(data);
		if (dataMap != null) {
			if (dataMap.containsKey("skillQueue")) {
				info.setSkillQueue((String) dataMap.get("skillQueue"));
			}
			if (dataMap.containsKey("skillType")) {
				info.setSkillType((String) dataMap.get("skillType"));
			}
			if (dataMap.containsKey("businessType")) {
				info.setBusinessType((String) dataMap.get("businessType"));
			}
			if (dataMap.containsKey("workNo")) {
				info.setWorkNos((String) dataMap.get("workNo"));
			}
			if (dataMap.containsKey("taskId")) {
				info.setTaskId((String) dataMap.get("taskId"));
			}
		}
		info.setStatus(SessionStatus.SELF.getCode());
		info.setStatusSymbol(SessionStatus.SELF.name());

		updateSession(info);
		cacheSession(sessionId, info);
		// cacheData("SESSION_" + sessionId, info, timeout, TimeUnit.SECONDS);

	}

	@Override
	public void addSeesionCount(String tenantCode, String skillQueue,
			String workNo, String skillType) throws Exception {
		if (StringUtils.isEmpty(skillQueue))
			return;
		logger.info("add skilltype:" + skillType);
		SkillType skillTypeModel = null;
		try {
			skillTypeModel = sysInitService.getSkillType(tenantCode, skillType);
		} catch (Exception e) {
			logger.error("tenantCode:" + tenantCode + " skillType:" + skillType
					+ " getSkillType error", e.getMessage());
			return;
		}
		if (StringUtils.isNotEmpty(skillType)
				&& !"Y".equals(skillTypeModel.getIsRecord()))
			return;
		String key = Utils.getAgentKey(tenantCode, skillQueue, workNo);
		String mapKey = Utils.getSessionCountMapKey();
		RedisLock lock = RedisLock.getRedisLock(key + mapKey);
		while (true) {
			if (lock.lock()) {
				try {
					int count = getWorkNoSessionCount(tenantCode, skillQueue,
							workNo);
					logger.info("addSessionCount currentSessionCount:tenantcode-{},skillQueue-{},workNo-{},count-{}", new Object[] { tenantCode, skillQueue, workNo, Integer.valueOf(count) });
					count++;
					redisDao.mapPut(key, mapKey, count);
					logger.info("addSessionCount result:tenantcode-{},skillQueue-{},workNo-{},count-{}", new Object[] { tenantCode, skillQueue, workNo, this.redisDao.mapGetValue(key, mapKey) });
				} finally {
					lock.unlock();
				}
				break;
			}
		}

		// new Thread(new SyncSessionCount(tenantCode, skillQueue,
		// workNo)).start();
	}

	@Override
	public void reduceSeesionCount(String tenantCode, String skillQueue,
			String workNo, String skillType) throws Exception {
		if (StringUtils.isEmpty(skillQueue))
			return;
		logger.info("reduce skilltype:" + skillType);
		SkillType skillTypeModel = null;
		try {
			skillTypeModel = sysInitService.getSkillType(tenantCode, skillType);
		} catch (Exception e) {
			logger.error("tenantCode:" + tenantCode + " skillType:" + skillType
					+ " getSkillType error", e.getMessage());
			return;
		}
		if (StringUtils.isNotEmpty(skillType)
				&& !"Y".equals(skillTypeModel.getIsRecord()))
			return;
		String key = Utils.getAgentKey(tenantCode, skillQueue, workNo);
		String mapKey = Utils.getSessionCountMapKey();
		RedisLock lock = RedisLock.getRedisLock(key + mapKey);
		while (true) {
			if (lock.lock()) {
				try {
					int count = getWorkNoSessionCount(tenantCode, skillQueue,
							workNo);
					logger.info("reduceSeesionCount currentSessionCount:tenantcode-{},skillQueue-{},workNo-{},count-{}", new Object[] { tenantCode, skillQueue, workNo, Integer.valueOf(count) });
					if (count > 0) {
						count--;
						redisDao.mapPut(key, mapKey, count);
						logger.info("reduceSeesionCount result:tenantcode-{},skillQueue-{},workNo-{},count-{}", new Object[] { tenantCode, skillQueue, workNo, this.redisDao.mapGetValue(key, mapKey) });
					} else {
						logger.error(
								"workNo:{},tenantCode:{},skillQueue:{} sessionCount is 0 reduce error",
								workNo, tenantCode, skillQueue);
					}

				} finally {
					lock.unlock();
				}
				break;
			}
		}

		// new Thread(new SyncSessionCount(tenantCode, skillQueue,
		// workNo)).start();
		/*
		 * if(StringUtils.isNotEmpty(skillType)){ SkillType skillTypeModel =
		 * sysInitService.getSkillType(tenantCode, skillType);
		 * logger.debug("{} get setIsRecord {}"
		 * ,skillType,skillTypeModel.getIsRecord()); //不等于Y表示会话计入坐席会话数
		 * if(!"Y".equals(skillTypeModel.getIsRecord())) return;
		 *
		 * }
		 */

		/*
		 * SysParam param = sysInitService.getSysParam(Constants.MIR_ADDRESS);
		 * String mirAddress = param.getParamValue();
		 *//*
			 * String mirAddress = cmsSysParamService
			 * .getParamValue(Constants.MIR_ADDRESS);
			 *//*
				 * String decreaseUrl = mirAddress + decreaseSessionCountUrl;
				 * logger.info("减少会话数url:{}", decreaseUrl); JSONObject textJson
				 * = new JSONObject();
				 *//* textJson.put("tenantId", tenantCode); *//*
																 * textJson.put(
																 * "tenantCode",
																 * tenantCode);
																 * textJson
																 * .put(
																 * "skillQueue"
																 * ,
																 * skillQueue);
																 * textJson
																 * .put(
																 * "skillType" ,
																 * skillType);
																 * textJson
																 * .put("workNo"
																 * , workNo);
																 *
																 * String
																 * msgBody =
																 * textJson
																 * .toJSONString
																 * ();
																 *
																 * String result
																 * =
																 * commonSendService
																 * .sendMsg(
																 * decreaseUrl,
																 * msgBody,
																 * PARAMTYPE
																 * .POST);
																 *
																 * logger.info(
																 * "返回减少会话数结果："
																 * + result);
																 */

		/*
		 * String signinUserKey = Constants.SIGNIN_TENANT + tenantCode +
		 * Constants.SIGNIN_SKILLQUEUE + skillQueue + Constants.SIGNIN_WORKNO +
		 * workNo;
		 *
		 * // 该工号的会话数-1 SigninUserModel signinUserModel = (SigninUserModel)
		 * redisDao .readValue(signinUserKey);
		 *
		 *
		 * if (signinUserModel == null) {
		 * logger.error("****************************缓存中key: " + signinUserKey +
		 * "丢失*********无法对工号会话数减一"); // throw new
		 * Exception("缓存中key: "+signinUserKey+"丢失"); } else {
		 *
		 * if(signinUserModel.getLocalSessionCount()>0){
		 * signinUserModel.setLocalTime(Utils.date2String(new Date()));
		 * signinUserModel.setLocalSessionCount(signinUserModel
		 * .getLocalSessionCount() - 1); redisDao.saveValue(signinUserKey,
		 * signinUserModel); }
		 *
		 * }
		 */

	}

	public int getWorkNoSessionCount(String tenantCode, String skillQueue,
			String workNo) throws Exception {
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is null");
		if (StringUtils.isEmpty(skillQueue))
			throw new Exception("skillQueue is null");
		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is null");
		String key = Utils.getAgentKey(tenantCode, skillQueue, workNo);
		String mapKey = Utils.getSessionCountMapKey();
		Object o = redisDao.mapGetValue(key, mapKey);
		if (o == null)
			return 0;
		return (Integer) o;

		/*
		 * Object o = redisDao.mapGetValue(key, mapKey); if (o != null) { int
		 * count = Integer.valueOf(String.valueOf(o)); return count; } else {
		 * logger
		 * .warn("workNo:{},tenantCode:{},skillQueue:{} sessionCount key is null "
		 * ,workNo,tenantCode,skillQueue); return 0; }
		 */

	}

	@Override
	public void updateSessionDelay(SessionData data) throws Exception {
		SessionInfo oldInfo = getSession(data);
		/*
		 * String tenantCode = oldInfo.getTenantCode(); String skillQueue =
		 * oldInfo.getSkillQueue();
		 */
		String sessionId = oldInfo.getSessionId();
		/*
		 * String workNos = oldInfo.getWorkNos(); String oldStatus =
		 * oldInfo.getStatus(); String skillType = oldInfo.getSkillType();
		 */
		// SessionInfo info = new SessionInfo();
		/*
		 * oldInfo.setSessionId(data.getSessionId());
		 * oldInfo.setTenantCode(data.getTenantCode());
		 */
		oldInfo.setStatus(SessionStatus.DELAY.getCode());
		oldInfo.setStatusSymbol(SessionStatus.DELAY.name());
		String sessionType = oldInfo.getSessionType();
		if (sessionType.equals(SessionType.MULTI.getName()))
			throw new Exception("多方通话不能延迟！");

		/*
		 * if (!SessionStatus.DELAY.getCode().equals(oldStatus)) { String[]
		 * works = workNos.split(","); for (String workNo2 : works) {
		 * reduceSeesionCount(tenantCode, skillQueue, workNo2);
		 *
		 * } }
		 */
		oldInfo.setSessionType(SessionType.DELAY.getName());
		cacheSession(sessionId, oldInfo);
		// cacheData("SESSION_" + sessionId, oldInfo, timeout,
		// TimeUnit.SECONDS);
		updateSession(oldInfo);
		// 延迟会话先减会话
		reduceSeesionCount(oldInfo.getTenantCode(), oldInfo.getSkillQueue(),
				oldInfo.getWorkNos(), oldInfo.getSkillType());

	}

	@Override
	public List<AgSession> getAgSessionAll(Map<String, Object> sessionData)
			throws Exception {
		return sessionInfoDao.getAgSessionAll(sessionData);
	}

	@Override
	public int getAgSessionCount(Map<String, Object> sessionData)
			throws Exception {
		return sessionInfoDao.getAgSessionCount(sessionData);
	}

	@Override
	public int getSessionCount(SessionData sessionData) throws Exception {
		String tenantCode = sessionData.getTenantCode();
		String status = sessionData.getStatus();
		if (StringUtils.isNotEmpty(status)) {
			String[] statuses = status.split(",");
			sessionData.setStatuses(statuses);
		}

		if (StringUtils.isEmpty(tenantCode)) {
			throw new Exception("tenantCode is not null");
		} else {
			String[] tenantCodes = tenantCode.split(",");
			sessionData.setTenantCodes(tenantCodes);

		}

		/*
		 * if (StringUtils.isEmpty(status)) throw new
		 * Exception("status is not null");
		 */

		return sessionInfoDao.getSessionCount(sessionData);
	}

	@Override
	public int getSessionSortCount(SessionData sessionData, FlagModel flag)
			throws Exception {
		String tenantCode = sessionData.getTenantCode();
		String channelCode = sessionData.getChannelCode();
		String sendAccount = sessionData.getSendAccount();
		String skillQueue = sessionData.getSkillQueue();
		String[] statuses = { SessionStatus.SORT.getCode() };
		sessionData.setStatuses(statuses);

		if (StringUtils.isEmpty(skillQueue)
				&& StringUtils.isNotEmpty(sendAccount)) {
			if (StringUtils.isEmpty(tenantCode))
				throw new Exception("tenantCode is not null");
			SessionInfo info = new SessionInfo();
			info.setTenantCode(tenantCode);
			info.setChannelCode(channelCode);
			info.setSendAccount(sendAccount);
			if (StringUtils.isNotEmpty(channelCode)
					&& StringUtils.isNotEmpty(sendAccount)) {
				skillQueue = sessionInfoDao.getSessionSkillQueue(info);
			}
			if (StringUtils.isEmpty(skillQueue))
				throw new Exception("can not find skillQueue!");
			SessionInfo oldInfo = getSession(sessionData);
			if (oldInfo == null) {
				flag.setFlag(0);
				return 0;
			}

			if (Integer.valueOf(oldInfo.getStatus()) >= 5) {
				flag.setFlag(0);
				return 0;
			}

		}

		if (StringUtils.isNotEmpty(channelCode)
				&& StringUtils.isNotEmpty(sendAccount)) {
			if (StringUtils.isEmpty(skillQueue))
				throw new Exception("can not find skillQueue!");
		}

		sessionData.setStatus(SessionStatus.SORT.getCode());
		sessionData.setSkillQueue(skillQueue);
		if (StringUtils.isEmpty(tenantCode)) {
			throw new Exception("tenantCode is not null");
		} else {
			String[] tenantCodes = tenantCode.split(",");
			sessionData.setTenantCodes(tenantCodes);

		}
		return sessionInfoDao.getSessionCount(sessionData);
	}

	@Override
	public int getSessionActualSortCount(SessionData sessionData)
			throws Exception {
		SessionInfo info = getSession(sessionData);
		if (info == null)
			throw new Exception("the  sessionId does not exist !");
		String status = info.getStatus();
		int st = Integer.valueOf(status).intValue();
		int sortSt = Integer.valueOf(SessionStatus.SORT.getCode()).intValue();

		if (st < sortSt) {
			throw new Exception("this sessionId 's status :" + st);
		}

		if (st > sortSt) {
			return 0;
		}
		String skillQueue = info.getSkillQueue();

		String allocateQueueCache = getAlloQueueKey(skillQueue);

		String cacheQueueCache = getCacheQueueKey(skillQueue);
		LinkedList infos = new LinkedList();
		infos.addAll(getQueueSessions(allocateQueueCache));
		infos.addAll(getQueueSessions(cacheQueueCache));
		int index = infos.indexOf(info);

		if (index > -1)
			return index + 1;

		return 1;
	}

	public LinkedList<SessionInfo> getQueueSessions(String queueCaccheKey) {
		LinkedList queue = (LinkedList) this.redisDao.readValue(queueCaccheKey);
		return (queue != null) ? queue : new LinkedList();
	}

	protected String getAlloQueueKey(String queueId) {
		return cn.sh.ideal.util.Constants.SKILLQUEUE + queueId
				+ cn.sh.ideal.util.Constants.ALLOCATE_QUEUE;
	}

	protected String getCacheQueueKey(String queueId) {
		return cn.sh.ideal.util.Constants.SKILLQUEUE + queueId
				+ cn.sh.ideal.util.Constants.CACHE_QUEUE;
	}

	/*
	 * @Override public int getSessionTimelySortCount(SessionData sessionData)
	 * throws Exception { SessionInfo sessionInfo = getSession(sessionData); if
	 * (sessionInfo == null) throw new Exception("session is not null"); String
	 * status = sessionInfo.getStatus(); if
	 * (SessionStatus.SORT.getCode().equals(status)) { String skillQueue =
	 * sessionInfo.getSkillQueue(); String allocateQueue =
	 * cn.sh.ideal.util.Constants.SKILLQUEUE + skillQueue +
	 * cn.sh.ideal.util.Constants.ALLOCATE_QUEUE; String cacheQueue =
	 * cn.sh.ideal.util.Constants.SKILLQUEUE + skillQueue +
	 * cn.sh.ideal.util.Constants.CACHE_QUEUE; redisDao.
	 *
	 * } return 0; }
	 */

	/**
	 * 会话实时排队数
	 *
	 * @return
	 */
	public int getTimelySortCount() {
		return 0;
	}

	@Override
	public int getSessionSortTime(SessionData sessionData) throws Exception {

		return 30;
	}

	@Override
	public List<Map<String, Object>> getAgSessionContent(SessionData sessionData)
			throws Exception {
		List<Map<String, Object>> sessions = new ArrayList<Map<String, Object>>();
		String tenantCode = sessionData.getTenantCode();
		String workNo = sessionData.getWorkNo();
		String status = sessionData.getStatus();
		Map<String, Object> agSession = new HashMap<String, Object>();
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is not null");
		agSession.put("tenantCode", tenantCode);
		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is not null");
		agSession.put("workNo", workNo);
		if ("0".equals(status) || "1".equals(status) || "2".equals(status))
			agSession.put("sessionStatus", status);
		else
			throw new Exception("status is not rightful");
		List<SessionInfo> infos = getAgSession(agSession);
		for (SessionInfo info : infos) {
			Map<String, Object> session = new HashMap<String, Object>();
			String sessionId = info.getSessionId();
			agSession.put("sessionId", sessionId);
			int size = sessionInfoDao.getMessageCount(agSession);
			MessageModel messageData = sessionInfoDao.getMessage(agSession);
			if (messageData != null) {
				if (StringUtils.isNotEmpty(info.getBusinessType()))
					messageData.setMessageBusiness(info.getBusinessType());
				messageData.setSkillQueue(info.getSkillQueue());
				messageData.setSessionId(info.getSessionId());
				messageData.setTenantCode(info.getTenantCode());
				if (StringUtils.isEmpty(messageData.getWorkNo())) {
					messageData.setStatus("0");
				} else {
					messageData.setStatus("1");
				}
				session.put("message", messageData);
				session.put("size", size);
				sessions.add(session);
			}

		}
		return sessions;
	}

	@Override
	public List<Map<String, Object>> getSessionContent(SessionData sessionData)
			throws Exception {
		List<Map<String, Object>> sessions = new ArrayList<Map<String, Object>>();
		String tenantCode = sessionData.getTenantCode();
		String workNo = sessionData.getWorkNo();
		String status = sessionData.getStatus();
		String skillType = sessionData.getSkillType();
		Map<String, Object> agSession = new HashMap<String, Object>();
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is null");
		agSession.put("tenantCode", tenantCode);
		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is null");
		agSession.put("workNo", workNo);
		if (AgSessionStatus.RUNTIME.getCode().equals(status)
				|| AgSessionStatus.DELAY.getCode().equals(status)
				|| AgSessionStatus.CLOSE.getCode().equals(status))
			agSession.put("sessionStatus", status);
		else
			throw new Exception("status is not rightful");
		if (StringUtils.isNotEmpty(skillType)) {
			String[] skillTypes = skillType.split(",");
			agSession.put("skillTypes", skillTypes);
		}
		List<SessionInfo> infos = getAgSession(agSession);
		for (SessionInfo info : infos) {
			Map<String, Object> session = new HashMap<String, Object>();
			String sessionId = info.getSessionId();
			agSession.put("sessionId", sessionId);
			MessageInfo messageInfo = new MessageInfo();
			messageInfo.setTenantCode(tenantCode);
			agSession.put("workNo", null);
			// messageInfo.setWorkNo(workNo);
			messageInfo.setSessionId(sessionId);
			/*
			 * int size = messageInfoService.getCount(
			 * "session.MessageMapper.queryMsgCount", messageInfo);
			 */

			int size = messageInfoService.getCount(messageInfo);
			messageInfo = messageInfoService.getLastMessage(agSession);
			// MessageModel messageData = sessionInfoDao.getMessage(agSession);
			messageInfo.setBusinessType(info.getBusinessType());
			messageInfo.setSkillQueue(info.getSkillQueue());

			session.put("sessionId", sessionId);
			session.put("message", messageInfo);
			session.put("size", size);
			sessions.add(session);
		}
		return sessions;
	}

	@Override
	public List<SessionInfo> getAgSession(Map<String, Object> agSession)
			throws Exception {
		return sessionInfoDao.getAgSession(agSession);
	}

	@Override
	public Map<String, Object> getHistoryMessage(MessageData data) {
		Map<String, Object> mapmsg = new HashMap<String, Object>();
		List<HisMessage> listMsg = new ArrayList<HisMessage>();
		int msgCount = -1;

		// logger.info(data.toString());
		/*
		 * int datefrom = data.getDataFrom(); datenum = datenum == null ? "" :
		 * datenum; if ("".equals(datefrom) || "".equals(datenum)) {
		 * mapmsg.put("error", "datefrom和datenum不能为空"); return mapmsg; }
		 */
		/*
		 * HisMessage message = new HisMessage();
		 * message.setSessionId(sessionId); message.setWorkNo(workNo);
		 * message.setAccout(accout); message.setContentmsg(contentmsg);
		 * message.setBegintime(begintime); message.setEndtime(endtime);
		 * message.setDatefrom(Integer.valueOf(datefrom));
		 * message.setDatenum(Integer.valueOf(datenum));
		 */
		try {
			listMsg = sessionInfoDao.getHistoryMsgs(data);
			msgCount = sessionInfoDao.getHistoryMsgCount(data);
		} catch (Exception e) {
			logger.error("查询会话异常", e);
		}
		mapmsg.put("total", msgCount);
		mapmsg.put("data", listMsg);
		return mapmsg;
	}

	@Override
	public String removeSessionCache(String sessionId, String tenantCode)
			throws Exception {
		SessionInfo info = readCacheSessionBySessionId(sessionId);
		/*
		 * if (info == null) { info = new SessionInfo();
		 * info.setSessionId(sessionId); info.setTenantCode(tenantCode);
		 *//*
			 * info = this.baseDao.get(
			 * "session.SessionInfoMapper.getUnfinishSession", info);
			 *//*
				 * info = sessionInfoDao.getUnfinishSession(info); if (info ==
				 * null) return null; }
				 */
		if (info != null) {
			sessionCacheDeleteCascade(info);
		}

		sessionInfoDao.deleteById(sessionId);
		return sessionId;
	}

	@Override
	public List<Session> getUnfinishSessionMsgs(SessionData data)
			throws Exception {
		String tenantCode = data.getTenantCode();
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is not null");
		String workNo = data.getWorkNo();
		if (StringUtils.isEmpty(workNo))
			throw new Exception("workNo is not null");
		/*
		 * if (!(status.equals(AgSessionStatus.CLOSE.getCode()) ||
		 * status.equals(AgSessionStatus.DELAY.getCode()) || status
		 * .equals(AgSessionStatus.RUNTIME.getCode()))) { throw new
		 * Exception("status is not rightful"); }
		 */
		Map<String, Object> session = new HashMap<String, Object>();
		session.put("tenantCode", tenantCode);
		session.put("workNo", workNo);
		session.put("sessionStatus", data.getStatus());
		session.put("sessionId", data.getSessionId());
		return messageInfoDao.getUnfinishSessionMessages(session);
	}

	@Override
	public Map<String, Object> unfinishSessionMsgToAg(List<Session> sessions)
			throws Exception {

		Map<String, Object> sessionMap = new HashMap<String, Object>();
		for (Session session : sessions) {

			sessionMap.put(session.getSessionId(), session.getMessages());

		}

		return sessionMap;
	}

	@Override
	public SessionInfo createSessionSelf(SessionInfo info) throws Exception {
		info.setChangeStatus(ChangeStatus.NO.getCode());
		sessionInfoDao.add(info);
		cacheSession(info.getSessionId(), info);
		return info;
	}

	@Override
	public List<SessionSortItem> getSessionSortItemCount(SessionData sessionData)
			throws Exception {
		return sessionInfoDao.getSessionSortItemCount(sessionData);
	}

	public void addSkillSortItem(SessionSortItem item,
			Map<String, Object> skillQueueMap) {
		if (skillQueueMap.containsKey(item.getSkillQueue())) {
			@SuppressWarnings("unchecked")
			Map<String, Object> skillSortItem = (Map<String, Object>) skillQueueMap
					.get(item.getSkillQueue());
			@SuppressWarnings("unchecked")
			Map<String, Long> data = (Map<String, Long>) skillSortItem
					.get("data");
			long sortCount = 0;
			if (data.containsKey(item.getChannelCode())) {
				sortCount = data.get(item.getChannelCode());
			}
			sortCount += item.getSortCount();
			data.put(item.getChannelCode(), sortCount);
			skillSortItem.put("data", data);
			skillQueueMap.put(item.getSkillQueue(), skillSortItem);

		} else {

			Map<String, Object> skillSortItem = new HashMap<String, Object>();
			skillSortItem.put("skillQueue", item.getSkillQueue());
			skillSortItem.put("queueName", item.getQueueName());
			Map<String, Long> data = new HashMap<String, Long>();
			data.put(item.getChannelCode(), item.getSortCount());
			skillSortItem.put("data", data);

			skillQueueMap.put(item.getSkillQueue(), skillSortItem);

		}
	}

	public void addChannelSortItem(SessionSortItem item,
			Map<String, Object> channelCodeMap) {
		if (channelCodeMap.containsKey(item.getChannelCode())) {
			@SuppressWarnings("unchecked")
			Map<String, Object> channelSortItem = (Map<String, Object>) channelCodeMap
					.get(item.getChannelCode());
			@SuppressWarnings("unchecked")
			Map<String, Long> data = (Map<String, Long>) channelSortItem
					.get("data");
			long sortCount = 0;
			if (data.containsKey(item.getSkillQueue())) {
				sortCount = data.get(item.getSkillQueue());
			}
			sortCount += item.getSortCount();
			data.put(item.getSkillQueue(), sortCount);
			channelSortItem.put("data", data);
			channelCodeMap.put(item.getSkillQueue(), channelSortItem);

		} else {

			Map<String, Object> channelSortItem = new HashMap<String, Object>();
			channelSortItem.put("channelCode", item.getChannelCode());
			channelSortItem.put("channelName", item.getChannelName());
			Map<String, Long> data = new HashMap<String, Long>();
			data.put(item.getSkillQueue(), item.getSortCount());
			channelSortItem.put("data", data);
			channelCodeMap.put(item.getSkillQueue(), channelSortItem);

		}
	}

	@Override
	public SessionInfo updateSessionTimeout(SessionInfo sessionInfo)
			throws Exception {

		sessionInfo.setStatus(SessionStatus.TIMEOUT.getCode());
		sessionInfo.setStatusSymbol(SessionStatus.TIMEOUT.name());
		sessionInfo.setEndTime(new Date());
		this.updateSession(sessionInfo);
		return sessionInfo;
	}

	@Override
	public String getMessageId(String sessionId, String type) throws Exception {
		return messageInfoDao.getUserMessageId(sessionId);
	}

	@Override
	public List<SessionInfo> getSessionList(SessionData data) throws Exception {
		Map<String, Object> info = new HashMap<String, Object>();
		String tenantCode = data.getTenantCode();
		String workNo = data.getWorkNo();
		String status = data.getStatus();
		String sessionId = data.getSessionId();
		String sendAccount = data.getSendAccount();
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is not null");
		// mir接口用来校验丢失的会话
		if ("0".equals(tenantCode))
			tenantCode = null;
		/*
		 * if (StringUtils.isEmpty(status)) throw new
		 * Exception("status is not null");
		 */
		String channelCode = data.getChannelCode();
		String businessType = data.getBusinessType();
		String skillQueue = data.getSkillQueue();
		String[] statuses = null;
		if (StringUtils.isNotEmpty(status)) {
			statuses = status.split(",");
		}
		String skillType = data.getSkillType();
		String[] skillTypes = null;
		String[] skillQueues = null;
		if (StringUtils.isNotEmpty(skillType)) {
			skillTypes = skillType.split(",");
		}
		if (StringUtils.isNotEmpty(skillQueue)) {
			skillQueues = skillQueue.split(",");
		}
		info.put("skillTypes", skillTypes);
		info.put("skillQueues", skillQueues);
		info.put("tenantCode", tenantCode);
		info.put("workNo", workNo);
		info.put("statuses", statuses);
		info.put("channelCode", channelCode);
		info.put("businessType", businessType);
		info.put("skillQueue", skillQueue);
		info.put("sendAccount", sendAccount);
		info.put("pageSize", data.getPageSize());
		info.put("pageNum", data.getPageNum());
		info.put("beginDate", data.getBeginDate());
		info.put("endDate", data.getEndDate());
		info.put("sessionId", sessionId);

		// sql优化，获取nickname(除去子查询)
		List<SessionInfo> sessionInfoList = sessionInfoDao.getSessionList(info);
		// for(SessionInfo sessionSingle : sessionInfoList){
		// String sessionIdSingle = sessionSingle.getSessionId();
		// String nickName = "";
		// try {
		// sessionInfoDao.getNickName(sessionIdSingle);
		// } catch (Exception e) {
		// logger.info("读取nickname字段有异常，该异常可以忽略！请不必担心！",e);
		// }
		// sessionSingle.setNickname(nickName);
		// }

		return sessionInfoList;
	}

	@Override
	public int getSessionListCount(SessionData data) throws Exception {
		Map<String, Object> info = new HashMap<String, Object>();
		String tenantCode = data.getTenantCode();
		String workNo = data.getWorkNo();
		String status = data.getStatus();
		String sendAccount = data.getSendAccount();
		String sessionId = data.getSessionId();
		if (StringUtils.isEmpty(tenantCode))
			throw new Exception("tenantCode is not null");
		/*
		 * if (StringUtils.isEmpty(status)) throw new
		 * Exception("status is not null");
		 */
		String channelCode = data.getChannelCode();
		String businessType = data.getBusinessType();
		String skillQueue = data.getSkillQueue();
		String[] statuses = null;
		if (StringUtils.isNotEmpty(status)) {
			statuses = status.split(",");
		}
		String skillType = data.getSkillType();
		String[] skillTypes = null;
		String[] skillQueues = null;
		if (StringUtils.isNotEmpty(skillType)) {
			skillTypes = skillType.split(",");
		}
		if (StringUtils.isNotEmpty(skillQueue)) {
			skillQueues = skillQueue.split(",");
		}
		info.put("skillTypes", skillTypes);
		info.put("skillQueues", skillQueues);

		info.put("tenantCode", tenantCode);
		info.put("workNo", workNo);
		info.put("statuses", statuses);
		info.put("channelCode", channelCode);
		info.put("businessType", businessType);
		info.put("skillQueue", skillQueue);
		info.put("sendAccount", sendAccount);
		info.put("pageSize", data.getPageSize());
		info.put("pageNum", data.getPageNum());
		info.put("beginDate", data.getBeginDate());
		info.put("endDate", data.getEndDate());
		info.put("sessionId", sessionId);
		return sessionInfoDao.getSessionListCount(info);
	}

	@Override
	public void removeWorkNo(SessionData data) throws Exception {
		String sessionId = data.getSessionId();
		RedisLock lock = RedisLock.getRedisLock("SESSION_" + sessionId);
		while (true) {
			if (lock.lock()) {
				try {
					SessionInfo info = getSession(data);
					String workNo = data.getWorkNo();
					if (StringUtils.isEmpty(workNo))
						throw new Exception("workNo is not null");
					String workNos = info.getWorkNos();
					if (StringUtils.isEmpty(workNos))
						throw new Exception("sessionId :{"
								+ info.getSessionId() + "} workNO is null");
					info.removeWorkNo(workNo);
					String workNos2 = info.getWorkNos();
					if (workNos2.split(",").length == 1)
						info.setSessionType(SessionType.COMMON.getName());
					Map<String, Object> sessionMap = new HashMap<String, Object>();
					sessionMap.put("sessionId", info.getSessionId());
					sessionMap.put("workNo", workNo);
					sessionInfoDao.deleteSessionWorkNo(sessionMap);
					updateSession(info);
					cacheSession(sessionId, info);
					/*
					 * if(!info.getWorkNos().equals(workNos)){
					 * reduceSeesionCount
					 * (info.getTenantCode(),info.getSkillQueue
					 * (),workNo,info.getSkillType()); }
					 */
				} finally {
					lock.unlock();
				}
				break;
			}
		}
	}

	@Override
	public Map<String, Object> getSortItem(List<SessionSortItem> items)
			throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, Object> skillQueueMap = new HashMap<String, Object>();
		Map<String, Object> channelCodeMap = new HashMap<String, Object>();
		if (items != null) {
			for (SessionSortItem item : items) {
				if (StringUtils.isNotEmpty(item.getSkillQueue())) {
					addSkillSortItem(item, skillQueueMap);
				}

				addChannelSortItem(item, channelCodeMap);
			}
		}
		map.put("skillQueueMap", skillQueueMap);
		map.put("channelCodeMap", channelCodeMap);
		return map;
	}

	@Override
	public void cacheSession(String sessionId, SessionInfo info) {
		String sessionKey = sessionId.substring(0, sessionId.length() - 13);
		// 防止渠道切换
		String oldSesionKey = info.getSessionModel();
		if (!sessionKey.equals(oldSesionKey)) {
			cacheSessionBySessionKey(oldSesionKey, info);
		}
		cacheSessionBySessionKey(sessionKey, info);
	}

	public void cacheSessionBySessionKey(String sessionKey, SessionInfo info) {
		if (sessionKey.startsWith("SESSION_"))
			redisDao.saveValue(sessionKey, info);
		else
			redisDao.saveValue("SESSION_" + sessionKey, info);
	}

	/**
	 * 根据缓存key获取sessionInfo
	 *
	 * @param sessionKey
	 * @return
	 */
	public SessionInfo readCacheSessionBySessionKey(String sessionKey) {
		Object obj = null;
		if (sessionKey.startsWith("SESSION_"))
			obj = redisDao.readValue(sessionKey);
		else
			obj = redisDao.readValue("SESSION_" + sessionKey);
		if (obj != null && obj instanceof SessionInfo)
			return (SessionInfo) obj;
		return null;
	}

	@Override
	public void quitSessionSort(SessionData data) throws Exception {
		SessionInfo info = this.getSession(data);
		if (info == null) {
			logger.error("退出排队失败，改会话id不存在：{}" + data.getSessionId());
			throw new Exception("sessionId is not exist");
		}
		data.setStatus(SessionStatus.USER_CLOSE.getCode());
		((SessionInfoService) AopContext.currentProxy())
				.updateSessionStatus(data);
		this.quitMirSort(info);
	}

	public void quitMirSort(SessionInfo info) throws Exception {
		SysParam param = sysInitService.getSysParam(Constants.MIR_ADDRESS);
		String mirAddress = param.getParamValue();
		String pushUrl = mirAddress + quitSessionSort;
		JSONObject textJson = new JSONObject();
		textJson.put("sessionId", info.getSessionId());
		textJson.put("skillQueue", info.getSkillQueue());
		String msgBody = textJson.toJSONString();
		commonSendService.sendMsg(pushUrl, msgBody, PARAMTYPE.POST);
	}

	/**
	 * 根据会话ID获取sessionInfo
	 *
	 * @param sessionId
	 * @return
	 */
	public SessionInfo readCacheSessionBySessionId(String sessionId) {
		String sessionKey = sessionId.substring(0, sessionId.length() - 13);
		SessionInfo info = readCacheSessionBySessionKey(sessionKey);
		if (info != null) {
			String realSessionKey = info.getSessionModel();
			if (!sessionKey.equals(realSessionKey)) {
				info = readCacheSessionBySessionKey(realSessionKey);
			}
		}
		return info;
	}

	@Override
	public List<SkillQueueWorkNo> getSkillWorkNo() {
		return sessionInfoDao.getSkillWorkNo();
	}

	@Override
	public int getSkillWorkNoSessionCount(SkillQueueWorkNo skillQueueWorkNo) {
		return sessionInfoDao.getSkillWorkNoSessionCount(skillQueueWorkNo);
	}

	@Override
	public List<SessionInfo> queryNotClosedTask(String workno) {
		return sessionInfoDao.queryNotClosedTask(workno);
	}

	@Override
	public List<SessionInfo> queryNotConclusionTask(String workno) {
		return sessionInfoDao.queryNotConclusionTask(workno);
	}

	@Override
	public Map<String, String> getPendingTask(String workNo) {
		return sessionInfoDao.getPendingTask(workNo);
	}

	@Override
	public void addCheckTimeOutActiveLog(TimeOutSession tos) {
		try {

			sessionTimeoutLogDao.checkTimeOutActiveLog(tos);
		} catch (Exception e) {
			logger.error("记录超时动作日志异常", e);
		}

	}

}
