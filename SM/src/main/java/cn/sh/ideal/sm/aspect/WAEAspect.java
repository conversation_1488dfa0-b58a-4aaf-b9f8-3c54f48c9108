package cn.sh.ideal.sm.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import cn.sh.ideal.init.SysInitService;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.sm.service.SessionInfoService;
import cn.sh.ideal.util.NetUtil;
/**
 * WAE rest api
 * <AUTHOR>
 *
 */
//@Aspect
//@Component
public class WAEAspect {
private static final Logger logger = LoggerFactory.getLogger(WAEAspect.class);
	@Autowired
	private SysInitService initService;
	
	@Autowired
	private SessionInfoService sis;
	
	@Autowired
    private SessionInfoService sessionInfoService;
	
	@Value("#{config['wae.providerId']}")
	private String providerId = "";
	
	@Value("#{config['wae.endWork']}")
	private String endWorkUrl = "";
	
	private  final String WAE_ADDRESS = "WAE_ADDRESS";
	private final String WAE_ROUTE_RULE = "waeMessageHandler";
	/**
	 * end resource work pointcut
	 */
	@Pointcut("execution(* cn.sh.ideal.mir.session.service.SessionInfoService.updateSessionStatus(..))")
	public void workEnd(){
	}
	
	@Around("workEnd()")
	public void doWorkEnd(ProceedingJoinPoint joinPoint) throws Throwable{
		SessionData data = (SessionData)joinPoint.getArgs()[0];
		SessionInfo session = sessionInfoService.getSession(data);
		String sessionId = session.getSessionId();
		String workNo = session.getWorkNos();
		
		joinPoint.proceed();
		logger.info("worknos:{},status:{}",workNo,data.getStatus());
		if(StringUtils.isEmpty(workNo)) return;
		workNo = workNo.split(",")[0];
		
		String routeRule = "";
		try{
			routeRule = initService.getTenantInfo(session.getTenantCode()).getRouteImpl().getRuleCode();
		}catch(Exception e){
			return;
		}
		if(!WAE_ROUTE_RULE.equals(routeRule)) return;
		
		String status = data.getStatus();
		if("7".equals(status) || "9".equals(status) || "10".equals(status)){
			String result = "fail";
			
			try{
				String url = initService.assemblyUrl(WAE_ADDRESS, String.format(endWorkUrl, providerId,workNo,sessionId));
				int respCode = NetUtil.codeSend(url, "DELETE", "","text/html");
				if(200 == respCode){
					result = "success";
				}
			}catch(Exception e){
				result = "fail";
			}
			
			logger.info("end resource:{} work:{} on work item result:{}",workNo,sessionId,result);
		}
	}
}
