package cn.sh.ideal.sm.service;

import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionInfo;

public interface CommonSendService {

	/**
     * 
     * sendMsgAg:(发消息到坐席). <br/>
     *
     * <AUTHOR>
     * @param messageBody
     * @param paramType
     * @param tenantCode
     * @param workNo
     * @return 
     * @return String
     * @since JDK 1.6
     */
	public String sendMsgAg(String messageBody,PARAMTYPE paramType,String tenantCode, String workNo);
	
	
	public String sendCustomerInfo(String requestBody,PARAMTYPE paramType);
	/**
	 * 
	 * tranText:(转换微易信文本消息). <br/>
	 *
	 * <AUTHOR>
	 * @param msg
	 * @param channelCode
	 * @return 
	 * @return String
	 * @since JDK 1.6
	 */
	public MessageInfo tranText(SessionInfo info,String msg,String workNo,String sendType);
	/**
	 * 发送消息给网关
	 * @param messageBody
	 * @return
	 */
	public String sendMsgMGW(MessageInfo info,PARAMTYPE paramType);
	
	/**
	 * 发送消息
	 * @param url 发送地址
	 * @param info 发送消息体
	 * @return
	 */
	public String sendMsg(String url,String messageBody,PARAMTYPE paramType);
	
	public static enum PARAMTYPE{
		POST,GET
	}
}
