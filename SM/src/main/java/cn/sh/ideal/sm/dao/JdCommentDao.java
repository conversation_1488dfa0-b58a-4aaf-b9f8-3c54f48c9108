/**
 * 
 */
package cn.sh.ideal.sm.dao;

import cn.sh.ideal.sm.model.JdComment;

/**
 * @project SM
 * @Package cn.sh.ideal.sm.dao
 * @typeName CommentDao
 * <AUTHOR>
 * @Description:  
 * @date 2016年7月26日 下午5:49:59
 * @version 
 */
public interface JdCommentDao {

	JdComment queryCommentById(String object);

	/** @Description 
	 */
	void insert(JdComment comment);

	/** @param comment 
	 * @Description 
	 */
	int isExist(JdComment comment);

}
