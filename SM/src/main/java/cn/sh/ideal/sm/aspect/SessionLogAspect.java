/**
 * Project Name:SM Maven Webapp
 * File Name:SessionLogAspect.java
 * Package Name:cn.sh.ideal.mir.session.aspect
 * Date:2014年12月20日下午4:54:45
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.aspect;


import java.util.Date;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionInfo.ChangeStatus;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.sm.model.SessionEventInfo;
import cn.sh.ideal.sm.model.SessionStatusLog;
import cn.sh.ideal.sm.service.SessionEventService;
import cn.sh.ideal.sm.service.SessionInfoService;
import cn.sh.ideal.sm.service.SessionLogService;

/**
 * ClassName:SessionLogAspect <br/>
 * Function: 定义添加日志的切面，记录会话状态以及基本信息的变更
 * Date:     2014年12月20日 下午4:54:45 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Component("sessionLogAspect")
public class SessionLogAspect {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private SessionLogService sessionLogService;
    @Autowired
    private SessionEventService sessionEventService;
    @Autowired
    private SessionInfoService sessionInfoService;

    
    
    /**
     * 
     * addSessionLog:(添加会话状态更新日志). <br/>
     *
     * <AUTHOR>
     * @param pjpParam
     * @return
     * @throws Throwable 
     * @return Object
     * @since JDK 1.6
     */
    public Object addSessionLog(ProceedingJoinPoint pjpParam) throws Throwable{
       
     
        Signature sig=pjpParam.getSignature();
      
        if(sig instanceof MethodSignature){
            Object[] args = pjpParam.getArgs();
            Object o= args[0];
            logger.info(sig.getName()+"access sessionLog apsect");
            //方法参数为 SessionData
            if(o instanceof SessionData){
                
                
                SessionData data = (SessionData) o;
                SessionStatusLog log =new SessionStatusLog();
                
                String status = data.getStatus();
                String sessionId = data.getSessionId();
                String acceptedAccount = data.getAcceptedAccount();
                String businessType = data.getBusinessType();
                String channelCode = data.getChannelCode();
                String skillQueue =null;
                if(data.getData() != null && data.getData().get("skillQueue") !=null)  skillQueue= (String) data.getData().get("skillQueue");
                
                if(StringUtils.isEmpty(businessType) && data.getData() != null && data.getData().get("businessType")!=null) businessType = (String) data.getData().get("businessType");
                String workNo = null;
                if(data.getData() !=null && data.getData().get("workNo") != null ) workNo = (String) data.getData().get("workNo");
                if(StringUtils.isEmpty(skillQueue)) skillQueue = data.getSkillQueue();
                
                String sendAccount = data.getSendAccount();
                if(StringUtils.isEmpty(skillQueue)) data.getSkillQueue();
                String tenantCode = data.getTenantCode();
                int time = data.getTime();
                Object extTime = null;
                if(data.getData() != null) extTime = data.getData().get("time");
                if(time<=0 && extTime != null) time = (Integer) data.getData().get("time");
                
                
                
                Map<String, Object> dataMap = data.getData();
                
                if(dataMap != null){
                    
                    if(StringUtils.isEmpty(sendAccount)&&dataMap.get("sendAccount")!=null){
                        sendAccount = (String) dataMap.get("sendAccount")  ;
                        data.setSendAccount(sendAccount);
                    }
                        
                    if(StringUtils.isEmpty(acceptedAccount)&&dataMap.get("acceptedAccount")!=null){
                        acceptedAccount = (String) dataMap.get("acceptedAccount")  ;
                        data.setAcceptedAccount(acceptedAccount);
                    }
                       
                    if(StringUtils.isEmpty(channelCode)&&dataMap.get("channelCode")!=null) {
                        channelCode = (String) dataMap.get("channelCode")  ;
                        data.setChannelCode(channelCode);
                    }
                        
                }
                
                if(time>0){
                    long now = new Date().getTime()+time;
                    Date extDate = new Date(now);
                    log.setExtTime(extDate);
                }
                if(StringUtils.isEmpty(workNo)) workNo = data.getWorkNo();
                SessionInfo sessionInfo = sessionInfoService.getSession(data);

                sessionEventService.doEvent(data,sessionInfo,SessionEventInfo.SessionEvent.BEFORE);
                Object o2 =pjpParam.proceed();
                sessionEventService.doEvent(data,sessionInfo,SessionEventInfo.SessionEvent.AFTER);
                
                if(o2 instanceof SessionInfo){
                    sessionId = ((SessionInfo) o2).getSessionId();
                    status = SessionStatus.SELF.getCode();
                }
                
                log.setSessionId(sessionId);
                log.setTenantCode(tenantCode);
                log.setChannelCode(channelCode);
                log.setAcceptedAccount(acceptedAccount);
                log.setSendAccount(sendAccount);
                log.setSessionStatus(status);
                log.setBusinessType(businessType);
                log.setSkillQueue(skillQueue);
                log.setWorkNo(workNo);
                log.setCreateTime(new Date());
                log.setChangeStatus(ChangeStatus.NO.getCode());
               
                sessionLogService.add(log);
                
                return o2;
                //方法参数为 SessionInfo
            }else if(o instanceof SessionInfo){
                SessionStatusLog log =new SessionStatusLog();
                Object o2 =pjpParam.proceed();
                if(o2 instanceof SessionInfo){
                    SessionInfo info = (SessionInfo) o2;
                    String status = info.getStatus();
                    String sessionId = info.getSessionId();
                    String acceptedAccount = info.getAcceptedAccount();
                    String businessType = info.getBusinessType();
                    String channelCode = info.getChannelCode();
                    String skillQueue =info.getSkillQueue();
                    String sendAccount = info.getSendAccount();
                    String workNo = info.getWorkNos();
                   
                    String tenantCode = info.getTenantCode();
                    Date extDate = new Date();
                    
                    log.setSessionId(sessionId);
                    log.setTenantCode(tenantCode);
                    log.setChannelCode(channelCode);
                    log.setAcceptedAccount(acceptedAccount);
                    log.setSendAccount(sendAccount);
                    
                    log.setSessionStatus(status);
                    log.setBusinessType(businessType);
                    log.setSkillQueue(skillQueue);
                    
                    log.setExtTime(extDate);
                    log.setWorkNo(workNo);
                    log.setCreateTime(new Date());
                    log.setChangeStatus(info.getChangeStatus());
                   /* sessionLogService.add("session.SessionInfoMapper.insertSessionLog", log);*/
                    sessionLogService.add(log);
                    return o2;
                }else{
                   logger.warn("return type:"+o2.getClass()+" nonsupport！"); 
                }
                
                
                
            }
            logger.warn("request type:"+o.getClass()+" nonsupport！"); 
        }
        return null;
   
    }
    

}
