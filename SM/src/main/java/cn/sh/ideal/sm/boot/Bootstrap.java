package cn.sh.ideal.sm.boot;

import java.io.FileNotFoundException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.util.Log4jConfigurer;

import com.google.common.util.concurrent.AbstractIdleService;

/**
 * SM模块启动入口
 * <AUTHOR>
 * @package cn.sh.ideal.session.boot
 * @date 16-3-11
 */

public class Bootstrap extends AbstractIdleService {

    private ClassPathXmlApplicationContext context;
    private static Logger log = LoggerFactory.getLogger(Bootstrap.class);
    static {
		try {
			Log4jConfigurer.initLogging("classpath:conf/log4j.properties");
		} catch (FileNotFoundException ex) {
			System.err.println("Cannot Initialize log4j");
		}
	}
    
    public static void main(String[] args) {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.startAsync();
        try {
            Object lock = new Object();
            synchronized (lock) {
                while (true) {
                    lock.wait();
                }
            }
        } catch (InterruptedException ex) {
            log.error("ignore interruption");
        }
    }

    @Override
    protected void startUp() throws Exception {
        context = new ClassPathXmlApplicationContext(new String[]{"spring/spring.xml"});
        context.start();
        context.registerShutdownHook();
        log.info("session-manager service started successfully!");
    }

    @Override
    protected void shutDown() throws Exception {
        context.stop();
        log.info("session-manager service stop successfully!");
    }
}
