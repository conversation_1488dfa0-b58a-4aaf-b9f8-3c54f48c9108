/**
 * Project Name:<PERSON> Maven Webapp
 * File Name:MessageInfoServiceImpl.java
 * Package Name:cn.sh.ideal.mir.session.service.impl
 * Date:2015年3月9日上午10:29:53
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionMessage;
import cn.sh.ideal.sm.dao.MessageInfoDao;
import cn.sh.ideal.sm.service.MessageInfoService;
import cn.sh.ideal.sm.utils.Constants;
import cn.sh.ideal.util.DateUtils;

/**
 * ClassName:MessageInfoServiceImpl <br/>
 * Function: 新的消息表ServiceImpl
 * Date:     2015年3月9日 上午10:29:53 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Service("messageInfoService")
public class MessageInfoServiceImpl extends BaseServiceImpl<MessageInfo> implements MessageInfoService{
	
	 private static final Logger logger = LoggerFactory.getLogger(MessageInfoServiceImpl.class);
    @Autowired
    private MessageInfoDao messageInfoDao;

    @Override
    public Map<String, Object> getHistoryMsgs(MessageInfo info) throws Exception{
        Map<String, Object> map = new HashMap<String, Object>();
        List<MessageInfo> msgList;
        int msgCount;
       // msgList = this.getAllList("session.MessageMapper.queryMsgList", info);  
      
        msgList = this.getAllList(info); 
       // msgCount = this.getCount("session.MessageMapper.queryMsgCount", info);  
        msgCount = this.getCount(info);        
        map.put("total", msgCount);
        map.put("data", msgList);        
        return map;
    }

    /**
     * 
     * @param getLastHistoryMegsData(获取上一次的消息数据)
     * @return
     * @throws Exception
     */
    @Override
    public List<MessageInfo> getLastHistoryMegsData(Map<String, Object> mapHistoryMsgs,Integer lastSize)
    		throws Exception {
    	try {
    		//1：根据条件查处历史会话消息，根据消息取出渠道、发送账号、接受账号、租户号、sessionId
    		MessageInfo lastMesInfo = getLastHistoryMessageInfo(mapHistoryMsgs);
    		List<MessageInfo> lastMesList = new ArrayList<MessageInfo>();
    		MessageInfo lastSecondMesInfo = new MessageInfo();
    		String lastSessionId = "";
    		Integer pageSize = lastSize == null? Constants.PAGESIZETWO : lastSize;//上一次会话展示消息数量处理
    		//校验返回对象
    		if ( StringUtils.isEmpty(lastMesInfo.getSessionId())||StringUtils.isEmpty(lastMesInfo.getTenantCode())
    				|| StringUtils.isEmpty(lastMesInfo.getAcceptedAccount())
    				|| StringUtils.isEmpty(lastMesInfo.getSendAccount())
    				|| StringUtils.isEmpty(lastMesInfo.getChannelCode())) {
    			throw new RuntimeException("=========MessageInfoServiceImpl.getLastHistoryMegsData========,获取上一次历史消息失败【sessionId:"
    					+ lastMesInfo.getSessionId()
    					+ ",TenantCode:"
    					+ lastMesInfo.getTenantCode()
    					+ ",AcceptedAccount:"
    					+ lastMesInfo.getAcceptedAccount()
    					+ ",SendAccount:"
    					+ lastMesInfo.getSendAccount() 
    					+ ",ChannelCode:"
    					+ lastMesInfo.getChannelCode() + "】");
    		}

			//2.根据渠道、发送账号、接受账号、租户号，sessionId,找出上一次会话的会话ID，分页处理
    		lastSessionId = getLastSessionId(lastMesInfo);
    		logger.info("上一次会话ID为："+lastSessionId);
    		//3。封装参数
    		lastSecondMesInfo.setSessionId(lastSessionId);
    		lastSecondMesInfo.setAcceptedAccount(lastMesInfo.getAcceptedAccount());
    		lastSecondMesInfo.setSendAccount(lastMesInfo.getSendAccount());
    		lastSecondMesInfo.setChannelCode(lastMesInfo.getChannelCode());
    		lastSecondMesInfo.setTenantCode(lastMesInfo.getTenantCode());
    			//分页数据、上一次历史消息第一页，和第N行
    		lastSecondMesInfo.setPageNum(Constants.PAGEFIRST);
    		
    		lastSecondMesInfo.setPageSize(pageSize);
    		//消息倒序
    		lastSecondMesInfo.setOrderByRule(Constants.DESC);
    		
			//4.根据会话ID，查询历史会话消息。
    		lastMesList = this.getAllList(lastSecondMesInfo);
			
			return lastMesList;
		} catch (Exception e) {
			logger.info(e.getMessage());
			return null;
		}
    }
    

    /**
     * 获取上一次会话ID
     * @return
     * @throws Exception
     */
	public String getLastSessionId(MessageInfo lastMesInfo) throws Exception {
		//根据sessionId，取出创建时间
		String createTime = messageInfoDao.getSessionCreateTime(lastMesInfo);
		Date createTimeDate = DateUtils.str2Date(createTime, "yyyy-MM-dd HH:mm:ss");
		lastMesInfo.setCreateTime(createTimeDate);
		return messageInfoDao.getLastSessionId(lastMesInfo);
	}

    
    
    /**
     * 根据条件查处历史会话消息，根据消息取出渠道、发送账号、接受账号、租户号、sessionId
     * @param mapHistoryMsgs
     */
    private MessageInfo getLastHistoryMessageInfo(Map<String, Object> mapHistoryMsgs){
    	MessageInfo lastMesInfo = new  MessageInfo();
    	//没有数据，返回空
    	if(StringUtils.isEmpty(String.valueOf(mapHistoryMsgs.get(Constants.DATA)))){
    		return lastMesInfo;
    	}
    	//解析mapHistoryMsgs对象
    	Object data = mapHistoryMsgs.get(Constants.DATA);
    	List<MessageInfo> mesInfoList = JSONObject.parseArray(JSONObject.toJSONString(data), MessageInfo.class);
//    	List<MessageInfo> mesInfoList = (List<MessageInfo>)mapHistoryMsgs.get(Constants.DATA);
    	//取出需要的数据
    	for(MessageInfo mesInfoEntity : mesInfoList){
    		if(Integer.valueOf(mesInfoEntity.getSource()) == Constants.CUSTOMER){
    			lastMesInfo.setSendAccount(mesInfoEntity.getSendAccount());
    			lastMesInfo.setChannelCode(mesInfoEntity.getChannelCode());
    			lastMesInfo.setAcceptedAccount(mesInfoEntity.getAcceptedAccount());
    			lastMesInfo.setTenantCode(mesInfoEntity.getTenantCode());
    			lastMesInfo.setSessionId(mesInfoEntity.getSessionId());
    			break;
    		}else if(Integer.valueOf(mesInfoEntity.getSource()) == Constants.AGENT){
    			lastMesInfo.setSendAccount(mesInfoEntity.getAcceptedAccount());
    			lastMesInfo.setChannelCode(mesInfoEntity.getChannelCode());
    			lastMesInfo.setAcceptedAccount(mesInfoEntity.getSendAccount());
    			lastMesInfo.setTenantCode(mesInfoEntity.getTenantCode());
    			lastMesInfo.setSessionId(mesInfoEntity.getSessionId());
    			break;
    		}
    	}
    	return lastMesInfo;
    }
    
    @Override
   public MessageInfo getLastMessage(Map<String, Object> map) throws Exception {
        
        return messageInfoDao.getLastMessage(map);
    }


    @Override
    public Map<String, List<MessageInfo>> getUnfinishSessionMsgs(SessionData data)
            throws Exception {
        String tenantCode = data.getTenantCode();
        String skillType = data.getSkillType();
        if (StringUtils.isEmpty(tenantCode))
            throw new Exception("tenantCode is  null");
        String workNo = data.getWorkNo();
        if (StringUtils.isEmpty(workNo))
            throw new Exception("workNo is  null");

        Map<String, Object> session = new HashMap<String, Object>();
        session.put("tenantCode", tenantCode);
        session.put("workNo", workNo);
        session.put("sessionStatus", data.getStatus());
        session.put("sessionId", data.getSessionId());
        if(StringUtils.isNotEmpty(skillType)){
            String[] skillTypes = skillType.split(",");
            session.put("skillTypes",skillTypes);
        }
        List<SessionMessage> sessionMessages = messageInfoDao.getSessionMessages(session);
        Map<String, List<MessageInfo>> messages = new HashMap<String,List<MessageInfo>>();
        if(sessionMessages != null){
            for(SessionMessage sessionMessage : sessionMessages){
                messages.put(sessionMessage.getSessionId(), sessionMessage.getMessageInfos());
            }
        }
        return messages;
    }

   

}
