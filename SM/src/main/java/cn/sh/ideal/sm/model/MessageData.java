/**
 * Project Name:<PERSON> Maven Webapp
 * File Name:MessageData.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月15日下午8:17:17
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;
/**
 * ClassName:MessageData <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2014年12月15日 下午8:17:17 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class MessageData {
    /*会话ID*/
    private String sessionId;
    
    /*工号*/
    private String workNo;
    
    /*账号*/
    private String account;
    
    /*消息内容关键字*/
    private String contentMsg;
    
    /*开始时间*/
    private String beginTime;
    
    /*结束时间*/
    private String endTime;
    
    /*记录开始游标*/
    private int dataFrom;
    
    /*查询数目*/
    private int dataNum;
    
    private String channelCode;
    
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getWorkNo() {
        return workNo;
    }
    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }
    public String getAccount() {
        return account;
    }
    public void setAccount(String account) {
        this.account = account;
    }
    public String getContentMsg() {
        return contentMsg;
    }
    public void setContentMsg(String contentMsg) {
        this.contentMsg = contentMsg;
    }
    public String getBeginTime() {
        return beginTime;
    }
    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }
    public String getEndTime() {
        return endTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    public int getDataFrom() {
        return dataFrom;
    }
    public void setDataFrom(int dataFrom) {
        this.dataFrom = dataFrom;
    }
    public int getDataNum() {
        return dataNum;
    }
    public void setDataNum(int dataNum) {
        this.dataNum = dataNum;
    }
    public String getChannelCode() {
        return channelCode;
    }
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
    @Override
    public String toString() {
        return "MessageData [sessionId=" + sessionId + ", workNo=" + workNo
                + ", account=" + account + ", contentMsg=" + contentMsg
                + ", beginTime=" + beginTime + ", endTime=" + endTime
                + ", dataFrom=" + dataFrom + ", dataNum=" + dataNum
                + ", channelCode=" + channelCode + "]";
    }
    
   
    

}
