package cn.sh.ideal.sm.dubbo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.model.BaseResponse;
import cn.sh.ideal.model.RespCode;
import cn.sh.ideal.model.Session;
import cn.sh.ideal.model.SessionData;
import cn.sh.ideal.model.SessionInfo;
import cn.sh.ideal.model.SessionStatus;
import cn.sh.ideal.sm.dao.MessageInfoDao;
import cn.sh.ideal.sm.exception.SessionOperationException;
import cn.sh.ideal.sm.resp.FlagModel;
import cn.sh.ideal.sm.resp.RespAddMessage;
import cn.sh.ideal.sm.resp.RespAddWorkNo;
import cn.sh.ideal.sm.resp.RespCreateInitiativeSession;
import cn.sh.ideal.sm.resp.RespCreateSession;
import cn.sh.ideal.sm.resp.RespGetFinishSessionMsgs;
import cn.sh.ideal.sm.resp.RespGetPendingTask;
import cn.sh.ideal.sm.resp.RespGetSession;
import cn.sh.ideal.sm.resp.RespGetSessionActiveFlag;
import cn.sh.ideal.sm.resp.RespGetSessionCount;
import cn.sh.ideal.sm.resp.RespGetSessionList;
import cn.sh.ideal.sm.resp.RespGetSessionSortCount;
import cn.sh.ideal.sm.resp.RespGetSessionStatus;
import cn.sh.ideal.sm.resp.RespQuitSessionSort;
import cn.sh.ideal.sm.resp.RespRemoveSessionCache;
import cn.sh.ideal.sm.resp.RespRemoveWorkNo;
import cn.sh.ideal.sm.resp.RespUpdateSession;
import cn.sh.ideal.sm.resp.RespUpdateSessionAgActive;
import cn.sh.ideal.sm.resp.RespUpdateSessionUserActive;
import cn.sh.ideal.sm.resp.SessionSortItem;
import cn.sh.ideal.sm.resp.SessionStatusModel;
import cn.sh.ideal.sm.service.SessionInfoService;
import cn.sh.ideal.sm.service.SessionService;
import cn.sh.ideal.sm.service.SessionTimeoutService;
import cn.sh.ideal.sm.service.SessionTipService;
import cn.sh.ideal.sm.utils.CallStatus;
import cn.sh.ideal.sm.utils.Constants;
import cn.sh.ideal.sm.utils.Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

@Service("sessionService") 
public class SessionServiceImpl implements SessionService {
	
	private static final Logger logger = LoggerFactory.getLogger(SessionServiceImpl.class);
	
	@Autowired
	private SessionInfoService sessionInfoService;
	
	@Autowired
	private MessageInfoDao messageInfoDao;
	@Autowired
	private RedisDao<String, Serializable> redisDao;
	@Autowired
	SessionTimeoutService sessionTimeoutService;
	@Autowired
	SessionTipService sessionTipService;

	@Override
	public RespCreateSession createSession(SessionData sessionData) {
		
		  try {
	            logger.info("【request】创建会话-------请求参数：" + sessionData.toString());
	        
	            /*校验基本数据是否完整*/
	            Utils.checkSessionDate(sessionData);
	            if(StringUtils.isEmpty(sessionData.getCallStatus())){
	                sessionData.setCallStatus(CallStatus.IN.getCode());
	            }

	            //会话创建处理逻辑
	            SessionInfo session = sessionInfoService.createSession(sessionData);

	            Map<String, Object> data = new HashMap<String, Object>();

	            if (session == null) throw new Exception("create session fail!");
	            
	            logger.info("【response】创建会话-------请求参数：" + JSONObject.toJSONString(session));
	            return new RespCreateSession(Constants.SUCCESS,Constants.SUCCESS_MSG,session);
	        } catch (Exception e) {
	            logger.error("创建会话异常！", e);
	            return new RespCreateSession(Constants.FAILED,Constants.FAILED_MSG,null);
	        }
	    }

	
	
	
	
	
	/**
	 * 创建主动会话
	 */
	public RespCreateInitiativeSession createInitiativeSession(SessionData sessionData) {
		RespCreateInitiativeSession re =  new RespCreateInitiativeSession(Constants.FAILED,Constants.FAILED_MSG,null,null);
		 List<SessionInfo> sessionInfos = new ArrayList<SessionInfo>();
         List<String> failAccounts = new ArrayList<String>();
		  try {
	            logger.info("创建自动发起会话-------请求参数：" + sessionData.toString());
	           
	            /*校验基本数据是否完整*/
	            Utils.checkSessionDate(sessionData);
	            sessionData.setCallStatus(CallStatus.OUT.getCode());
	            sessionInfos = sessionInfoService.batchCreateInitiativeSession(sessionData,failAccounts);
	            return new RespCreateInitiativeSession(Constants.SUCCESS,Constants.SUCCESS_MSG, sessionInfos,failAccounts);
	        } catch (Exception e) {
	            if (e instanceof SessionOperationException) {
	                //add 2015-5-29 添加修复重复分配功能
	                //  if("status resubmit".equals(e.getMessage())){
	            	 re =  new RespCreateInitiativeSession(Constants.FAILED,Constants.FAILED_MSG,sessionInfos,failAccounts);
	            	re.setResultCode(Constants.RESPONSE_RESUBMIT);
	                //}
	            } else {
	            	re =  new RespCreateInitiativeSession(Constants.FAILED,Constants.FAILED_MSG,sessionInfos,failAccounts);
	            }
	            logger.error("创建主动发起会话异常！", e);
	        return re;
	        }
	}

	@Override
	public RespGetSession getSession(SessionData sessionData) {
		  SessionInfo sessionInfo = null;
		 
		  try {
			  
	            logger.info("【request】获取会话-------请求参数：" + sessionData.toString());
	            Utils.checkSessionDate(sessionData);
	            sessionInfo = sessionInfoService.getSession(sessionData);
	            RespGetSession result = new RespGetSession(Constants.SUCCESS,Constants.SUCCESS_MSG, null);
	            
	            if (sessionInfo == null) {
	            	result.setResultCode(Constants.FAILED);
	            	result.setResultMsg("can't find session!");
	            	
	            } else {
	            	result.setData(sessionInfo);
	            }
	            logger.info("【response】获取会话-------返回参数：" + JSONObject.toJSONString(result));
	            return result;
	        } catch (Exception e) {
	            logger.error("获取会话信息异常！", e);
	            return new RespGetSession(Constants.FAILED,Constants.FAILED_MSG,null);
	        }
	}

	@Override
	public RespGetSessionActiveFlag getSessionActiveFlag(SessionData sessionData) {
		 try {
	            logger.info("获取会话可发送状态-------请求参数：" + sessionData.toString());
	            int flag = sessionInfoService.getSessionActiveFlag(sessionData);
	            return new RespGetSessionActiveFlag(Constants.SUCCESS,Constants.SUCCESS_MSG,flag);

	        } catch (Exception e) {
	            logger.error("获取会话可发送状态异常！", e);
	            return new RespGetSessionActiveFlag(Constants.FAILED,"获取会话可发送状态异常！",null);
	           
	        }
	}

	@Override
	public RespGetSessionList getSessionList(SessionData sessionData) {
		  List<SessionInfo> infos = new ArrayList<SessionInfo>();
	        int total = 0;
	        try {
	            infos = sessionInfoService.getSessionList(sessionData);
	            total = sessionInfoService.getSessionListCount(sessionData);
	            return new RespGetSessionList(Constants.SUCCESS,Constants.SUCCESS_MSG,infos);
	        } catch (Exception e) {
	            logger.error("获取会话信息列表异常！", e);
	            return new RespGetSessionList(Constants.FAILED,"获取会话信息列表异常！",null);
	        }
	}

	@Override
	public RespGetSessionCount getSessionCount(SessionData sessionData) {
        int count = 0;
        try {
            logger.info("获取会话状态数量-------请求参数：" + sessionData.toString());
            count = sessionInfoService.getSessionCount(sessionData);
            return new RespGetSessionCount(Constants.SUCCESS,Constants.SUCCESS_MSG,count);
        } catch (Exception e) {
            logger.error("查询各个分状态会话数量异常！", e);
           return new RespGetSessionCount(Constants.FAILED,"查询各个分状态会话数量异常！",count);
        }
	}

	@Override
	public RespGetSessionSortCount getSessionSortCount(SessionData sessionData) {
		RespGetSessionSortCount result = new RespGetSessionSortCount(Constants.SUCCESS,Constants.SUCCESS_MSG,null);
		FlagModel flag = new FlagModel();
		Integer count =0;
		Integer time = 0;
		 List<SessionSortItem> items = new ArrayList<SessionSortItem>();
		  Map<String, Object> skillQueueMap = new HashMap<String, Object>();
		  Map<String, Object> channelCodeMap = new HashMap<String, Object>();
		  List<Map<String, Object>> skillQueueSorts = new ArrayList<Map<String, Object>>();
		try {
			 logger.info("获取会话排队数-------请求参数：" + sessionData.toString());
			 
			
			 flag.setFlag(1);
			  count = sessionInfoService.getSessionSortCount(sessionData,flag);
			  time = sessionInfoService.getSessionSortTime(sessionData);
			 Integer sortFlag = flag.getFlag();
			 if(0 == sortFlag){
	             new RespGetSessionSortCount(Constants.OTHER,"该会话已进入人工坐席",null);
			 }
			 if (count > 0) {
	                items = sessionInfoService.getSessionSortItemCount(sessionData);
	                Map<String, Object> map = sessionInfoService.getSortItem(items);
	                skillQueueMap = (Map<String, Object>) map.get("skillQueueMap");
	                channelCodeMap = (Map<String, Object>) map.get("channelCodeMap");
	                if (skillQueueMap != null) {
	                    for (String key : skillQueueMap.keySet()) {
	                        Map<String, Object> skillQeueuSort = new HashMap<String, Object>();
	                        Map<String, Object> skillSortItem = (Map<String, Object>) skillQueueMap.get(key);
	                        if (skillSortItem != null) {
	                            Map<String, Long> sortData = (Map<String, Long>) skillSortItem.get("data");
	                            skillQeueuSort.put("skillQueue", skillSortItem.get("skillQueue"));
	                            List<Object> o = new ArrayList<Object>();
	                            if (sortData != null) {
	                                for (String dataKey : sortData.keySet()) {
	                                    Map<String, Object> itemMap = new HashMap<String, Object>();
	                                    itemMap.put(dataKey, sortData.get(dataKey));
	                                    o.add(itemMap);
	                                }
	                            }
	                            skillQeueuSort.put("data", o);
	                            skillQueueSorts.add(skillQeueuSort);
	                        }

	                    }
	                }

	            }
			  
			 
		} catch (Exception e) {
			 logger.error("查询会话排队数量异常！", e);
			 result.setResultMsg("消息未排队");
			 result.setResultCode(Constants.FAILED);
		}
		
		flag.setCount(count);
		flag.setTime(time);
		flag.setItems(items);
		flag.setSkillQueueSorts(skillQueueSorts);
		
		result.setData(flag);
		
		return result;
	}


	@Override
	public RespGetSessionStatus getSessionStatus(SessionData sessionData) {
		 SessionInfo sessionInfo = null;
	        try {
	            logger.info("获取会话状态-------请求参数：" + sessionData.toString());
	            Utils.checkSessionDate(sessionData);
	            sessionInfo = sessionInfoService.getSession(sessionData);
	            SessionStatusModel  sessionStatus = new SessionStatusModel();
	             if(sessionInfo != null){
	     			sessionStatus.setSessionId(sessionInfo.getSessionId());
	     			sessionStatus.setStatusSymbol(sessionInfo.getStatus());
	     			sessionStatus.setStatusSymbol(sessionInfo.getStatusSymbol());
	     			}
	     			logger.info("获取会话状态：" + sessionStatus.getSessionId()+","+ sessionStatus.getStatus()+","+sessionStatus.getStatusSymbol());
	     			return new RespGetSessionStatus(Constants.SUCCESS,Constants.SUCCESS_MSG,sessionStatus);
	        } catch (Exception e) {
	            logger.error("获取会话信息异常！", e);
	            return new RespGetSessionStatus(Constants.FAILED,Constants.FAILED_MSG,null);
	        }
	}

	@Override
	public RespUpdateSessionAgActive updateSessionAgActive(SessionData sessionData) {
	        try {
	            logger.info("更新坐席最后一次会话活动时间-------请求参数：" + sessionData.toString());
	            Utils.check(sessionData);
	            sessionInfoService.updateSessionAgActive(sessionData);
	            Map<String, Object> data = new HashMap<String, Object>();
	            data.put("sessionId", sessionData.getSessionId());
	            data.put("tenantCode", sessionData.getTenantCode());
	            return new RespUpdateSessionAgActive(Constants.SUCCESS,Constants.SUCCESS_MSG,data);
	        } catch (Exception e) {
	            logger.error("更新坐席最后一次会话活动异常！", e);
	            return new RespUpdateSessionAgActive(Constants.FAILED,Constants.FAILED_MSG,null);
	        }
	}

	@Override
	public RespUpdateSessionUserActive updateSessionUserActive(SessionData sessionData) {
		 try {
	            logger.info("更新用户最后一次会话活动时间-------请求参数：" + sessionData.toString());
	            Utils.check(sessionData);
	            sessionInfoService.updateSessionUserActive(sessionData);
	            Map<String, Object> data = new HashMap<String, Object>();
	            data.put("sessionId", sessionData.getSessionId());
	            data.put("tenantCode", sessionData.getTenantCode());
	            return new RespUpdateSessionUserActive(Constants.SUCCESS,Constants.SUCCESS_MSG,data);
	        } catch (Exception e) {
	            logger.error("更新用户最后一次会话活动异常！", e);
	            return new RespUpdateSessionUserActive(Constants.FAILED,Constants.FAILED_MSG,null);
	        }
	}

	@Override
	public RespAddMessage addMessage(SessionData sessionData) {
		try {
            logger.info("添加消息-------请求参数：" + sessionData.toString());
            sessionInfoService.addMessage(sessionData);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("messageId", sessionData.getMessageId());
           return new RespAddMessage(Constants.SUCCESS,Constants.SUCCESS_MSG,data);
        } catch (Exception e) {
            logger.error("添加消息异常！", e);
            return new RespAddMessage(Constants.SUCCESS,Constants.SUCCESS_MSG,null);
        }
	}

	@Override
	public RespAddWorkNo addWorkNo(SessionData sessionData) {
		try {
			logger.info("添加坐席-------请求参数：" + sessionData.toString());
			Map<String, Object> data = new HashMap<String, Object>();
			sessionInfoService.addWorkNo(sessionData);
			data.put("workNo", sessionData.getWorkNo());
			return new RespAddWorkNo(Constants.SUCCESS,Constants.SUCCESS_MSG,data);
		} catch (Exception e) {
			logger.error("添加坐席工号异常！",e);
			return new RespAddWorkNo(Constants.FAILED,Constants.FAILED_MSG,null);
    }
		}

	@Override
	public RespRemoveWorkNo removeWorkNo(SessionData sessionData) {
		try {
			logger.info("从会话中移除坐席-------请求参数：" + sessionData.toString());
			Map<String, Object> data = new HashMap<String, Object>();
			sessionInfoService.removeWorkNo(sessionData);
			data.put("workNo", sessionData.getWorkNo());
			return new RespRemoveWorkNo(Constants.SUCCESS,Constants.SUCCESS_MSG,data);
		} catch (Exception e) {
			logger.error("从会话中移除坐席异常！",e);
			return new RespRemoveWorkNo(Constants.FAILED,Constants.FAILED_MSG,null);
		} 
	}

	@Override
	public RespUpdateSession updateSession(SessionData sessionData) {
		RespUpdateSession result = null;
        try {
			logger.info("更新会话-------请求参数：" + JSONObject.toJSONString(sessionData));
        	if(sessionData == null ) throw new Exception("参数格式不合法！");
            String sessionId = sessionData.getSessionId();
            String tenantCode = sessionData.getTenantCode();
            if(StringUtils.isEmpty(sessionId)||StringUtils.isEmpty(tenantCode)){
                throw new Exception("sessionId or tenantCode is null");
            }
            sessionInfoService.updateSessionStatus(sessionData);
			if(StringUtils.isNotEmpty(sessionData.getStatus())&& SessionStatus.MANUAL.getCode().equals(sessionData.getStatus())){
				updateSessionAgActive(sessionData);
			}
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("sessionId", sessionData.getSessionId());
            map.put("tenantCode", sessionData.getTenantCode());
            result = new RespUpdateSession(Constants.RESPONSE_OK,Constants.SUCCESS_MSG,map);
        } catch (Exception e) {
            if (e instanceof SessionOperationException) {
            	result = new RespUpdateSession(Constants.RESPONSE_RESUBMIT,e.getMessage(),null);
            } else {
            	result = new RespUpdateSession(Constants.RESPONSE_ERROR,e.getMessage(),null);
            }
            logger.error("更新会话信息异常！", e);
            result = new RespUpdateSession(Constants.FAILED,"更新异常稍后重试！",null);
        }
		logger.info("更新会话-------返回参数：" + JSONObject.toJSONString(result));
        return result;
	}

	@Override
	public RespRemoveSessionCache removeSessionCache(SessionData sessionData) {
		String sessionId = sessionData.getSessionId();
        String tenantCode = sessionData.getTenantCode();
        RespRemoveSessionCache result = new RespRemoveSessionCache(Constants.SUCCESS,Constants.SUCCESS_MSG,null);
        try {
            sessionId = sessionInfoService.removeSessionCache(sessionId, tenantCode);
            if (StringUtils.isNotEmpty(sessionId)) {
                result.setData(sessionId);
            } else {
            	result.setResultCode(Constants.FAILED);
            	result.setResultMsg("session can not find!");
            }
            return result;
        } catch (Exception e) {
            logger.error("根据sessionId 移出会话失败", e);
           return new RespRemoveSessionCache(Constants.FAILED,Constants.FAILED_MSG,null);
        }
	}

	@Override
	public RespGetPendingTask getPendingTask(SessionData sessionData) {
		RespGetPendingTask result = null;
		try {
    	
			List<SessionInfo> notClosedSessions = sessionInfoService.queryNotClosedTask(sessionData.getWorkNo());
        	List<SessionInfo> notConclusionSessions = sessionInfoService.queryNotConclusionTask(sessionData.getWorkNo());
        	Map<String, String> pendingTasks = sessionInfoService.getPendingTask(sessionData.getWorkNo());
    	
    	JSONArray resultObj = new JSONArray();
    	
    	JSONObject notClosedObj = new JSONObject();
    	notClosedObj.put("type", "1");
    	notClosedObj.put("count", notClosedSessions.size());
    	notClosedObj.put("data", notClosedSessions);
    	notClosedObj.put("category", JSONObject.parseObject((JSONObject.toJSONString(pendingTasks).toLowerCase())));
    	
    	

    	JSONObject notConclusionObj = new JSONObject();
    	notConclusionObj.put("type", "2");
    	notConclusionObj.put("count", notConclusionSessions.size());
    	notConclusionObj.put("data", notConclusionSessions);
    	
    	resultObj.add(notClosedObj);
    	resultObj.add(notConclusionObj);
    	
    	result = new RespGetPendingTask(Constants.RESPONSE_OK,Constants.SUCCESS_MSG,resultObj);
    	
    } catch (Exception e) {
        logger.error("获取坐席待处理任务(人工状态会话)数", e);
        result = new RespGetPendingTask(Constants.FAILED,Constants.FAILED_MSG,null);
    }
	return result;
	}

	@Override
	public RespGetFinishSessionMsgs getUnfinishSessionMsgs(SessionData sessionData) {
		logger.info("查询坐席未完成会话消息内容传入参数：" + sessionData.toString());
    	Map<String, Object> sessionMap = null;
    	List<Session> sessions = null;
    	try {
    		sessions = sessionInfoService.getUnfinishSessionMsgs(sessionData);
    		sessionMap = sessionInfoService.unfinishSessionMsgToAg(sessions);
    		logger.info("查询坐席未完成会话消息内容返回参数：" + sessionMap.toString()); 
    		return new RespGetFinishSessionMsgs(Constants.RESPONSE_OK,Constants.SUCCESS_MSG,sessionMap);
    	} catch (Exception e) {
    		logger.error("查询坐席未完成会话消息内容异常", e);
    		return new RespGetFinishSessionMsgs(Constants.FAILED,"查询坐席未完成会话消息内容异常",null);
    	}
	}

	@Override
	public RespQuitSessionSort quitSessionSort(SessionData sessionData) {
		logger.info("退出排队 传入参数："+ sessionData.toString());
        try {
            sessionInfoService.quitSessionSort(sessionData);
            return new RespQuitSessionSort(Constants.RESPONSE_OK,Constants.SUCCESS_MSG);
        } catch (Exception e) {
            logger.error("退出排队 异常", e);
            return new RespQuitSessionSort(Constants.FAILED,"退出排队 异常");
        }
	}
	
	public BaseResponse smInit() throws Exception{
		try{
			sessionInfoService.init();	
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("初始化系统参数异常:",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}
	
	public BaseResponse reloadSessionTip() throws Exception{
		try{
			sessionTimeoutService.init();
			sessionTipService.init();
			return new BaseResponse(RespCode.SUCCESS.getResultCode(), RespCode.SUCCESS.getResultMsg());
		}catch(Exception e){
			logger.error("初始化系统参数异常:",e);
			return new BaseResponse(RespCode.FAIL.getResultCode(), e.getMessage());
		}
	}
	
}
