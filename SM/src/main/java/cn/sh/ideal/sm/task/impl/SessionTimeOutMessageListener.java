package cn.sh.ideal.sm.task.impl;

import cn.sh.ideal.dao.RedisDao;
import cn.sh.ideal.listener.IMessageListener;
import cn.sh.ideal.sm.task.TaskService;
import cn.sh.ideal.util.RedisLock;

import java.io.Serializable;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by martin on 2016/6/30.
 */
public class SessionTimeOutMessageListener implements IMessageListener {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private RedisDao<String, Serializable> redisDao;

	private TaskService taskService;

	public SessionTimeOutMessageListener() {
		super();
	}

	public SessionTimeOutMessageListener(RedisDao<String, Serializable> redisDao, TaskService taskService) {
		super();
		this.redisDao = redisDao;
		this.taskService = taskService;
	}


	@Override
	public void handleMessage(Serializable message, String channel) {

		try {
			String key = message.toString();

			if (key.startsWith("expire_")) {
				
				logger.info("redis-key : {} 超时", key);

				String sessionKey = key.substring(7, key.length() - 13);
				
				RedisLock sessionLock = RedisLock.getRedisLock(sessionKey);
				if (sessionLock.lock()) {

					try {
						taskService.checkTimeout(sessionKey);
					} catch (Exception e) {
						e.printStackTrace();
					} finally {
						sessionLock.unlock();
					}
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
