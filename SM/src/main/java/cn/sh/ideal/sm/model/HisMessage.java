package cn.sh.ideal.sm.model;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

// TODO: Auto-generated Javadoc
/**
 * The Class HisMessage.
 */
public class HisMessage implements Serializable{

	/** The Constant serialVersionUID. */
	private static final long serialVersionUID = 1L;
	
	/**  工号. */
	private String workNo;
	
	/**  客户帐号. */
	private String customerId;
	
	/**  租户ID. */
	private String tenantId;
	
	/**  渠道. */
	private String channel;
	
	/**  回话ID. */
	private String sessionId;
	
	/**   消息类型. */
	private String messageBusiness;
	
	/**  内容消息. */
	private Object contentmsg;
	
	/**  帐号. */
	private String accout;
	
	/**  开始时间. */
	private String begintime;
	
	/**  结束时间. */
	private String endtime;
	
	/**  返回时间. */
	private String timemsg;
	
	/** 发送人帐号. */
	private String sendAccout;
	
	/** 接收人帐号. */
	private String acceptAccout;
	
	
	/** 返回类型 FROM表示MGW_MESSAGE信息 TO表示MGW_MEDIA_SEND信息. */
	private String resultType;
	
	/** 第几条数据. */
	private int datefrom;

	/** 数据量. */
	private int datenum;
	/**
	 * Gets the work no.
	 *
	 * @return the work no
	 */
	public String getWorkNo() {
		return workNo;
	}

	/**
	 * Sets the work no.
	 *
	 * @param workNo the new work no
	 */
	public void setWorkNo(String workNo) {
		this.workNo = workNo;
	}

	/**
	 * Gets the channel.
	 *
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}

	/**
	 * Sets the channel.
	 *
	 * @param channel the new channel
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}

	/**
	 * Gets the session id.
	 *
	 * @return the session id
	 */
	public String getSessionId() {
		return sessionId;
	}

	/**
	 * Sets the session id.
	 *
	 * @param sessionId the new session id
	 */
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	
	/**
	 * Gets the message business.
	 *
	 * @return the message business
	 */
	public String getMessageBusiness() {
		return messageBusiness;
	}

	/**
	 * Sets the message business.
	 *
	 * @param messageBusiness the new message business
	 */
	public void setMessageBusiness(String messageBusiness) {
		this.messageBusiness = messageBusiness;
	}

	/**
	 * Gets the contentmsg.
	 *
	 * @return the contentmsg
	 */
	public Object getContentmsg() {
		return contentmsg;
	}

	/**
	 * Sets the contentmsg.
	 *
	 * @param contentmsg the new contentmsg
	 */
	public void setContentmsg(String contentmsg) {
	    JSONObject json = new JSONObject();
	    @SuppressWarnings("static-access")
        Object jsonContent=  json.parse(contentmsg);
		this.contentmsg = jsonContent;
	}

	/**
	 * Gets the accout.
	 *
	 * @return the accout
	 */
	public String getAccout() {
		return accout;
	}

	/**
	 * Sets the accout.
	 *
	 * @param accout the new accout
	 */
	public void setAccout(String accout) {
		this.accout = accout;
	}

	/**
	 * Gets the begintime.
	 *
	 * @return the begintime
	 */
	public String getBegintime() {
		return begintime;
	}

	/**
	 * Sets the begintime.
	 *
	 * @param begintime the new begintime
	 */
	public void setBegintime(String begintime) {
		this.begintime = begintime;
	}

	/**
	 * Gets the endtime.
	 *
	 * @return the endtime
	 */
	public String getEndtime() {
		return endtime;
	}

	/**
	 * Sets the endtime.
	 *
	 * @param endtime the new endtime
	 */
	public void setEndtime(String endtime) {
		this.endtime = endtime;
	}

	/**
	 * Gets the timemsg.
	 *
	 * @return the timemsg
	 */
	public String getTimemsg() {
		return timemsg;
	}

	/**
	 * Sets the timemsg.
	 *
	 * @param timemsg the new timemsg
	 */
	public void setTimemsg(String timemsg) {
		this.timemsg = timemsg;
	}

	/**
	 * Gets the datefrom.
	 *
	 * @return the datefrom
	 */
	public int getDatefrom() {
		return datefrom;
	}

	/**
	 * Sets the datefrom.
	 *
	 * @param datefrom the new datefrom
	 */
	public void setDatefrom(int datefrom) {
		this.datefrom = datefrom;
	}

	/**
	 * Gets the datenum.
	 *
	 * @return the datenum
	 */
	public int getDatenum() {
		return datenum;
	}

	/**
	 * Sets the datenum.
	 *
	 * @param datenum the new datenum
	 */
	public void setDatenum(int datenum) {
		this.datenum = datenum;
	}

	/**
	 * Gets the send accout.
	 *
	 * @return the send accout
	 */
	public String getSendAccout() {
		return sendAccout;
	}

	/**
	 * Sets the send accout.
	 *
	 * @param sendAccout the new send accout
	 */
	public void setSendAccout(String sendAccout) {
		this.sendAccout = sendAccout;
	}

	/**
	 * Gets the accept accout.
	 *
	 * @return the accept accout
	 */
	public String getAcceptAccout() {
		return acceptAccout;
	}

	/**
	 * Sets the accept accout.
	 *
	 * @param acceptAccout the new accept accout
	 */
	public void setAcceptAccout(String acceptAccout) {
		this.acceptAccout = acceptAccout;
	}

	/**
	 * Gets the result type.
	 *
	 * @return the result type
	 */
	public String getResultType() {
		return resultType;
	}

	/**
	 * Sets the result type.
	 *
	 * @param resultType the new result type
	 */
	public void setResultType(String resultType) {
		this.resultType = resultType;
	}

	/**
	 * Gets the customer id.
	 *
	 * @return the customer id
	 */
	public String getCustomerId() {
		return customerId;
	}

	/**
	 * Sets the customer id.
	 *
	 * @param customerId the new customer id
	 */
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	/**
	 * Gets the tenant id.
	 *
	 * @return the tenant id
	 */
	public String getTenantId() {
		return tenantId;
	}

	/**
	 * Sets the tenant id.
	 *
	 * @param tenantId the new tenant id
	 */
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	
	

	
}
