/**
  * Project Name:SM Maven Webapp
 * File Name:SessionWorkNo.java
 * Package Name:cn.sh.ideal.mir.session.model
 * Date:2014年12月12日上午11:08:01
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.model;

import java.io.Serializable;
import java.util.Date;

import cn.sh.ideal.model.BaseModel;


/**
 * ClassName:SessionWorkNo <br/>
 * Function: 会话工号关联表
 * Date:     2014年12月12日 上午11:08:01 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class SessionWorkNo extends BaseModel implements Serializable{
  
    private static final long serialVersionUID = 7130531337979514870L;
    private String id;
    private String sessionId;
    private String workNo;
    private Date createTime;
    private String tenantCode;
    /*服务开始时间*/
    private Date startTime;
    /*服务结束时间*/
    private Date endTime;
    /*工号加入类型 1 创建者 2 加入者*/
    private String joinType;
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getSessionId() {
        return sessionId;
    }
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    public String getWorkNo() {
        return workNo;
    }
    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getJoinType() {
        return joinType;
    }

    public void setJoinType(String joinType) {
        this.joinType = joinType;
    }

   public enum JoinType{
        CREATER("1"),JOINER("2");
        private String code;
        JoinType(String code){
            this.code = code;
        }
        public String code(){
            return this.code;
        }
    }
    
    
    

}
