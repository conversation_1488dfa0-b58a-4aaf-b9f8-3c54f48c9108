package cn.sh.ideal.sm.model;

import java.io.Serializable;

import cn.sh.ideal.model.BaseModel;


/** 会话超时实体类
 * Created by genghc on 2016/1/25.
 */
public class SessionTimeout extends BaseModel implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private int id;
    /*租户code*/
    private String tenantCode;
    /*坐席第一次超时时间*/
    private int agSessionTimeout;
    /*坐席第二次超时时间*/
    private int agSessionTimeout2;
    /*用户第一次超时时间*/
    private int userSessionTimeout;
    /*用户第二次超时时间*/
    private int userSessionTimeout2;
    /*坐席第一次超时动作*/
    private String agSessionTimeoutAction;
    /*坐席第二次超时动作*/
    private String agSessionTimeoutAction2;
    /*用户第一次超时动作*/
    private String userSessionTimeoutAction;
    /*用户第二次超时动作*/
    private String userSessionTimeoutAction2;
    /*所属技能组*/
    private String skillQueue;
    /*渠道账号*/
    private String acceptedAccount;
    /*排队超时时间*/
    private int sorttimeout;
    /*节假日排队超时时间*/
    private int sorttimeoutHoliday;
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public int getAgSessionTimeout() {
        return agSessionTimeout;
    }

    public void setAgSessionTimeout(int agSessionTimeout) {
        this.agSessionTimeout = agSessionTimeout;
    }

    public int getAgSessionTimeout2() {
        return agSessionTimeout2;
    }

    public void setAgSessionTimeout2(int agSessionTimeout2) {
        this.agSessionTimeout2 = agSessionTimeout2;
    }

    public int getUserSessionTimeout() {
        return userSessionTimeout;
    }

    public void setUserSessionTimeout(int userSessionTimeout) {
        this.userSessionTimeout = userSessionTimeout;
    }

    public String getAgSessionTimeoutAction() {
        return agSessionTimeoutAction;
    }

    public void setAgSessionTimeoutAction(String agSessionTimeoutAction) {
        this.agSessionTimeoutAction = agSessionTimeoutAction;
    }

    public int getUserSessionTimeout2() {
        return userSessionTimeout2;
    }

    public void setUserSessionTimeout2(int userSessionTimeout2) {
        this.userSessionTimeout2 = userSessionTimeout2;
    }

    public String getAgSessionTimeoutAction2() {
        return agSessionTimeoutAction2;
    }

    public void setAgSessionTimeoutAction2(String agSessionTimeoutAction2) {
        this.agSessionTimeoutAction2 = agSessionTimeoutAction2;
    }

    public String getUserSessionTimeoutAction() {
        return userSessionTimeoutAction;
    }

    public void setUserSessionTimeoutAction(String userSessionTimeoutAction) {
        this.userSessionTimeoutAction = userSessionTimeoutAction;
    }

    public String getUserSessionTimeoutAction2() {
        return userSessionTimeoutAction2;
    }

    public void setUserSessionTimeoutAction2(String userSessionTimeoutAction2) {
        this.userSessionTimeoutAction2 = userSessionTimeoutAction2;
    }
    
    public String getSkillQueue() {
		return skillQueue;
	}

	public void setSkillQueue(String skillQueue) {
		this.skillQueue = skillQueue;
	}

	public String getAcceptedAccount() {
		return acceptedAccount;
	}

	public void setAcceptedAccount(String acceptedAccount) {
		this.acceptedAccount = acceptedAccount;
	}

	public int getSorttimeout() {
		return sorttimeout;
	}

	public void setSorttimeout(int sorttimeout) {
		this.sorttimeout = sorttimeout;
	}

	public int getSorttimeoutHoliday() {
		return sorttimeoutHoliday;
	}

	public void setSorttimeoutHoliday(int sorttimeoutHoliday) {
		this.sorttimeoutHoliday = sorttimeoutHoliday;
	}
	
}
