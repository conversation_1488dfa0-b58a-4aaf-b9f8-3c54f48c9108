package cn.sh.ideal.sm.model;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

public class TbComment {
	//主键
	private int id;
	//交易ID
	private long tid; 
	//子订单ID
	private long oid;
	//评价者角色
	private String role;
	//评价者昵称
	private String nick;
	
	//被评价者昵称
	@JSONField(name="rated_nick")
	private String ratedNick;
	
	//评价结果
	private String result;
	
	//商品标题
	@JSONField(name="item_title")
	private String title;
	
	//商品价格  数据库字段是double
	@JSONField(name="item_price")
	private String price;
	//评价关键字（评价内容） 
	private String content;
	//评价解释
	private String reply;
	
	//商品的数字ID
	@JSONField(name="num_iid")
	private long numIid;
	//评价创建时间,格式:yyyy-MM-dd HH:mm:ss
	private Date created;	
	//评价信息是否用于记分， 可取值：1:true(参与记分),2:false(不参与记分)
	private int validScore;	
	//回复状态   1:待回复 2:已回复 3:无需回复
	private String replyStatus;
	
	private String sessionId;
	
	private Date replyTime;
	//导入时间
	private Date importTime;
	
	//查询出的评论总数
	private int count;
	
	private String picUrl;
	
	private String shop;
	
	private String shopUrl;
	
	private String customerId;
	
	private String goodCommentMark;
	
	private String mark;
	
	public String getMark() {
		return mark;
	}
	public void setMark(String mark) {
		this.mark = mark;
	}
	public String getGoodCommentMark() {
		return goodCommentMark;
	}
	public void setGoodCommentMark(String goodCommentMark) {
		this.goodCommentMark = goodCommentMark;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public long getTid() {
		return tid;
	}
	public void setTid(long tid) {
		this.tid = tid;
	}
	public long getOid() {
		return oid;
	}
	public void setOid(long oid) {
		this.oid = oid;
	}
	public String getRole() {
		return role;
	}
	public void setRole(String role) {
		this.role = role;
	}
	public String getNick() {
		return nick;
	}
	public void setNick(String nick) {
		this.nick = nick;
	}
	public String getRatedNick() {
		return ratedNick;
	}
	public void setRatedNick(String ratedNick) {
		this.ratedNick = ratedNick;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getReply() {
		return reply;
	}
	public void setReply(String reply) {
		this.reply = reply;
	}
	public long getNumIid() {
		return numIid;
	}
	public void setNumIid(long numIid) {
		this.numIid = numIid;
	}
	public Date getCreated() {
		return created;
	}
	public void setCreated(Date created) {
		this.created = created;
	}
	public int getValidScore() {
		return validScore;
	}
	public void setValidScore(int validScore) {
		this.validScore = validScore;
	}
	
	public String getReplyStatus() {
		return replyStatus;
	}
	public void setReplyStatus(String replyStatus) {
		this.replyStatus = replyStatus;
	}
	public Date getImportTime() {
		return importTime;
	}
	public void setImportTime(Date importTime) {
		this.importTime = importTime;
	}
	public Date getReplyTime() {
		return replyTime;
	}
	public void setReplyTime(Date replyTime) {
		this.replyTime = replyTime;
	}
	public String getShop() {
		return shop;
	}
	public void setShop(String shop) {
		this.shop = shop;
	}
	public String getShopUrl() {
		return shopUrl;
	}
	public void setShopUrl(String shopUrl) {
		this.shopUrl = shopUrl;
	}
	public String getPicUrl() {
		return picUrl;
	}
	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}
}
