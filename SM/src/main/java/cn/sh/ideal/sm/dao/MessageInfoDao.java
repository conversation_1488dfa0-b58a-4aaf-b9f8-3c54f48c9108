/**
 * Project Name:SM Maven Webapp
 * File Name:MessageInfoDao.java
 * Package Name:cn.sh.ideal.mir.session.dao
 * Date:2015年3月9日下午4:45:45
 * Copyright (c) 2015, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.dao;

import java.util.List;
import java.util.Map;

import cn.sh.ideal.model.MessageInfo;
import cn.sh.ideal.model.Session;
import cn.sh.ideal.model.SessionMessage;

/**
 * ClassName:MessageInfoDao <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2015年3月9日 下午4:45:45 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface MessageInfoDao extends BaseDao<MessageInfo>{
    /**
     * 
     * getUnfinishSessionMessages:(获取未完成会话消息内容). <br/
     *
     * <AUTHOR>
     * @param session
     * @return
     * @throws Exception 
     * @return List<Session>
     * @since JDK 1.6
     */
    public List<Session> getUnfinishSessionMessages(Map<String, Object> session) throws Exception;
    /**
     * 
     * getUserMessageId:(根据会话id获取用户发送的消息id). <br/>
     *
     * <AUTHOR>
     * @param sessionId
     * @return
     * @throws Exception 
     * @return String
     * @since JDK 1.6
     */
    public String getUserMessageId(String sessionId) throws Exception;
    
    /**
     * 
     * getLastMessage:(获取最新一条会话下的消息). <br/>
     *
     * <AUTHOR>
     * @param map
     * @return
     * @throws Exception 
     * @return MessageInfo
     * @since JDK 1.6
     */
    public MessageInfo getLastMessage(Map<String, Object> map) throws Exception;
    /**
     * 
     * getSessionMessages:(获取会话的消息内容). <br/>
     *
     * <AUTHOR>
     * @param session
     * @return
     * @throws Exception 
     * @return List<SessionMessage>
     * @since JDK 1.6
     */
    public List<SessionMessage> getSessionMessages(Map<String, Object> session) throws Exception;
    
    /**
     * 获取上一次会话ID
     * @return
     * @throws Exception
     */
    public String getLastSessionId(MessageInfo lastMesInfo) throws Exception;
    /**
     * 获取当前会话的创建时间
     */
    public String getSessionCreateTime(MessageInfo lastMesInfo) throws Exception;
public abstract void insert(MessageInfo paramMessageInfo)throws Exception;
    
    public abstract List<MessageInfo> getMessages(String paramString)throws Exception;
}
