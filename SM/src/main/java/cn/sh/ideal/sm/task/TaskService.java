/**
 * Project Name:<PERSON> Maven Webapp
 * File Name:TaskService.java
 * Package Name:cn.sh.ideal.mir.session.task
 * Date:2014年12月11日上午10:17:20
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.task;

/**
 * ClassName:TaskService <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 * Date:     2014年12月11日 上午10:17:20 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface TaskService {
    /**
     * 
     * checkSessionTimeout:(定时检查超时会话). <br/>
     *
     * <AUTHOR> void
     * @since JDK 1.6
     */
    public void checkSessionTimeout();

    /**
     * 检查会话数
     */
    public void checkSessionCount();

	/** @Description 
	 */
	public void checkTimeout(String sessionId);

	/** @Description 
	 */
    

}
