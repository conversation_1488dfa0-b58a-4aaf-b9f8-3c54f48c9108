/**
 * Project Name:SM Maven Webapp
 * File Name:AgSessionStatus.java
 * Package Name:cn.sh.ideal.mir.session.util
 * Date:2014年12月20日下午3:01:29
 * Copyright (c) 2014, 上海理想信息产业（集团） All Rights Reserved.
 *
*/

package cn.sh.ideal.sm.utils;
/**
 * ClassName:AgSessionStatus <br/>
 * Function: 坐席会话状态
 * Date:     2014年12月20日 下午3:01:29 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public enum AgSessionStatus {
    /*正在处理*/
    RUNTIME("0"),
    
    /*已关闭*/
    CLOSE("1"),
    
    /*延迟*/
    DELAY("2");
    
    private String code;
    private AgSessionStatus(String  code){
        this.code = code;
    }
    
    public String getCode(){
        return this.code;
    }

}
