package cn.sh.ideal.sm.model;

import java.io.Serializable;

/**
 * SkillQueueWorkNo
 * 技能组工号中间表对象
 * <AUTHOR>
 * @date 2015/8/27
 */
public class SkillQueueWorkNo implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String tenantCode;
    private String skillQueue;
    private String workNo;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getSkillQueue() {
        return skillQueue;
    }

    public void setSkillQueue(String skillQueue) {
        this.skillQueue = skillQueue;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }
}
