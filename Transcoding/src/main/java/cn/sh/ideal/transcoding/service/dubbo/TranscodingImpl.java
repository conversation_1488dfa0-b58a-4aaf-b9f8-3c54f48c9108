/**
 * 
 */
package cn.sh.ideal.transcoding.service.dubbo;

import it.sauronsoftware.jave.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

import cn.sh.ideal.transcoding.model.req.TranscodingRequestDTO;
import cn.sh.ideal.transcoding.model.res.BaseResponseDTO;
import cn.sh.ideal.transcoding.service.TranscodingService;
import cn.sh.ideal.transcoding.util.IdealFtpClient;

/**
 * @project Transcoding
 * @Package cn.sh.ideal.transcoding.service.impl
 * @typeName TranscodingImpl
 * <AUTHOR> Zhou
 * @Description:
 * @date 2016年3月28日 下午5:18:51
 * @version
 */

@Component("transcoding")
public class TranscodingImpl implements TranscodingService {

	private static final Logger log = Logger.getLogger(TranscodingImpl.class);

	@Override
	public BaseResponseDTO transcoding(TranscodingRequestDTO requestObj) {

		BaseResponseDTO baseResponseDTO = new BaseResponseDTO(BaseResponseDTO.SUCCESS_CODE,BaseResponseDTO.SUCCESS_MSG);
		
		try {
			
			String path = requestObj.getPath();
			String ftppath = requestObj.getFtpPath();
			String ftpConfig = requestObj.getFtpConfig();
			String localPath = "./tmp/";
			// 文件名
			String fileName = requestObj.getFileName();
			// 源文件类型
			String sourceFileType = requestObj.getSourceFileType();
			// 目标文件类型
			String targetFileType = requestObj.getTargetFileType();

			downLoadFile(fileName + sourceFileType, ftppath, ftpConfig,
					localPath);
			File source = null;
			File target = null;
			if (!StringUtils.isEmpty(ftpConfig)) {
				source = new File(localPath + fileName + sourceFileType);
				log.info(source.getAbsoluteFile());
				target = new File(localPath + fileName + targetFileType);
				log.info(target.getAbsoluteFile());
			} else {
				source = new File(path + fileName + sourceFileType);
				target = new File(path + fileName + targetFileType);
			}

			AudioAttributes audio = new AudioAttributes();
			audio.setCodec("libmp3lame");
			audio.setBitRate(new Integer(128000));
			audio.setChannels(new Integer(2));
			audio.setSamplingRate(new Integer(44100));
			EncodingAttributes attrs = new EncodingAttributes();
			attrs.setFormat("mp3");
			attrs.setAudioAttributes(audio);
			Encoder encoder = new Encoder();
			encoder.encode(source, target, attrs);

			if (!StringUtils.isEmpty(ftpConfig)) {
				saveFile(fileName + targetFileType,
						new FileInputStream(target), ftppath, ftpConfig);
				source.delete();
				target.delete();
			}
			
			log.info(Thread.currentThread().getName()
					+ " TranscodingController.transcoding["
					+ JSON.toJSONString(baseResponseDTO) + "]");
			
			return baseResponseDTO;
			
		} catch (Exception e) {
			e.printStackTrace();
			log.error("", e);
			baseResponseDTO.setResultCode(BaseResponseDTO.ERROR_CODE);
			baseResponseDTO.setResultMsg("transcoding occur error,error is "+e.getMessage());
			return baseResponseDTO;
		}

	}

	public String saveFile(String fileName, InputStream in, String pathFile,
			String ftpConfig) {
		// 将附件上传到指定FTP目录上去
		IdealFtpClient ftpClient = null;
		String fileName2 = "";
		try {

			ftpClient = new IdealFtpClient();
			String[] ftp = ftpConfig.split(":");
			ftpClient.connectServer(ftp[0], ftp[1], ftp[2], ftp[3]);
			ftpClient.changeDir(pathFile);
			if (ftpClient.isExists(fileName)) {
				fileName2 = "[1]" + fileName;
			} else {
				fileName2 = fileName;
			}

			ftpClient.uploadFile(in, fileName2);
			ftpClient.close();

		} catch (Exception e1) {
			log.error("附件上传FTP出错:" + e1.getMessage(), e1);
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
		return fileName2;
	}

	public static String downLoadFile(String fileName, String pathFile,
			String ftpConfig, String localPath) {
		IdealFtpClient ftpClient = null;
		String fileName2 = "";
		try {
			File file1 = new File(localPath);
			file1.mkdirs();
			ftpClient = new IdealFtpClient();
			String[] ftp = ftpConfig.split(":");
			ftpClient.connectServer(ftp[0], ftp[1], ftp[2], ftp[3]);
			ftpClient.changeDir(ftp[3] + pathFile);
			ftpClient.downLoad(localPath + fileName, ftp[3] + pathFile
					+ fileName);
			ftpClient.close();

		} catch (Exception e1) {
			log.error("附件上传FTP出错:" + e1.getMessage(), e1);
		} finally {
			if (ftpClient != null)
				ftpClient.close();
		}
		return fileName2;
	}

	public static void main(String[] args) throws EncoderException {
		File source = new File("D:\\IMG_1490.mov");
		File target = new File("D:\\IMG_1491.mp4");
		AudioAttributes audio = new AudioAttributes();
		audio.setCodec("libmp3lame");
		audio.setBitRate(new Integer(64000));
		audio.setChannels(new Integer(1));
		audio.setSamplingRate(new Integer(22050));
		VideoAttributes video = new VideoAttributes();
		video.setCodec("mpeg4");
		//video.setBitRate(new Integer(160000));
		//video.setFrameRate(new Integer(15));
		//video.setSize(new VideoSize(400, 300));
		EncodingAttributes attrs = new EncodingAttributes();
		attrs.setFormat("mp4");
		attrs.setAudioAttributes(audio);
		attrs.setVideoAttributes(video);
		Encoder encoder = new Encoder();
		encoder.encode(source, target, attrs);


	}

}
