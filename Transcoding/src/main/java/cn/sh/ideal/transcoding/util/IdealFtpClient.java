package cn.sh.ideal.transcoding.util;

import java.io.InputStream;
import java.io.OutputStream;

import com.enterprisedt.net.ftp.FTPClient;
import com.enterprisedt.net.ftp.FTPConnectMode;
import com.enterprisedt.net.ftp.FTPException;
import com.enterprisedt.net.ftp.FTPTransferType;


/**
 * 
 * <AUTHOR>
 * @date 2009-12-01
 */
public class IdealFtpClient {
	private FTPClient ftpClient = null;
	/**
	 * 连接FTP
	 * @param server
	 * @param user
	 * @param password
	 */
	public void connectServer(String server, String user, String password,String rootDir){
		if(ftpClient == null || !ftpClient.connected()){
			ftpClient = new FTPClient();
			try{
				ftpClient.setControlEncoding("gbk");
				ftpClient.setRemoteHost(server);
				//ftpClient.setTimeout(3000);
				ftpClient.connect();
				ftpClient.login(user, password);
				ftpClient.setConnectMode(FTPConnectMode.PASV); 
				ftpClient.setType(FTPTransferType.BINARY); 
				ftpClient.chdir(rootDir);
			}catch(Exception e){
				e.printStackTrace();
			}
		}
	}
	/**
	 * 进入目录
	 * @param path
	 */
	public void changeDir(String path){
		if(path == null || path.equals("")){
			return;
		}
		String temp[] = path.replace('\\', '/').split("/");
		for(int i=0;i<temp.length;i++){
			try{
				if(temp[i] == null || temp[i].equals("")){
					continue;
				}
				ftpClient.chdir(temp[i]);
			}catch(FTPException fe){
				try{
					ftpClient.mkdir(temp[i]);
					ftpClient.chdir(temp[i]);
				}catch(Exception e){
					
				}
			}catch(Exception ee){
				
			}
		}
	}
	/**
	 * 上传文件
	 * @param in
	 * @param filename
	 */
	public void uploadFile(InputStream in,String filename){
		try{
			ftpClient.put(in,filename);
			
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	
	
	public void downLoad(OutputStream out ,String filename){
		  try{
			  ftpClient.get(out, filename);
		  }catch(Exception e){
			  e.printStackTrace();
		  }
	}
	public void downLoad(String out ,String filename){
		try{
			ftpClient.get(out, filename);
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	/**
	 * Description: 得到文件的字节数组
	 * <AUTHOR>
	 * CreateDate: 2010-3-8
	 * @param filename
	 * @return
	 * @throws Exception
	 */
	public byte[] get(String filename) throws Exception{
		return ftpClient.get(filename);
	}
	/**
	 * Description: 将字节数组写入ftp
	 * <AUTHOR>
	 * CreateDate: 2010-3-8
	 * @param b
	 * @param filename
	 * @throws Exception
	 */
	public void put(byte[] b,String filename)throws Exception{
		ftpClient.put(b, filename);
	}
	
	public boolean isExists(String filename){
		boolean isExists = true;
		  try{
			 byte [] bt = ftpClient.get(filename);
			 if(bt == null || bt.length == 0) isExists = false;
		  }catch(Exception e){
			  isExists = false;
		  }
		 return isExists;
	}
	
	
	
	public boolean delete(String filename){
		  try{
			  ftpClient.delete(filename);
			  return true;
		  }catch(Exception e){
			  return false;
		  }
	}
	
	/**
	 * 关闭连接
	 *
	 */
	public void close(){
		try{
			if(ftpClient != null && ftpClient.connected()){
				ftpClient.quit();
				ftpClient = null;
			}
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	public static void main(String[] args){
		String filename = "logo1.png";
		
		IdealFtpClient ftp1 = new IdealFtpClient();
		ftp1.connectServer("10.4.247.115", "imacp", "imacp","root");
		ftp1.changeDir("info_img");

		IdealFtpClient ftp2 = new IdealFtpClient();
		ftp2.connectServer("10.4.247.115", "imacp", "imacp","root");
		ftp2.changeDir("filestemp");
		
		
		
		try{//拷贝文件
			byte[] b = ftp1.get(filename);
			ftp2.put(b, filename);
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			ftp1.close();
			ftp2.close();
		}
		
	}
}
