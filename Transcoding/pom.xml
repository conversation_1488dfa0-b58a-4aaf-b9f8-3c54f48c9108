<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>multi_media</groupId>
		<artifactId>multi_media</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<artifactId>Transcoding</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>Transcoding_impl</name>
	<description>Transcoding的实现类</description>

	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<env>dev</env>
				<warName>TRANSCODING</warName>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env>test</env>
				<warName>TRANSCODING</warName>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<env>prod</env>
				<warName>TRANSCODING</warName>
			</properties>
		</profile>
	</profiles>



	<dependencies>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>Transcoding_API</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>multi_media</groupId>
			<artifactId>CM</artifactId>
		</dependency>
		<dependency>
			<groupId>com.enterprisedt</groupId>
			<artifactId>edtFTPj</artifactId>
			<version>1.5.3</version>
		</dependency>
		<dependency>
		  <groupId>it.sauronsoftware</groupId>
		  <artifactId>jave</artifactId>
		  <version>1.0.2</version>
		</dependency>
	</dependencies>
	<build>
		<finalName>TRANSCODING</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<filters>
				<filter>src/main/resources/conf/${env}/app-${env}.properties</filter>
		</filters>
		
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<transformers>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>cn.sh.ideal.transcoding.boot.Bootstrap</mainClass>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.handlers</resource>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
									<resource>META-INF/spring.schemas</resource>
								</transformer>
							</transformers>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!--  <plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-jar</id>
						<phase>package</phase>
						<configuration>
							<tasks>
								<delete dir="D:/alljar/TRANSCODING.jar" />
								<copy todir="D:/alljar/">
									<fileset dir="target/">
										<include name="TRANSCODING.jar" />
									</fileset>
								</copy>
							</tasks>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->
		</plugins>
	</build>
</project>