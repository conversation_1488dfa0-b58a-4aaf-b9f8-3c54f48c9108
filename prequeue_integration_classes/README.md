# 进线预排队功能相关文件整理

本文件夹包含了进线预排队功能涉及的所有编译后的class文件、配置文件和相关资源。

## 文件结构说明

### 1. Java Class文件 (按包路径组织)

#### MIR项目相关类：
- `cn/sh/ideal/mir/req/PreQueueOptRequest.class` - 预排队操作请求类
- `cn/sh/ideal/mir/dao/SessionDao.class` - 会话数据访问对象
- `cn/sh/ideal/mir/service/AllocateService.class` - 分配服务接口
- `cn/sh/ideal/mir/service/local/impl/PreQueueHandler.class` - 预排队处理器实现类
- `cn/sh/ideal/mir/service/local/impl/PreQueueHandler$1.class` - 预排队处理器内部类
- `cn/sh/ideal/mir/service/dubbo/AllocateServiceImpl.class` - 分配服务Dubbo实现
- `cn/sh/ideal/mir/service/local/SkillQueueService.class` - 技能队列服务接口
- `cn/sh/ideal/mir/service/local/impl/SkillQueueServiceImpl.class` - 技能队列服务实现

#### IMR项目相关类：
- `cn/sh/ideal/imr/service/systemflow/AgentFlowService.class` - 坐席流程服务

#### MGW项目相关类：
- `cn/sh/ideal/mgw/service/dubbo/channel/CustomServiceImpl.class` - 自定义服务实现

### 2. 配置文件

#### IMR项目配置：
- `conf/imr/app-dev.properties` - IMR开发环境配置文件
  - 新增配置：`msg.alreadyagent=当前您已在转接人工服务排队中，请您耐心等待~，在等待期间，您也可以继续简述问题与智能客服小鹿沟通，可能会有助于更加快捷地解答您的问题~`

#### SM项目配置：
- `conf/sm/app-dev.properties` - SM开发环境配置文件
  - 修改配置：`session.isSortTimeout = false`

#### SI项目配置：
- `conf/si/customMessageTypes.properties` - SI自定义消息类型配置
  - 新增配置：`dubbo.preQueueOpt=cn.sh.ideal.mir.service.AllocateService;preQueueOpt;cn.sh.ideal.mir.req.PreQueueOptRequest`

### 3. Mapper文件
- `mapper/mysql/routing/SessionMapper.xml` - 会话相关的MyBatis映射文件

### 4. 依赖库
- `lib/MIR-API.jar` - 更新后的MIR API jar包，包含PreQueueOptRequest和AllocateService接口

## 部署说明

1. **MIR项目**：将相关class文件部署到MIR项目的classes目录下
2. **IMR项目**：
   - 部署AgentFlowService.class到对应目录
   - 更新app-dev.properties配置文件，添加msg.alreadyagent配置
3. **MGW项目**：部署CustomServiceImpl.class到对应目录
4. **SM项目**：更新app-dev.properties配置文件，修改session.isSortTimeout为false
5. **SI项目**：
   - 添加customMessageTypes.properties配置文件
   - 替换lib目录下的MIR-API.jar文件

## 功能说明

此次整理的文件实现了进线预排队功能，主要包括：
- 预排队操作请求处理
- 排队超时处理
- 坐席分配逻辑
- 消息推送机制
- 配置管理优化

所有文件都是编译后的class文件，可以直接用于生产环境部署。
