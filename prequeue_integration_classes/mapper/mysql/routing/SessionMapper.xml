<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mir.dao.SessionDao" >

  <!-- <resultMap id="NoticeResultMap" type="cn.sh.ideal.mir.model.NoticeAccount" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
     <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
     <result column="service_account" property="serviceAccount" jdbcType="VARCHAR" />
    <result column="work_no" property="workNo" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
  </resultMap> -->

  <select id="queryPriority"  resultType="string" parameterType="hashmap">
  select floor(sum(a.priority)) from
		(select priority from mgw_channel_priority where tenant_code = #{tenantCode, jdbcType = VARCHAR}
		 and skill_queue_id =  #{queueId, jdbcType = VARCHAR} and channel_code = #{channelCode, jdbcType = VARCHAR}
		union all
		select priority from mgw_busi_priority where tenant_code = #{tenantCode, jdbcType = VARCHAR}
		 and skill_queue_id =  #{queueId, jdbcType = VARCHAR} and busi_id = #{businessType, jdbcType = VARCHAR}
		)a

  </select>

  <select id="getLastAgent"  resultType="cn.sh.ideal.model.SessionInfo" parameterType="hashmap">
			select workno workNos,
			  skill_queue skillQueue
			  from mgw_session_info
			  where tenant_code = #{tenantCode, jdbcType = VARCHAR}
			  and customer_id= #{customerId, jdbcType = VARCHAR}
			  and workno is not null
			   <if test="timeout != null and timeout !=''">
				and create_time &gt;= #{timeout,jdbcType=TIMESTAMP}
			  </if>
			  <if test="skillQueue != null and skillQueue !=''">
				and skill_queue = #{skillQueue,jdbcType=VARCHAR}
			  </if>
			 order by create_time desc
			 limit 1
  </select>

    <select id="getQueueSession"  resultType="cn.sh.ideal.mir.resp.entity.SessionQueue" parameterType="hashmap">
  		select
		DATE_FORMAT(TIMEDIFF(now(),t.create_time),'%H:%i:%s') queueTime,
		t.SESSION_ID sessionId,
		t.TENANT_CODE tenantCode,
		t.CHANNEL_CODE channelCode,
		t.SEND_ACCOUNT sendAccount,
		t.ACCEPTED_ACCOUNT acceptAccount,
		t.CUSTOMER_ID customerId,
		IFNULL(t1.REAL_NAME,t.NICK_NAME) customerName
		from mgw_session_info t
		inner join cuv_customer_info t1
		on t.customer_id = t1.customer_id and t.status=3
		and t.create_time > (select date_sub(now(),interval 1 day))
		and skill_queue = #{skillQueue,jdbcType=VARCHAR}
  </select>

    <select id="getVoiceSession"  resultType="cn.sh.ideal.model.SessionInfo" parameterType="cn.sh.ideal.model.SessionInfo">
			select workno workNos,
			  skill_queue skillQueue
			  from mgw_session_info
			  where channel_code = '1003'
			  and status=5
			  and tenant_code = #{tenantCode, jdbcType = VARCHAR}
			  and customer_id= #{customerId, jdbcType = VARCHAR}
			 order by create_time desc
			 limit 1
  </select>

    <select id="getExclusiveAgent"  resultType="string" parameterType="string">
	   select work_no from cms_user_owner_test where customer_id=#{customerId}
  </select>

  <select id="getCurrTimeStamp" resultType="string">
        select replace(unix_timestamp(current_timestamp(3)),'.','') from dual
  </select>

  <select id="getSessionCount" resultType="int" parameterType="map">
		select count(1) from mgw_session_info t1
		left join mgw_tenant_skill_type t2 on(t1.tenant_code = t2.tenant_id and t1.skill_type = t2.skill_type_code)
		where t1.status in('5','8') and t2.status=1 and t2.is_record = 'Y'
		and t1.TENANT_CODE = #{tenantCode} and t1.workno=#{workNo}
	</select>

	<delete id="delSessionStatusLog" parameterType="string">
		delete from mgw_session_status_log where session_id = #{sessionId} and session_status='3'
	</delete>
</mapper>
